using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.Services;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Users.Commands.SyncUsersFromAd;

/// <summary>
/// Handler for SyncUsersFromAdCommand
/// </summary>
public class SyncUsersFromAdCommandHandler : BaseCommandHandler<SyncUsersFromAdCommand, SyncUsersFromAdResult>
{
    private readonly IApplicationDbContext _context;
    private readonly IActiveDirectoryService _adService;
    private readonly IAdRoleMappingService _roleMappingService;
    private readonly ILogger<SyncUsersFromAdCommandHandler> _logger;

    public SyncUsersFromAdCommandHandler(
        IApplicationDbContext context,
        IActiveDirectoryService adService,
        IAdRoleMappingService roleMappingService,
        ILogger<SyncUsersFromAdCommandHandler> logger)
    {
        _context = context;
        _adService = adService;
        _roleMappingService = roleMappingService;
        _logger = logger;
    }

    public override async Task<SyncUsersFromAdResult> Handle(SyncUsersFromAdCommand request, CancellationToken cancellationToken)
    {
        var result = new SyncUsersFromAdResult
        {
            SyncStartTime = DateTime.UtcNow,
            WasDryRun = request.DryRun
        };

        try
        {
            _logger.LogInformation("Starting AD user sync {Mode}", request.DryRun ? "(DRY RUN)" : "");

            // Get all users from Active Directory
            var adUsers = await _adService.GetUsersAsync(cancellationToken);
            var adUsersList = adUsers.Where(u => IsValidAdUser(u)).ToList();
            result.TotalAdUsers = adUsersList.Count;

            _logger.LogInformation("Retrieved {Count} valid users from Active Directory", adUsersList.Count);

            // Get all existing users from database
            var dbUsers = await _context.Users
                .Include(u => u.Role)
                .Where(u => !string.IsNullOrEmpty(u.AdObjectGuid))
                .ToListAsync(cancellationToken);
            result.TotalDbUsers = dbUsers.Count;

            // Create lookup dictionaries
            var dbUsersByAdGuid = dbUsers.ToDictionary(u => u.AdObjectGuid, StringComparer.OrdinalIgnoreCase);
            var adUsersByGuid = adUsersList.ToDictionary(u => u.ObjectGuid, StringComparer.OrdinalIgnoreCase);

            // Process AD users (create new or update existing)
            foreach (var adUser in adUsersList)
            {
                try
                {
                    if (dbUsersByAdGuid.TryGetValue(adUser.ObjectGuid, out var existingUser))
                    {
                        // Update existing user
                        if (await UpdateUserFromAd(existingUser, adUser, request.DryRun, cancellationToken))
                        {
                            result.UsersUpdated++;
                        }
                    }
                    else
                    {
                        // Create new user
                        if (await CreateUserFromAd(adUser, request.DryRun, cancellationToken))
                        {
                            result.UsersCreated++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error processing AD user {adUser.Username}: {ex.Message}";
                    result.Errors.Add(error);
                    _logger.LogError(ex, "Error processing AD user {Username}", adUser.Username);
                }
            }

            // Disable users that are no longer in AD
            foreach (var dbUser in dbUsers)
            {
                if (!adUsersByGuid.ContainsKey(dbUser.AdObjectGuid) && dbUser.IsActive)
                {
                    try
                    {
                        if (await DisableUser(dbUser, request.DryRun, cancellationToken))
                        {
                            result.UsersDisabled++;
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = $"Error disabling user {dbUser.Username}: {ex.Message}";
                        result.Errors.Add(error);
                        _logger.LogError(ex, "Error disabling user {Username}", dbUser.Username);
                    }
                }
            }

            // Save changes if not a dry run
            if (!request.DryRun)
            {
                await _context.SaveChangesAsync(cancellationToken);
            }

            result.SyncEndTime = DateTime.UtcNow;
            _logger.LogInformation("AD user sync completed: {Result}", result.ToString());

            return result;
        }
        catch (Exception ex)
        {
            result.SyncEndTime = DateTime.UtcNow;
            result.Errors.Add($"Sync failed: {ex.Message}");
            _logger.LogError(ex, "AD user sync failed");
            throw;
        }
    }

    private static bool IsValidAdUser(AdUserDto adUser)
    {
        return !string.IsNullOrWhiteSpace(adUser.ObjectGuid) &&
               !string.IsNullOrWhiteSpace(adUser.Username) &&
               !string.IsNullOrWhiteSpace(adUser.LastName) &&
               adUser.IsEnabled;
    }

    private async Task<bool> CreateUserFromAd(AdUserDto adUser, bool dryRun, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating new user from AD: {Username}", adUser.Username);

        // Determine role from AD groups (user doesn't exist in DB yet, so use AD group mapping)
        var role = await _roleMappingService.MapAdGroupsToRoleAsync(adUser.MemberOf ?? [], cancellationToken);
        var roleEntity = await _context.Roles.FirstOrDefaultAsync(r => r.RoleName == role, cancellationToken);

        if (roleEntity == null)
        {
            throw new InvalidOperationException($"Role '{role}' not found in database");
        }

        if (!dryRun)
        {
            var user = User.Create(
                username: adUser.Username,
                firstName: adUser.FirstName,
                lastName: adUser.LastName,
                email: adUser.Email,
                roleId: roleEntity.Id,
                factoryId: null, // Will be assigned separately if needed
                isActive: adUser.IsEnabled,
                adObjectGuid: adUser.ObjectGuid,
                adDistinguishedName: adUser.DistinguishedName,
                createdByUserId: "SYSTEM_AD_SYNC");

            user.AdSyncLastDate = DateTime.UtcNow;
            await _context.Users.AddAsync(user, cancellationToken);
        }

        _logger.LogInformation("Created user {Username} with role {Role} {DryRun}", 
            adUser.Username, role, dryRun ? "(DRY RUN)" : "");
        return true;
    }

    private async Task<bool> UpdateUserFromAd(User dbUser, AdUserDto adUser, bool dryRun, CancellationToken cancellationToken)
    {
        var hasChanges = false;

        // Check if user information has changed
        if (dbUser.FirstName != adUser.FirstName ||
            dbUser.LastName != adUser.LastName ||
            dbUser.Email != adUser.Email ||
            dbUser.AdDistinguishedName != adUser.DistinguishedName ||
            dbUser.IsActive != adUser.IsEnabled)
        {
            hasChanges = true;
        }

        // Check if role needs to be updated - use database-first approach
        var (newRole, userExistsInDb) = await _roleMappingService.GetUserRoleWithExistenceAsync(dbUser.AdObjectGuid, adUser.MemberOf ?? [], cancellationToken);
        if (dbUser.Role.RoleName != newRole)
        {
            var roleEntity = await _context.Roles.FirstOrDefaultAsync(r => r.RoleName == newRole, cancellationToken);
            if (roleEntity != null)
            {
                hasChanges = true;
                if (!dryRun)
                {
                    dbUser.RoleId = roleEntity.Id;
                }
                _logger.LogInformation("Updated role for user {Username}: {OldRole} -> {NewRole} {DryRun}",
                    dbUser.Username, dbUser.Role.RoleName, newRole, dryRun ? "(DRY RUN)" : "");
            }
        }

        if (hasChanges && !dryRun)
        {
            dbUser.UpdateFromAdSync(
                firstName: adUser.FirstName,
                lastName: adUser.LastName,
                email: adUser.Email,
                adDistinguishedName: adUser.DistinguishedName,
                isActive: adUser.IsEnabled);

            _logger.LogDebug("Updated user {Username} from AD {DryRun}", 
                dbUser.Username, dryRun ? "(DRY RUN)" : "");
        }

        return hasChanges;
    }

    private async Task<bool> DisableUser(User dbUser, bool dryRun, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Disabling user {Username} (no longer in AD) {DryRun}", 
            dbUser.Username, dryRun ? "(DRY RUN)" : "");

        if (!dryRun)
        {
            dbUser.Disable();
        }

        return true;
    }
}
