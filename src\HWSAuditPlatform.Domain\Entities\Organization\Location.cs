using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Organization;

/// <summary>
/// Represents a geographical location where factories are situated.
/// Maps to the Location table in the database.
/// </summary>
public class Location : BaseEntity<int>
{
    /// <summary>
    /// Name of the location (e.g., country, region)
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string LocationName { get; set; } = string.Empty;

    /// <summary>
    /// Country where the location is situated
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string LocationCountry { get; set; } = string.Empty;

    /// <summary>
    /// Standard country code (e.g., ISO 3166-1 alpha-2)
    /// </summary>
    [MaxLength(10)]
    public string? LocationCountryCode { get; set; }

    /// <summary>
    /// Identifier for an external owning group or business unit, context-specific
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string OwnerGroupId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for factories in this location
    /// </summary>
    public virtual ICollection<Factory> Factories { get; set; } = new List<Factory>();
}
