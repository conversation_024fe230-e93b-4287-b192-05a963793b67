#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Health check script for HWS Audit Platform Docker environment
.DESCRIPTION
    This script checks the health of all Docker containers and services,
    verifying connectivity and the existence of key data.
#>

# Set error action preference to stop on the first error
$ErrorActionPreference = "Stop"

# Helper function for colored console output
function Write-ColorOutput {
  param([string]$Message, [string]$Color = "White")
  Write-Host $Message -ForegroundColor $Color
}

# Checks the running and health status of a specific container
function Test-ContainerHealth {
  param([string]$ContainerName)

  try {
    # Use a single docker inspect call for efficiency
    $status =
      docker inspect --format '{{.State.Running}},{{.State.Health.Status}}' $ContainerName
    $isRunning, $health = $status.Split(",")

    if ($isRunning -eq "true" -and $health -eq "healthy") {
      Write-ColorOutput "  [pipa] $ContainerName is running and healthy" "Green"
      return $true
    }
    elseif ($isRunning -eq "true") {
      Write-ColorOutput "  [!] $ContainerName is running but not healthy (Status: $health)" "Yellow"
      return $false
    }
    else {
      Write-ColorOutput "  [x] $ContainerName is not running" "Red"
      return $false
    }
  }
  catch {
    Write-ColorOutput "  [x] $ContainerName not found or error checking status" "Red"
    return $false
  }
}

# Tests the connection to SQL Server using the 'sa' account
function Test-SqlConnection {
  try {
    # Use 'sa' user for a reliable check that doesn't depend on migrations
    $result =
      docker exec -it hwsaudit-sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P "HWSAudit123!" -C -Q "SELECT 1" -h -1   
    if ($result.Trim() -eq "1") {
      Write-ColorOutput "  [pipa] SQL Server connection successful (as sa)" "Green"
      return $true
    }
    else {
      Write-ColorOutput "  [x] SQL Server connection failed. Unexpected result: $result" "Red"
      return $false
    }
  }
  catch {
    Write-ColorOutput "  [x] Error executing SQL Server connection test." "Red"
    return $false
  }
}

# Tests the connection to Active Directory by querying domain info
function Test-AdConnection {
  try {
    # Execute the command and capture all output (stdout and stderr)
    $result = docker exec hwsaudit-ad samba-tool domain info hwsaudit.local 2>&1

    # The most reliable check:
    # 1. Did the command itself succeed ($? -eq $true)?
    # 2. Does the successful output contain the correct domain name?
    if ($? -and ($result -match "(?i)Domain\s+:\s+hwsaudit\.local")) {
      Write-ColorOutput "  [OK] Active Directory connection successful (Domain: hwsaudit.local)" "Green"
      return $true
    }
    else {
      Write-ColorOutput "  [X] Active Directory connection failed. Could not verify domain info." "Red"
      # Add a debug line to show what the script actually received, which is very helpful.
      Write-Host "    -> Script received the following output from the container:"
      $result | ForEach-Object { Write-Host "       $_" -ForegroundColor "Gray" }
      return $false
    }
  }
  catch {
    # This block would catch a PowerShell-level error, like 'docker' not being a valid command.
    Write-ColorOutput "  [X] A script error occurred while executing the AD connection test." "Red"
    return $false
  }
}

# Checks if a specific database exists on the SQL Server instance
function Test-DatabaseExists {
  param([string]$DatabaseName)

  try {
    # Use 'sa' user for reliability
    $query = "SELECT name FROM sys.databases WHERE name = '$DatabaseName'"
    $result =
    
      docker exec -it hwsaudit-sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P "HWSAudit123!" -C -Q  $query -h -1
    if ($result.Trim() -eq $DatabaseName) {
      Write-ColorOutput "  [pipa] Database '$DatabaseName' exists" "Green"
      return $true
    }
    else {
      Write-ColorOutput "  [x] Database '$DatabaseName' not found" "Red"
      return $false
    }
  }
  catch {
    Write-ColorOutput "  [x] Error checking for database '$DatabaseName'." "Red"
    return $false
  }
}

# Checks if a specific user exists in Active Directory
function Test-AdUser {
  param([string]$Username)

  # FIXED: Don't use try/catch. Instead, execute the command and then check
  # PowerShell's automatic variable '$?' which is $true if the last command
  # succeeded and $false if it failed. This is the most reliable method.
  docker exec hwsaudit-ad samba-tool user show $Username 2>&1 | Out-Null
  if ($?) {
    Write-ColorOutput "  [OK] AD User '$Username' exists" "Green"
    return $true
  }
  else {
    Write-ColorOutput "  [X] AD User '$Username' not found" "Red"
    return $false
  }
}

# --- Main Health Check Execution ---
try {
  Write-ColorOutput "HWS Audit Platform Docker Health Check" "Blue"
  Write-ColorOutput "=========================================" "Blue"
  Write-Host ""

  $allHealthy = $true

  # Check container health
  Write-ColorOutput "1. Container Health:" "Blue"
  if (-not (Test-ContainerHealth "hwsaudit-sqlserver")) { $allHealthy = $false }
  if (-not (Test-ContainerHealth "hwsaudit-ad")) { $allHealthy = $false }
  Write-Host ""

  # Check SQL Server connection and databases
  Write-ColorOutput "2. SQL Server Details:" "Blue"
  if (-not (Test-SqlConnection)) { $allHealthy = $false }
  if (-not (Test-DatabaseExists "HWSAuditPlatformDb")) { $allHealthy = $false }
  Write-Host ""

  # Check Active Directory connection and users
  Write-ColorOutput "3. Active Directory Details:" "Blue"
  if (-not (Test-AdConnection)) { $allHealthy = $false }
  if (-not (Test-AdUser "admin.user")) { $allHealthy = $false }
  if (-not (Test-AdUser "auditor.user")) { $allHealthy = $false }
  Write-Host ""

  # Overall status
  if ($allHealthy) {
    Write-ColorOutput "[OK] All systems are healthy!" "Green"
    Write-Host ""
    Write-ColorOutput "Ready to run the application:" "Blue"
    Write-Host "  dotnet run --project src/HWSAuditPlatform.ApiService --environment Docker"
    Write-Host "  or"
    Write-Host "  dotnet run --project src/HWSAuditPlatform.AppHost"
    exit 0
  }
  else {
    Write-ColorOutput "[ERROR] Some systems reported issues. Please review the checks above." "Red"
    Write-Host ""
    Write-ColorOutput "Troubleshooting:" "Blue"
    Write-Host "  * Check container logs: docker-compose logs -f"
    Write-Host "  * If AD users are missing, check your 'setup-ad.sh' script and re-run the start script."
    Write-Host "  * For a full reset: ./docker-start.ps1 -Clean"
    exit 1
  }
}
catch {
  $errorMessage = $_.Exception.Message
  Write-ColorOutput "A critical error occurred during the health check: $errorMessage" "Red"
  exit 1
}