@page "/users"
@attribute [Authorize(Roles = "<PERSON><PERSON><PERSON><PERSON>,SystemManager")]

<PageTitle>Users - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">
                        <i class="bi bi-people me-2 text-info"></i>
                        User Management
                    </h1>
                    <p class="text-muted mb-0">Manage users, roles, and permissions</p>
                </div>
                <div>
                    <button class="btn btn-info" @onclick="CreateNewUser">
                        <i class="bi bi-plus-circle me-2"></i>Add User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Search users..." @bind="searchTerm" @onkeypress="@(async (e) => { if (e.Key == "Enter") await SearchUsers(); })" />
                <button class="btn btn-outline-secondary" @onclick="SearchUsers">Search</button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" @bind="roleFilter" @bind:after="OnRoleFilterChanged">
                <option value="">All Roles</option>
                <option value="DevAdmin">Dev Admin</option>
                <option value="SystemManager">System Manager</option>
                <option value="ProcessOwner">Process Owner</option>
                <option value="Auditor">Auditor</option>
                <option value="Viewer">Viewer</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" @bind="statusFilter" @bind:after="OnStatusFilterChanged">
                <option value="">All Users</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                <i class="bi bi-x-circle me-1"></i>Clear
            </button>
        </div>
    </div>

    <!-- User List -->
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading users...</p>
        </div>
    }
    else if (users?.Any() == true)
    {
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var user in users)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                @GetUserInitials(user.FullName)
                                            </div>
                                            <div>
                                                <div class="fw-semibold">@user.FullName</div>
                                                <small class="text-muted">@user.Username</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>@user.Email</td>
                                    <td>
                                        <span class="badge @GetRoleBadgeClass(user.RoleId)">
                                            @GetRoleDisplayName(user.RoleId)
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge @(user.IsActive ? "bg-success" : "bg-secondary")">
                                            @(user.IsActive ? "Active" : "Inactive")
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted">N/A</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewUser(user.Id)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" @onclick="() => EditUser(user.Id)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            @if (user.IsActive)
                                            {
                                                <button class="btn btn-outline-warning" @onclick="() => DeactivateUser(user.Id)">
                                                    <i class="bi bi-pause"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-outline-success" @onclick="() => ActivateUser(user.Id)">
                                                    <i class="bi bi-play"></i>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-people-fill display-1 text-muted"></i>
            <h3 class="mt-3">No users found</h3>
            <p class="text-muted">No users match your current filters.</p>
            <button class="btn btn-info" @onclick="CreateNewUser">
                <i class="bi bi-plus-circle me-2"></i>Add User
            </button>
        </div>
    }
</div>

<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>

@code {
    private IEnumerable<User>? users;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string roleFilter = string.Empty;
    private string statusFilter = string.Empty;

    [Inject] private IUserApiService UserService { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<Users> Logger { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            users = await UserService.GetUsersAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading users");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchUsers()
    {
        await LoadUsers(); // For now, just reload all data
    }

    private async Task OnRoleFilterChanged()
    {
        await LoadUsers(); // For now, just reload all data
    }

    private async Task OnStatusFilterChanged()
    {
        await LoadUsers(); // For now, just reload all data
    }

    private async Task ClearFilters()
    {
        searchTerm = string.Empty;
        roleFilter = string.Empty;
        statusFilter = string.Empty;
        await LoadUsers();
    }

    private string GetUserInitials(string fullName)
    {
        if (string.IsNullOrEmpty(fullName)) return "?";
        
        var parts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return fullName[0].ToString().ToUpper();
    }

    private string GetRoleBadgeClass(int roleId)
    {
        return roleId switch
        {
            1 => "bg-danger",      // DevAdmin
            2 => "bg-warning",     // SystemManager
            3 => "bg-info",        // ProcessOwner
            4 => "bg-primary",     // Auditor
            5 => "bg-secondary",   // Viewer
            _ => "bg-secondary"
        };
    }

    private string GetRoleDisplayName(int roleId)
    {
        return roleId switch
        {
            1 => "Dev Admin",
            2 => "System Manager",
            3 => "Process Owner",
            4 => "Auditor",
            5 => "Viewer",
            _ => "Unknown"
        };
    }

    private void CreateNewUser()
    {
        Navigation.NavigateTo("/users/create");
    }

    private void ViewUser(string userId)
    {
        Navigation.NavigateTo($"/users/{userId}");
    }

    private void EditUser(string userId)
    {
        Navigation.NavigateTo($"/users/{userId}/edit");
    }

    private async Task ActivateUser(string userId)
    {
        // TODO: Implement activate user API call
        await LoadUsers();
    }

    private async Task DeactivateUser(string userId)
    {
        // TODO: Implement deactivate user API call
        await LoadUsers();
    }
}
