using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Findings.DTOs;

/// <summary>
/// Data Transfer Object for FindingCategory entity
/// </summary>
public class FindingCategoryDto : AuditableDto<int>
{
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool RequiresDocumentation { get; set; }
    public int FindingCount { get; set; }
    public int OpenFindingCount { get; set; }
    public bool IsUsable { get; set; }
}
