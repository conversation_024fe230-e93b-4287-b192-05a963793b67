using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace HWSAuditPlatform.Application.Users.Queries.GetUserGroups;

/// <summary>
/// Handler for GetUserGroupsQuery
/// </summary>
public class GetUserGroupsQueryHandler : BaseQueryHandler<GetUserGroupsQuery, PaginatedResult<UserGroupSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetUserGroupsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<UserGroupSummaryDto>> Handle(GetUserGroupsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.UserGroups
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(ug =>
                EF.Functions.Like(ug.GroupName, searchPattern) ||
                (ug.Description != null && EF.Functions.Like(ug.Description, searchPattern)));
        }

        if (request.IsAdSynced.HasValue)
        {
            query = query.Where(ug => ug.IsAdSynced == request.IsAdSynced.Value);
        }

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDescending);

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var userGroups = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(ug => new UserGroupSummaryDto
            {
                Id = ug.Id,
                GroupName = ug.GroupName,
                Description = ug.Description,
                MemberCount = _context.UserGroupMembers.Count(ugm => ugm.UserGroupId == ug.Id),
                IsAdSynced = ug.IsAdSynced
            })
            .ToListAsync(cancellationToken);

        return PaginatedResult<UserGroupSummaryDto>.Create(userGroups, totalCount, request.PageNumber, request.PageSize);
    }

    private IQueryable<Domain.Entities.Users.UserGroup> ApplySorting(
        IQueryable<Domain.Entities.Users.UserGroup> query,
        string? sortBy,
        bool sortDescending)
    {
        if (string.IsNullOrEmpty(sortBy))
            sortBy = "GroupName";

        Expression<Func<Domain.Entities.Users.UserGroup, object>> keySelector = sortBy.ToLower() switch
        {
            "groupname" => ug => ug.GroupName,
            "description" => ug => ug.Description ?? string.Empty,
            "membercount" => ug => _context.UserGroupMembers.Count(ugm => ugm.UserGroupId == ug.Id),
            "isadsynced" => ug => ug.IsAdSynced,
            "createdat" => ug => ug.CreatedAt,
            _ => ug => ug.GroupName
        };

        return sortDescending
            ? query.OrderByDescending(keySelector)
            : query.OrderBy(keySelector);
    }
}
