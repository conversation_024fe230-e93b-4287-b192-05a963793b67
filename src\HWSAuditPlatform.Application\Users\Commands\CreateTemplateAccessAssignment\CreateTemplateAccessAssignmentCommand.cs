using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Users.Commands.CreateTemplateAccessAssignment;

/// <summary>
/// Command to create a new template access assignment
/// </summary>
public class CreateTemplateAccessAssignmentCommand : BaseCommand<int>
{
    public string UserId { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public string? Description { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
