// Caution! Be sure you understand the caveats before publishing an application with
// offline support. See https://aka.ms/blazor-offline-considerations

self.importScripts('./service-worker-assets.js');
self.addEventListener('install', event => event.waitUntil(onInstall(event)));
self.addEventListener('activate', event => event.waitUntil(onActivate(event)));
self.addEventListener('fetch', event => event.respondWith(onFetch(event)));
self.addEventListener('sync', event => event.waitUntil(onBackgroundSync(event)));
self.addEventListener('message', event => onMessage(event));

const cacheNamePrefix = 'offline-cache-';
const cacheName = `${cacheNamePrefix}${self.assetsManifest.version}`;
const offlineAssetsInclude = [ /\.dll$/, /\.pdb$/, /\.wasm/, /\.html/, /\.js$/, /\.json$/, /\.css$/, /\.woff$/, /\.png$/, /\.jpe?g$/, /\.gif$/, /\.ico$/, /\.blat$/, /\.dat$/ ];
const offlineAssetsExclude = [ /^service-worker\.js$/ ];

// Replace with your base path if you are hosting on a subfolder. Ensure there is a trailing '/'.
const base = "/";
const baseUrl = new URL(base, self.origin);
const manifestUrlList = self.assetsManifest.assets.map(asset => new URL(asset.url, baseUrl).href);

async function onInstall(event) {
    console.info('Service worker: Install');

    // Fetch and cache all matching items from the assets manifest
    const assetsRequests = self.assetsManifest.assets
        .filter(asset => offlineAssetsInclude.some(pattern => pattern.test(asset.url)))
        .filter(asset => !offlineAssetsExclude.some(pattern => pattern.test(asset.url)))
        .map(asset => new Request(asset.url, { integrity: asset.hash, cache: 'no-cache' }));
    await caches.open(cacheName).then(cache => cache.addAll(assetsRequests));
}

async function onActivate(event) {
    console.info('Service worker: Activate');

    // Delete unused caches
    const cacheKeys = await caches.keys();
    await Promise.all(cacheKeys
        .filter(key => key.startsWith(cacheNamePrefix) && key !== cacheName)
        .map(key => caches.delete(key)));
}

async function onFetch(event) {
    let cachedResponse = null;
    if (event.request.method === 'GET') {
        // For all navigation requests, try to serve index.html from cache,
        // unless that request is for an offline resource.
        // If you need some URLs to be server-rendered, edit the following check to exclude those URLs
        const shouldServeIndexHtml = event.request.mode === 'navigate'
            && !manifestUrlList.some(url => url === event.request.url);

        const request = shouldServeIndexHtml ? 'index.html' : event.request;
        const cache = await caches.open(cacheName);
        cachedResponse = await cache.match(request);
    }

    return cachedResponse || fetch(event.request);
}

async function onBackgroundSync(event) {
    console.log('Background sync event:', event.tag);

    if (event.tag === 'audit-data') {
        await syncAuditData();
    } else if (event.tag === 'attachments') {
        await syncAttachments();
    } else if (event.tag === 'audit-answers') {
        await syncAuditAnswers();
    }
}

async function syncAuditData() {
    try {
        console.log('Syncing audit data in background...');

        // Get pending sync items from IndexedDB
        const pendingItems = await getPendingSyncItems('audit-data');

        for (const item of pendingItems) {
            try {
                // Attempt to sync the item
                const success = await performSync(item);

                if (success) {
                    // Remove from sync queue
                    await removeSyncItem(item.id);
                    console.log('Successfully synced audit data item:', item.id);
                } else {
                    // Increment attempt count
                    await incrementSyncAttempts(item.id);
                }
            } catch (error) {
                console.error('Error syncing audit data item:', item.id, error);
                await incrementSyncAttempts(item.id);
            }
        }
    } catch (error) {
        console.error('Error during audit data background sync:', error);
    }
}

async function syncAttachments() {
    try {
        console.log('Syncing attachments in background...');

        const pendingItems = await getPendingSyncItems('attachments');

        for (const item of pendingItems) {
            try {
                const success = await performSync(item);

                if (success) {
                    await removeSyncItem(item.id);
                    console.log('Successfully synced attachment:', item.id);
                } else {
                    await incrementSyncAttempts(item.id);
                }
            } catch (error) {
                console.error('Error syncing attachment:', item.id, error);
                await incrementSyncAttempts(item.id);
            }
        }
    } catch (error) {
        console.error('Error during attachment background sync:', error);
    }
}

async function syncAuditAnswers() {
    try {
        console.log('Syncing audit answers in background...');

        const pendingItems = await getPendingSyncItems('audit-answers');

        for (const item of pendingItems) {
            try {
                const success = await performSync(item);

                if (success) {
                    await removeSyncItem(item.id);
                    console.log('Successfully synced audit answers:', item.id);
                } else {
                    await incrementSyncAttempts(item.id);
                }
            } catch (error) {
                console.error('Error syncing audit answers:', item.id, error);
                await incrementSyncAttempts(item.id);
            }
        }
    } catch (error) {
        console.error('Error during audit answers background sync:', error);
    }
}

async function performSync(item) {
    // This would make the actual API call to sync the data
    // For now, we'll simulate success/failure
    try {
        // In a real implementation, this would:
        // 1. Make API call with item.data
        // 2. Handle authentication
        // 3. Return success/failure based on response

        console.log('Performing sync for item:', item.id, 'Type:', item.syncType);

        // Simulate network request
        const response = await fetch('/api/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(item.data)
        });

        return response.ok;
    } catch (error) {
        console.error('Sync request failed:', error);
        return false;
    }
}

function onMessage(event) {
    console.log('Service worker received message:', event.data);

    if (event.data && event.data.type === 'SYNC_REQUEST') {
        // Handle manual sync request from the app
        event.waitUntil(handleSyncRequest(event.data));
    }
}

async function handleSyncRequest(data) {
    try {
        console.log('Handling sync request:', data.syncType);

        if (data.syncType === 'audit-data') {
            await syncAuditData();
        } else if (data.syncType === 'attachments') {
            await syncAttachments();
        } else if (data.syncType === 'audit-answers') {
            await syncAuditAnswers();
        }

        // Notify the app that sync is complete
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_COMPLETE',
                syncType: data.syncType,
                success: true
            });
        });
    } catch (error) {
        console.error('Error handling sync request:', error);

        // Notify the app that sync failed
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_COMPLETE',
                syncType: data.syncType,
                success: false,
                error: error.message
            });
        });
    }
}

// IndexedDB helper functions for service worker
async function getPendingSyncItems(syncType = null) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readonly');
            const store = transaction.objectStore('syncQueue');

            let getRequest;
            if (syncType) {
                const index = store.index('syncType');
                getRequest = index.getAll(syncType);
            } else {
                getRequest = store.getAll();
            }

            getRequest.onsuccess = () => {
                // Filter out items that have exceeded max attempts
                const items = getRequest.result.filter(item => item.attempts < item.maxAttempts);
                resolve(items);
            };
            getRequest.onerror = () => reject(getRequest.error);
        };
    });
}

async function removeSyncItem(itemId) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readwrite');
            const store = transaction.objectStore('syncQueue');

            const deleteRequest = store.delete(itemId);
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
        };
    });
}

async function incrementSyncAttempts(itemId) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readwrite');
            const store = transaction.objectStore('syncQueue');

            const getRequest = store.get(itemId);
            getRequest.onsuccess = () => {
                const item = getRequest.result;
                if (item) {
                    item.attempts = (item.attempts || 0) + 1;
                    item.lastAttempt = Date.now();

                    const putRequest = store.put(item);
                    putRequest.onsuccess = () => resolve();
                    putRequest.onerror = () => reject(putRequest.error);
                } else {
                    resolve(); // Item not found, nothing to update
                }
            };
            getRequest.onerror = () => reject(getRequest.error);
        };
    });
}
