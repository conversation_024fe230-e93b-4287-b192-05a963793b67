using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Queries.GetTemplateQuestions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Queries;

public class GetTemplateQuestionsQueryHandlerTests : BaseDbTestClass
{
    private readonly GetTemplateQuestionsQueryHandler _handler;

    public GetTemplateQuestionsQueryHandlerTests()
    {
        _handler = new GetTemplateQuestionsQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidTemplateId_ShouldReturnQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        var query = new GetTemplateQuestionsQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be(question.Id);
        result[0].QuestionText.Should().Be(question.QuestionText);
        result[0].AuditTemplateId.Should().Be(template.Id);
    }

    [Fact]
    public async Task Handle_WithInvalidTemplateId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var query = new GetTemplateQuestionsQuery(999);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(query, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*AuditTemplate*999*");
    }

    [Fact]
    public async Task Handle_WithQuestionGroupFilter_ShouldReturnFilteredQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var questionGroup = await CreateTestQuestionGroupAsync(template.Id);
        
        var question1 = await CreateTestQuestionAsync(template.Id, questionGroup.Id);
        var question2 = await CreateTestQuestionAsync(template.Id); // No group
        
        var query = new GetTemplateQuestionsQuery(template.Id)
        {
            QuestionGroupId = questionGroup.Id
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be(question1.Id);
        result[0].QuestionGroupId.Should().Be(questionGroup.Id);
    }

    [Fact]
    public async Task Handle_WithIncludeInactive_ShouldReturnInactiveQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var activeQuestion = await CreateTestQuestionAsync(template.Id);
        var inactiveQuestion = await CreateTestQuestionAsync(template.Id);
        
        // Make one question inactive
        inactiveQuestion.IsActive = false;
        await Context.SaveChangesAsync();
        
        var query = new GetTemplateQuestionsQuery(template.Id)
        {
            IncludeInactive = true
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(q => q.Id == activeQuestion.Id && q.IsActive);
        result.Should().Contain(q => q.Id == inactiveQuestion.Id && !q.IsActive);
    }

    [Fact]
    public async Task Handle_WithoutIncludeInactive_ShouldReturnOnlyActiveQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var activeQuestion = await CreateTestQuestionAsync(template.Id);
        var inactiveQuestion = await CreateTestQuestionAsync(template.Id);
        
        // Make one question inactive
        inactiveQuestion.IsActive = false;
        await Context.SaveChangesAsync();
        
        var query = new GetTemplateQuestionsQuery(template.Id)
        {
            IncludeInactive = false
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be(activeQuestion.Id);
        result[0].IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldReturnQuestionsOrderedByDisplayOrder()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var question1 = await CreateTestQuestionAsync(template.Id, displayOrder: 3);
        var question2 = await CreateTestQuestionAsync(template.Id, displayOrder: 1);
        var question3 = await CreateTestQuestionAsync(template.Id, displayOrder: 2);
        
        var query = new GetTemplateQuestionsQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        result[0].Id.Should().Be(question2.Id); // DisplayOrder 1
        result[1].Id.Should().Be(question3.Id); // DisplayOrder 2
        result[2].Id.Should().Be(question1.Id); // DisplayOrder 3
    }

    [Fact]
    public async Task Handle_ShouldIncludeQuestionOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, questionType: QuestionType.SingleSelect);
        
        var option1 = new QuestionOption
        {
            QuestionId = question.Id,
            OptionText = "Option 1",
            OptionValue = "opt1",
            DisplayOrder = 1,
            IsActive = true
        };
        var option2 = new QuestionOption
        {
            QuestionId = question.Id,
            OptionText = "Option 2",
            OptionValue = "opt2",
            DisplayOrder = 2,
            IsActive = true
        };
        
        Context.QuestionOptions.AddRange(option1, option2);
        await Context.SaveChangesAsync();
        
        var query = new GetTemplateQuestionsQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Options.Should().HaveCount(2);
        result[0].Options.Should().Contain(o => o.OptionText == "Option 1" && o.OptionValue == "opt1");
        result[0].Options.Should().Contain(o => o.OptionText == "Option 2" && o.OptionValue == "opt2");
    }

    private async Task<Question> CreateTestQuestionAsync(int templateId, int? questionGroupId = null, int displayOrder = 1, QuestionType questionType = QuestionType.YesNo)
    {
        var question = new Question
        {
            AuditTemplateId = templateId,
            QuestionGroupId = questionGroupId,
            QuestionText = $"Test Question {Guid.NewGuid()}",
            QuestionType = questionType,
            DisplayOrder = displayOrder,
            IsRequired = true,
            IsActive = true
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return question;
    }

    private async Task<QuestionGroup> CreateTestQuestionGroupAsync(int templateId)
    {
        var questionGroup = new QuestionGroup
        {
            AuditTemplateId = templateId,
            GroupName = $"Test Group {Guid.NewGuid()}",
            DisplayOrder = 1,
            IsActive = true
        };

        Context.QuestionGroups.Add(questionGroup);
        await Context.SaveChangesAsync();
        return questionGroup;
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isPublished = false)
    {
        var template = new AuditTemplate
        {
            TemplateName = $"Test Template {Guid.NewGuid()}",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
