using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using Microsoft.VisualStudio.TestPlatform.TestHost;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Integration.Controllers;

public class TemplatesControllerTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ApplicationDbContext _context;

    public TemplatesControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the real database context
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb_Templates_" + Guid.NewGuid());
                });
            });
        });

        _client = _factory.CreateClient();
        
        // Get the test database context
        var scope = _factory.Services.CreateScope();
        _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Seed test data
        SeedTestData().Wait();
    }

    private async Task SeedTestData()
    {
        await TestDbContextFactory.SeedTestDataAsync(_context);
        
        // Add some test templates
        var template1 = new AuditTemplate
        {
            TemplateName = "Safety Audit Template",
            Description = "Template for safety audits",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedByUserId = "admin",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var template2 = new AuditTemplate
        {
            TemplateName = "Quality Audit Template",
            Description = "Template for quality audits",
            Version = 1,
            IsPublished = false,
            IsActive = true,
            CreatedByUserId = "admin",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.AuditTemplates.AddRangeAsync(template1, template2);
        await _context.SaveChangesAsync();
    }

    [Fact]
    public async Task GetTemplates_ShouldReturnOkWithTemplateList()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/templates");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<AuditTemplateSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(2);
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task GetTemplates_WithSearchTerm_ShouldFilterResults()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/templates?searchTerm=safety");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<AuditTemplateSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(1);
        result.Items.First().TemplateName.Should().Contain("Safety");
    }

    [Fact]
    public async Task GetTemplates_WithPublishedFilter_ShouldFilterByPublishedStatus()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/templates?isPublished=true");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<AuditTemplateSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(1);
        result.Items.First().IsPublished.Should().BeTrue();
    }

    [Fact]
    public async Task GetTemplate_WithValidId_ShouldReturnTemplate()
    {
        // Arrange
        var existingTemplate = await _context.AuditTemplates.FirstAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/templates/{existingTemplate.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<AuditTemplateDto>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Id.Should().Be(existingTemplate.Id);
        result.TemplateName.Should().Be(existingTemplate.TemplateName);
    }

    [Fact]
    public async Task GetTemplate_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/templates/999");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateTemplate_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "New Test Template",
            Description = "A new test template",
            Version = 1
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/templates", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var templateIdContent = await response.Content.ReadAsStringAsync();
        var templateId = int.Parse(templateIdContent.Trim('"'));
        templateId.Should().BeGreaterThan(0);
        
        // Verify template was created
        var createdTemplate = await _context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == templateId);
        createdTemplate.Should().NotBeNull();
        createdTemplate!.TemplateName.Should().Be(command.TemplateName);
    }

    [Fact]
    public async Task CreateTemplate_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "", // Invalid - empty name
            Description = "A test template",
            Version = 0 // Invalid - version must be > 0
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/templates", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task UpdateTemplate_WithValidData_ShouldReturnNoContent()
    {
        // Arrange
        var existingTemplate = await _context.AuditTemplates.FirstAsync(t => !t.IsPublished);
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = existingTemplate.Id,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = existingTemplate.RecordVersion
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/v1/templates/{existingTemplate.Id}", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify template was updated
        var updatedTemplate = await _context.AuditTemplates.FirstAsync(t => t.Id == existingTemplate.Id);
        updatedTemplate.TemplateName.Should().Be(command.TemplateName);
        updatedTemplate.Description.Should().Be(command.Description);
        updatedTemplate.Version.Should().Be(command.Version);
    }

    [Fact]
    public async Task UpdateTemplate_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var command = new UpdateAuditTemplateCommand
        {
            Id = 999,
            TemplateName = "Updated Template",
            Description = "Updated description",
            Version = 2,
            RecordVersion = 1
        };

        // Act
        var response = await _client.PutAsJsonAsync("/api/v1/templates/999", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task DeleteTemplate_WithValidId_ShouldReturnNoContent()
    {
        // Arrange
        var existingTemplate = await _context.AuditTemplates.FirstAsync(t => !t.IsPublished);

        // Act
        var response = await _client.DeleteAsync($"/api/v1/templates/{existingTemplate.Id}?recordVersion={existingTemplate.RecordVersion}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify template was soft deleted
        var deletedTemplate = await _context.AuditTemplates.FirstAsync(t => t.Id == existingTemplate.Id);
        deletedTemplate.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task DeleteTemplate_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.DeleteAsync("/api/v1/templates/999?recordVersion=1");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task PublishTemplate_WithValidId_ShouldReturnNoContent()
    {
        // Arrange
        var existingTemplate = await _context.AuditTemplates.FirstAsync(t => !t.IsPublished);

        // Act
        var response = await _client.PostAsync($"/api/v1/templates/{existingTemplate.Id}/publish", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify template was published
        var publishedTemplate = await _context.AuditTemplates.FirstAsync(t => t.Id == existingTemplate.Id);
        publishedTemplate.IsPublished.Should().BeTrue();
    }

    [Fact]
    public async Task PublishTemplate_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.PostAsync("/api/v1/templates/999/publish", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _client?.Dispose();
    }
}
