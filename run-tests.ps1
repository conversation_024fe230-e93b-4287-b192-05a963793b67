# HWS Audit Platform Test Runner
# This script runs all tests with coverage reporting

param(
    [string]$Configuration = "Debug",
    [string]$Filter = "",
    [switch]$Coverage = $false,
    [switch]$Watch = $false,
    [switch]$Verbose = $false
)

Write-Host "🧪 HWS Audit Platform Test Runner" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Set test project path
$TestProject = "src/HWSAuditPlatform.Tests/HWSAuditPlatform.Tests.csproj"

# Check if test project exists
if (-not (Test-Path $TestProject)) {
    Write-Host "❌ Test project not found at: $TestProject" -ForegroundColor Red
    exit 1
}

# Build the solution first
Write-Host "🔨 Building solution..." -ForegroundColor Yellow
dotnet build --configuration $Configuration --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Prepare test command
$TestCommand = @("test", $TestProject, "--configuration", $Configuration, "--no-build")

# Add filter if specified
if ($Filter) {
    $TestCommand += "--filter"
    $TestCommand += $Filter
    Write-Host "🔍 Running tests with filter: $Filter" -ForegroundColor Yellow
}

# Add coverage collection if requested
if ($Coverage) {
    Write-Host "📊 Collecting code coverage..." -ForegroundColor Yellow
    $TestCommand += "--collect:XPlat Code Coverage"
    $TestCommand += "--results-directory:TestResults"
}

# Add verbose logging if requested
if ($Verbose) {
    $TestCommand += "--verbosity"
    $TestCommand += "detailed"
}

# Add watch mode if requested
if ($Watch) {
    Write-Host "👀 Running in watch mode..." -ForegroundColor Yellow
    $TestCommand = @("watch") + $TestCommand
}

# Run tests
Write-Host "🚀 Running tests..." -ForegroundColor Green
Write-Host "Command: dotnet $($TestCommand -join ' ')" -ForegroundColor Gray

& dotnet @TestCommand

$TestExitCode = $LASTEXITCODE

# Generate coverage report if coverage was collected
if ($Coverage -and $TestExitCode -eq 0) {
    Write-Host "📈 Generating coverage report..." -ForegroundColor Yellow
    
    # Check if reportgenerator is installed
    $ReportGenerator = Get-Command "reportgenerator" -ErrorAction SilentlyContinue
    if (-not $ReportGenerator) {
        Write-Host "Installing ReportGenerator tool..." -ForegroundColor Yellow
        dotnet tool install -g dotnet-reportgenerator-globaltool
    }
    
    # Find the latest coverage file
    $CoverageFiles = Get-ChildItem -Path "TestResults" -Filter "coverage.cobertura.xml" -Recurse | Sort-Object LastWriteTime -Descending
    
    if ($CoverageFiles.Count -gt 0) {
        $LatestCoverage = $CoverageFiles[0].FullName
        Write-Host "Using coverage file: $LatestCoverage" -ForegroundColor Gray
        
        # Generate HTML report
        reportgenerator -reports:$LatestCoverage -targetdir:TestResults/CoverageReport -reporttypes:Html
        
        Write-Host "✅ Coverage report generated at: TestResults/CoverageReport/index.html" -ForegroundColor Green
        
        # Open coverage report if on Windows
        if ($IsWindows -or $env:OS -eq "Windows_NT") {
            $CoverageReportPath = Join-Path (Get-Location) "TestResults/CoverageReport/index.html"
            if (Test-Path $CoverageReportPath) {
                Write-Host "🌐 Opening coverage report in browser..." -ForegroundColor Yellow
                Start-Process $CoverageReportPath
            }
        }
    } else {
        Write-Host "⚠️ No coverage files found" -ForegroundColor Yellow
    }
}

# Display results
Write-Host ""
if ($TestExitCode -eq 0) {
    Write-Host "✅ All tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Some tests failed!" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 Test Categories:" -ForegroundColor Cyan
Write-Host "  • Domain Tests: Domain entity and value object tests" -ForegroundColor Gray
Write-Host "  • Application Tests: Command/query handler tests" -ForegroundColor Gray
Write-Host "  • Infrastructure Tests: Repository and service tests" -ForegroundColor Gray
Write-Host "  • Integration Tests: End-to-end API tests" -ForegroundColor Gray

Write-Host ""
Write-Host "🔧 Usage Examples:" -ForegroundColor Cyan
Write-Host "  .\run-tests.ps1                           # Run all tests" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Coverage                 # Run with coverage" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Filter 'Domain'          # Run only domain tests" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Filter 'Users'           # Run only user-related tests" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Watch                    # Run in watch mode" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Verbose                  # Run with detailed output" -ForegroundColor Gray

exit $TestExitCode
