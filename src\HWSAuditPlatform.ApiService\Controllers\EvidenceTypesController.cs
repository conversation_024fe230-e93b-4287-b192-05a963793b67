using Asp.Versioning;
using HWSAuditPlatform.ApiService.Services;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for evidence type operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class EvidenceTypesController : ControllerBase
{
    private readonly IEvidenceTypeService _evidenceTypeService;

    public EvidenceTypesController(IEvidenceTypeService evidenceTypeService)
    {
        _evidenceTypeService = evidenceTypeService;
    }

    /// <summary>
    /// Get all available evidence types
    /// </summary>
    /// <returns>List of available evidence types</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<EvidenceTypeInfo>), 200)]
    public ActionResult<IEnumerable<EvidenceTypeInfo>> GetEvidenceTypes()
    {
        var evidenceTypes = Enum.GetValues<EvidenceType>()
            .Select(et => new EvidenceTypeInfo
            {
                EvidenceType = et,
                Name = et.ToString(),
                Description = GetEvidenceTypeDescription(et),
                AllowedMimeTypes = _evidenceTypeService.GetMimeTypesForEvidenceType(et).ToList()
            });

        return Ok(evidenceTypes);
    }

    /// <summary>
    /// Get MIME types for a specific evidence type
    /// </summary>
    /// <param name="evidenceType">The evidence type</param>
    /// <returns>List of allowed MIME types</returns>
    [HttpGet("{evidenceType}/mime-types")]
    [ProducesResponseType(typeof(IEnumerable<string>), 200)]
    public ActionResult<IEnumerable<string>> GetMimeTypesForEvidenceType(EvidenceType evidenceType)
    {
        var mimeTypes = _evidenceTypeService.GetMimeTypesForEvidenceType(evidenceType);
        return Ok(mimeTypes);
    }

    /// <summary>
    /// Validate if a MIME type is allowed for an evidence type
    /// </summary>
    /// <param name="evidenceType">The evidence type</param>
    /// <param name="mimeType">The MIME type to validate</param>
    /// <returns>Validation result</returns>
    [HttpGet("{evidenceType}/validate-mime-type")]
    [ProducesResponseType(typeof(MimeTypeValidationResult), 200)]
    public ActionResult<MimeTypeValidationResult> ValidateMimeType(EvidenceType evidenceType, [FromQuery] string mimeType)
    {
        var isAllowed = _evidenceTypeService.IsMimeTypeAllowedForEvidenceType(mimeType, evidenceType);
        
        return Ok(new MimeTypeValidationResult
        {
            MimeType = mimeType,
            EvidenceType = evidenceType,
            IsAllowed = isAllowed
        });
    }

    private static string GetEvidenceTypeDescription(EvidenceType evidenceType)
    {
        return evidenceType switch
        {
            EvidenceType.Image => "Image files (JPEG, PNG, GIF, BMP, WebP)",
            EvidenceType.Document => "Document files (PDF, Word, Text, RTF)",
            EvidenceType.Video => "Video files (MP4, AVI, MOV, WMV, WebM)",
            EvidenceType.Audio => "Audio files (MP3, WAV, AAC, OGG, M4A)",
            EvidenceType.Spreadsheet => "Spreadsheet files (Excel, CSV)",
            EvidenceType.Any => "Any file type (no restrictions)",
            _ => evidenceType.ToString()
        };
    }
}

/// <summary>
/// Information about an evidence type
/// </summary>
public class EvidenceTypeInfo
{
    /// <summary>
    /// The evidence type enum value
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Display name of the evidence type
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what files are allowed for this evidence type
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// List of allowed MIME types for this evidence type
    /// </summary>
    public List<string> AllowedMimeTypes { get; set; } = new();
}

/// <summary>
/// Result of MIME type validation
/// </summary>
public class MimeTypeValidationResult
{
    /// <summary>
    /// The MIME type that was validated
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// The evidence type it was validated against
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Whether the MIME type is allowed for the evidence type
    /// </summary>
    public bool IsAllowed { get; set; }
}
