﻿// <auto-generated />
using System;
using HWSAuditPlatform.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.Audit", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int>("AreaId")
                        .HasColumnType("int");

                    b.Property<string>("AssignedToUserGroupId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AssignedToUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("AssignmentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FactoryId")
                        .HasColumnType("int");

                    b.Property<string>("ManagerComments")
                        .HasColumnType("text");

                    b.Property<decimal?>("OverallScore")
                        .HasColumnType("decimal(7,2)");

                    b.Property<string>("OverallStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("RecurringAuditSettingId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime?>("ReviewedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReviewedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SubAreaId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId")
                        .HasDatabaseName("IX_Audits_AreaId");

                    b.HasIndex("AssignedToUserGroupId")
                        .HasDatabaseName("IX_Audits_AssignedToUserGroupId");

                    b.HasIndex("AssignedToUserId")
                        .HasDatabaseName("IX_Audits_AssignedToUserId");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_Audits_AuditTemplateId");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("IX_Audits_DueDate");

                    b.HasIndex("FactoryId")
                        .HasDatabaseName("IX_Audits_FactoryId");

                    b.HasIndex("OverallStatus")
                        .HasDatabaseName("IX_Audits_OverallStatus");

                    b.HasIndex("RecurringAuditSettingId");

                    b.HasIndex("ReviewedByUserId");

                    b.HasIndex("ScheduledDate")
                        .HasDatabaseName("IX_Audits_ScheduledDate");

                    b.HasIndex("SubAreaId");

                    b.ToTable("hwsap_aud_audits", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<bool?>("AnswerBoolean")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("AnswerDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("AnswerNumeric")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("AnswerText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("AnsweredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("AuditId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<bool>("IsNotApplicable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("OriginalAnswerValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("SelectedOptionId")
                        .HasColumnType("int");

                    b.Property<string>("SeverityLevel")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditId")
                        .HasDatabaseName("IX_AuditAnswers_AuditId");

                    b.HasIndex("QuestionId")
                        .HasDatabaseName("IX_AuditAnswers_QuestionId");

                    b.HasIndex("SelectedOptionId");

                    b.HasIndex("AuditId", "QuestionId")
                        .IsUnique()
                        .HasDatabaseName("IX_AuditAnswers_AuditId_QuestionId");

                    b.ToTable("hwsap_aud_answers", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswerFailureReason", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AuditAnswerId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("ReasonText")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RecordVersion")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditAnswerId")
                        .HasDatabaseName("IX_AuditAnswerFailureReasons_AuditAnswerId");

                    b.ToTable("hwsap_aud_answer_failure_reasons", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswerSelectedOption", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AuditAnswerId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("QuestionOptionId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AuditAnswerId")
                        .HasDatabaseName("IX_AuditAnswerSelectedOptions_AuditAnswerId");

                    b.HasIndex("QuestionOptionId")
                        .HasDatabaseName("IX_AuditAnswerSelectedOptions_QuestionOptionId");

                    b.ToTable("hwsap_aud_answer_selected_options", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAttachment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AuditAnswerId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("OriginalFileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UploadedByUserId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.HasKey("Id");

                    b.HasIndex("AuditAnswerId")
                        .HasDatabaseName("IX_AuditAttachments_AuditAnswerId");

                    b.HasIndex("FileName")
                        .HasDatabaseName("IX_AuditAttachments_FileName");

                    b.HasIndex("UploadedByUserId");

                    b.ToTable("hwsap_aud_attachments", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.CorrectiveAction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("ActionDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssignedToUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateOnly?>("CompletionDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateOnly>("DueDate")
                        .HasColumnType("date");

                    b.Property<string>("EvidenceNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FindingId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToUserId")
                        .HasDatabaseName("IX_CorrectiveActions_AssignedToUserId");

                    b.HasIndex("CompletionDate")
                        .HasDatabaseName("IX_CorrectiveActions_CompletionDate");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("IX_CorrectiveActions_DueDate");

                    b.HasIndex("FindingId")
                        .HasDatabaseName("IX_CorrectiveActions_FindingId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CorrectiveActions_Status");

                    b.ToTable("hwsap_fnd_corrective_actions", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.Finding", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AuditAnswerId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateOnly?>("DueDate")
                        .HasColumnType("date");

                    b.Property<int?>("FindingCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("FindingCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FindingDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FindingSeverityLevel")
                        .HasColumnType("int");

                    b.Property<string>("ImmediateActionTaken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("ReportedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ResponsibleUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("RetrospectiveAnalystUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("RootCauseAnalysis")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditAnswerId")
                        .HasDatabaseName("IX_Findings_AuditAnswerId");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("IX_Findings_DueDate");

                    b.HasIndex("FindingCategoryId")
                        .HasDatabaseName("IX_Findings_FindingCategoryId");

                    b.HasIndex("FindingCode")
                        .HasDatabaseName("IX_Findings_FindingCode");

                    b.HasIndex("FindingSeverityLevel")
                        .HasDatabaseName("IX_Findings_FindingSeverityLevel");

                    b.HasIndex("ReportedByUserId")
                        .HasDatabaseName("IX_Findings_ReportedByUserId");

                    b.HasIndex("ResponsibleUserId")
                        .HasDatabaseName("IX_Findings_ResponsibleUserId");

                    b.HasIndex("RetrospectiveAnalystUserId")
                        .HasDatabaseName("IX_Findings_RetrospectiveAnalystUserId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Findings_Status");

                    b.ToTable("hwsap_fnd_findings", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.FindingCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ColorCode")
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("IconName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<bool>("RequiresDocumentation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_FindingCategories_AuditTemplateId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_FindingCategories_IsActive");

                    b.HasIndex("UpdatedByUserId");

                    b.HasIndex("AuditTemplateId", "CategoryName")
                        .IsUnique()
                        .HasDatabaseName("IX_FindingCategories_Template_Name");

                    b.HasIndex("AuditTemplateId", "DisplayOrder")
                        .HasDatabaseName("IX_FindingCategories_Template_DisplayOrder");

                    b.ToTable("hwsap_fnd_finding_categories", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Area", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AreaName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RecordVersion")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FactoryId")
                        .HasDatabaseName("IX_Areas_FactoryId");

                    b.HasIndex("UpdatedByUserId");

                    b.HasIndex("FactoryId", "AreaName")
                        .IsUnique()
                        .HasDatabaseName("IX_Areas_Factory_AreaName");

                    b.ToTable("hwsap_org_areas", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.AreaResponsibility", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int>("AreaId")
                        .HasColumnType("int");

                    b.Property<int?>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("ResponsibilityType")
                        .HasColumnType("int");

                    b.Property<string>("ResponsibleUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId")
                        .HasDatabaseName("IX_AreaResponsibilities_AreaId");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_AreaResponsibilities_AuditTemplateId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_AreaResponsibilities_IsActive");

                    b.HasIndex("ResponsibleUserId")
                        .HasDatabaseName("IX_AreaResponsibilities_ResponsibleUserId");

                    b.HasIndex("AreaId", "ResponsibilityType", "AuditTemplateId")
                        .HasDatabaseName("IX_AreaResponsibilities_Area_Type_Template");

                    b.ToTable("hwsap_org_area_responsibilities", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Factory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("FactoryName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("FactoryProcess")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("LocationId")
                        .HasColumnType("int");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("RecordVersion")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LocationId")
                        .HasDatabaseName("IX_Factories_LocationId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("hwsap_org_factories", null, t =>
                        {
                            t.Property("AddressLine1")
                                .HasColumnName("Factory_AddressLine1");

                            t.Property("City")
                                .HasColumnName("Factory_City");

                            t.Property("PostalCode")
                                .HasColumnName("Factory_PostalCode");
                        });
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Location", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("LocationCountry")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LocationCountryCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("LocationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OwnerGroupId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("LocationCountryCode")
                        .IsUnique()
                        .HasDatabaseName("IX_Location_LocationCountryCode")
                        .HasFilter("[LocationCountryCode] IS NOT NULL");

                    b.HasIndex("LocationName")
                        .IsUnique()
                        .HasDatabaseName("IX_Location_LocationName");

                    b.ToTable("hwsap_org_locations", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.SubArea", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AreaId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RecordVersion")
                        .HasColumnType("int");

                    b.Property<string>("SubAreaName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId")
                        .HasDatabaseName("IX_SubAreas_AreaId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("UpdatedByUserId");

                    b.HasIndex("AreaId", "SubAreaName")
                        .IsUnique()
                        .HasDatabaseName("IX_SubAreas_Area_SubAreaName");

                    b.ToTable("hwsap_org_sub_areas", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Scheduling.RecurrenceRule", b =>
                {
                    b.Property<string>("RecurringAuditSettingId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateOnly?>("EndDate")
                        .HasColumnType("date");

                    b.Property<int>("FrequencyType")
                        .HasColumnType("int");

                    b.Property<int>("Interval")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("MonthlyDayOfMonth")
                        .HasColumnType("int");

                    b.Property<int?>("MonthlyDayOfWeek")
                        .HasColumnType("int");

                    b.Property<int?>("MonthlyNthWeek")
                        .HasColumnType("int");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("WeeklyDaysOfWeekMask")
                        .HasColumnType("int");

                    b.Property<int?>("YearlyDayOfMonth")
                        .HasColumnType("int");

                    b.Property<int?>("YearlyDayOfWeek")
                        .HasColumnType("int");

                    b.Property<int?>("YearlyMonth")
                        .HasColumnType("int");

                    b.Property<int?>("YearlyNthWeek")
                        .HasColumnType("int");

                    b.HasKey("RecurringAuditSettingId");

                    b.HasIndex("EndDate")
                        .HasDatabaseName("IX_RecurrenceRules_EndDate");

                    b.HasIndex("FrequencyType")
                        .HasDatabaseName("IX_RecurrenceRules_FrequencyType");

                    b.HasIndex("StartDate")
                        .HasDatabaseName("IX_RecurrenceRules_StartDate");

                    b.ToTable("hwsap_sch_recurrence_rules", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Scheduling.RecurringAuditSetting", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int?>("AreaId")
                        .HasColumnType("int");

                    b.Property<string>("AssignToUserGroupId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AssignToUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("AssignmentType")
                        .HasColumnType("int");

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DeadlineDays")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(7);

                    b.Property<int>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastGeneratedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateOnly?>("NextGenerationDate")
                        .HasColumnType("date");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("SettingName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("SubAreaId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId")
                        .HasDatabaseName("IX_RecurringAuditSettings_AreaId");

                    b.HasIndex("AssignToUserGroupId")
                        .HasDatabaseName("IX_RecurringAuditSettings_AssignToUserGroupId");

                    b.HasIndex("AssignToUserId")
                        .HasDatabaseName("IX_RecurringAuditSettings_AssignToUserId");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_RecurringAuditSettings_AuditTemplateId");

                    b.HasIndex("FactoryId")
                        .HasDatabaseName("IX_RecurringAuditSettings_FactoryId");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("IX_RecurringAuditSettings_IsEnabled");

                    b.HasIndex("NextGenerationDate")
                        .HasDatabaseName("IX_RecurringAuditSettings_NextGenerationDate");

                    b.HasIndex("SubAreaId")
                        .HasDatabaseName("IX_RecurringAuditSettings_SubAreaId");

                    b.ToTable("hwsap_sch_recurring_audit_settings", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("EnableAreaBasedResponsibility")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EnableFindingCategorization")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsPublished")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_AuditTemplates_IsActive");

                    b.HasIndex("IsPublished")
                        .HasDatabaseName("IX_AuditTemplates_IsPublished");

                    b.HasIndex("TemplateName")
                        .HasDatabaseName("IX_AuditTemplates_TemplateName");

                    b.HasIndex("TemplateName", "Version")
                        .IsUnique()
                        .HasDatabaseName("IX_AuditTemplates_TemplateName_Version");

                    b.ToTable("hwsap_tpl_audit_templates", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.Question", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("EvidenceInstructions")
                        .HasColumnType("text");

                    b.Property<bool>("EvidenceRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EvidenceTimingHint")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HelpText")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int?>("ParentQuestionId")
                        .HasColumnType("int");

                    b.Property<int?>("QuestionGroupId")
                        .HasColumnType("int");

                    b.Property<string>("QuestionText")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("QuestionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("SeverityLevel")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TriggerAnswerValue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(7,2)");

                    b.HasKey("Id");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_Questions_AuditTemplateId");

                    b.HasIndex("ParentQuestionId")
                        .HasDatabaseName("IX_Questions_ParentQuestionId");

                    b.HasIndex("QuestionGroupId")
                        .HasDatabaseName("IX_Questions_QuestionGroupId");

                    b.HasIndex("QuestionType")
                        .HasDatabaseName("IX_Questions_QuestionType");

                    b.HasIndex("AuditTemplateId", "DisplayOrder")
                        .HasDatabaseName("IX_Questions_AuditTemplateId_DisplayOrder");

                    b.ToTable("hwsap_tpl_questions", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionAllowedEvidenceType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("EvidenceType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("QuestionId")
                        .HasDatabaseName("IX_QuestionAllowedEvidenceTypes_QuestionId");

                    b.HasIndex("QuestionId", "DisplayOrder")
                        .HasDatabaseName("IX_QuestionAllowedEvidenceTypes_QuestionId_DisplayOrder");

                    b.HasIndex("QuestionId", "EvidenceType")
                        .IsUnique()
                        .HasDatabaseName("UQ_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType");

                    b.ToTable("hwsap_tpl_question_allowed_evidence_types", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_QuestionGroups_AuditTemplateId");

                    b.HasIndex("GroupName")
                        .HasDatabaseName("IX_QuestionGroups_GroupName");

                    b.HasIndex("AuditTemplateId", "DisplayOrder")
                        .HasDatabaseName("IX_QuestionGroups_AuditTemplateId_DisplayOrder");

                    b.ToTable("hwsap_tpl_question_groups", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionOption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool?>("IsCorrectOption")
                        .HasColumnType("bit");

                    b.Property<string>("OptionText")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("OptionValue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("OptionText")
                        .HasDatabaseName("IX_QuestionOptions_OptionText");

                    b.HasIndex("QuestionId")
                        .HasDatabaseName("IX_QuestionOptions_QuestionId");

                    b.HasIndex("QuestionId", "DisplayOrder")
                        .HasDatabaseName("IX_QuestionOptions_QuestionId_DisplayOrder");

                    b.ToTable("hwsap_tpl_question_options", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.AdGroupRoleMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdGroupName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("AdGroupName")
                        .IsUnique()
                        .HasDatabaseName("IX_AdGroupRoleMappings_AdGroupName");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_AdGroupRoleMappings_RoleId");

                    b.ToTable("hwsap_usr_ad_group_role_mappings", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AdGroupName = "HWSAudit-DevAdministrators",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Maps AD DevAdministrators group to DevAdmin role",
                            IsActive = true,
                            RoleId = 1,
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 2,
                            AdGroupName = "HWSAudit-SystemManagers",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Maps AD SystemManagers group to SystemManager role",
                            IsActive = true,
                            RoleId = 2,
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 3,
                            AdGroupName = "HWSAudit-ProcessOwners",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Maps AD ProcessOwners group to ProcessOwner role",
                            IsActive = true,
                            RoleId = 3,
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 4,
                            AdGroupName = "HWSAudit-Auditors",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Maps AD Auditors group to Auditor role",
                            IsActive = true,
                            RoleId = 4,
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 5,
                            AdGroupName = "HWSAudit-Viewers",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Maps AD Viewers group to Viewer role",
                            IsActive = true,
                            RoleId = 5,
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.ProcessOwnerAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AreaId")
                        .HasColumnType("int");

                    b.Property<string>("AssignedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("ProcessOwnerUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("SubAreaId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId");

                    b.HasIndex("AssignedByUserId");

                    b.HasIndex("FactoryId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ProcessOwnerAssignments_IsActive");

                    b.HasIndex("ProcessOwnerUserId")
                        .HasDatabaseName("IX_ProcessOwnerAssignments_ProcessOwnerUserId");

                    b.HasIndex("SubAreaId");

                    b.HasIndex("ProcessOwnerUserId", "FactoryId", "AreaId", "SubAreaId")
                        .IsUnique()
                        .HasDatabaseName("IX_ProcessOwnerAssignments_Scope")
                        .HasFilter("[FactoryId] IS NOT NULL AND [AreaId] IS NOT NULL AND [SubAreaId] IS NOT NULL");

                    b.ToTable("hwsap_usr_process_owner_assignments", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("RoleName")
                        .IsUnique()
                        .HasDatabaseName("IX_Roles_RoleName");

                    b.ToTable("hwsap_usr_roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Developer Administrator with full system access",
                            RoleName = "DevAdmin",
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "System Manager with template creation and user management capabilities",
                            RoleName = "SystemManager",
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Process Owner with audit assignment and results management capabilities",
                            RoleName = "ProcessOwner",
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Auditor who performs audit activities",
                            RoleName = "Auditor",
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Viewer with read-only access",
                            RoleName = "Viewer",
                            UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.TemplateAccessAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("AuditTemplateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedByUserId");

                    b.HasIndex("AuditTemplateId")
                        .HasDatabaseName("IX_TemplateAccessAssignments_AuditTemplateId");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_TemplateAccessAssignments_ExpiresAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_TemplateAccessAssignments_IsActive");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_TemplateAccessAssignments_UserId");

                    b.HasIndex("UserId", "AuditTemplateId")
                        .IsUnique()
                        .HasDatabaseName("IX_TemplateAccessAssignments_UserTemplate");

                    b.ToTable("hwsap_usr_template_access_assignments", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.User", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AdDistinguishedName")
                        .HasColumnType("text");

                    b.Property<string>("AdObjectGuid")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("AdSyncLastDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("FactoryId")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("AdObjectGuid")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_AdObjectGuid")
                        .HasFilter("[AdObjectGuid] IS NOT NULL");

                    b.HasIndex("FactoryId")
                        .HasDatabaseName("IX_Users_FactoryId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_Users_RoleId");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Username");

                    b.ToTable("hwsap_usr_users", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.UserGroup", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AdObjectGuid")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsAdSynced")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AdObjectGuid")
                        .IsUnique()
                        .HasDatabaseName("IX_UserGroups_AdObjectGuid")
                        .HasFilter("[AdObjectGuid] IS NOT NULL");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("GroupName")
                        .IsUnique()
                        .HasDatabaseName("IX_UserGroups_GroupName");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("hwsap_usr_groups", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.UserGroupMember", b =>
                {
                    b.Property<string>("UserGroupId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("UserId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.HasKey("UserGroupId", "UserId");

                    b.HasIndex("UserGroupId")
                        .HasDatabaseName("IX_UserGroupMembers_UserGroupId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserGroupMembers_UserId");

                    b.ToTable("hwsap_usr_group_members", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Workflow.AuditCorrectionRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("AuditId")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ManagerComments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RecordVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("RequestReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("RequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("RequestedByUserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime?>("ReviewedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReviewedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("AuditId")
                        .IsUnique()
                        .HasDatabaseName("IX_AuditCorrectionRequests_AuditId");

                    b.HasIndex("RequestedAt")
                        .HasDatabaseName("IX_AuditCorrectionRequests_RequestedAt");

                    b.HasIndex("RequestedByUserId")
                        .HasDatabaseName("IX_AuditCorrectionRequests_RequestedByUserId");

                    b.HasIndex("ReviewedByUserId")
                        .HasDatabaseName("IX_AuditCorrectionRequests_ReviewedByUserId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_AuditCorrectionRequests_Status");

                    b.ToTable("hwsap_wfl_audit_correction_requests", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Workflow.AuditLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AppVersion")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("EventTimestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ServerReceivedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.HasKey("Id");

                    b.HasIndex("EntityId")
                        .HasDatabaseName("IX_AuditLogs_EntityId");

                    b.HasIndex("EntityType")
                        .HasDatabaseName("IX_AuditLogs_EntityType");

                    b.HasIndex("EventTimestamp")
                        .HasDatabaseName("IX_AuditLogs_EventTimestamp");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_AuditLogs_UserId");

                    b.HasIndex("EntityType", "EntityId")
                        .HasDatabaseName("IX_AuditLogs_EntityType_EntityId");

                    b.ToTable("hwsap_wfl_audit_logs", (string)null);
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.Audit", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.UserGroup", "AssignedToUserGroup")
                        .WithMany()
                        .HasForeignKey("AssignedToUserGroupId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "AssignedToUser")
                        .WithMany()
                        .HasForeignKey("AssignedToUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany()
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Scheduling.RecurringAuditSetting", "RecurringAuditSetting")
                        .WithMany()
                        .HasForeignKey("RecurringAuditSettingId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ReviewedByUser")
                        .WithMany()
                        .HasForeignKey("ReviewedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.SubArea", "SubArea")
                        .WithMany()
                        .HasForeignKey("SubAreaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Area");

                    b.Navigation("AssignedToUser");

                    b.Navigation("AssignedToUserGroup");

                    b.Navigation("AuditTemplate");

                    b.Navigation("Factory");

                    b.Navigation("RecurringAuditSetting");

                    b.Navigation("ReviewedByUser");

                    b.Navigation("SubArea");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("Answers")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.Question", "Question")
                        .WithMany()
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.QuestionOption", "SelectedOption")
                        .WithMany()
                        .HasForeignKey("SelectedOptionId");

                    b.Navigation("Audit");

                    b.Navigation("Question");

                    b.Navigation("SelectedOption");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswerFailureReason", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", "AuditAnswer")
                        .WithMany("FailureReasons")
                        .HasForeignKey("AuditAnswerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditAnswer");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswerSelectedOption", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", "AuditAnswer")
                        .WithMany("SelectedOptions")
                        .HasForeignKey("AuditAnswerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.QuestionOption", "QuestionOption")
                        .WithMany()
                        .HasForeignKey("QuestionOptionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AuditAnswer");

                    b.Navigation("QuestionOption");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAttachment", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", "AuditAnswer")
                        .WithMany("Attachments")
                        .HasForeignKey("AuditAnswerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UploadedByUser")
                        .WithMany()
                        .HasForeignKey("UploadedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditAnswer");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.CorrectiveAction", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "AssignedToUser")
                        .WithMany()
                        .HasForeignKey("AssignedToUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Findings.Finding", "Finding")
                        .WithMany("CorrectiveActions")
                        .HasForeignKey("FindingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedToUser");

                    b.Navigation("Finding");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.Finding", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", "AuditAnswer")
                        .WithMany()
                        .HasForeignKey("AuditAnswerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Findings.FindingCategory", "FindingCategory")
                        .WithMany("Findings")
                        .HasForeignKey("FindingCategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ReportedByUser")
                        .WithMany()
                        .HasForeignKey("ReportedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ResponsibleUser")
                        .WithMany()
                        .HasForeignKey("ResponsibleUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "RetrospectiveAnalystUser")
                        .WithMany()
                        .HasForeignKey("RetrospectiveAnalystUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AuditAnswer");

                    b.Navigation("FindingCategory");

                    b.Navigation("ReportedByUser");

                    b.Navigation("ResponsibleUser");

                    b.Navigation("RetrospectiveAnalystUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.FindingCategory", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany("FindingCategories")
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AuditTemplate");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Area", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Factory", "Factory")
                        .WithMany("Areas")
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Factory");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.AreaResponsibility", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Area", "Area")
                        .WithMany("AreaResponsibilities")
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany("AreaResponsibilities")
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ResponsibleUser")
                        .WithMany()
                        .HasForeignKey("ResponsibleUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("AuditTemplate");

                    b.Navigation("ResponsibleUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Factory", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Location", "Location")
                        .WithMany("Factories")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.OwnsOne("HWSAuditPlatform.Domain.ValueObjects.Address", "Address", b1 =>
                        {
                            b1.Property<int>("FactoryId")
                                .HasColumnType("int");

                            b1.Property<string>("AddressLine1")
                                .HasMaxLength(255)
                                .HasColumnType("nvarchar(255)")
                                .HasColumnName("AddressLine1");

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("nvarchar(100)")
                                .HasColumnName("City");

                            b1.Property<bool>("HasValue")
                                .ValueGeneratedOnAddOrUpdate()
                                .HasColumnType("bit")
                                .HasComputedColumnSql("CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END", true);

                            b1.Property<string>("PostalCode")
                                .HasMaxLength(20)
                                .HasColumnType("nvarchar(20)")
                                .HasColumnName("PostalCode");

                            b1.HasKey("FactoryId");

                            b1.ToTable("hwsap_org_factories");

                            b1.WithOwner()
                                .HasForeignKey("FactoryId");
                        });

                    b.Navigation("Address");

                    b.Navigation("CreatedByUser");

                    b.Navigation("Location");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.SubArea", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Area", "Area")
                        .WithMany("SubAreas")
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Area");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Scheduling.RecurrenceRule", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Scheduling.RecurringAuditSetting", "RecurringAuditSetting")
                        .WithOne("RecurrenceRule")
                        .HasForeignKey("HWSAuditPlatform.Domain.Entities.Scheduling.RecurrenceRule", "RecurringAuditSettingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RecurringAuditSetting");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Scheduling.RecurringAuditSetting", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.UserGroup", "AssignToUserGroup")
                        .WithMany()
                        .HasForeignKey("AssignToUserGroupId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "AssignToUser")
                        .WithMany()
                        .HasForeignKey("AssignToUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany()
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.SubArea", "SubArea")
                        .WithMany()
                        .HasForeignKey("SubAreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Area");

                    b.Navigation("AssignToUser");

                    b.Navigation("AssignToUserGroup");

                    b.Navigation("AuditTemplate");

                    b.Navigation("Factory");

                    b.Navigation("SubArea");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.Question", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany("Questions")
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.Question", "ParentQuestion")
                        .WithMany("ChildQuestions")
                        .HasForeignKey("ParentQuestionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.QuestionGroup", "QuestionGroup")
                        .WithMany("Questions")
                        .HasForeignKey("QuestionGroupId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AuditTemplate");

                    b.Navigation("ParentQuestion");

                    b.Navigation("QuestionGroup");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionAllowedEvidenceType", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.Question", "Question")
                        .WithMany("AllowedEvidenceTypes")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Question");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionGroup", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany("QuestionGroups")
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AuditTemplate");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionOption", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.Question", "Question")
                        .WithMany("Options")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Question");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.AdGroupRoleMapping", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.Role", "Role")
                        .WithMany("AdGroupRoleMappings")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.ProcessOwnerAssignment", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "AssignedByUser")
                        .WithMany()
                        .HasForeignKey("AssignedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ProcessOwnerUser")
                        .WithMany()
                        .HasForeignKey("ProcessOwnerUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.SubArea", "SubArea")
                        .WithMany()
                        .HasForeignKey("SubAreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Area");

                    b.Navigation("AssignedByUser");

                    b.Navigation("Factory");

                    b.Navigation("ProcessOwnerUser");

                    b.Navigation("SubArea");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.TemplateAccessAssignment", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "AssignedByUser")
                        .WithMany()
                        .HasForeignKey("AssignedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", "AuditTemplate")
                        .WithMany()
                        .HasForeignKey("AuditTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AssignedByUser");

                    b.Navigation("AuditTemplate");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.User", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Organization.Factory", "Factory")
                        .WithMany("Users")
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Factory");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.UserGroup", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.UserGroupMember", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.UserGroup", "UserGroup")
                        .WithMany("Members")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "User")
                        .WithMany("UserGroupMemberships")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Workflow.AuditCorrectionRequest", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("CorrectionRequests")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "RequestedByUser")
                        .WithMany()
                        .HasForeignKey("RequestedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "ReviewedByUser")
                        .WithMany()
                        .HasForeignKey("ReviewedByUserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Audit");

                    b.Navigation("RequestedByUser");

                    b.Navigation("ReviewedByUser");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Workflow.AuditLog", b =>
                {
                    b.HasOne("HWSAuditPlatform.Domain.Entities.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasPrincipalKey("AdObjectGuid")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("User");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.Audit", b =>
                {
                    b.Navigation("Answers");

                    b.Navigation("CorrectionRequests");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Audits.AuditAnswer", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("FailureReasons");

                    b.Navigation("SelectedOptions");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.Finding", b =>
                {
                    b.Navigation("CorrectiveActions");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Findings.FindingCategory", b =>
                {
                    b.Navigation("Findings");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Area", b =>
                {
                    b.Navigation("AreaResponsibilities");

                    b.Navigation("SubAreas");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Factory", b =>
                {
                    b.Navigation("Areas");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Organization.Location", b =>
                {
                    b.Navigation("Factories");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Scheduling.RecurringAuditSetting", b =>
                {
                    b.Navigation("RecurrenceRule");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.AuditTemplate", b =>
                {
                    b.Navigation("AreaResponsibilities");

                    b.Navigation("FindingCategories");

                    b.Navigation("QuestionGroups");

                    b.Navigation("Questions");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.Question", b =>
                {
                    b.Navigation("AllowedEvidenceTypes");

                    b.Navigation("ChildQuestions");

                    b.Navigation("Options");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Templates.QuestionGroup", b =>
                {
                    b.Navigation("Questions");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.Role", b =>
                {
                    b.Navigation("AdGroupRoleMappings");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.User", b =>
                {
                    b.Navigation("UserGroupMemberships");
                });

            modelBuilder.Entity("HWSAuditPlatform.Domain.Entities.Users.UserGroup", b =>
                {
                    b.Navigation("Members");
                });
#pragma warning restore 612, 618
        }
    }
}
