using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Events;

/// <summary>
/// Domain event raised when a finding is created
/// </summary>
public class FindingCreatedEvent : IDomainEvent
{
    public string FindingId { get; }
    public string AuditAnswerId { get; }
    public SeverityLevel SeverityLevel { get; }
    public string ReportedByUserId { get; }
    public DateTime OccurredOn { get; }

    public FindingCreatedEvent(string findingId, string auditAnswerId, SeverityLevel severityLevel, string reportedByUserId)
    {
        FindingId = findingId;
        AuditAnswerId = auditAnswerId;
        SeverityLevel = severityLevel;
        ReportedByUserId = reportedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a finding status changes
/// </summary>
public class FindingStatusChangedEvent : IDomainEvent
{
    public string FindingId { get; }
    public FindingStatus PreviousStatus { get; }
    public FindingStatus NewStatus { get; }
    public string? ChangedByUserId { get; }
    public DateTime OccurredOn { get; }

    public FindingStatusChangedEvent(string findingId, FindingStatus previousStatus, FindingStatus newStatus, string? changedByUserId)
    {
        FindingId = findingId;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        ChangedByUserId = changedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a corrective action is created
/// </summary>
public class CorrectiveActionCreatedEvent : IDomainEvent
{
    public string CorrectiveActionId { get; }
    public string FindingId { get; }
    public string AssignedToUserId { get; }
    public DateOnly DueDate { get; }
    public string CreatedByUserId { get; }
    public DateTime OccurredOn { get; }

    public CorrectiveActionCreatedEvent(string correctiveActionId, string findingId, string assignedToUserId, DateOnly dueDate, string createdByUserId)
    {
        CorrectiveActionId = correctiveActionId;
        FindingId = findingId;
        AssignedToUserId = assignedToUserId;
        DueDate = dueDate;
        CreatedByUserId = createdByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a corrective action status changes
/// </summary>
public class CorrectiveActionStatusChangedEvent : IDomainEvent
{
    public string CorrectiveActionId { get; }
    public CorrectiveActionStatus PreviousStatus { get; }
    public CorrectiveActionStatus NewStatus { get; }
    public string? ChangedByUserId { get; }
    public DateTime OccurredOn { get; }

    public CorrectiveActionStatusChangedEvent(string correctiveActionId, CorrectiveActionStatus previousStatus, CorrectiveActionStatus newStatus, string? changedByUserId)
    {
        CorrectiveActionId = correctiveActionId;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        ChangedByUserId = changedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a corrective action is completed
/// </summary>
public class CorrectiveActionCompletedEvent : IDomainEvent
{
    public string CorrectiveActionId { get; }
    public string FindingId { get; }
    public string CompletedByUserId { get; }
    public DateOnly CompletionDate { get; }
    public DateTime OccurredOn { get; }

    public CorrectiveActionCompletedEvent(string correctiveActionId, string findingId, string completedByUserId, DateOnly completionDate)
    {
        CorrectiveActionId = correctiveActionId;
        FindingId = findingId;
        CompletedByUserId = completedByUserId;
        CompletionDate = completionDate;
        OccurredOn = DateTime.UtcNow;
    }
}
