using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Users.Commands.SyncUsersFromAd;

/// <summary>
/// Command to synchronize users from Active Directory
/// </summary>
public class SyncUsersFromAdCommand : BaseCommand<SyncUsersFromAdResult>
{
    /// <summary>
    /// Whether to perform a dry run (no actual changes to database)
    /// </summary>
    public bool DryRun { get; set; } = false;
}

/// <summary>
/// Result of AD user synchronization
/// </summary>
public class SyncUsersFromAdResult
{
    public int UsersCreated { get; set; }
    public int UsersUpdated { get; set; }
    public int UsersDisabled { get; set; }
    public int TotalAdUsers { get; set; }
    public int TotalDbUsers { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime SyncStartTime { get; set; }
    public DateTime SyncEndTime { get; set; }
    public TimeSpan Duration => SyncEndTime - SyncStartTime;
    public bool WasDryRun { get; set; }

    public override string ToString()
    {
        return $"AD Sync {(WasDryRun ? "(DRY RUN) " : "")}completed in {Duration:mm\\:ss}: " +
               $"Created: {UsersCreated}, Updated: {UsersUpdated}, Disabled: {UsersDisabled}, " +
               $"AD Users: {TotalAdUsers}, DB Users: {TotalDbUsers}, Errors: {Errors.Count}";
    }
}
