using FluentValidation;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Scheduling.Commands.CreateRecurringAuditSetting;

/// <summary>
/// Validator for CreateRecurringAuditSettingCommand
/// </summary>
public class CreateRecurringAuditSettingCommandValidator : AbstractValidator<CreateRecurringAuditSettingCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateRecurringAuditSettingCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.SettingName)
            .NotEmpty().WithMessage("Setting name is required")
            .MaximumLength(255).WithMessage("Setting name cannot exceed 255 characters")
            .MustAsync(BeUniqueSettingName).WithMessage("A recurring audit setting with this name already exists");

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0).WithMessage("Audit template ID must be greater than 0")
            .MustAsync(BeValidPublishedTemplate).WithMessage("Audit template does not exist or is not published");

        RuleFor(x => x.DeadlineDays)
            .GreaterThan(0).WithMessage("Deadline days must be greater than 0")
            .LessThanOrEqualTo(365).WithMessage("Deadline days cannot exceed 365 days");

        RuleFor(x => x.FactoryId)
            .GreaterThan(0).WithMessage("Factory ID must be greater than 0")
            .MustAsync(BeValidFactory).WithMessage("Factory does not exist");

        RuleFor(x => x.AreaId)
            .MustAsync(BeValidArea).WithMessage("Area does not exist or does not belong to the specified factory")
            .When(x => x.AreaId.HasValue);

        RuleFor(x => x.SubAreaId)
            .MustAsync(BeValidSubArea).WithMessage("SubArea does not exist or does not belong to the specified area")
            .When(x => x.SubAreaId.HasValue);

        RuleFor(x => x.AssignmentType)
            .IsInEnum().WithMessage("Assignment type must be valid");

        RuleFor(x => x.AssignToUserId)
            .NotEmpty().WithMessage("User ID is required for Individual assignment type")
            .MustAsync(BeValidUser).WithMessage("User does not exist")
            .When(x => x.AssignmentType == AssignmentType.Individual);

        RuleFor(x => x.AssignToUserGroupId)
            .NotEmpty().WithMessage("User Group ID is required for Group assignment types")
            .MustAsync(BeValidUserGroup).WithMessage("User group does not exist")
            .When(x => x.AssignmentType == AssignmentType.GroupAny || x.AssignmentType == AssignmentType.GroupAllScheduled);

        // Recurrence rule validation
        RuleFor(x => x.RecurrenceRule)
            .NotNull().WithMessage("Recurrence rule is required")
            .SetValidator(new CreateRecurrenceRuleDtoValidator());
    }

    private async Task<bool> BeUniqueSettingName(string settingName, CancellationToken cancellationToken)
    {
        return !await _context.RecurringAuditSettings
            .AnyAsync(r => r.SettingName == settingName && r.IsEnabled, cancellationToken);
    }

    private async Task<bool> BeValidPublishedTemplate(int templateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates
            .AnyAsync(t => t.Id == templateId && t.IsPublished && t.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidFactory(int factoryId, CancellationToken cancellationToken)
    {
        return await _context.Factories
            .AnyAsync(f => f.Id == factoryId && f.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidArea(CreateRecurringAuditSettingCommand command, int? areaId, CancellationToken cancellationToken)
    {
        if (!areaId.HasValue)
            return true;

        return await _context.Areas
            .AnyAsync(a => a.Id == areaId.Value && a.FactoryId == command.FactoryId && a.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidSubArea(CreateRecurringAuditSettingCommand command, int? subAreaId, CancellationToken cancellationToken)
    {
        if (!subAreaId.HasValue)
            return true;

        return await _context.SubAreas
            .AnyAsync(sa => sa.Id == subAreaId.Value && 
                           (command.AreaId == null || sa.AreaId == command.AreaId.Value) && 
                           sa.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidUser(string? userId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(userId))
            return false;

        return await _context.Users
            .AnyAsync(u => u.AdObjectGuid == userId && u.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidUserGroup(string? userGroupId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(userGroupId))
            return false;

        // UserGroup doesn't have IsActive property, just check if it exists
        return await _context.UserGroups
            .AnyAsync(g => g.Id == userGroupId, cancellationToken);
    }
}

/// <summary>
/// Validator for CreateRecurrenceRuleDto
/// </summary>
public class CreateRecurrenceRuleDtoValidator : AbstractValidator<CreateRecurrenceRuleDto>
{
    public CreateRecurrenceRuleDtoValidator()
    {
        RuleFor(x => x.FrequencyType)
            .IsInEnum().WithMessage("Frequency type must be valid");

        RuleFor(x => x.Interval)
            .GreaterThan(0).WithMessage("Interval must be greater than 0")
            .LessThanOrEqualTo(999).WithMessage("Interval cannot exceed 999");

        RuleFor(x => x.StartDate)
            .NotEmpty().WithMessage("Start date is required");

        RuleFor(x => x.EndDate)
            .GreaterThan(x => x.StartDate).WithMessage("End date must be after start date")
            .When(x => x.EndDate.HasValue);

        // Weekly validation
        RuleFor(x => x.WeeklyDaysOfWeekMask)
            .NotNull().WithMessage("Days of week must be specified for weekly frequency")
            .GreaterThan(0).WithMessage("At least one day of the week must be selected")
            .LessThan(128).WithMessage("Invalid days of week mask")
            .When(x => x.FrequencyType == FrequencyType.WEEKLY);

        // Monthly validation
        RuleFor(x => x)
            .Must(HaveValidMonthlyConfiguration).WithMessage("Monthly frequency requires either a day of month or nth weekday configuration")
            .When(x => x.FrequencyType == FrequencyType.MONTHLY);

        RuleFor(x => x.MonthlyDayOfMonth)
            .InclusiveBetween(1, 31).WithMessage("Monthly day of month must be between 1 and 31")
            .When(x => x.MonthlyDayOfMonth.HasValue);

        RuleFor(x => x.MonthlyNthWeek)
            .InclusiveBetween(1, 5).WithMessage("Monthly nth week must be between 1 and 5")
            .When(x => x.MonthlyNthWeek.HasValue);

        RuleFor(x => x.MonthlyDayOfWeek)
            .InclusiveBetween(1, 10).WithMessage("Monthly day of week must be between 1 and 10")
            .When(x => x.MonthlyDayOfWeek.HasValue);

        // Yearly validation
        RuleFor(x => x)
            .Must(HaveValidYearlyConfiguration).WithMessage("Yearly frequency requires month and either day of month or nth weekday configuration")
            .When(x => x.FrequencyType == FrequencyType.YEARLY);

        RuleFor(x => x.YearlyMonth)
            .InclusiveBetween(1, 12).WithMessage("Yearly month must be between 1 and 12")
            .When(x => x.YearlyMonth.HasValue);

        RuleFor(x => x.YearlyDayOfMonth)
            .InclusiveBetween(1, 31).WithMessage("Yearly day of month must be between 1 and 31")
            .When(x => x.YearlyDayOfMonth.HasValue);

        RuleFor(x => x.YearlyNthWeek)
            .InclusiveBetween(1, 5).WithMessage("Yearly nth week must be between 1 and 5")
            .When(x => x.YearlyNthWeek.HasValue);

        RuleFor(x => x.YearlyDayOfWeek)
            .InclusiveBetween(1, 10).WithMessage("Yearly day of week must be between 1 and 10")
            .When(x => x.YearlyDayOfWeek.HasValue);
    }

    private static bool HaveValidMonthlyConfiguration(CreateRecurrenceRuleDto rule)
    {
        return rule.MonthlyDayOfMonth.HasValue || 
               (rule.MonthlyNthWeek.HasValue && rule.MonthlyDayOfWeek.HasValue);
    }

    private static bool HaveValidYearlyConfiguration(CreateRecurrenceRuleDto rule)
    {
        return rule.YearlyMonth.HasValue && 
               (rule.YearlyDayOfMonth.HasValue || 
                (rule.YearlyNthWeek.HasValue && rule.YearlyDayOfWeek.HasValue));
    }
}
