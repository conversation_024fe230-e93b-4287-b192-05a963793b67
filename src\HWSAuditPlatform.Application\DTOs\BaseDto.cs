namespace HWSAuditPlatform.Application.DTOs;

/// <summary>
/// Base class for Data Transfer Objects
/// </summary>
public abstract class BaseDto
{
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Base class for DTOs with typed primary keys
/// </summary>
/// <typeparam name="TKey">The primary key type</typeparam>
public abstract class BaseDto<TKey> : BaseDto where TKey : IEquatable<TKey>
{
    public TKey Id { get; set; } = default!;
}

/// <summary>
/// Base class for auditable DTOs with versioning support
/// </summary>
/// <typeparam name="TKey">The primary key type</typeparam>
public abstract class AuditableDto<TKey> : BaseDto<TKey> where TKey : IEquatable<TKey>
{
    public int RecordVersion { get; set; }

    /// <summary>
    /// User AD ObjectGUID who created this entity (FK to User.AdObjectGuid)
    /// </summary>
    public string? CreatedByUserId { get; set; }

    /// <summary>
    /// User AD ObjectGUID who last updated this entity (FK to User.AdObjectGuid)
    /// </summary>
    public string? UpdatedByUserId { get; set; }
}
