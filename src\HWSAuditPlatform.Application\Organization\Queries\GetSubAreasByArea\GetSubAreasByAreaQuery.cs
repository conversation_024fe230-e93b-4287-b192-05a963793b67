using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Organization.Queries.GetSubAreasByArea;

/// <summary>
/// Query to get sub-areas for a specific area
/// </summary>
public class GetSubAreasByAreaQuery : BaseQuery<List<SubAreaSummaryDto>>
{
    public int AreaId { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
}
