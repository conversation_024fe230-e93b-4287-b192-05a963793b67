@page "/audits/create"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject ITemplateApiService TemplateApiService
@inject IAuditApiService AuditApiService
@inject IOrganizationApiService OrganizationApiService
@inject ILogger<CreateAudit> Logger
@inject NavigationManager Navigation
@inject IConfiguration Configuration
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Create Audit - HWS Audit Platform</PageTitle>

@if (skipAuthentication)
{
    <!-- Debug mode: Skip authorization check -->
    <div class="container-fluid mt-4">
        @RenderCreateAuditContent()
    </div>
}
else
{
    <!-- Normal mode: Require authorization -->
    <AuthorizeView>
        <Authorized>
            <div class="container-fluid mt-4">
                @RenderCreateAuditContent()
            </div>
        </Authorized>
        <NotAuthorized>
            <div class="text-center p-4">
                <h3>Access Denied</h3>
                <p>You need to be logged in to create audits.</p>
                <a href="/login" class="btn btn-primary">Go to Login</a>
            </div>
        </NotAuthorized>
    </AuthorizeView>
}

@code {
    private bool skipAuthentication = false;
    private CreateAuditRequest auditRequest = new();
    private List<AuditTemplate> templates = new();
    private List<FactorySummary> factories = new();
    private List<AreaSummary> areas = new();
    private List<SubAreaSummary> subAreas = new();
    private List<UserGroupSummary> userGroups = new();
    
    private AuditTemplate? selectedTemplate = null;
    private bool isLoading = true;
    private bool isCreating = false;
    private string? successMessage = null;
    private string? errorMessage = null;
    private bool showTemplatePreview = false;

    // User search functionality
    private UserSummary? selectedUser = null;

    protected override async Task OnInitializedAsync()
    {
        // Check if we should skip authentication (debug mode)
        skipAuthentication = Configuration.GetValue<bool>("Debug:SkipAuthentication", false);

        await LoadInitialData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            isLoading = true;
            
            // Load all required data in parallel (users are loaded on-demand via search)
            var templatesTask = TemplateApiService.GetTemplatesAsync(isActive: true);
            var factoriesTask = OrganizationApiService.GetFactoriesAsync();
            var userGroupsTask = OrganizationApiService.GetUserGroupsAsync();

            await Task.WhenAll(templatesTask, factoriesTask, userGroupsTask);

            templates = (await templatesTask).Where(t => t.IsPublished).ToList();
            factories = (await factoriesTask).Where(f => f.IsActive).ToList();
            userGroups = (await userGroupsTask).Where(g => g.IsActive).ToList();

            // Debug logging
            Logger.LogInformation("Loaded {TemplateCount} templates, {FactoryCount} factories, {GroupCount} groups",
                templates.Count, factories.Count, userGroups.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading initial data for audit creation");
            ShowErrorMessage("Failed to load required data. Please refresh the page.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private RenderFragment RenderCreateAuditContent() => __builder =>
    {
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="bi bi-plus-circle me-2 text-primary"></i>
                            Create New Audit
                        </h4>
                    </div>
                    <div class="card-body">
                        @if (isLoading)
                        {
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading data...</p>
                            </div>
                        }
                        else
                        {
                            <EditForm Model="auditRequest" OnValidSubmit="CreateAuditAsync">
                                <DataAnnotationsValidator />
                                
                                <!-- Template Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label class="form-label">Audit Template *</label>
                                        <select class="form-select" value="@auditRequest.AuditTemplateId" @onchange="OnTemplateChangedHandler" required>
                                            <option value="0">Select a template...</option>
                                            @foreach (var template in templates)
                                            {
                                                <option value="@template.Id">@template.TemplateName</option>
                                            }
                                        </select>
                                        <ValidationMessage For="@(() => auditRequest.AuditTemplateId)" />
                                    </div>
                                    <div class="col-md-4">
                                        @if (selectedTemplate != null)
                                        {
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button type="button" class="btn btn-outline-info btn-sm" @onclick="ToggleTemplatePreview">
                                                    <i class="bi bi-eye me-1"></i>Preview Template
                                                </button>
                                            </div>
                                        }
                                    </div>
                                </div>

                                <!-- Assignment Type -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Assignment Type *</label>
                                        <select class="form-select" value="@auditRequest.AssignmentType" @onchange="OnAssignmentTypeChangedHandler" required>
                                            <option value="@AssignmentType.Individual">Individual</option>
                                            <option value="@AssignmentType.GroupAny">Group (Any Member)</option>
                                            <option value="@AssignmentType.GroupAllScheduled">Group (All Members)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        @if (auditRequest.AssignmentType == AssignmentType.Individual)
                                        {
                                            <label class="form-label">Assigned User *</label>
                                            <SearchDropdown TItem="UserSummary"
                                                          Placeholder="Type to search users..."
                                                          Required="true"
                                                          MaxResults="5"
                                                          DebounceMs="300"
                                                          SearchFunction="SearchUsersAsync"
                                                          GetPrimaryText="@(user => user.FullName)"
                                                          GetSecondaryText="@(user => user.Username)"
                                                          GetAdditionalInfo="@(user => user.FactoryName ?? string.Empty)"
                                                          GetDisplayText="@(user => $"{user.FullName} ({user.Username})")"
                                                          @bind-SelectedItem="selectedUser"
                                                          OnItemSelected="OnUserSelected"
                                                          OnCleared="OnUserCleared" />
                                            <ValidationMessage For="@(() => auditRequest.AssignedToUserId)" />
                                        }
                                        else
                                        {
                                            <label class="form-label">Assigned Group *</label>
                                            <select class="form-select" @bind="auditRequest.AssignedToUserGroupId" required>
                                                <option value="">Select a group...</option>
                                                @foreach (var group in userGroups)
                                                {
                                                    <option value="@group.Id">@group.GroupName (@group.MemberCount members)</option>
                                                }
                                            </select>
                                            <ValidationMessage For="@(() => auditRequest.AssignedToUserGroupId)" />
                                        }
                                    </div>
                                </div>

                                <!-- Location Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Factory *</label>
                                        <select class="form-select" value="@auditRequest.FactoryId" @onchange="OnFactoryChangedHandler" required>
                                            <option value="0">Select a factory...</option>
                                            @foreach (var factory in factories)
                                            {
                                                <option value="@factory.Id">@factory.FactoryName</option>
                                            }
                                        </select>
                                        <ValidationMessage For="@(() => auditRequest.FactoryId)" />
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Area *</label>
                                        <select class="form-select" value="@auditRequest.AreaId" @onchange="OnAreaChangedHandler" required disabled="@(auditRequest.FactoryId == 0)">
                                            <option value="0">Select an area...</option>
                                            @foreach (var area in areas)
                                            {
                                                <option value="@area.Id">@area.AreaName</option>
                                            }
                                        </select>
                                        <ValidationMessage For="@(() => auditRequest.AreaId)" />
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Sub-Area</label>
                                        <select class="form-select" @bind="auditRequest.SubAreaId" disabled="@(auditRequest.AreaId == 0)">
                                            <option value="">Select a sub-area...</option>
                                            @foreach (var subArea in subAreas)
                                            {
                                                <option value="@subArea.Id">@subArea.SubAreaName</option>
                                            }
                                        </select>
                                    </div>
                                </div>

                                <!-- Scheduling -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Scheduled Date *</label>
                                        <input type="datetime-local" class="form-control" @bind="auditRequest.ScheduledDate" required />
                                        <ValidationMessage For="@(() => auditRequest.ScheduledDate)" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Due Date</label>
                                        <input type="datetime-local" class="form-control" @bind="auditRequest.DueDate" />
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" @onclick="Cancel">
                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                    </button>
                                    <button type="submit" class="btn btn-primary" disabled="@isCreating">
                                        @if (isCreating)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        }
                                        else
                                        {
                                            <i class="bi bi-plus-circle me-1"></i>
                                        }
                                        Create Audit
                                    </button>
                                </div>
                            </EditForm>
                        }
                    </div>
                </div>
            </div>
            
            <!-- Template Preview Panel -->
            <div class="col-md-4">
                @if (showTemplatePreview && selectedTemplate != null)
                {
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Template Preview</h5>
                            <button type="button" class="btn-close" @onclick="() => showTemplatePreview = false"></button>
                        </div>
                        <div class="card-body">
                            <h6>@selectedTemplate.TemplateName</h6>
                            @if (!string.IsNullOrEmpty(selectedTemplate.Description))
                            {
                                <p class="text-muted small">@selectedTemplate.Description</p>
                            }
                            <div class="small">
                                <strong>Version:</strong> @selectedTemplate.Version<br />
                                <strong>Status:</strong> 
                                <span class="badge @(selectedTemplate.IsPublished ? "bg-success" : "bg-warning")">
                                    @(selectedTemplate.IsPublished ? "Published" : "Draft")
                                </span><br />
                                <strong>Created:</strong> @selectedTemplate.CreatedAt.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-check-circle me-2"></i>@successMessage
                <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
            </div>
        }

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>@errorMessage
                <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
            </div>
        }
    };

    private void OnTemplateChanged()
    {
        selectedTemplate = templates.FirstOrDefault(t => t.Id == auditRequest.AuditTemplateId);
        showTemplatePreview = selectedTemplate != null;
    }

    private void OnAssignmentTypeChanged()
    {
        // Clear assignment fields when type changes
        auditRequest.AssignedToUserId = null;
        auditRequest.AssignedToUserGroupId = null;

        // Clear user search state
        selectedUser = null;
    }

    private async Task OnFactoryChanged()
    {
        // Clear dependent fields
        auditRequest.AreaId = 0;
        auditRequest.SubAreaId = null;
        areas.Clear();
        subAreas.Clear();

        if (auditRequest.FactoryId > 0)
        {
            try
            {
                areas = (await OrganizationApiService.GetAreasByFactoryAsync(auditRequest.FactoryId))
                    .Where(a => a.IsActive).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading areas for factory {FactoryId}", auditRequest.FactoryId);
                ShowErrorMessage("Failed to load areas for the selected factory.");
            }
        }
    }

    private async Task OnAreaChanged()
    {
        // Clear dependent fields
        auditRequest.SubAreaId = null;
        subAreas.Clear();

        if (auditRequest.AreaId > 0)
        {
            try
            {
                subAreas = (await OrganizationApiService.GetSubAreasByAreaAsync(auditRequest.AreaId))
                    .Where(sa => sa.IsActive).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading sub-areas for area {AreaId}", auditRequest.AreaId);
                ShowErrorMessage("Failed to load sub-areas for the selected area.");
            }
        }
    }

    private void ToggleTemplatePreview()
    {
        showTemplatePreview = !showTemplatePreview;
    }

    private async Task CreateAuditAsync()
    {
        try
        {
            isCreating = true;
            ClearMessages();

            Logger.LogInformation("Creating audit for template {TemplateId}", auditRequest.AuditTemplateId);

            var auditId = await AuditApiService.CreateAuditAsync(auditRequest);

            ShowSuccessMessage($"Audit created successfully! ID: {auditId}");

            // Navigate to the audit detail page after a short delay
            await Task.Delay(2000);
            Navigation.NavigateTo($"/audits/{auditId}");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating audit");
            ShowErrorMessage("Failed to create audit. Please check your inputs and try again.");
        }
        finally
        {
            isCreating = false;
        }
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/audits");
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
    }

    private void ClearMessages()
    {
        successMessage = null;
        errorMessage = null;
    }

    // User search methods for SearchDropdown component
    private async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int maxResults)
    {
        try
        {
            return await OrganizationApiService.SearchUsersAsync(searchTerm, maxResults);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error searching users with term: {SearchTerm}", searchTerm);
            return Enumerable.Empty<UserSummary>();
        }
    }

    private void OnUserSelected(UserSummary user)
    {
        selectedUser = user;
        auditRequest.AssignedToUserId = user.AdObjectGuid;
        StateHasChanged();
    }

    private void OnUserCleared()
    {
        selectedUser = null;
        auditRequest.AssignedToUserId = null;
        StateHasChanged();
    }

    // Event handlers for manual binding
    private void OnTemplateChangedHandler(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var templateId))
        {
            auditRequest.AuditTemplateId = templateId;
            OnTemplateChanged();
        }
    }

    private void OnAssignmentTypeChangedHandler(ChangeEventArgs e)
    {
        if (Enum.TryParse<AssignmentType>(e.Value?.ToString(), out var assignmentType))
        {
            auditRequest.AssignmentType = assignmentType;
            OnAssignmentTypeChanged();
        }
    }

    private async Task OnFactoryChangedHandler(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var factoryId))
        {
            auditRequest.FactoryId = factoryId;
            await OnFactoryChanged();
        }
    }

    private async Task OnAreaChangedHandler(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var areaId))
        {
            auditRequest.AreaId = areaId;
            await OnAreaChanged();
        }
    }
}
