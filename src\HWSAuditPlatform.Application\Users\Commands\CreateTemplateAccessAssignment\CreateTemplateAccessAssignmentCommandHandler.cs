using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.CreateTemplateAccessAssignment;

/// <summary>
/// Handler for CreateTemplateAccessAssignmentCommand
/// </summary>
public class CreateTemplateAccessAssignmentCommandHandler : BaseCommandHandler<CreateTemplateAccessAssignmentCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ITemplateAccessService _templateAccessService;

    public CreateTemplateAccessAssignmentCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ITemplateAccessService templateAccessService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _templateAccessService = templateAccessService;
    }

    public override async Task<int> Handle(CreateTemplateAccessAssignmentCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Verify current user has permission (SystemManager or DevAdmin)
        var currentUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == currentUserId, cancellationToken);

        if (currentUser?.Role.RoleName != UserRole.SystemManager && currentUser?.Role.RoleName != UserRole.DevAdmin)
        {
            throw new UnauthorizedAccessException("Only SystemManager or DevAdmin can create template access assignments");
        }

        // Verify the target user exists
        var targetUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == request.UserId, cancellationToken);

        if (targetUser == null)
        {
            throw new InvalidOperationException($"User with ID {request.UserId} not found");
        }

        // Verify the template exists and is published
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId && t.IsPublished && t.IsActive, cancellationToken);

        if (template == null)
        {
            throw new InvalidOperationException($"Audit template with ID {request.AuditTemplateId} not found or not published");
        }

        // Create the assignment using the service
        var assignment = await _templateAccessService.GrantTemplateAccessAsync(
            request.UserId,
            request.AuditTemplateId,
            currentUserId,
            request.Description,
            request.ExpiresAt,
            cancellationToken);

        return assignment.Id;
    }
}
