using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Debug authentication state provider that returns mock authentication state
/// </summary>
public class DebugAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly ILogger<DebugAuthenticationStateProvider> _logger;
    private readonly IConfiguration _configuration;
    private ClaimsPrincipal _currentUser;
    private AuthenticationState _authenticationState;

    public DebugAuthenticationStateProvider(
        ILogger<DebugAuthenticationStateProvider> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
        // Initialize with mock user
        var mockUser = CreateMockUser();
        _currentUser = new ClaimsPrincipal(mockUser);
        _authenticationState = new AuthenticationState(_currentUser);
        
        _logger.LogWarning("🚨 DEBUG MODE: Authentication state provider initialized with mock user");
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        _logger.LogDebug("🚨 DEBUG MODE: Returning mock authentication state");
        return Task.FromResult(_authenticationState);
    }

    private ClaimsIdentity CreateMockUser()
    {
        var mockUserSection = _configuration.GetSection("Debug:MockUser");
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, mockUserSection["UserId"] ?? "debug-user-001"),
            new(ClaimTypes.Name, mockUserSection["Username"] ?? "debug.user"),
            new(ClaimTypes.Email, mockUserSection["Email"] ?? "<EMAIL>"),
            new(ClaimTypes.Role, mockUserSection["Role"] ?? "DevAdmin"),
            new("FullName", mockUserSection["FullName"] ?? "Debug User"),
            new("FactoryId", mockUserSection["FactoryId"] ?? "1")
        };

        return new ClaimsIdentity(claims, "debug");
    }

    /// <summary>
    /// Marks the user as authenticated (for debug mode, always authenticated)
    /// </summary>
    /// <param name="user">User information</param>
    public void MarkUserAsAuthenticated(UserInfo user)
    {
        _logger.LogWarning("🚨 DEBUG MODE: MarkUserAsAuthenticated called for user: {Username}", user.Username);
        
        // In debug mode, we always use the mock user
        var mockUser = CreateMockUser();
        _currentUser = new ClaimsPrincipal(mockUser);
        _authenticationState = new AuthenticationState(_currentUser);
        
        NotifyAuthenticationStateChanged(Task.FromResult(_authenticationState));
    }

    /// <summary>
    /// Marks the user as logged out (in debug mode, immediately re-authenticate)
    /// </summary>
    public void MarkUserAsLoggedOut()
    {
        _logger.LogWarning("🚨 DEBUG MODE: MarkUserAsLoggedOut called - re-authenticating with mock user");
        
        // In debug mode, immediately re-authenticate with mock user
        var mockUser = CreateMockUser();
        _currentUser = new ClaimsPrincipal(mockUser);
        _authenticationState = new AuthenticationState(_currentUser);
        
        NotifyAuthenticationStateChanged(Task.FromResult(_authenticationState));
    }

    /// <summary>
    /// Gets the current user information
    /// </summary>
    /// <returns>Current user info</returns>
    public UserInfo GetCurrentUser()
    {
        var mockUserSection = _configuration.GetSection("Debug:MockUser");
        
        return new UserInfo
        {
            UserId = mockUserSection["UserId"] ?? "debug-user-001",
            Username = mockUserSection["Username"] ?? "debug.user",
            Email = mockUserSection["Email"] ?? "<EMAIL>",
            FullName = mockUserSection["FullName"] ?? "Debug User",
            Role = mockUserSection["Role"] ?? "DevAdmin",
            FactoryId = mockUserSection["FactoryId"] ?? "1",
            IsActive = true
        };
    }

    /// <summary>
    /// Checks if the current user has a specific role
    /// </summary>
    /// <param name="role">Role to check</param>
    /// <returns>True if user has the role, false otherwise</returns>
    public bool IsInRole(string role)
    {
        var mockRole = _configuration["Debug:MockUser:Role"] ?? "DevAdmin";
        return mockRole == role;
    }

    /// <summary>
    /// Checks if the current user has any of the specified roles
    /// </summary>
    /// <param name="roles">Roles to check</param>
    /// <returns>True if user has any of the roles, false otherwise</returns>
    public bool HasAnyRole(params string[] roles)
    {
        var mockRole = _configuration["Debug:MockUser:Role"] ?? "DevAdmin";
        return roles.Contains(mockRole);
    }

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    public bool IsAuthenticated()
    {
        return _currentUser.Identity?.IsAuthenticated == true;
    }
}
