# HWS Audit Platform - Authentication & User Management API

## Overview

This document provides comprehensive documentation for the Authentication and User Management endpoints of the HWS Audit Platform API. These endpoints handle user authentication, authorization, and user lifecycle management.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **Authentication**: JW<PERSON> (except where noted)
- **Content-Type**: `application/json`
- **API Version**: `1.0`

## Authentication Endpoints

### POST /auth/login

Authenticate a user with Active Directory credentials and receive a JWT token.

**Authorization**: None (Anonymous)

**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "username": "john.doe",
    "email": "<EMAIL>",
    "fullName": "<PERSON>",
    "role": "Manager",
    "factoryId": "1",
    "expiresAt": "2024-01-01T16:00:00Z"
  },
  "message": "Login successful"
}
```

**Error Responses**:
- `400 Bad Request`: Invalid credentials or missing fields
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Server error

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin.user",
    "password": "HWSAudit123!"
  }'
```

### GET /auth/me

Get information about the currently authenticated user.

**Authorization**: Bearer Token Required

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "userId": "c1234567890abcdef",
    "username": "john.doe",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "role": "Manager",
    "factoryId": "1"
  }
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing token

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /auth/refresh

Refresh an existing JWT token (if implemented).

**Authorization**: Bearer Token Required

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-01T16:00:00Z"
  },
  "message": "Token refreshed successfully"
}
```

### GET /auth/test-ad

Test Active Directory connectivity (for debugging).

**Authorization**: None (Anonymous)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "isConnected": true,
    "message": "Active Directory connection successful",
    "timestamp": "2024-01-01T08:00:00Z"
  }
}
```

## User Management Endpoints

### GET /users

Get a paginated list of users with filtering and sorting options.

**Authorization**: Manager or Admin role required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `searchTerm` (string, optional): Search in username, email, or full name
- `role` (string, optional): Filter by user role
- `factoryId` (int, optional): Filter by factory ID
- `isActive` (bool, optional): Filter by active status
- `sortBy` (string, optional): Sort field
- `sortDirection` (string, optional): "asc" or "desc"

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "c1234567890abcdef",
        "username": "john.doe",
        "email": "<EMAIL>",
        "fullName": "John Doe",
        "role": "Manager",
        "factoryId": 1,
        "factoryName": "Main Factory",
        "isActive": true,
        "lastLoginAt": "2024-01-01T08:00:00Z",
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 25,
    "totalPages": 3,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/users?pageNumber=1&pageSize=20&searchTerm=john&role=Manager" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /users/{id}

Get detailed information about a specific user.

**Authorization**: Manager or Admin role required

**Path Parameters**:
- `id` (string): User ID

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": "c1234567890abcdef",
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "fullName": "John Doe",
    "role": "Manager",
    "factoryId": 1,
    "factory": {
      "id": 1,
      "name": "Main Factory",
      "location": "New York"
    },
    "isActive": true,
    "lastLoginAt": "2024-01-01T08:00:00Z",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "recordVersion": 5
  }
}
```

**Error Responses**:
- `404 Not Found`: User not found

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/users/c1234567890abcdef" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /users

Create a new user.

**Authorization**: Manager or Admin role required

**Request Body**:
```json
{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "Auditor",
  "factoryId": 1,
  "isActive": true
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": "c9876543210fedcba",
  "message": "User created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `409 Conflict`: Username or email already exists

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/users" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jane.smith",
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith",
    "role": "Auditor",
    "factoryId": 1,
    "isActive": true
  }'
```

### PUT /users/{id}

Update an existing user.

**Authorization**: Manager or Admin role required

**Path Parameters**:
- `id` (string): User ID

**Request Body**:
```json
{
  "id": "c1234567890abcdef",
  "username": "john.doe",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "Manager",
  "factoryId": 1,
  "isActive": true,
  "recordVersion": 5
}
```

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Validation errors or ID mismatch
- `404 Not Found`: User not found
- `409 Conflict`: Concurrency conflict (record version mismatch)

**Example**:
```bash
curl -X PUT "https://localhost:7490/api/v1/users/c1234567890abcdef" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "c1234567890abcdef",
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "Manager",
    "factoryId": 1,
    "isActive": true,
    "recordVersion": 5
  }'
```

## Authentication Flow

1. **Login**: Send credentials to `/auth/login`
2. **Receive Token**: Get JWT token in response
3. **Use Token**: Include token in `Authorization` header as `Bearer TOKEN`
4. **Token Expiry**: Tokens expire after 8 hours
5. **Refresh** (if needed): Use `/auth/refresh` to get new token

## Error Handling

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "username",
      "message": "Username is required"
    }
  ]
}
```

## Role-Based Access Control

- **Admin**: Full access to all user management operations
- **Manager**: Can view and manage users within their factory
- **Auditor**: Cannot access user management endpoints

## Rate Limiting

- Authentication endpoints: 5 requests per minute per IP
- User management endpoints: 100 requests per minute per user

## Security Notes

- All passwords are validated against Active Directory
- JWT tokens are signed with HS256 algorithm
- Tokens include user ID, username, role, and factory ID claims
- All user management operations are logged for audit purposes
