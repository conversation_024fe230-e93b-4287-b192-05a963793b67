# User Hierarchy System

## Overview

The HWS Audit Platform implements a hierarchical user role system designed to support enterprise-scale audit management with clear separation of responsibilities and appropriate access controls.

## User Roles

### 1. <PERSON><PERSON><PERSON><PERSON> (Developer Administrator)
- **Purpose**: Full system access for developers and system administrators
- **Capabilities**:
  - Complete system administration
  - Database management
  - System configuration
  - User role management
  - Template creation and management
  - Process owner assignments
  - Template access management
- **AD Group**: `HWSAudit-DevAdministrators`

### 2. SystemManager (System Manager)
- **Purpose**: High-level system management and template administration
- **Capabilities**:
  - Audit template creation and management
  - User management (except DevAdmin users)
  - Process owner assignments
  - Template access management
  - System-wide reporting
  - Organizational structure management
- **AD Group**: `HWSAudit-SystemManagers`

### 3. <PERSON>Owner (Process Owner)
- **Purpose**: Audit assignment and results management within assigned areas
- **Capabilities**:
  - Assign audits to users in their scope
  - Review and approve audit results
  - Manage audit schedules
  - Access templates assigned to them
  - View reports for their areas
- **AD Group**: `HWSAudit-ProcessOwners`
- **Scope**: Can be assigned to specific factories, areas, or sub-areas

### 4. Auditor
- **Purpose**: Perform audit activities
- **Capabilities**:
  - Complete assigned audits
  - Submit audit responses
  - View audit history
  - Access assigned templates
- **AD Group**: `HWSAudit-Auditors`

### 5. Viewer
- **Purpose**: Read-only access to audit information
- **Capabilities**:
  - View audit results
  - Access reports
  - Read-only template access
- **AD Group**: `HWSAudit-Viewers`

## Process Owner Assignment System

### Hierarchical Scope Assignment

Process owners can be assigned to different organizational levels:

1. **Global Scope**: No specific assignment (all factories)
2. **Factory Level**: Assigned to a specific factory
3. **Area Level**: Assigned to a specific area within a factory
4. **Sub-Area Level**: Assigned to a specific sub-area within an area

### Assignment Rules

- **Hierarchy Validation**: Assignments must follow organizational hierarchy
  - Sub-area assignments require area assignment
  - Area assignments require factory assignment
- **Unique Assignments**: Each process owner can have only one assignment per scope
- **Active Status**: Assignments can be activated/deactivated
- **Audit Trail**: All assignments track who created/modified them

### Example Assignments

```csharp
// Global assignment - all factories
ProcessOwnerAssignment.Create("user-123", "admin-456");

// Factory-specific assignment
ProcessOwnerAssignment.Create("user-123", "admin-456", factoryId: 1);

// Area-specific assignment
ProcessOwnerAssignment.Create("user-123", "admin-456", factoryId: 1, areaId: 2);

// Sub-area-specific assignment
ProcessOwnerAssignment.Create("user-123", "admin-456", factoryId: 1, areaId: 2, subAreaId: 3);
```

## Template Access Management

### Access Control System

Template access is managed through explicit assignments:

- **User-Template Mapping**: Direct assignment of users to specific templates
- **Expiration Support**: Access can have expiration dates
- **Revocation**: Access can be revoked while maintaining audit trail
- **Extension**: Access expiration can be extended

### Access Validation

```csharp
// Check if user has current access to template
bool hasAccess = templateAccess.IsCurrentlyValid();

// Factors considered:
// - Assignment is active
// - Assignment has not expired
// - User is still active
```

## Permission Matrix

| Action | DevAdmin | SystemManager | ProcessOwner | Auditor | Viewer |
|--------|----------|---------------|--------------|---------|--------|
| Create Templates | ✅ | ✅ | ❌ | ❌ | ❌ |
| Assign Process Owners | ✅ | ✅ | ❌ | ❌ | ❌ |
| Grant Template Access | ✅ | ✅ | ❌ | ❌ | ❌ |
| Assign Audits | ✅ | ✅ | ✅* | ❌ | ❌ |
| Complete Audits | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Audit Results | ✅ | ✅ | ✅* | ✅* | ✅* |
| System Administration | ✅ | ❌ | ❌ | ❌ | ❌ |

*\* Within assigned scope or assigned audits*

## API Endpoints

### Process Owner Assignments

#### Get Process Owner Assignments
```http
GET /api/v1/process-owner-assignments?pageNumber=1&pageSize=20&searchTerm=&factoryId=&isActive=
```
**Authorization**: DevAdmin, SystemManager
**Description**: Retrieve paginated list of process owner assignments with optional filtering

#### Create Process Owner Assignment
```http
POST /api/v1/process-owner-assignments
Content-Type: application/json

{
  "processOwnerUserId": "user-guid",
  "factoryId": 1,
  "areaId": 2,
  "subAreaId": 3,
  "description": "Assignment description"
}
```
**Authorization**: DevAdmin, SystemManager
**Description**: Create a new process owner assignment

#### Get My Assignments
```http
GET /api/v1/process-owner-assignments/my-assignments
```
**Authorization**: ProcessOwner
**Description**: Get assignments for the current process owner

### Template Access Management

#### Grant Template Access
```http
POST /api/v1/template-access/grant
Content-Type: application/json

{
  "userId": "user-guid",
  "auditTemplateId": 123,
  "description": "Access description",
  "expiresAt": "2024-12-31T23:59:59Z"
}
```
**Authorization**: DevAdmin, SystemManager
**Description**: Grant a user access to a specific audit template

#### Revoke Template Access
```http
DELETE /api/v1/template-access/revoke/{userId}/{templateId}
```
**Authorization**: DevAdmin, SystemManager
**Description**: Revoke user access to a specific template

#### Get User Template Access
```http
GET /api/v1/template-access/user/{userId}
```
**Authorization**: DevAdmin, SystemManager
**Description**: Get all templates a user has access to

#### Get Template User Access
```http
GET /api/v1/template-access/template/{templateId}/users
```
**Authorization**: DevAdmin, SystemManager
**Description**: Get all users who have access to a specific template

## Database Schema

### Process Owner Assignments Table

```sql
CREATE TABLE hwsap_usr_process_owner_assignments (
    Id int IDENTITY(1,1) PRIMARY KEY,
    ProcessOwnerUserId nvarchar(36) NOT NULL,
    FactoryId int NULL,
    AreaId int NULL,
    SubAreaId int NULL,
    AssignedByUserId nvarchar(36) NOT NULL,
    IsActive bit NOT NULL DEFAULT 1,
    Description nvarchar(500) NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    -- Foreign keys and indexes...
);
```

### Template Access Assignments Table

```sql
CREATE TABLE hwsap_usr_template_access_assignments (
    Id int IDENTITY(1,1) PRIMARY KEY,
    UserId nvarchar(36) NOT NULL,
    AuditTemplateId int NOT NULL,
    AssignedByUserId nvarchar(36) NOT NULL,
    IsActive bit NOT NULL DEFAULT 1,
    Description nvarchar(500) NULL,
    ExpiresAt datetime2 NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    -- Foreign keys and indexes...
);
```

## Migration Guide

### From Previous System

1. **Role Mapping**:
   - `Admin` → `DevAdmin`
   - `Manager` → `SystemManager` or `ProcessOwner` (based on responsibilities)
   - `Auditor` → `Auditor` (unchanged)
   - `Viewer` → `Viewer` (unchanged)

2. **AD Group Updates**:
   - Update AD group names to match new naming convention
   - Reassign users to appropriate groups

3. **Data Migration**:
   - Run the `UpdateUserRolesToNewHierarchy` migration
   - Review and update user assignments
   - Configure process owner assignments

## Best Practices

1. **Principle of Least Privilege**: Assign users the minimum role required for their responsibilities
2. **Regular Review**: Periodically review process owner assignments and template access
3. **Audit Trail**: Maintain comprehensive logs of all permission changes
4. **Scope Clarity**: Clearly define organizational scopes for process owners
5. **Access Expiration**: Use expiration dates for temporary template access

## Troubleshooting

### Common Issues

1. **Invalid Hierarchy**: Ensure organizational assignments follow proper hierarchy
2. **Missing Permissions**: Verify user roles and active assignments
3. **Expired Access**: Check template access expiration dates
4. **AD Sync Issues**: Ensure AD groups are properly configured and synchronized
