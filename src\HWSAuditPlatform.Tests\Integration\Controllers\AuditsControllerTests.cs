using System.Net;
using System.Net.Http.Json;
using FluentAssertions;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestPlatform.TestHost;

namespace HWSAuditPlatform.Tests.Integration.Controllers;

public class AuditsControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public AuditsControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the real database context
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // Add in-memory database for testing
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb_" + Guid.NewGuid());
                });
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetAudit_WithValidId_ShouldReturnAuditWithQuestionsAndOptions()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        await SeedTestDataAsync(context);
        var audit = await CreateTestAuditWithQuestionsAsync(context);

        // Add authentication header (mock JWT token)
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "mock-jwt-token");

        // Act
        var response = await _client.GetAsync($"/api/v1/audits/{audit.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadFromJsonAsync<ApiResponse<AuditDto>>();
        result.Should().NotBeNull();
        result!.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        
        var auditDto = result.Data!;
        auditDto.Id.Should().Be(audit.Id);
        auditDto.AuditTemplateName.Should().Be("Test Template with Questions");
        
        // Verify questions are included
        auditDto.Questions.Should().NotBeEmpty();
        auditDto.Questions.Should().HaveCount(2);
        
        // Verify question details
        var yesNoQuestion = auditDto.Questions.First(q => q.QuestionType == QuestionType.YesNo);
        yesNoQuestion.QuestionText.Should().Be("Is the equipment in good condition?");
        yesNoQuestion.IsRequired.Should().BeTrue();
        yesNoQuestion.Options.Should().BeEmpty(); // Yes/No questions don't have options
        
        var singleSelectQuestion = auditDto.Questions.First(q => q.QuestionType == QuestionType.SingleSelect);
        singleSelectQuestion.QuestionText.Should().Be("What is the condition level?");
        singleSelectQuestion.Options.Should().NotBeEmpty();
        singleSelectQuestion.Options.Should().HaveCount(3);
        singleSelectQuestion.Options.Should().BeInAscendingOrder(o => o.DisplayOrder);
        
        // Verify option details
        var firstOption = singleSelectQuestion.Options.First();
        firstOption.OptionText.Should().Be("Excellent");
        firstOption.OptionValue.Should().Be("excellent");
    }

    [Fact]
    public async Task GetAudit_WithNonExistentId_ShouldReturnNotFound()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await SeedTestDataAsync(context);

        // Add authentication header
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "mock-jwt-token");

        // Act
        var response = await _client.GetAsync("/api/v1/audits/non-existent-id");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    private async Task SeedTestDataAsync(ApplicationDbContext context)
    {
        await TestDbContextFactory.SeedTestDataAsync(context);
    }

    private async Task<Audit> CreateTestAuditWithQuestionsAsync(ApplicationDbContext context)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template with Questions",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.AuditTemplates.Add(template);
        await context.SaveChangesAsync();

        // Create questions
        var yesNoQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Is the equipment in good condition?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var singleSelectQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "What is the condition level?",
            QuestionType = QuestionType.SingleSelect,
            DisplayOrder = 2,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Questions.AddRange(yesNoQuestion, singleSelectQuestion);
        await context.SaveChangesAsync();

        // Create options for single select question
        var options = new[]
        {
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Excellent",
                OptionValue = "excellent",
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Good",
                OptionValue = "good",
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Poor",
                OptionValue = "poor",
                DisplayOrder = 3,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }
        };

        context.QuestionOptions.AddRange(options);
        await context.SaveChangesAsync();

        // Create audit
        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        context.Audits.Add(audit);
        await context.SaveChangesAsync();
        return audit;
    }

    // Helper class to match the API response structure
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
