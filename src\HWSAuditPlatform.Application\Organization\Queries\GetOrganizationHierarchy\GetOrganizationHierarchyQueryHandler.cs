using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetOrganizationHierarchy;

/// <summary>
/// Handler for GetOrganizationHierarchyQuery
/// </summary>
public class GetOrganizationHierarchyQueryHandler : BaseQueryHandler<GetOrganizationHierarchyQuery, List<LocationDto>>
{
    private readonly IApplicationDbContext _context;

    public GetOrganizationHierarchyQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<LocationDto>> Handle(GetOrganizationHierarchyQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Locations
            .Include(l => l.Factories.Where(f => request.IncludeInactive || f.IsActive))
                .ThenInclude(f => f.Areas.Where(a => request.IncludeInactive || a.IsActive))
                    .ThenInclude(a => a.SubAreas.Where(sa => request.IncludeInactive || sa.IsActive))
            .AsQueryable();

        // Apply location filter if specified
        if (request.LocationId.HasValue)
        {
            query = query.Where(l => l.Id == request.LocationId.Value);
        }

        var locations = await query
            .Select(l => new LocationDto
            {
                Id = l.Id,
                LocationName = l.LocationName,
                LocationCountry = l.LocationCountry,
                LocationCountryCode = l.LocationCountryCode,
                OwnerGroupId = l.OwnerGroupId,
                CreatedAt = l.CreatedAt,
                UpdatedAt = l.UpdatedAt,
                Factories = l.Factories
                    .Where(f => request.IncludeInactive || f.IsActive)
                    .Select(f => new FactorySummaryDto
                    {
                        Id = f.Id,
                        FactoryName = f.FactoryName,
                        LocationName = l.LocationName,
                        LocationCountry = l.LocationCountry,
                        IsActive = f.IsActive,
                        AreaCount = f.Areas.Count(a => request.IncludeInactive || a.IsActive)
                    }).ToList()
            })
            .OrderBy(l => l.LocationCountry)
            .ThenBy(l => l.LocationName)
            .ToListAsync(cancellationToken);

        return locations;
    }
}
