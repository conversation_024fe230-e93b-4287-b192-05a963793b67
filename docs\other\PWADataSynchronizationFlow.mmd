sequenceDiagram
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as B<PERSON>zor PWA (Client)
    participant LocalDB as PWA Local Storage (IndexedDB)
    participant API_Service as Web API
    participant H<PERSON><PERSON>_<PERSON> as HWSAP DB (SQL Server)

    PWA_Client->>LocalDB: 1. Load assigned audits & data (if online initially or previously synced)
    Note over PWA_Client,LocalDB: Auditor goes offline

    PWA_Client->>LocalDB: 2. Auditor performs audit, answers questions, captures evidence
    LocalDB-->>PWA_Client: Stores data locally (CUIDs generated)

    Note over PWA_Client,LocalDB: Auditor comes back online

    PWA_Client->>API_Service: 3. Initiate Sync: Send queued local changes (answers, attachments, new CUIDs, RecordVersions)
    API_Service->>HWSAP_DB: 4. Process Batch: Validate data, check RecordVersions for conflicts
    alt No Conflicts or PWA data is newer/acceptable
        HWSAP_DB-->>API_Service: 5a. Persist changes, update RecordVersions
        API_Service-->>PWA_Client: 6a. Sync Success, return updated server data/versions
        PWA_Client->>LocalDB: 7a. Update local data with server versions, clear sync queue
    else Conflict Detected (e.g., server RecordVersion > PWA RecordVersion)
        API_Service-->>PWA_Client: 5b. Sync Conflict, return conflict details
        PWA_Client->>LocalDB: 6b. Mark conflicted items, await resolution (manual/auto strategy)
    end
    PWA_Client->>API_Service: 8. (Optional) Request fresh data for other audits/entities
    API_Service->>HWSAP_DB: 9. Fetch requested data
    HWSAP_DB-->>API_Service: Return data
    API_Service-->>PWA_Client: Deliver fresh data
    PWA_Client->>LocalDB: 10. Update local storage with fresh data