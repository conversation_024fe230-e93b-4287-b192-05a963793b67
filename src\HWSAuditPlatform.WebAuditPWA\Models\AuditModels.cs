using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Entities.Audits;

namespace HWSAuditPlatform.WebAuditPWA.Models;

/// <summary>
/// Audit summary DTO for list views
/// </summary>
public class AuditSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public AuditOverallStatus OverallStatus { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public string? SubAreaName { get; set; }
    public double? OverallScore { get; set; }
    public bool IsOverdue { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int TotalQuestions { get; set; }
    public int AnsweredQuestions { get; set; }
    public double ProgressPercentage => TotalQuestions > 0 ? (double)AnsweredQuestions / TotalQuestions * 100 : 0;

    // Additional properties for history view
    public string LocationName => !string.IsNullOrEmpty(SubAreaName) ? $"{FactoryName} - {AreaName} - {SubAreaName}" :
                                 !string.IsNullOrEmpty(AreaName) ? $"{FactoryName} - {AreaName}" :
                                 FactoryName ?? "Unknown Location";
    public int? EvidenceCount { get; set; }
    public int? NonConformanceCount { get; set; }

    // Additional properties for detailed audit results
    public int? PassedQuestions { get; set; }
    public int? FailedQuestions { get; set; }
    public int? NotApplicableQuestions { get; set; }
    public string? ManagerComments { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
}

/// <summary>
/// Audit answer submission request
/// </summary>
public class SubmitAuditAnswerRequest
{
    [Required]
    public int QuestionId { get; set; }
    
    public bool? AnswerBoolean { get; set; }
    public string? AnswerText { get; set; }
    public decimal? AnswerNumeric { get; set; }
    public DateTime? AnswerDate { get; set; }
    public int? SelectedOptionId { get; set; }
    public List<int> SelectedOptionIds { get; set; } = new();
    public bool IsNotApplicable { get; set; }
    public string? Comments { get; set; }
    public List<string> AttachmentIds { get; set; } = new();
}

/// <summary>
/// Audit submission request
/// </summary>
public class SubmitAuditRequest
{
    public string? AuditorComments { get; set; }
}

/// <summary>
/// File upload result
/// </summary>
public class FileUploadResult
{
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public string FileId { get; set; } = string.Empty;
}

/// <summary>
/// Audit execution context for offline storage
/// </summary>
public class AuditExecutionContext
{
    public string AuditId { get; set; } = string.Empty;
    public AuditSummaryDto? AuditSummary { get; set; }
    public List<SubmitAuditAnswerRequest> PendingAnswers { get; set; } = new();
    public List<string> PendingAttachments { get; set; } = new();
    public DateTime LastModified { get; set; } = DateTime.UtcNow;
    public bool IsSynced { get; set; } = true;
    public int SyncAttempts { get; set; } = 0;
    public DateTime? LastSyncAttempt { get; set; }
}

/// <summary>
/// Offline attachment data
/// </summary>
public class OfflineAttachment
{
    public string Id { get; set; } = string.Empty;
    public string AuditId { get; set; } = string.Empty;
    public int QuestionId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public long Size { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsUploaded { get; set; } = false;
    public string? UploadedFileId { get; set; }

    /// <summary>
    /// Indicates if this attachment is an image file
    /// </summary>
    public bool IsImage => ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
}

/// <summary>
/// Sync status information
/// </summary>
public class SyncStatus
{
    public bool IsOnline { get; set; }
    public bool IsSyncInProgress { get; set; }
    public int PendingAudits { get; set; }
    public int PendingAnswers { get; set; }
    public int PendingAttachments { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public string? LastSyncError { get; set; }
}

/// <summary>
/// Audit filter options
/// </summary>
public class AuditFilterOptions
{
    public AuditOverallStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public bool? IsOverdue { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// Paginated result wrapper
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// Camera capture options
/// </summary>
public class CameraCaptureOptions
{
    public int MaxWidth { get; set; } = 1920;
    public int MaxHeight { get; set; } = 1080;
    public double Quality { get; set; } = 0.8;
    public string FacingMode { get; set; } = "environment"; // "user" for front camera
    public bool AllowGallerySelection { get; set; } = true;
}

/// <summary>
/// Network status information
/// </summary>
public class NetworkStatus
{
    public bool IsOnline { get; set; }
    public string ConnectionType { get; set; } = "unknown";
    public DateTime LastChecked { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// PWA installation status
/// </summary>
public class PwaInstallStatus
{
    public bool CanInstall { get; set; }
    public bool IsInstalled { get; set; }
    public bool IsStandalone { get; set; }
    public string? InstallPromptOutcome { get; set; }
}

/// <summary>
/// Extension methods for domain entities to work with PWA models
/// </summary>
public static class AuditAnswerExtensions
{
    /// <summary>
    /// Gets the selected option IDs as a list for PWA compatibility
    /// </summary>
    public static List<int> GetSelectedOptionIds(this AuditAnswer answer)
    {
        var ids = new List<int>();

        if (answer.SelectedOptionId.HasValue)
            ids.Add(answer.SelectedOptionId.Value);

        if (answer.SelectedOptions?.Any() == true)
            ids.AddRange(answer.SelectedOptions.Select(so => so.QuestionOptionId));

        return ids.Distinct().ToList();
    }

    /// <summary>
    /// Gets the attachment IDs as a list for PWA compatibility
    /// </summary>
    public static List<string> GetAttachmentIds(this AuditAnswer answer)
    {
        return answer.Attachments?.Select(a => a.Id).ToList() ?? new List<string>();
    }

    /// <summary>
    /// Sets the selected option IDs from a list for PWA compatibility
    /// </summary>
    public static void SetSelectedOptionIds(this AuditAnswer answer, List<int> optionIds)
    {
        if (optionIds?.Any() == true)
        {
            // For single select, use the first option
            answer.SelectedOptionId = optionIds.First();

            // For multi-select, this would need to be handled by the service layer
            // as it requires creating AuditAnswerSelectedOption entities
        }
        else
        {
            answer.SelectedOptionId = null;
        }
    }

    /// <summary>
    /// Gets the failure reasons (findings) as a list of strings for PWA compatibility
    /// </summary>
    public static List<string> GetFailureReasons(this AuditAnswer answer)
    {
        return answer.FailureReasons?.Select(fr => fr.ReasonText).ToList() ?? new List<string>();
    }

    /// <summary>
    /// Sets the failure reasons (findings) from a list of strings for PWA compatibility
    /// This creates temporary failure reason objects that will be properly handled by the service layer
    /// </summary>
    public static void SetFailureReasons(this AuditAnswer answer, List<string> reasonTexts)
    {
        // Clear existing failure reasons
        answer.FailureReasons.Clear();

        if (reasonTexts?.Any() == true)
        {
            var displayOrder = 0;
            foreach (var reasonText in reasonTexts.Where(r => !string.IsNullOrWhiteSpace(r)))
            {
                var failureReason = new AuditAnswerFailureReason
                {
                    Id = Guid.NewGuid().ToString(), // Temporary ID for PWA
                    AuditAnswerId = answer.Id,
                    ReasonText = reasonText.Trim(),
                    DisplayOrder = displayOrder++,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                answer.FailureReasons.Add(failureReason);
            }
        }
    }

    /// <summary>
    /// Checks if the answer has any failure reasons (findings)
    /// </summary>
    public static bool HasFailureReasons(this AuditAnswer answer)
    {
        return answer.FailureReasons?.Any(fr => !string.IsNullOrWhiteSpace(fr.ReasonText)) == true;
    }
}

/// <summary>
/// Correction request DTO for PWA
/// </summary>
public class CorrectionRequestDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditId { get; set; } = string.Empty;
    public string RequestedByUserId { get; set; } = string.Empty;
    public string RequestedByUserName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Please provide a reason for the correction request")]
    [MinLength(10, ErrorMessage = "Please provide a more detailed reason (at least 10 characters)")]
    public string RequestReason { get; set; } = string.Empty;

    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    public CorrectionRequestStatus Status { get; set; } = CorrectionRequestStatus.PendingApproval;
    public string? ReviewedByUserId { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public string? ManagerComments { get; set; }

    // Additional properties for display
    public string? AuditTemplateName { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }

    // Helper properties
    public bool IsPending => Status == CorrectionRequestStatus.PendingApproval;
    public bool IsApproved => Status == CorrectionRequestStatus.Approved;
    public bool IsDenied => Status == CorrectionRequestStatus.Denied;
    public bool ChangesSubmitted => Status == CorrectionRequestStatus.ChangesSubmitted;
    public bool IsReviewed => ReviewedAt.HasValue && !string.IsNullOrEmpty(ReviewedByUserId);

    public string StatusDisplayName => Status switch
    {
        CorrectionRequestStatus.PendingApproval => "Pending Approval",
        CorrectionRequestStatus.Approved => "Approved",
        CorrectionRequestStatus.Denied => "Denied",
        CorrectionRequestStatus.ChangesSubmitted => "Changes Submitted",
        _ => "Unknown"
    };

    public string StatusBadgeClass => Status switch
    {
        CorrectionRequestStatus.PendingApproval => "warning",
        CorrectionRequestStatus.Approved => "success",
        CorrectionRequestStatus.Denied => "danger",
        CorrectionRequestStatus.ChangesSubmitted => "info",
        _ => "secondary"
    };
}

/// <summary>
/// Summary DTO for correction requests in lists
/// </summary>
public class CorrectionRequestSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditId { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public DateTime RequestedAt { get; set; }
    public CorrectionRequestStatus Status { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public bool IsPending => Status == CorrectionRequestStatus.PendingApproval;
}

/// <summary>
/// Paginated result wrapper
/// </summary>
public class PaginatedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
