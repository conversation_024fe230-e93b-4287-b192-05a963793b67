using FluentAssertions;
using FluentValidation.TestHelper;
using HWSAuditPlatform.Application.Audits.Commands.StartAudit;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Tests.Application.Audits.Commands;

public class StartAuditCommandValidatorTests : BaseDbTestClass
{
    private readonly StartAuditCommandValidator _validator;

    public StartAuditCommandValidatorTests()
    {
        _validator = new StartAuditCommandValidator(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Validate_WithValidCommand_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        MockCurrentUserService.Setup(x => x.UserId).Returns(audit.AssignedToUserId);
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithEmptyAuditId_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new StartAuditCommand
        {
            AuditId = string.Empty
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AuditId)
            .WithErrorMessage("Audit ID is required");
    }

    [Fact]
    public async Task Validate_WithNonExistentAudit_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new StartAuditCommand
        {
            AuditId = "non-existent-id"
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AuditId)
            .WithErrorMessage("Audit does not exist");
    }

    [Fact]
    public async Task Validate_WithInProgressAudit_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(AuditOverallStatus.InProgress);
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AuditId)
            .WithErrorMessage("Audit must be in Scheduled status to start");
    }

    [Fact]
    public async Task Validate_WithUnassignedUser_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        MockCurrentUserService.Setup(x => x.UserId).Returns("different-user-id");
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AuditId)
            .WithErrorMessage("Audit is not assigned to current user");
    }

    [Fact]
    public async Task Validate_WithGroupAssignment_ShouldValidateGroupMembership()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-user-id";
        var audit = await CreateTestAuditWithGroupAssignmentAsync(currentUserId);
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithGroupAssignmentButNotMember_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithGroupAssignmentAsync("other-user-id");
        MockCurrentUserService.Setup(x => x.UserId).Returns("current-user-id");
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.AuditId)
            .WithErrorMessage("Audit is not assigned to current user");
    }

    private async Task<Audit> CreateTestAuditAsync(AuditOverallStatus status = AuditOverallStatus.Scheduled)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = status,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }

    private async Task<Audit> CreateTestAuditWithGroupAssignmentAsync(string memberUserId)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var userGroup = new UserGroup
        {
            Id = "test-group-id",
            GroupName = "Test Group",
            IsAdSynced = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.UserGroups.Add(userGroup);
        await Context.SaveChangesAsync();

        var userGroupMember = new UserGroupMember
        {           
            UserGroupId = userGroup.Id,
            UserId = memberUserId,
  
        };

        Context.UserGroupMembers.Add(userGroupMember);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.GroupAny,
            AssignedToUserGroupId = userGroup.Id,
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }
}
