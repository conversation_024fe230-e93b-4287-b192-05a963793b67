@page "/audits/{AuditId}/execute"
@attribute [Authorize]
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@using HWSAuditPlatform.WebAuditPWA.Components.Audit
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditService
@inject IOfflineStorageService OfflineStorage
@inject ICameraService CameraService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ILogger<AuditExecution> Logger

<PageTitle>Execute Audit - HWS Audit Platform PWA</PageTitle>

<div class="audit-execution-container">
    @if (isLoading)
    {
        <div class="loading-overlay">
            <LoadingSpinner Message="Loading audit..." />
        </div>
    }
    else if (audit != null && questions.Any())
    {
        <!-- Audit Header Component -->
        <AuditExecutionHeader Audit="audit"
                            ProgressPercentage="progressPercentage"
                            AnsweredCount="answeredCount"
                            TotalQuestions="questions.Count"
                            OnSaveAndExit="SaveAndExit" />

        <!-- Question Navigation Component -->
        <QuestionNavigation CurrentQuestionIndex="currentQuestionIndex"
                          TotalQuestions="questions.Count"
                          AnsweredQuestions="GetAnsweredQuestions()"
                          OnPrevious="PreviousQuestion"
                          OnNext="NextQuestion"
                          OnGoToQuestion="GoToQuestion" />

        <!-- Question Content Area -->
        <div class="audit-question-content">
            @if (currentQuestion != null)
            {
                <QuestionCard Question="currentQuestion"
                            Answer="GetCurrentAnswer()"
                            QuestionNumber="currentQuestionIndex + 1"
                            AttachmentIds="GetCombinedAttachmentIds()"
                            ShowPreviousAnswer="ShouldShowPreviousAnswer()"
                            OnAnswerChanged="HandleAnswerChanged"
                            OnCommentsChanged="HandleCommentsChanged"
                            OnValidationStateChanged="HandleValidationStateChanged"
                            OnCapturePhoto="CapturePhoto"
                            OnSelectPhoto="SelectPhoto"
                            OnRemoveAttachment="HandleRemoveAttachment" />
            }
        </div>

        <!-- Audit Footer Component -->
        <AuditExecutionFooter IsAuditComplete="IsAuditComplete()"
                            IsSubmitting="isSubmitting"
                            IsLastQuestion="currentQuestionIndex >= questions.Count - 1"
                            RemainingRequiredQuestions="GetRemainingRequiredQuestions()"
                            OnSaveAndExit="SaveAndExit"
                            OnCompleteAudit="CompleteAudit"
                            OnNextQuestion="NextQuestion" />
    }
    else
    {
        <div class="empty-state-container">
            <EmptyState Title="Audit Not Available"
                       Description="The requested audit could not be loaded or does not contain any questions."
                       IconClass="fas fa-exclamation-triangle fa-3x text-warning"
                       ActionText="Back to Audits"
                       ActionIcon="fas fa-arrow-left"
                       OnActionClick="@(() => Navigation.NavigateTo("/audits"))" />
        </div>
    }
</div>

<style>
    /* Audit execution layout with proper scrolling */
    .audit-execution-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        background: var(--industrial-dark);
    }

    .audit-question-content {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        /* Ensure smooth scrolling */
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */
    }

    /* Mobile layout adjustments for non-sticky header/footer */
    @@media (max-width: 767px) {
        .audit-execution-container {
            /* Keep flex layout but allow natural flow */
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .audit-question-content {
            /* Remove overflow constraints but keep flex */
            flex: 1;
            overflow-y: visible;
            /* Let content flow naturally */
            min-height: auto;
        }
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--industrial-dark);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1001;
    }

    .empty-state-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    /* Ensure proper spacing for tablet interfaces */
    @@media (min-width: 768px) and (max-width: 1024px) {
        .audit-question-content {
            padding: 1.5rem;
        }
    }

    /* Mobile optimizations */
    @@media (max-width: 767px) {
        .audit-question-content {
            padding: 0.75rem;
            /* Add extra padding at bottom to account for smaller footer */
            padding-bottom: 1rem;
        }
    }

    /* Extra small devices */
    @@media (max-width: 480px) {
        .audit-question-content {
            padding: 0.5rem;
            padding-bottom: 1rem;
        }
    }

    /* Ensure scrollable content doesn't get cut off by sticky elements */
    .audit-question-content {
        padding-bottom: 2rem; /* Extra space at bottom for footer */
        /* Fix any potential layout issues */
        position: relative;
        width: 100%;
        max-width: 100%;
    }

    /* Remove extra padding on mobile since footer is not sticky */
    @@media (max-width: 767px) {
        .audit-question-content {
            padding-bottom: 1rem;
        }
    }

    /* Ensure the main container takes full height */
    body, html {
        height: 100%;
        overflow-x: hidden;
    }

    /* Fix for potential scroll issues on mobile */
    @@media (max-width: 767px) {
        .audit-execution-container {
            min-height: 100vh;
            min-height: -webkit-fill-available; /* iOS Safari fix */
        }

        .audit-question-content {
            /* Ensure touch scrolling works properly on iOS */
            -webkit-overflow-scrolling: touch;
            overflow-y: auto;
            height: auto;
        }
    }
</style>

@code {
    [Parameter] public string AuditId { get; set; } = string.Empty;

    private Audit? audit;
    private List<Question> questions = new();
    private Dictionary<int, AuditAnswer> answers = new();
    private int currentQuestionIndex = 0;
    private bool isLoading = true;
    private bool isSubmitting = false;
    private bool hasUnsavedChanges = false;
    private List<string>? offlineAttachmentIds;
    private bool currentAnswerIsValid = true;

    private Question? currentQuestion => currentQuestionIndex < questions.Count ? questions[currentQuestionIndex] : null;
    private int answeredCount => answers.Values.Count(HasAnswer);
    private double progressPercentage => questions.Count > 0 ? (double)answeredCount / questions.Count * 100 : 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadAudit();
    }

    // ===== COMPONENT EVENT HANDLERS =====

    private Task HandleAnswerChanged(AuditAnswer answer)
    {
        if (currentQuestion != null)
        {
            answers[currentQuestion.Id] = answer;
            hasUnsavedChanges = true;
            StateHasChanged();
        }

        return Task.CompletedTask;
    }

    private Task HandleCommentsChanged(string comments)
    {
        var answer = GetOrCreateAnswer();
        answer.Comments = string.IsNullOrWhiteSpace(comments) ? null : comments;
        hasUnsavedChanges = true;
        StateHasChanged();

        return Task.CompletedTask;
    }

    private Task HandleValidationStateChanged(bool isValid)
    {
        currentAnswerIsValid = isValid;
        StateHasChanged();

        return Task.CompletedTask;
    }

    // ===== HELPER METHODS =====

    private HashSet<int> GetAnsweredQuestions()
    {
        var answeredQuestions = new HashSet<int>();
        for (int i = 0; i < questions.Count; i++)
        {
            if (answers.TryGetValue(questions[i].Id, out var answer) && HasAnswer(answer))
            {
                answeredQuestions.Add(i);
            }
        }
        return answeredQuestions;
    }

    private AuditAnswer? GetCurrentAnswer()
    {
        if (currentQuestion == null) return null;
        return answers.TryGetValue(currentQuestion.Id, out var answer) ? answer : null;
    }

    private AuditAnswer GetOrCreateAnswer()
    {
        if (currentQuestion == null) throw new InvalidOperationException("No current question");

        if (!answers.TryGetValue(currentQuestion.Id, out var answer))
        {
            answer = new AuditAnswer
            {
                Id = Guid.NewGuid().ToString(),
                AuditId = AuditId,
                QuestionId = currentQuestion.Id,
                Question = currentQuestion,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            answers[currentQuestion.Id] = answer;
        }

        answer.UpdatedAt = DateTime.UtcNow;
        hasUnsavedChanges = true;
        return answer;
    }

    private List<string> GetCombinedAttachmentIds()
    {
        var attachmentIds = new List<string>();

        // Add existing answer attachments
        var currentAnswer = GetCurrentAnswer();
        if (currentAnswer != null)
        {
            var existingAttachments = currentAnswer.GetAttachmentIds();
            if (existingAttachments?.Any() == true)
            {
                attachmentIds.AddRange(existingAttachments);
            }
        }

        // Add offline attachments
        if (offlineAttachmentIds?.Any() == true)
        {
            attachmentIds.AddRange(offlineAttachmentIds);
        }

        return attachmentIds;
    }

    // ===== NAVIGATION METHODS =====

    private async Task PreviousQuestion()
    {
        if (currentQuestionIndex > 0)
        {
            await SaveCurrentAnswer();
            currentQuestionIndex--;
            StateHasChanged();
        }
    }

    private async Task NextQuestion()
    {
        if (currentQuestionIndex < questions.Count - 1)
        {
            // Validate current answer before proceeding
            if (!await ValidateCurrentAnswer())
            {
                return; // Don't proceed if validation fails
            }

            await SaveCurrentAnswer();
            currentQuestionIndex++;
            StateHasChanged();
        }
    }

    private async Task GoToQuestion(int questionIndex)
    {
        if (questionIndex >= 0 && questionIndex < questions.Count && questionIndex != currentQuestionIndex)
        {
            await SaveCurrentAnswer();
            currentQuestionIndex = questionIndex;
            StateHasChanged();
        }
    }

    // ===== AUDIT MANAGEMENT METHODS =====

    private async Task LoadAudit()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await AuditService.GetAuditAsync(AuditId);
            if (result.IsSuccess && result.Data != null)
            {
                audit = result.Data;

                // Load questions from the audit template
                if (audit.AuditTemplate?.Questions != null)
                {
                    questions = audit.AuditTemplate.Questions.OrderBy(q => q.DisplayOrder).ToList();
                }

                // Load existing answers
                if (audit.Answers != null)
                {
                    foreach (var answer in audit.Answers)
                    {
                        answers[answer.QuestionId] = answer;
                    }
                }

                // Start the audit if it's not already started
                if (audit.OverallStatus == AuditOverallStatus.Scheduled)
                {
                    await AuditService.StartAuditAsync(AuditId);
                }
            }
            else
            {
                Logger.LogWarning("Failed to load audit: {AuditId}, Error: {Error}", AuditId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit: {AuditId}", AuditId);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveCurrentAnswer()
    {
        if (hasUnsavedChanges && currentQuestion != null && answers.ContainsKey(currentQuestion.Id))
        {
            var answer = answers[currentQuestion.Id];

            try
            {
                // Try to submit to API first
                var result = await AuditService.SubmitAuditAnswersAsync(AuditId, new List<AuditAnswer> { answer });

                if (result.IsSuccess)
                {
                    Logger.LogInformation("Successfully submitted answer for question {QuestionId}", currentQuestion.Id);
                    hasUnsavedChanges = false;
                }
                else
                {
                    Logger.LogWarning("Failed to submit answer to API for question {QuestionId}: {Error}. Storing offline.",
                        currentQuestion.Id, result.ErrorMessage);

                    // Fallback to offline storage
                    await OfflineStorage.StoreAuditAnswersAsync(AuditId, new List<AuditAnswer> { answer });
                    hasUnsavedChanges = false; // Mark as saved offline
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving answer for question {QuestionId}. Storing offline.", currentQuestion.Id);

                try
                {
                    // Fallback to offline storage
                    await OfflineStorage.StoreAuditAnswersAsync(AuditId, new List<AuditAnswer> { answer });
                    hasUnsavedChanges = false; // Mark as saved offline
                }
                catch (Exception offlineEx)
                {
                    Logger.LogError(offlineEx, "Failed to store answer offline for question {QuestionId}", currentQuestion.Id);
                }
            }
        }
    }

    private async Task SaveAndExit()
    {
        await SaveCurrentAnswer();
        Navigation.NavigateTo("/audits");
    }

    private async Task CompleteAudit()
    {
        try
        {
            isSubmitting = true;
            StateHasChanged();

            // First, save the current answer
            await SaveCurrentAnswer();

            // Then, ensure all answers are submitted to the API
            await SubmitAllAnswers();

            // Finally, complete the audit
            var result = await AuditService.CompleteAuditAsync(AuditId);
            if (result.IsSuccess)
            {
                Logger.LogInformation("Successfully completed audit: {AuditId}", AuditId);
                Navigation.NavigateTo($"/audits/{AuditId}/summary");
            }
            else
            {
                Logger.LogWarning("Failed to complete audit: {AuditId}, Error: {Error}", AuditId, result.ErrorMessage);

                // Show user-friendly error message
                await JSRuntime.InvokeVoidAsync("alert",
                    $"Failed to complete audit: {result.ErrorMessage}. Please ensure all required questions are answered.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error completing audit: {AuditId}", AuditId);
            await JSRuntime.InvokeVoidAsync("alert",
                "An error occurred while completing the audit. Please try again.");
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private async Task SubmitAllAnswers()
    {
        try
        {
            // Get all answers that have been provided
            var answersToSubmit = answers.Values.Where(HasAnswer).ToList();

            if (answersToSubmit.Any())
            {
                Logger.LogInformation("Submitting {Count} answers before completing audit", answersToSubmit.Count);

                var result = await AuditService.SubmitAuditAnswersAsync(AuditId, answersToSubmit);

                if (result.IsSuccess)
                {
                    Logger.LogInformation("Successfully submitted all answers for audit: {AuditId}", AuditId);
                }
                else
                {
                    Logger.LogWarning("Failed to submit some answers for audit: {AuditId}, Error: {Error}",
                        AuditId, result.ErrorMessage);
                    throw new InvalidOperationException($"Failed to submit answers: {result.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error submitting all answers for audit: {AuditId}", AuditId);
            throw;
        }
    }

    // ===== ATTACHMENT METHODS =====

    private async Task CapturePhoto()
    {
        try
        {
            var photoResult = await CameraService.CapturePhotoAsync();
            if (photoResult.IsSuccess && photoResult.Data != null)
            {
                // Create offline attachment
                var attachment = new OfflineAttachment
                {
                    Id = Guid.NewGuid().ToString(),
                    AuditId = AuditId,
                    QuestionId = currentQuestion?.Id ?? 0,
                    FileName = $"photo_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                    ContentType = "image/jpeg",
                    Data = photoResult.Data,
                    Size = photoResult.Data.Length,
                    CreatedAt = DateTime.UtcNow
                };

                // Store offline
                await OfflineStorage.StoreAttachmentAsync(attachment);

                // Add to current list
                if (offlineAttachmentIds == null)
                    offlineAttachmentIds = new List<string>();

                offlineAttachmentIds.Add(attachment.Id);
                hasUnsavedChanges = true;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error capturing photo");
        }
    }

    private async Task SelectPhoto()
    {
        try
        {
            var photoResult = await CameraService.SelectPhotoAsync();
            if (photoResult.IsSuccess && photoResult.Data != null)
            {
                // Create offline attachment
                var attachment = new OfflineAttachment
                {
                    Id = Guid.NewGuid().ToString(),
                    AuditId = AuditId,
                    QuestionId = currentQuestion?.Id ?? 0,
                    FileName = $"selected_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                    ContentType = "image/jpeg",
                    Data = photoResult.Data,
                    Size = photoResult.Data.Length,
                    CreatedAt = DateTime.UtcNow
                };

                // Store offline
                await OfflineStorage.StoreAttachmentAsync(attachment);

                // Add to current list
                if (offlineAttachmentIds == null)
                    offlineAttachmentIds = new List<string>();

                offlineAttachmentIds.Add(attachment.Id);
                hasUnsavedChanges = true;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error selecting photo");
        }
    }

    private async Task HandleRemoveAttachment(string attachmentId)
    {
        try
        {
            // Remove from offline attachments
            if (offlineAttachmentIds?.Contains(attachmentId) == true)
            {
                offlineAttachmentIds.Remove(attachmentId);
                await OfflineStorage.RemoveAttachmentAsync(attachmentId);
            }

            // Note: For uploaded attachments, we would need to call an API to remove them
            // For now, we'll just remove from the local list

            hasUnsavedChanges = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error removing attachment {AttachmentId}", attachmentId);
        }
    }

    // ===== VALIDATION AND STATUS METHODS =====

    private bool IsAuditComplete()
    {
        return GetRemainingRequiredQuestions() == 0;
    }

    private int GetRemainingRequiredQuestions()
    {
        return questions.Count(q => q.IsRequired && (!answers.TryGetValue(q.Id, out var answer) || !HasAnswer(answer)));
    }

    private bool HasAnswer(AuditAnswer answer)
    {
        // For Yes/No questions with "No" answer, also check for findings
        if (answer.AnswerBoolean == false && currentQuestion?.QuestionType == QuestionType.YesNo)
        {
            return answer.HasFailureReasons();
        }

        return answer.AnswerBoolean.HasValue ||
               !string.IsNullOrWhiteSpace(answer.AnswerText) ||
               answer.AnswerNumeric.HasValue ||
               answer.AnswerDate.HasValue ||
               answer.SelectedOptionId.HasValue ||
               (answer.SelectedOptions?.Any() == true);
    }

    private bool ShouldShowPreviousAnswer()
    {
        // Show previous answer indicator if we have an existing answer
        var currentAnswer = GetCurrentAnswer();
        return currentAnswer != null && HasAnswer(currentAnswer);
    }

    private async Task<bool> ValidateCurrentAnswer()
    {
        var currentAnswer = GetCurrentAnswer();

        // If no answer provided, allow navigation (not all questions are required)
        if (currentAnswer == null)
        {
            return true;
        }

        // For Yes/No questions with "No" answer, validate findings
        if (currentQuestion?.QuestionType == QuestionType.YesNo && currentAnswer.AnswerBoolean == false)
        {
            var hasValidFindings = currentAnswer.HasFailureReasons();

            if (!hasValidFindings)
            {
                // Show validation error and prevent navigation
                await JSRuntime.InvokeVoidAsync("alert",
                    "Please provide at least one finding to explain why this answer is 'No' before proceeding.");
                return false;
            }
        }

        return true;
    }
}
