using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Service for audit-related API operations
/// </summary>
public interface IAuditApiService
{
    /// <summary>
    /// Gets audits assigned to the current user
    /// </summary>
    Task<Result<List<Audit>>> GetAssignedAuditsAsync();

    /// <summary>
    /// Gets a specific audit by ID
    /// </summary>
    Task<Result<Audit?>> GetAuditAsync(string auditId);

    /// <summary>
    /// Gets detailed audit data for results display including answers, attachments, and statistics
    /// </summary>
    Task<Result<Models.AuditSummaryDto?>> GetAuditForResultsAsync(string auditId);

    /// <summary>
    /// Starts an audit
    /// </summary>
    Task<Result> StartAuditAsync(string auditId);

    /// <summary>
    /// Submits audit answers
    /// </summary>
    Task<Result> SubmitAuditAnswersAsync(string auditId, List<AuditAnswer> answers);

    /// <summary>
    /// Completes an audit
    /// </summary>
    Task<Result> CompleteAuditAsync(string auditId);

    /// <summary>
    /// Uploads audit attachment
    /// </summary>
    Task<Result<string>> UploadAttachmentAsync(string auditId, string questionId, byte[] fileData, string fileName, string contentType);

    /// <summary>
    /// Gets audit history for the current user (completed audits)
    /// </summary>
    Task<Result<PaginatedResult<Models.AuditSummaryDto>>> GetMyAuditHistoryAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null, string? status = null, DateTime? completedDateFrom = null, DateTime? completedDateTo = null);
}

/// <summary>
/// Service for template-related API operations
/// </summary>
public interface ITemplateApiService
{
    /// <summary>
    /// Gets audit templates available to the user
    /// </summary>
    Task<Result<List<AuditTemplate>>> GetAvailableTemplatesAsync();

    /// <summary>
    /// Gets a specific template by ID
    /// </summary>
    Task<Result<AuditTemplate?>> GetTemplateAsync(int templateId);
}

/// <summary>
/// Service for user-related API operations
/// </summary>
public interface IUserApiService
{
    /// <summary>
    /// Gets current user profile
    /// </summary>
    Task<Result<User?>> GetCurrentUserProfileAsync();

    /// <summary>
    /// Updates user profile
    /// </summary>
    Task<Result> UpdateUserProfileAsync(User user);
}

/// <summary>
/// Service for offline storage operations
/// </summary>
public interface IOfflineStorageService
{
    /// <summary>
    /// Stores audit data offline
    /// </summary>
    Task StoreAuditAsync(Audit audit);

    /// <summary>
    /// Gets offline audits
    /// </summary>
    Task<List<Audit>> GetOfflineAuditsAsync();

    /// <summary>
    /// Removes audit from offline storage
    /// </summary>
    Task RemoveOfflineAuditAsync(string auditId);

    /// <summary>
    /// Stores audit answers offline
    /// </summary>
    Task StoreAuditAnswersAsync(string auditId, List<AuditAnswer> answers);

    /// <summary>
    /// Gets offline audit answers
    /// </summary>
    Task<List<AuditAnswer>> GetOfflineAuditAnswersAsync(string auditId);

    /// <summary>
    /// Stores attachment offline
    /// </summary>
    Task StoreAttachmentAsync(OfflineAttachment attachment);

    /// <summary>
    /// Gets offline attachments for an audit
    /// </summary>
    Task<List<OfflineAttachment>> GetOfflineAttachmentsAsync(string auditId);

    /// <summary>
    /// Gets all pending offline attachments
    /// </summary>
    Task<List<OfflineAttachment>> GetPendingAttachmentsAsync();

    /// <summary>
    /// Updates an offline attachment
    /// </summary>
    Task UpdateAttachmentAsync(OfflineAttachment attachment);

    /// <summary>
    /// Removes an offline attachment
    /// </summary>
    Task RemoveAttachmentAsync(string attachmentId);
}

/// <summary>
/// Service for synchronizing offline data
/// </summary>
public interface ISyncService
{
    /// <summary>
    /// Synchronizes offline data with the server
    /// </summary>
    Task<Result> SyncAsync();

    /// <summary>
    /// Gets sync status
    /// </summary>
    Task<bool> IsSyncInProgressAsync();

    /// <summary>
    /// Gets pending sync items count
    /// </summary>
    Task<int> GetPendingSyncItemsCountAsync();

    /// <summary>
    /// Gets detailed sync status information
    /// </summary>
    Task<SyncStatus> GetSyncStatusAsync();

    /// <summary>
    /// Queues data for background sync
    /// </summary>
    Task<Result> QueueForBackgroundSyncAsync<T>(T data, string syncType);

    /// <summary>
    /// Requests manual sync via service worker
    /// </summary>
    Task<Result> RequestManualSyncAsync(string syncType);

    /// <summary>
    /// Gets pending sync items
    /// </summary>
    Task<List<BackgroundSyncItem>> GetPendingSyncItemsAsync(string? syncType = null);
}

/// <summary>
/// Service for PWA-specific operations
/// </summary>
public interface IPwaService
{
    /// <summary>
    /// Checks if the app is running as PWA
    /// </summary>
    Task<bool> IsPwaAsync();

    /// <summary>
    /// Checks if the app can be installed
    /// </summary>
    Task<bool> CanInstallAsync();

    /// <summary>
    /// Prompts user to install the app
    /// </summary>
    Task PromptInstallAsync();

    /// <summary>
    /// Checks network connectivity
    /// </summary>
    Task<bool> IsOnlineAsync();

    /// <summary>
    /// Registers for network status changes
    /// </summary>
    Task RegisterNetworkStatusChangedAsync(Func<bool, Task> callback);
}

/// <summary>
/// Service for background sync operations
/// </summary>
public interface IBackgroundSyncService
{
    /// <summary>
    /// Checks if background sync is supported
    /// </summary>
    Task<bool> IsBackgroundSyncSupportedAsync();

    /// <summary>
    /// Queues data for background sync
    /// </summary>
    Task<bool> QueueForBackgroundSyncAsync<T>(T data, string syncType);

    /// <summary>
    /// Gets pending sync items
    /// </summary>
    Task<List<BackgroundSyncItem>> GetPendingSyncItemsAsync(string? syncType = null);

    /// <summary>
    /// Removes a sync item after successful sync
    /// </summary>
    Task RemoveSyncItemAsync(int itemId);

    /// <summary>
    /// Registers a background sync tag
    /// </summary>
    Task<bool> RegisterBackgroundSyncAsync(string tag);

    /// <summary>
    /// Requests manual sync via service worker message
    /// </summary>
    Task RequestManualSyncAsync(string syncType);
}

/// <summary>
/// Background sync item
/// </summary>
public class BackgroundSyncItem
{
    public int Id { get; set; }
    public object Data { get; set; } = new();
    public string SyncType { get; set; } = string.Empty;
    public long Timestamp { get; set; }
    public int Attempts { get; set; }
    public int MaxAttempts { get; set; } = 3;
    public long? LastAttempt { get; set; }
}

/// <summary>
/// Service for cache management operations
/// </summary>
public interface ICacheManagementService
{
    /// <summary>
    /// Clears old caches
    /// </summary>
    Task<bool> ClearOldCachesAsync();

    /// <summary>
    /// Gets cache storage usage information
    /// </summary>
    Task<CacheStorageInfo> GetCacheStorageUsageAsync();

    /// <summary>
    /// Preloads critical resources
    /// </summary>
    Task<bool> PreloadCriticalResourcesAsync(string[] resources);

    /// <summary>
    /// Checks if a resource is cached
    /// </summary>
    Task<bool> IsResourceCachedAsync(string url);

    /// <summary>
    /// Updates cache with fresh data
    /// </summary>
    Task<bool> UpdateCacheAsync(string url, object data);
}

/// <summary>
/// Cache storage information
/// </summary>
public class CacheStorageInfo
{
    public bool Supported { get; set; }
    public long Usage { get; set; }
    public long Quota { get; set; }
    public Dictionary<string, long> UsageDetails { get; set; } = new();
}

/// <summary>
/// Service for camera operations
/// </summary>
public interface ICameraService
{
    /// <summary>
    /// Checks if camera is available
    /// </summary>
    Task<bool> IsCameraAvailableAsync();

    /// <summary>
    /// Captures a photo
    /// </summary>
    Task<Result<byte[]>> CapturePhotoAsync();

    /// <summary>
    /// Selects a photo from gallery
    /// </summary>
    Task<Result<byte[]>> SelectPhotoAsync();

    /// <summary>
    /// Resizes an image
    /// </summary>
    Task<byte[]> ResizeImageAsync(byte[] imageData, int maxWidth, int maxHeight, int quality = 80);
}

/// <summary>
/// Service for audit correction request operations
/// </summary>
public interface ICorrectionRequestService
{
    /// <summary>
    /// Submits a correction request for an audit
    /// </summary>
    Task<Result<string>> SubmitCorrectionRequestAsync(CorrectionRequestDto request);

    /// <summary>
    /// Gets correction requests for a specific audit
    /// </summary>
    Task<Result<List<CorrectionRequestDto>>> GetCorrectionRequestsAsync(string auditId);

    /// <summary>
    /// Gets all correction requests for the current user
    /// </summary>
    Task<Result<List<CorrectionRequestDto>>> GetMyCorrectionRequestsAsync();

    /// <summary>
    /// Gets pending correction requests for manager review
    /// </summary>
    Task<Result<List<CorrectionRequestDto>>> GetPendingCorrectionRequestsAsync();

    /// <summary>
    /// Approves a correction request
    /// </summary>
    Task<Result> ApproveCorrectionRequestAsync(string requestId, string managerComments = "");

    /// <summary>
    /// Denies a correction request
    /// </summary>
    Task<Result> DenyCorrectionRequestAsync(string requestId, string managerComments);

    /// <summary>
    /// Checks if an audit can have a correction request
    /// </summary>
    Task<Result<bool>> CanRequestCorrectionAsync(string auditId);
}

/// <summary>
/// Service for finding category operations in PWA
/// </summary>
public interface IFindingCategoryService
{
    /// <summary>
    /// Gets finding categories for a specific audit template
    /// </summary>
    Task<Result<List<FindingCategorySummaryModel>>> GetCategoriesForTemplateAsync(int auditTemplateId);

    /// <summary>
    /// Assigns a category to a finding
    /// </summary>
    Task<Result> AssignCategoryToFindingAsync(string findingId, int? categoryId);

    /// <summary>
    /// Gets category by ID
    /// </summary>
    Task<Result<FindingCategoryModel?>> GetCategoryAsync(int categoryId);
}
