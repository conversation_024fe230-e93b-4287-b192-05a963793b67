<div class="question-navigation">
    <div class="container-fluid py-3">
        <div class="navigation-content">
            <!-- Previous Button -->
            <button class="btn btn-nav-control btn-previous"
                    @onclick="HandlePrevious"
                    disabled="@(CurrentQuestionIndex <= 0)">
                <i class="fas fa-chevron-left"></i>
                <span class="btn-text d-none d-sm-inline">Previous</span>
            </button>
            
            <!-- Question Indicators (Desktop) -->
            <div class="question-indicators d-none d-lg-flex">
                @for (int i = 0; i < Math.Min(TotalQuestions, MaxVisibleIndicators); i++)
                {
                    var questionIndex = i;
                    var isAnswered = AnsweredQuestions?.Contains(i) == true;
                    var isCurrent = i == CurrentQuestionIndex;
                    
                    <button class="btn btn-indicator @GetIndicatorClass(isCurrent, isAnswered)"
                            @onclick="() => HandleGoToQuestion(questionIndex)">
                        @(i + 1)
                    </button>
                }
                @if (TotalQuestions > MaxVisibleIndicators)
                {
                    <span class="indicator-more">+@(TotalQuestions - MaxVisibleIndicators)</span>
                }
            </div>
            
            <!-- Question Counter and Navigation Info -->
            <div class="navigation-info">
                <div class="question-counter">
                    <span class="current-question">@(CurrentQuestionIndex + 1)</span>
                    <span class="question-separator">/</span>
                    <span class="total-questions">@TotalQuestions</span>
                </div>
                <div class="swipe-hint d-none d-md-block">
                    <small class="hint-text">
                        <i class="fas fa-hand-paper me-1"></i>
                        Swipe to navigate
                    </small>
                </div>
            </div>
            
            <!-- Next Button -->
            <button class="btn btn-nav-control btn-next"
                    @onclick="HandleNext"
                    disabled="@(CurrentQuestionIndex >= TotalQuestions - 1)">
                <span class="btn-text d-none d-sm-inline">Next</span>
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

@code {
    [Parameter] public int CurrentQuestionIndex { get; set; }
    [Parameter] public int TotalQuestions { get; set; }
    [Parameter] public HashSet<int>? AnsweredQuestions { get; set; }
    [Parameter] public EventCallback OnPrevious { get; set; }
    [Parameter] public EventCallback OnNext { get; set; }
    [Parameter] public EventCallback<int> OnGoToQuestion { get; set; }
    [Parameter] public int MaxVisibleIndicators { get; set; } = 8;

    private async Task HandlePrevious()
    {
        if (OnPrevious.HasDelegate)
        {
            await OnPrevious.InvokeAsync();
        }
    }

    private async Task HandleNext()
    {
        if (OnNext.HasDelegate)
        {
            await OnNext.InvokeAsync();
        }
    }

    private async Task HandleGoToQuestion(int questionIndex)
    {
        if (OnGoToQuestion.HasDelegate)
        {
            await OnGoToQuestion.InvokeAsync(questionIndex);
        }
    }

    private string GetIndicatorClass(bool isCurrent, bool isAnswered)
    {
        var classes = new List<string>();
        
        if (isCurrent)
            classes.Add("current");
        
        if (isAnswered)
            classes.Add("answered");
        
        return string.Join(" ", classes);
    }
}

<style>
    .question-navigation {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 1px solid var(--industrial-border);
        box-shadow: 0 2px 8px var(--industrial-shadow-light);
        position: sticky;
        top: 0;
        z-index: 90;
        /* Ensure navigation doesn't interfere with scrolling */
        flex-shrink: 0;
    }

    .navigation-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    /* Navigation Buttons */
    .btn-nav-control {
        background: transparent;
        border: 2px solid var(--industrial-border-light);
        color: var(--industrial-text);
        font-weight: 600;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        min-width: 80px;
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-nav-control:hover:not(:disabled) {
        background: var(--industrial-teal);
        border-color: var(--industrial-teal);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--industrial-glow);
    }

    .btn-nav-control:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        transform: none;
    }

    /* Question Indicators */
    .question-indicators {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: center;
        flex: 1;
        max-width: 400px;
    }

    .btn-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid var(--industrial-border);
        background: transparent;
        color: var(--industrial-text-muted);
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .btn-indicator:hover {
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
        transform: scale(1.1);
    }

    .btn-indicator.current {
        background: var(--industrial-teal);
        border-color: var(--industrial-teal);
        color: white;
        box-shadow: 0 0 12px var(--industrial-glow);
    }

    .btn-indicator.answered {
        background: var(--industrial-green);
        border-color: var(--industrial-green);
        color: white;
    }

    .btn-indicator.current.answered {
        background: var(--industrial-teal);
        border-color: var(--industrial-teal);
    }

    .indicator-more {
        color: var(--industrial-text-muted);
        font-weight: 600;
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }

    /* Navigation Info */
    .navigation-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .question-counter {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .current-question {
        color: var(--industrial-teal);
        font-size: 1.25rem;
    }

    .question-separator {
        color: var(--industrial-text-muted);
    }

    .total-questions {
        color: var(--industrial-text);
    }

    .swipe-hint {
        text-align: center;
    }

    .hint-text {
        color: var(--industrial-text-muted);
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
    }

    /* Responsive Design */
    @@media (max-width: 991px) {
        .navigation-content {
            gap: 0.75rem;
        }

        .btn-nav-control {
            min-width: 70px;
            padding: 0.5rem 0.75rem;
        }
    }

    @@media (max-width: 767px) {
        .question-navigation .container-fluid {
            padding: 0.75rem 1rem;
        }

        .navigation-content {
            gap: 0.5rem;
        }

        .btn-nav-control {
            min-width: 80px;
            padding: 0.75rem 0.5rem;
            min-height: 44px;
        }

        .btn-text {
            display: inline !important;
            font-size: 0.85rem;
        }

        .question-counter {
            font-size: 1rem;
        }

        .current-question {
            font-size: 1.1rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .btn-nav-control {
            min-height: 56px;
            padding: 1rem 1.25rem;
        }

        .btn-indicator {
            width: 48px;
            height: 48px;
            font-size: 1rem;
        }
    }
</style>
