using System.Text.Json;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Application.Organization.DTOs;
using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of organizational data API service
/// </summary>
public class OrganizationApiService : IOrganizationApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<OrganizationApiService> _logger;

    public OrganizationApiService(
        AuthenticatedHttpClientService httpClient,
        ILogger<OrganizationApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<IEnumerable<FactorySummary>> GetFactoriesAsync()
    {
        try
        {
            _logger.LogInformation("Fetching factories from API");
            var response = await _httpClient.GetAsync("api/v1/organization/factories?isActive=true");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<FactorySummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var factories = apiResponse.Data.Select(dto => new FactorySummary
                    {
                        Id = dto.Id,
                        FactoryName = dto.FactoryName,
                        LocationName = dto.LocationName,
                        IsActive = dto.IsActive
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} factories", factories.Count);
                    return factories;
                }
            }

            _logger.LogWarning("Failed to fetch factories. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<FactorySummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching factories");
            return Enumerable.Empty<FactorySummary>();
        }
    }

    public async Task<IEnumerable<AreaSummary>> GetAreasByFactoryAsync(int factoryId)
    {
        try
        {
            _logger.LogInformation("Fetching areas for factory {FactoryId}", factoryId);
            var response = await _httpClient.GetAsync($"api/v1/organization/factories/{factoryId}/areas");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<AreaSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var areas = apiResponse.Data.Select(dto => new AreaSummary
                    {
                        Id = dto.Id,
                        AreaName = dto.AreaName,
                        FactoryId = dto.FactoryId,
                        FactoryName = dto.FactoryName,
                        IsActive = dto.IsActive
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} areas for factory {FactoryId}", areas.Count, factoryId);
                    return areas;
                }
            }

            _logger.LogWarning("Failed to fetch areas for factory {FactoryId}. Status: {StatusCode}", factoryId, response.StatusCode);
            return Enumerable.Empty<AreaSummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching areas for factory {FactoryId}", factoryId);
            return Enumerable.Empty<AreaSummary>();
        }
    }

    public async Task<IEnumerable<SubAreaSummary>> GetSubAreasByAreaAsync(int areaId)
    {
        try
        {
            _logger.LogInformation("Fetching sub-areas for area {AreaId}", areaId);
            var response = await _httpClient.GetAsync($"api/v1/organization/areas/{areaId}/subareas");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<SubAreaSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var subAreas = apiResponse.Data.Select(dto => new SubAreaSummary
                    {
                        Id = dto.Id,
                        SubAreaName = dto.SubAreaName,
                        AreaId = dto.AreaId,
                        AreaName = dto.AreaName,
                        IsActive = dto.IsActive
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} sub-areas for area {AreaId}", subAreas.Count, areaId);
                    return subAreas;
                }
            }

            _logger.LogWarning("Failed to fetch sub-areas for area {AreaId}. Status: {StatusCode}", areaId, response.StatusCode);
            return Enumerable.Empty<SubAreaSummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching sub-areas for area {AreaId}", areaId);
            return Enumerable.Empty<SubAreaSummary>();
        }
    }

    public async Task<IEnumerable<UserSummary>> GetUsersAsync()
    {
        try
        {
            _logger.LogInformation("Fetching users from API");
            var response = await _httpClient.GetAsync("api/v1/users?pageSize=100"); // Get a large number for dropdown

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Common.PaginatedResult<UserSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    var users = apiResponse.Data.Items.Select(dto => new UserSummary
                    {
                        Id = dto.Id,
                        Username = dto.Username,
                        FullName = dto.FullName,
                        Email = dto.Email,
                        Role = dto.Role.ToString(),
                        IsActive = dto.IsActive,
                        FactoryName = dto.FactoryName,
                        AdObjectGuid = dto.AdObjectGuid
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} users", users.Count);
                    return users;
                }
            }

            _logger.LogWarning("Failed to fetch users. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<UserSummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching users");
            return Enumerable.Empty<UserSummary>();
        }
    }

    public async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int pageSize = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return Enumerable.Empty<UserSummary>();
            }

            _logger.LogInformation("Searching users with term: {SearchTerm}", searchTerm);
            var response = await _httpClient.GetAsync($"api/v1/users/search?searchTerm={Uri.EscapeDataString(searchTerm)}&pageSize={Math.Min(pageSize, 50)}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Common.PaginatedResult<UserSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    var users = apiResponse.Data.Items.Select(dto => new UserSummary
                    {
                        Id = dto.Id,
                        Username = dto.Username,
                        FullName = dto.FullName,
                        Email = dto.Email,
                        Role = dto.Role.ToString(),
                        IsActive = dto.IsActive,
                        FactoryName = dto.FactoryName,
                        AdObjectGuid = dto.AdObjectGuid
                    }).ToList();

                    _logger.LogInformation("Successfully found {Count} users for search term: {SearchTerm}", users.Count, searchTerm);
                    return users;
                }
            }

            _logger.LogWarning("Failed to search users. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<UserSummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching users with term: {SearchTerm}", searchTerm);
            return Enumerable.Empty<UserSummary>();
        }
    }

    public async Task<IEnumerable<UserGroupSummary>> GetUserGroupsAsync()
    {
        try
        {
            _logger.LogInformation("Fetching user groups from API");
            var response = await _httpClient.GetAsync("api/v1/usergroups?pageSize=100"); // Get a large number for dropdown

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Common.PaginatedResult<UserGroupSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    var userGroups = apiResponse.Data.Items.Select(dto => new UserGroupSummary
                    {
                        Id = dto.Id,
                        GroupName = dto.GroupName,
                        Description = dto.Description,
                        IsActive = true, // UserGroupSummaryDto doesn't have IsActive, assume active
                        MemberCount = dto.MemberCount
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} user groups", userGroups.Count);
                    return userGroups;
                }
            }

            _logger.LogWarning("Failed to fetch user groups. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<UserGroupSummary>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching user groups");
            return Enumerable.Empty<UserGroupSummary>();
        }
    }
}
