using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.Commands.CreateRecurringAuditSetting;
using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSettings;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Infrastructure.Services;
using HWSAuditPlatform.Tests.Common;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace HWSAuditPlatform.Tests.Integration;

/// <summary>
/// Comprehensive integration tests for recurring audit functionality covering the complete flow
/// from manager creating recurring schedules through worker generating individual audit instances
/// </summary>
public class RecurringAuditIntegrationTests : BaseDbTestClass
{
    private readonly IRecurrenceCalculationService _recurrenceService;
    private readonly IMediator _mediator;

    public RecurringAuditIntegrationTests()
    {
        _recurrenceService = new RecurrenceCalculationService();

        // Set up MediatR with the required services
        var services = new ServiceCollection();
        services.AddScoped<IApplicationDbContext>(_ => Context);
        services.AddScoped<ICurrentUserService>(_ => MockCurrentUserService.Object);
        services.AddScoped<IRecurrenceCalculationService>(_ => _recurrenceService);
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(CreateRecurringAuditSettingCommand).Assembly));

        var serviceProvider = services.BuildServiceProvider();
        _mediator = serviceProvider.GetRequiredService<IMediator>();
    }

    protected IMediator Mediator => _mediator;

    [Fact]
    public async Task CompleteRecurringAuditFlow_WeeklySchedule_ShouldGenerateCorrectAudits()
    {
        // Arrange - Set up organizational structure
        var location = await CreateLocationAsync("Test Location");
        var factory = await CreateFactoryAsync("Test Factory", location.Id);
        var area = await CreateAreaAsync("Production Line A", factory.Id);
        var subArea = await CreateSubAreaAsync("Welding Station 1", area.Id);

        // Set up users
        var manager = await CreateUserAsync("manager1", "Manager", "User", UserRole.ProcessOwner);
        var auditor = await CreateUserAsync("auditor1", "John", "Auditor", UserRole.Auditor);

        // Set up audit template
        var template = await CreateAuditTemplateAsync("Weekly Safety Check", manager.AdObjectGuid);

        // Create recurring audit setting
        var createCommand = new CreateRecurringAuditSettingCommand
        {
            SettingName = "Weekly Production Safety Audit",
            AuditTemplateId = template.Id,
            FactoryId = factory.Id,
            AreaId = area.Id,
            SubAreaId = subArea.Id,
            DeadlineDays = 7,
            AssignmentType = AssignmentType.Individual,
            AssignToUserId = auditor.AdObjectGuid,
            RecurrenceRule = new CreateRecurrenceRuleDto
            {
                FrequencyType = FrequencyType.WEEKLY,
                Interval = 1,
                StartDate = DateOnly.FromDateTime(DateTime.Today),
                EndDate = DateOnly.FromDateTime(DateTime.Today.AddMonths(3))
            }
        };

        var settingId = await Mediator.Send(createCommand);
        Assert.NotNull(settingId);
        Assert.NotEmpty(settingId);

        // Verify the setting was created
        var createdSetting = await Context.RecurringAuditSettings
            .Include(r => r.RecurrenceRule)
            .FirstOrDefaultAsync(r => r.Id == settingId);
        Assert.NotNull(createdSetting);

        // Act - Generate recurring audits (use specific setting ID to force generation)
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = settingId, // Force generation for this specific setting
            DryRun = false,
            MaxAuditsToGenerate = 10
        };

        var generateResult = await Mediator.Send(generateCommand);

        // Assert
        Assert.NotNull(generateResult);
        Assert.True(generateResult.GeneratedAuditIds.Count > 0);
        Assert.Equal(0, generateResult.ErrorsEncountered);

        // Verify audits were created correctly
        var generatedAudits = await Context.Audits
            .Where(a => generateResult.GeneratedAuditIds.Contains(a.Id))
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .ToListAsync();

        Assert.NotEmpty(generatedAudits);
        
        foreach (var audit in generatedAudits)
        {
            Assert.Equal(template.Id, audit.AuditTemplateId);
            Assert.Equal(factory.Id, audit.FactoryId);
            Assert.Equal(area.Id, audit.AreaId);
            Assert.Equal(subArea.Id, audit.SubAreaId);
            Assert.Equal(auditor.AdObjectGuid, audit.AssignedToUserId);
            Assert.Equal(AuditOverallStatus.Scheduled, audit.OverallStatus);
            Assert.Equal(settingId, audit.RecurringAuditSettingId);
        }
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithMultipleSettings_ShouldHandleCorrectly()
    {
        // Arrange - Set up multiple recurring settings with different frequencies
        var location = await CreateLocationAsync("Multi Location");
        var factory = await CreateFactoryAsync("Multi Factory", location.Id);
        var area1 = await CreateAreaAsync("Area 1", factory.Id);
        var area2 = await CreateAreaAsync("Area 2", factory.Id);

        var manager = await CreateUserAsync("manager2", "Manager", "Two", UserRole.ProcessOwner);
        var auditor1 = await CreateUserAsync("auditor2", "Jane", "Auditor", UserRole.Auditor);
        var auditor2 = await CreateUserAsync("auditor3", "Bob", "Auditor", UserRole.Auditor);

        var template1 = await CreateAuditTemplateAsync("Daily Check", manager.AdObjectGuid);
        var template2 = await CreateAuditTemplateAsync("Monthly Review", manager.AdObjectGuid);

        // Create daily recurring setting
        var dailySetting = await CreateRecurringSettingAsync(
            "Daily Area 1 Check", template1.Id, factory.Id, area1.Id, null,
            auditor1.AdObjectGuid, FrequencyType.DAILY, 1, 3);

        // Create monthly recurring setting
        var monthlySetting = await CreateRecurringSettingAsync(
            "Monthly Area 2 Review", template2.Id, factory.Id, area2.Id, null,
            auditor2.AdObjectGuid, FrequencyType.MONTHLY, 1, 14);

        // Act - Generate audits (force generation for both settings)
        var generateCommand1 = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = dailySetting,
            DryRun = false,
            MaxAuditsToGenerate = 25
        };

        var generateCommand2 = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = monthlySetting,
            DryRun = false,
            MaxAuditsToGenerate = 25
        };

        var result1 = await Mediator.Send(generateCommand1);
        var result2 = await Mediator.Send(generateCommand2);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.True(result1.GeneratedAuditIds.Count > 0);
        Assert.True(result2.GeneratedAuditIds.Count > 0);
        Assert.Equal(1, result1.SettingsProcessed);
        Assert.Equal(1, result2.SettingsProcessed);

        // Verify audits from both settings were created
        var allGeneratedIds = result1.GeneratedAuditIds.Concat(result2.GeneratedAuditIds).ToList();
        var audits = await Context.Audits
            .Where(a => allGeneratedIds.Contains(a.Id))
            .ToListAsync();

        var dailyAudits = audits.Where(a => a.RecurringAuditSettingId == dailySetting).ToList();
        var monthlyAudits = audits.Where(a => a.RecurringAuditSettingId == monthlySetting).ToList();

        Assert.NotEmpty(dailyAudits);
        Assert.NotEmpty(monthlyAudits);

        // Daily audits should be more frequent
        Assert.True(dailyAudits.Count >= monthlyAudits.Count);
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithInvalidSettings_ShouldHandleGracefully()
    {
        // Arrange - Create a setting with missing required area
        var location = await CreateLocationAsync("Invalid Location");
        var factory = await CreateFactoryAsync("Invalid Factory", location.Id);
        var manager = await CreateUserAsync("manager3", "Manager", "Three", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Invalid Template", manager.AdObjectGuid);

        // Create setting without area (should cause validation error)
        var setting = new RecurringAuditSetting
        {
            Id = CuidGenerator.Generate(),
            SettingName = "Invalid Setting",
            AuditTemplateId = template.Id,
            FactoryId = factory.Id,
            AreaId = null, // This will cause issues during audit generation
            DeadlineDays = 7,
            AssignmentType = AssignmentType.Individual,
            IsEnabled = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = manager.AdObjectGuid
        };

        var recurrenceRule = new RecurrenceRule
        {
            Id = CuidGenerator.Generate(),
            RecurringAuditSettingId = setting.Id,
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddMonths(1)),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        setting.RecurrenceRule = recurrenceRule;
        Context.RecurringAuditSettings.Add(setting);
        await Context.SaveChangesAsync();

        // Act - Try to generate audits (force generation for this specific invalid setting)
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = setting.Id,
            DryRun = false,
            MaxAuditsToGenerate = 10
        };

        var result = await Mediator.Send(generateCommand);

        // Assert - Should handle errors gracefully
        Assert.NotNull(result);
        Assert.True(result.ErrorsEncountered > 0);
        Assert.True(result.Errors.Count > 0);
        // Should contain some error message about the invalid setting
        var errorMessage = string.Join(" ", result.Errors);
        Assert.True(errorMessage.Contains("Error generating audits") || errorMessage.Contains("Invalid") || errorMessage.Contains("required"),
            $"Expected error message to contain validation error, but got: {errorMessage}");
    }

    [Fact]
    public async Task RecurringAuditQuery_WithLocationFiltering_ShouldReturnCorrectResults()
    {
        // Arrange - Set up multiple settings across different locations
        var location1 = await CreateLocationAsync("Location 1");
        var location2 = await CreateLocationAsync("Location 2");
        var factory1 = await CreateFactoryAsync("Factory 1", location1.Id);
        var factory2 = await CreateFactoryAsync("Factory 2", location2.Id);
        var area1 = await CreateAreaAsync("Area 1", factory1.Id);
        var area2 = await CreateAreaAsync("Area 2", factory2.Id);

        var manager = await CreateUserAsync("manager4", "Manager", "Four", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Filter Test Template", manager.AdObjectGuid);

        await CreateRecurringSettingAsync("Setting 1", template.Id, factory1.Id, area1.Id, null, manager.AdObjectGuid, FrequencyType.WEEKLY, 1, 7);
        await CreateRecurringSettingAsync("Setting 2", template.Id, factory2.Id, area2.Id, null, manager.AdObjectGuid, FrequencyType.MONTHLY, 1, 14);

        // Act - Query with factory filter
        var query = new GetRecurringAuditSettingsQuery
        {
            FactoryId = factory1.Id,
            PageSize = 10
        };

        var result = await Mediator.Send(query);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Items);
        Assert.Equal("Setting 1", result.Items.First().SettingName);
        Assert.Equal("Factory 1", result.Items.First().FactoryName);
        Assert.Equal("Area 1", result.Items.First().AreaName);
    }

    [Fact]
    public async Task RecurringAuditGeneration_DryRun_ShouldNotCreateActualAudits()
    {
        // Arrange
        var location = await CreateLocationAsync("Dry Run Location");
        var factory = await CreateFactoryAsync("Dry Run Factory", location.Id);
        var area = await CreateAreaAsync("Dry Run Area", factory.Id);
        var manager = await CreateUserAsync("manager5", "Manager", "Five", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Dry Run Template", manager.AdObjectGuid);

        var settingId = await CreateRecurringSettingAsync("Dry Run Setting", template.Id, factory.Id, area.Id, null, manager.AdObjectGuid, FrequencyType.DAILY, 1, 3);

        var initialAuditCount = await Context.Audits.CountAsync();

        // Act - Dry run generation (force generation for this specific setting)
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = settingId,
            DryRun = true,
            MaxAuditsToGenerate = 10
        };

        var result = await Mediator.Send(generateCommand);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.GeneratedAuditIds.Count > 0); // Should report what would be generated
        
        var finalAuditCount = await Context.Audits.CountAsync();
        Assert.Equal(initialAuditCount, finalAuditCount); // No actual audits should be created
    }

    // Helper methods for test setup
    private async Task<Location> CreateLocationAsync(string name)
    {
        var location = new Location { LocationName = name };
        Context.Locations.Add(location);
        await Context.SaveChangesAsync();
        return location;
    }

    private async Task<Factory> CreateFactoryAsync(string name, int locationId)
    {
        var factory = new Factory { FactoryName = name, LocationId = locationId };
        Context.Factories.Add(factory);
        await Context.SaveChangesAsync();
        return factory;
    }

    private async Task<Area> CreateAreaAsync(string name, int factoryId)
    {
        var area = new Area { AreaName = name, FactoryId = factoryId, IsActive = true };
        Context.Areas.Add(area);
        await Context.SaveChangesAsync();
        return area;
    }

    private async Task<SubArea> CreateSubAreaAsync(string name, int areaId)
    {
        var subArea = new SubArea { SubAreaName = name, AreaId = areaId, IsActive = true };
        Context.SubAreas.Add(subArea);
        await Context.SaveChangesAsync();
        return subArea;
    }

    private async Task<User> CreateUserAsync(string username, string firstName, string lastName, UserRole userRole)
    {
        // Ensure roles exist (create if not exists)
        var role = await Context.Roles.FirstOrDefaultAsync(r => r.RoleName == userRole);
        if (role == null)
        {
            role = new Role
            {
                RoleName = userRole,
                Description = userRole.ToString()
            };
            Context.Roles.Add(role);
            await Context.SaveChangesAsync();
        }

        var user = User.Create(
            username: username,
            firstName: firstName,
            lastName: lastName,
            email: $"{username}@test.com",
            roleId: role.Id,
            factoryId: null,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: $"CN={firstName} {lastName},OU=Users,DC=test,DC=com",
            createdByUserId: "system"
        );

        Context.Users.Add(user);
        await Context.SaveChangesAsync();
        return user;
    }

    private async Task<AuditTemplate> CreateAuditTemplateAsync(string name, string createdBy)
    {
        var template = new AuditTemplate
        {
            TemplateName = name,
            Description = $"Test template: {name}",
            Version = 1,
            IsPublished = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = createdBy
        };
        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<string> CreateRecurringSettingAsync(string name, int templateId, int factoryId, int areaId, int? subAreaId, string assignToUserId, FrequencyType frequency, int interval, int deadlineDays)
    {
        var setting = new RecurringAuditSetting
        {
            Id = CuidGenerator.Generate(),
            SettingName = name,
            AuditTemplateId = templateId,
            FactoryId = factoryId,
            AreaId = areaId,
            SubAreaId = subAreaId,
            DeadlineDays = deadlineDays,
            AssignmentType = AssignmentType.Individual,
            AssignToUserId = assignToUserId,
            IsEnabled = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = assignToUserId
        };

        var recurrenceRule = new RecurrenceRule
        {
            Id = CuidGenerator.Generate(),
            RecurringAuditSettingId = setting.Id,
            FrequencyType = frequency,
            Interval = interval,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddMonths(3)),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        setting.RecurrenceRule = recurrenceRule;
        Context.RecurringAuditSettings.Add(setting);
        await Context.SaveChangesAsync();
        return setting.Id;
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithExpiredSettings_ShouldNotGenerate()
    {
        // Arrange - Create setting with end date in the past
        var location = await CreateLocationAsync("Expired Location");
        var factory = await CreateFactoryAsync("Expired Factory", location.Id);
        var area = await CreateAreaAsync("Expired Area", factory.Id);
        var manager = await CreateUserAsync("manager6", "Manager", "Six", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Expired Template", manager.AdObjectGuid);

        var setting = new RecurringAuditSetting
        {
            Id = CuidGenerator.Generate(),
            SettingName = "Expired Setting",
            AuditTemplateId = template.Id,
            FactoryId = factory.Id,
            AreaId = area.Id,
            DeadlineDays = 7,
            AssignmentType = AssignmentType.Individual,
            AssignToUserId = manager.AdObjectGuid,
            IsEnabled = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = manager.AdObjectGuid
        };

        var recurrenceRule = new RecurrenceRule
        {
            Id = CuidGenerator.Generate(),
            RecurringAuditSettingId = setting.Id,
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)), // Expired yesterday
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        setting.RecurrenceRule = recurrenceRule;
        Context.RecurringAuditSettings.Add(setting);
        await Context.SaveChangesAsync();

        // Act
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            DryRun = false,
            MaxAuditsToGenerate = 10
        };

        var result = await Mediator.Send(generateCommand);

        // Assert - Should not generate any audits from expired setting
        Assert.NotNull(result);
        Assert.Empty(result.GeneratedAuditIds);
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithDisabledSettings_ShouldSkip()
    {
        // Arrange - Create disabled setting
        var location = await CreateLocationAsync("Disabled Location");
        var factory = await CreateFactoryAsync("Disabled Factory", location.Id);
        var area = await CreateAreaAsync("Disabled Area", factory.Id);
        var manager = await CreateUserAsync("manager7", "Manager", "Seven", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Disabled Template", manager.AdObjectGuid);

        var setting = new RecurringAuditSetting
        {
            Id = CuidGenerator.Generate(),
            SettingName = "Disabled Setting",
            AuditTemplateId = template.Id,
            FactoryId = factory.Id,
            AreaId = area.Id,
            DeadlineDays = 7,
            AssignmentType = AssignmentType.Individual,
            AssignToUserId = manager.AdObjectGuid,
            IsEnabled = false, // Disabled
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = manager.AdObjectGuid
        };

        var recurrenceRule = new RecurrenceRule
        {
            Id = CuidGenerator.Generate(),
            RecurringAuditSettingId = setting.Id,
            FrequencyType = FrequencyType.DAILY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddMonths(1)),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        setting.RecurrenceRule = recurrenceRule;
        Context.RecurringAuditSettings.Add(setting);
        await Context.SaveChangesAsync();

        // Act
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            DryRun = false,
            MaxAuditsToGenerate = 10
        };

        var result = await Mediator.Send(generateCommand);

        // Assert - Should not generate any audits from disabled setting
        Assert.NotNull(result);
        Assert.Empty(result.GeneratedAuditIds);
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithBatchLimit_ShouldRespectLimit()
    {
        // Arrange - Create multiple settings that would generate many audits
        var location = await CreateLocationAsync("Batch Limit Location");
        var factory = await CreateFactoryAsync("Batch Limit Factory", location.Id);
        var area = await CreateAreaAsync("Batch Limit Area", factory.Id);
        var manager = await CreateUserAsync("manager8", "Manager", "Eight", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Batch Limit Template", manager.AdObjectGuid);

        // Create multiple daily settings
        for (int i = 1; i <= 5; i++)
        {
            await CreateRecurringSettingAsync($"Daily Setting {i}", template.Id, factory.Id, area.Id, null, manager.AdObjectGuid, FrequencyType.DAILY, 1, 3);
        }

        // Act - Generate with small batch limit
        var generateCommand = new GenerateRecurringAuditsCommand
        {
            DryRun = false,
            MaxAuditsToGenerate = 3 // Small limit
        };

        var result = await Mediator.Send(generateCommand);

        // Assert - Should respect the batch limit
        Assert.NotNull(result);
        Assert.True(result.GeneratedAuditIds.Count <= 3);
    }

    [Fact]
    public async Task RecurringAuditGeneration_WithDifferentFrequencies_ShouldCalculateCorrectly()
    {
        // Arrange - Test different frequency types
        var location = await CreateLocationAsync("Frequency Test Location");
        var factory = await CreateFactoryAsync("Frequency Test Factory", location.Id);
        var area = await CreateAreaAsync("Frequency Test Area", factory.Id);
        var manager = await CreateUserAsync("manager9", "Manager", "Nine", UserRole.ProcessOwner);
        var template = await CreateAuditTemplateAsync("Frequency Test Template", manager.AdObjectGuid);

        // Create settings with different frequencies
        var dailySettingId = await CreateRecurringSettingAsync("Daily Test", template.Id, factory.Id, area.Id, null, manager.AdObjectGuid, FrequencyType.DAILY, 1, 1);
        var weeklySettingId = await CreateRecurringSettingAsync("Weekly Test", template.Id, factory.Id, area.Id, null, manager.AdObjectGuid, FrequencyType.WEEKLY, 1, 7);
        var monthlySettingId = await CreateRecurringSettingAsync("Monthly Test", template.Id, factory.Id, area.Id, null, manager.AdObjectGuid, FrequencyType.MONTHLY, 1, 30);

        // Act - Generate audits for each frequency type
        var dailyCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = dailySettingId,
            DryRun = false,
            MaxAuditsToGenerate = 33
        };

        var weeklyCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = weeklySettingId,
            DryRun = false,
            MaxAuditsToGenerate = 33
        };

        var monthlyCommand = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = monthlySettingId,
            DryRun = false,
            MaxAuditsToGenerate = 34
        };

        var dailyResult = await Mediator.Send(dailyCommand);
        var weeklyResult = await Mediator.Send(weeklyCommand);
        var monthlyResult = await Mediator.Send(monthlyCommand);

        // Assert
        Assert.NotNull(dailyResult);
        Assert.NotNull(weeklyResult);
        Assert.NotNull(monthlyResult);
        Assert.True(dailyResult.GeneratedAuditIds.Count > 0);
        Assert.True(weeklyResult.GeneratedAuditIds.Count > 0);
        Assert.True(monthlyResult.GeneratedAuditIds.Count > 0);

        var allGeneratedIds = dailyResult.GeneratedAuditIds
            .Concat(weeklyResult.GeneratedAuditIds)
            .Concat(monthlyResult.GeneratedAuditIds)
            .ToList();

        var audits = await Context.Audits
            .Where(a => allGeneratedIds.Contains(a.Id))
            .ToListAsync();

        var dailyAudits = audits.Where(a => a.RecurringAuditSettingId == dailySettingId).ToList();
        var weeklyAudits = audits.Where(a => a.RecurringAuditSettingId == weeklySettingId).ToList();
        var monthlyAudits = audits.Where(a => a.RecurringAuditSettingId == monthlySettingId).ToList();

        // Each frequency type should generate at least one audit
        Assert.True(dailyAudits.Count > 0);
        Assert.True(weeklyAudits.Count > 0);
        Assert.True(monthlyAudits.Count > 0);
    }
}
