﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateUserRolesToNewHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "hwsap_usr_process_owner_assignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProcessOwnerUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    FactoryId = table.Column<int>(type: "int", nullable: true),
                    AreaId = table.Column<int>(type: "int", nullable: true),
                    SubAreaId = table.Column<int>(type: "int", nullable: true),
                    AssignedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hwsap_usr_process_owner_assignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_process_owner_assignments_hwsap_org_areas_AreaId",
                        column: x => x.AreaId,
                        principalTable: "hwsap_org_areas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_process_owner_assignments_hwsap_org_factories_FactoryId",
                        column: x => x.FactoryId,
                        principalTable: "hwsap_org_factories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_process_owner_assignments_hwsap_org_sub_areas_SubAreaId",
                        column: x => x.SubAreaId,
                        principalTable: "hwsap_org_sub_areas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_process_owner_assignments_hwsap_usr_users_AssignedByUserId",
                        column: x => x.AssignedByUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid");
                    table.ForeignKey(
                        name: "FK_hwsap_usr_process_owner_assignments_hwsap_usr_users_ProcessOwnerUserId",
                        column: x => x.ProcessOwnerUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "hwsap_usr_template_access_assignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    AssignedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hwsap_usr_template_access_assignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_template_access_assignments_hwsap_tpl_audit_templates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "hwsap_tpl_audit_templates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_hwsap_usr_template_access_assignments_hwsap_usr_users_AssignedByUserId",
                        column: x => x.AssignedByUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid");
                    table.ForeignKey(
                        name: "FK_hwsap_usr_template_access_assignments_hwsap_usr_users_UserId",
                        column: x => x.UserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-DevAdministrators", "Maps AD DevAdministrators group to DevAdmin role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-SystemManagers", "Maps AD SystemManagers group to SystemManager role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-ProcessOwners", "Maps AD ProcessOwners group to ProcessOwner role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-Auditors", "Maps AD Auditors group to Auditor role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Developer Administrator with full system access", "DevAdmin" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "System Manager with template creation and user management capabilities", "SystemManager" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Process Owner with audit assignment and results management capabilities", "ProcessOwner" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Auditor who performs audit activities", "Auditor" });

            migrationBuilder.InsertData(
                table: "hwsap_usr_roles",
                columns: new[] { "Id", "CreatedAt", "Description", "RoleName", "UpdatedAt" },
                values: new object[] { 5, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Viewer with read-only access", "Viewer", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) });

            migrationBuilder.InsertData(
                table: "hwsap_usr_ad_group_role_mappings",
                columns: new[] { "Id", "AdGroupName", "CreatedAt", "Description", "IsActive", "RoleId", "UpdatedAt" },
                values: new object[] { 5, "HWSAudit-Viewers", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Maps AD Viewers group to Viewer role", true, 5, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) });

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_usr_process_owner_assignments_AreaId",
                table: "hwsap_usr_process_owner_assignments",
                column: "AreaId");

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_usr_process_owner_assignments_AssignedByUserId",
                table: "hwsap_usr_process_owner_assignments",
                column: "AssignedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_usr_process_owner_assignments_FactoryId",
                table: "hwsap_usr_process_owner_assignments",
                column: "FactoryId");

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_usr_process_owner_assignments_SubAreaId",
                table: "hwsap_usr_process_owner_assignments",
                column: "SubAreaId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessOwnerAssignments_IsActive",
                table: "hwsap_usr_process_owner_assignments",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessOwnerAssignments_ProcessOwnerUserId",
                table: "hwsap_usr_process_owner_assignments",
                column: "ProcessOwnerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessOwnerAssignments_Scope",
                table: "hwsap_usr_process_owner_assignments",
                columns: new[] { "ProcessOwnerUserId", "FactoryId", "AreaId", "SubAreaId" },
                unique: true,
                filter: "[FactoryId] IS NOT NULL AND [AreaId] IS NOT NULL AND [SubAreaId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_usr_template_access_assignments_AssignedByUserId",
                table: "hwsap_usr_template_access_assignments",
                column: "AssignedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_TemplateAccessAssignments_AuditTemplateId",
                table: "hwsap_usr_template_access_assignments",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_TemplateAccessAssignments_ExpiresAt",
                table: "hwsap_usr_template_access_assignments",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_TemplateAccessAssignments_IsActive",
                table: "hwsap_usr_template_access_assignments",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TemplateAccessAssignments_UserId",
                table: "hwsap_usr_template_access_assignments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TemplateAccessAssignments_UserTemplate",
                table: "hwsap_usr_template_access_assignments",
                columns: new[] { "UserId", "AuditTemplateId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "hwsap_usr_process_owner_assignments");

            migrationBuilder.DropTable(
                name: "hwsap_usr_template_access_assignments");

            migrationBuilder.DeleteData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-Administrators", "Maps AD Administrators group to Admin role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-Managers", "Maps AD Managers group to Manager role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-Auditors", "Maps AD Auditors group to Auditor role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "AdGroupName", "Description" },
                values: new object[] { "HWSAudit-Viewers", "Maps AD Viewers group to Viewer role" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "System Administrator with full access", "Admin" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Manager with audit oversight and approval capabilities", "Manager" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Auditor who performs audit activities", "Auditor" });

            migrationBuilder.UpdateData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "Description", "RoleName" },
                values: new object[] { "Viewer with read-only access", "Viewer" });
        }
    }
}
