using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Entities.Findings;

public class FindingCategoryTests
{
    [Fact]
    public void FindingCategory_ShouldInitializeWithDefaultValues()
    {
        // Act
        var category = new FindingCategory();

        // Assert
        Assert.Equal(0, category.DisplayOrder);
        Assert.True(category.IsActive);
        Assert.False(category.RequiresDocumentation);
        Assert.Empty(category.Findings);
        Assert.True(category.IsUsable);
    }

    [Fact]
    public void FindingCategory_ShouldSetBasicProperties()
    {
        // Arrange
        var category = new FindingCategory
        {
            CategoryName = "Safety Violation",
            Description = "Issues related to safety procedures",
            AuditTemplateId = 1,
            DisplayOrder = 5,
            ColorCode = "#FF5733",
            IconName = "warning-triangle",
            RequiresDocumentation = true
        };

        // Act & Assert
        Assert.Equal("Safety Violation", category.CategoryName);
        Assert.Equal("Issues related to safety procedures", category.Description);
        Assert.Equal(1, category.AuditTemplateId);
        Assert.Equal(5, category.DisplayOrder);
        Assert.Equal("#FF5733", category.ColorCode);
        Assert.Equal("warning-triangle", category.IconName);
        Assert.True(category.RequiresDocumentation);
    }

    [Fact]
    public void FindingCategory_IsUsable_ShouldReturnTrueWhenActive()
    {
        // Arrange
        var category = new FindingCategory { IsActive = true };

        // Act & Assert
        Assert.True(category.IsUsable);
    }

    [Fact]
    public void FindingCategory_IsUsable_ShouldReturnFalseWhenInactive()
    {
        // Arrange
        var category = new FindingCategory { IsActive = false };

        // Act & Assert
        Assert.False(category.IsUsable);
    }

    [Fact]
    public void FindingCategory_FindingCount_ShouldReturnCorrectCount()
    {
        // Arrange
        var category = new FindingCategory();
        var finding1 = new Finding { Id = "f1", FindingDescription = "Finding 1" };
        var finding2 = new Finding { Id = "f2", FindingDescription = "Finding 2" };

        category.Findings.Add(finding1);
        category.Findings.Add(finding2);

        // Act & Assert
        Assert.Equal(2, category.FindingCount);
    }

    [Fact]
    public void FindingCategory_OpenFindingCount_ShouldReturnCorrectCount()
    {
        // Arrange
        var category = new FindingCategory();
        var openFinding = new Finding 
        { 
            Id = "f1", 
            FindingDescription = "Open Finding",
            Status = FindingStatus.Open
        };
        var closedFinding = new Finding 
        { 
            Id = "f2", 
            FindingDescription = "Closed Finding",
            Status = FindingStatus.Closed
        };

        category.Findings.Add(openFinding);
        category.Findings.Add(closedFinding);

        // Act & Assert
        Assert.Equal(1, category.OpenFindingCount);
    }

    [Fact]
    public void FindingCategory_ShouldSetNavigationProperties()
    {
        // Arrange
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var category = new FindingCategory
        {
            AuditTemplateId = template.Id,
            AuditTemplate = template
        };

        // Act & Assert
        Assert.Equal(template, category.AuditTemplate);
        Assert.Equal(template.Id, category.AuditTemplateId);
    }

    [Theory]
    [InlineData("#FF5733")]
    [InlineData("#000000")]
    [InlineData("#FFFFFF")]
    [InlineData(null)]
    public void FindingCategory_ColorCode_ShouldAcceptValidValues(string? colorCode)
    {
        // Arrange & Act
        var category = new FindingCategory { ColorCode = colorCode };

        // Assert
        Assert.Equal(colorCode, category.ColorCode);
    }

    [Theory]
    [InlineData("warning-triangle")]
    [InlineData("exclamation-circle")]
    [InlineData("info")]
    [InlineData(null)]
    public void FindingCategory_IconName_ShouldAcceptValidValues(string? iconName)
    {
        // Arrange & Act
        var category = new FindingCategory { IconName = iconName };

        // Assert
        Assert.Equal(iconName, category.IconName);
    }

    [Fact]
    public void FindingCategory_ShouldValidateRequiredProperties()
    {
        // Arrange
        var category = new FindingCategory
        {
            CategoryName = "Test Category",
            AuditTemplateId = 1
        };

        // Act & Assert
        Assert.NotEmpty(category.CategoryName);
        Assert.True(category.AuditTemplateId > 0);
    }

    [Fact]
    public void FindingCategory_Description_ShouldAcceptNullAndValidStrings()
    {
        // Arrange
        var category = new FindingCategory();

        // Act & Assert - Null description
        category.Description = null;
        Assert.Null(category.Description);

        // Act & Assert - Valid description
        category.Description = "Test description";
        Assert.Equal("Test description", category.Description);
    }

    [Fact]
    public void FindingCategory_DisplayOrder_ShouldDefaultToZero()
    {
        // Arrange & Act
        var category = new FindingCategory();

        // Assert
        Assert.Equal(0, category.DisplayOrder);
    }

    [Fact]
    public void FindingCategory_RequiresDocumentation_ShouldDefaultToFalse()
    {
        // Arrange & Act
        var category = new FindingCategory();

        // Assert
        Assert.False(category.RequiresDocumentation);
    }
}
