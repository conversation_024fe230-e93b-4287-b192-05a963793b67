using HWSAuditPlatform.Domain.Entities.Audits;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Service interface for managing audit attachments
/// </summary>
public interface IAuditAttachmentService
{
    /// <summary>
    /// Uploads an attachment for an audit answer
    /// </summary>
    /// <param name="auditAnswerId">The ID of the audit answer</param>
    /// <param name="fileName">Original file name</param>
    /// <param name="contentType">MIME type of the file</param>
    /// <param name="fileStream">File content stream</param>
    /// <param name="description">Optional description of the attachment</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit attachment</returns>
    Task<AuditAttachment> UploadAttachmentAsync(
        string auditAnswerId,
        string fileName,
        string contentType,
        Stream fileStream,
        string? description = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads an attachment by ID
    /// </summary>
    /// <param name="attachmentId">The ID of the attachment</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream, content type, and file name</returns>
    Task<(Stream FileStream, string ContentType, string FileName)> DownloadAttachmentAsync(
        string attachmentId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes an attachment
    /// </summary>
    /// <param name="attachmentId">The ID of the attachment to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteAttachmentAsync(string attachmentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all attachments for an audit answer
    /// </summary>
    /// <param name="auditAnswerId">The ID of the audit answer</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of attachments</returns>
    Task<IReadOnlyList<AuditAttachment>> GetAttachmentsByAuditAnswerAsync(
        string auditAnswerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if a user has permission to access an attachment
    /// </summary>
    /// <param name="attachmentId">The ID of the attachment</param>
    /// <param name="userId">The ID of the user</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has permission, false otherwise</returns>
    Task<bool> ValidateAttachmentPermissionAsync(
        string attachmentId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the total size of all attachments for an audit
    /// </summary>
    /// <param name="auditId">The ID of the audit</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total size in bytes</returns>
    Task<long> GetTotalAttachmentSizeAsync(string auditId, CancellationToken cancellationToken = default);
}
