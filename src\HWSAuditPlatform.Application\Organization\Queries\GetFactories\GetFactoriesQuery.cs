using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Organization.Queries.GetFactories;

/// <summary>
/// Query to get factories with optional filtering
/// </summary>
public class GetFactoriesQuery : BaseQuery<List<FactorySummaryDto>>
{
    public int? LocationId { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public int? RestrictToFactoryId { get; set; } // For non-admin users
}
