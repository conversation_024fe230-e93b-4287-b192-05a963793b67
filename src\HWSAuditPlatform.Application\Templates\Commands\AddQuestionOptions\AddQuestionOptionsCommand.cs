using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestionOptions;

/// <summary>
/// Command to add options to an existing question
/// </summary>
public class AddQuestionOptionsCommand : BaseCommand<List<int>>
{
    /// <summary>
    /// The ID of the question to add options to
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// The ID of the audit template (for validation)
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// List of options to add
    /// </summary>
    public List<AddQuestionOptionDto> Options { get; set; } = new();
}

/// <summary>
/// DTO for adding question options
/// </summary>
public class AddQuestionOptionDto
{
    /// <summary>
    /// Display text for the option
    /// </summary>
    public string OptionText { get; set; } = string.Empty;

    /// <summary>
    /// Internal value of the option
    /// </summary>
    public string? OptionValue { get; set; }

    /// <summary>
    /// Display order for the option
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Whether this is the correct option (for quiz-like audits)
    /// </summary>
    public bool? IsCorrectOption { get; set; }
}
