using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Workflow.DTOs;

namespace HWSAuditPlatform.Application.Workflow.Queries.GetMyCorrectionRequests;

/// <summary>
/// Handler for GetMyCorrectionRequestsQuery
/// </summary>
public class GetMyCorrectionRequestsQueryHandler : BaseQueryHandler<GetMyCorrectionRequestsQuery, PaginatedResult<CorrectionRequestSummaryDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetMyCorrectionRequestsQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<PaginatedResult<CorrectionRequestSummaryDto>> Handle(GetMyCorrectionRequestsQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        var query = _context.AuditCorrectionRequests
            .Include(cr => cr.Audit)
                .ThenInclude(a => a.AuditTemplate)
            .Include(cr => cr.Audit)
                .ThenInclude(a => a.Factory)
            .Include(cr => cr.Audit)
                .ThenInclude(a => a.Area)
            .Include(cr => cr.ReviewedByUser)
            .Where(cr => cr.RequestedByUserId == currentUserId)
            .AsQueryable();

        // Apply filters
        if (request.Status.HasValue)
        {
            query = query.Where(cr => cr.Status == request.Status.Value);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "status" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(cr => cr.Status)
                : query.OrderBy(cr => cr.Status),
            "reviewedat" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(cr => cr.ReviewedAt)
                : query.OrderBy(cr => cr.ReviewedAt),
            "audittemplate" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(cr => cr.Audit.AuditTemplate.TemplateName)
                : query.OrderBy(cr => cr.Audit.AuditTemplate.TemplateName),
            _ => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(cr => cr.RequestedAt)
                : query.OrderBy(cr => cr.RequestedAt)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and select data
        var correctionRequests = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(cr => new CorrectionRequestSummaryDto
            {
                Id = cr.Id,
                AuditId = cr.AuditId,
                AuditTemplateName = cr.Audit.AuditTemplate.TemplateName,
                FactoryName = cr.Audit.Factory.FactoryName,
                AreaName = cr.Audit.Area.AreaName,
                RequestedAt = cr.RequestedAt,
                Status = cr.Status,
                ReviewedByUserName = cr.ReviewedByUser != null ? $"{cr.ReviewedByUser.FirstName} {cr.ReviewedByUser.LastName}".Trim() : null,
                ReviewedAt = cr.ReviewedAt
            })
            .ToListAsync(cancellationToken);

        return new PaginatedResult<CorrectionRequestSummaryDto>(correctionRequests, totalCount, request.PageNumber, request.PageSize);
    }
}
