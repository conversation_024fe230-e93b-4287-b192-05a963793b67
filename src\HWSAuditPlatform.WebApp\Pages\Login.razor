@page "/login"
@layout EmptyLayout

<PageTitle>Login - HWS Audit Platform</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="bi bi-shield-check"></i>
            </div>
            <h2>HWS <span class="text-orange">Audit</span> Platform</h2>
            <p class="login-subtitle">Management Dashboard</p>
        </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }

        <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin" class="login-form">
            <DataAnnotationsValidator />
            <ValidationSummary class="text-danger" />

            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="bi bi-person me-2"></i>Username
                </label>
                <InputText id="username" class="form-control" @bind-Value="loginModel.Username" placeholder="Enter your username" />
                <ValidationMessage For="@(() => loginModel.Username)" />
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">
                    <i class="bi bi-lock me-2"></i>Password
                </label>
                <InputText id="password" type="password" class="form-control" @bind-Value="loginModel.Password" placeholder="Enter your password" />
                <ValidationMessage For="@(() => loginModel.Password)" />
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        <span>Signing in...</span>
                    }
                    else
                    {
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        <span>Sign In</span>
                    }
                </button>
            </div>
        </EditForm>

        <div class="login-footer">
            <small class="text-muted">
                <i class="bi bi-shield-check"></i>
                Secure Industrial Management System
            </small>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--industrial-darker) 0%, var(--industrial-dark) 50%, var(--industrial-gray) 100%);
        padding: 20px;
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(237, 137, 54, 0.03) 2px,
                rgba(237, 137, 54, 0.03) 4px
            );
        pointer-events: none;
    }

    .login-card {
        background: linear-gradient(135deg, var(--industrial-gray) 0%, var(--industrial-steel) 100%);
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        padding: 40px;
        width: 100%;
        max-width: 420px;
        position: relative;
        color: var(--industrial-text);
    }

    .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--industrial-orange) 0%, #fbb040 50%, var(--industrial-orange) 100%);
        border-radius: 8px 8px 0 0;
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        font-size: 3rem;
        color: var(--industrial-orange);
        margin-bottom: 15px;
        text-shadow: 0 0 20px rgba(237, 137, 54, 0.5);
    }

    .login-header h2 {
        color: var(--industrial-text);
        margin-bottom: 5px;
        font-weight: 700;
        font-family: 'Roboto Condensed', sans-serif;
        text-transform: uppercase;
        letter-spacing: 2px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }

    .text-orange {
        color: var(--industrial-orange);
    }

    .login-subtitle {
        color: #c0c0c0;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .login-form .form-label {
        font-weight: 600;
        color: var(--industrial-text);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .login-form .form-control {
        background-color: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 14px;
    }

    .login-form .form-control:focus {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-orange);
        color: var(--industrial-text);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }

    .login-form .form-control::placeholder {
        color: var(--industrial-text-muted);
    }

    .login-form .btn-primary {
        background: linear-gradient(135deg, var(--industrial-orange) 0%, #fbb040 100%);
        border: none;
        border-radius: 8px;
        padding: 12px;
        font-weight: 600;
        font-size: 16px;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
    }

    .login-form .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(237, 137, 54, 0.4);
        background: linear-gradient(135deg, #fbb040 0%, var(--industrial-orange) 100%);
    }

    .login-footer {
        text-align: center;
        margin-top: 30px;
    }

    .login-footer .bi {
        margin-right: 5px;
    }

    .alert-danger {
        background-color: rgba(229, 62, 62, 0.1);
        border: 1px solid var(--industrial-red);
        color: #ff6b6b;
        border-radius: 6px;
    }
</style>

@code {
    private LoginRequest loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    [Inject] private IAuthenticationService AuthService { get; set; } = default!;
    [Inject] private AuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            var result = await AuthService.LoginAsync(loginModel.Username, loginModel.Password);

            if (result.IsSuccess)
            {
                var userInfo = new UserInfo
                {
                    UserId = result.Username, // We'll improve this later
                    Username = result.Username,
                    Email = result.Email,
                    FullName = result.FullName,
                    Role = result.Role,
                    FactoryId = result.FactoryId
                };

                // Only call MarkUserAsAuthenticated if it's the custom provider
                if (AuthStateProvider is CustomAuthenticationStateProvider customProvider)
                {
                    customProvider.MarkUserAsAuthenticated(userInfo);
                }

                Navigation.NavigateTo("/");
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "Login failed. Please try again.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
