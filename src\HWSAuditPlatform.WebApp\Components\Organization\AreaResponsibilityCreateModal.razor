@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@using System.ComponentModel.DataAnnotations
@inject IAreaResponsibilityApiService AreaResponsibilityApiService
@inject IOrganizationApiService OrganizationApiService
@inject ITemplateApiService TemplateApiService
@inject IUserApiService UserApiService
@inject ILogger<AreaResponsibilityCreateModal> Logger

@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Area Responsibility</h5>
                    <button type="button" class="btn-close" @onclick="Close"></button>
                </div>
                <EditForm Model="model" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger">
                                @errorMessage
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Area *</label>
                                    <select class="form-select" @bind="model.AreaId">
                                        <option value="0">Select an area...</option>
                                        @foreach (var area in areas)
                                        {
                                            <option value="@area.Id">@area.FactoryName - @area.AreaName</option>
                                        }
                                    </select>
                                    <ValidationMessage For="@(() => model.AreaId)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Responsibility Type *</label>
                                    <select class="form-select" @bind="model.ResponsibilityType">
                                        @foreach (var type in responsibilityTypes)
                                        {
                                            <option value="@type.Value">@type.Name</option>
                                        }
                                    </select>
                                    <ValidationMessage For="@(() => model.ResponsibilityType)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Responsible User *</label>
                                    <select class="form-select" @bind="model.ResponsibleUserId">
                                        <option value="">Select a user...</option>
                                        @foreach (var user in users)
                                        {
                                            <option value="@user.AdObjectGuid">@user.FullName (@user.Username)</option>
                                        }
                                    </select>
                                    <ValidationMessage For="@(() => model.ResponsibleUserId)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Audit Template (Optional)</label>
                                    <select class="form-select" @bind="model.AuditTemplateId">
                                        <option value="">General (All Templates)</option>
                                        @foreach (var template in templates)
                                        {
                                            <option value="@template.Id">@template.TemplateName <EMAIL></option>
                                        }
                                    </select>
                                    <small class="form-text text-muted">
                                        Leave empty for general responsibility across all templates.<br />
                                        <strong>Note:</strong> Template-specific responsibilities require area-based responsibility to be enabled on the template.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <input type="number" class="form-control" @bind="model.Priority" min="0" />
                                    <small class="form-text text-muted">Lower number = higher priority (0 is highest)</small>
                                    <ValidationMessage For="@(() => model.Priority)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" @bind="model.IsActive" id="isActive">
                                        <label class="form-check-label" for="isActive">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description (Optional)</label>
                            <textarea class="form-control" @bind="model.Description" rows="3" maxlength="500"></textarea>
                            <ValidationMessage For="@(() => model.Description)" />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Create Responsibility
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public EventCallback OnResponsibilityCreated { get; set; }

    private CreateAreaResponsibilityModel model = new();
    private List<AreaSummary> areas = new();
    private List<ResponsibilityTypeModel> responsibilityTypes = new();
    private List<User> users = new();
    private List<AuditTemplate> templates = new();
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;

    protected override async Task OnParametersSetAsync()
    {
        if (IsVisible && !areas.Any())
        {
            await LoadData();
        }
    }

    private async Task LoadData()
    {
        try
        {
            // Load areas - get all factories first, then get areas for each factory
            var factories = await OrganizationApiService.GetFactoriesAsync();
            var allAreas = new List<AreaSummary>();
            foreach (var factory in factories)
            {
                var factoryAreas = await OrganizationApiService.GetAreasByFactoryAsync(factory.Id);
                allAreas.AddRange(factoryAreas);
            }
            areas = allAreas;

            // Load responsibility types
            responsibilityTypes = await AreaResponsibilityApiService.GetResponsibilityTypesAsync();

            // Load users
            users = (await UserApiService.GetUsersAsync()).ToList();

            // Load templates
            templates = (await TemplateApiService.GetTemplatesAsync()).ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading data for area responsibility creation");
            errorMessage = "Failed to load required data. Please try again.";
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            await AreaResponsibilityApiService.CreateAreaResponsibilityAsync(model);
            await OnResponsibilityCreated.InvokeAsync();
            await Close();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating area responsibility");

            // Check if the error is related to area-based responsibility not being enabled
            if (ex.Message.Contains("Area-based responsibility is not enabled"))
            {
                errorMessage = "The selected audit template does not have area-based responsibility enabled. " +
                              "Please either select 'General (All Templates)' or enable area-based responsibility " +
                              "for the template in Templates → [Template Name] → Advanced Features.";
            }
            else
            {
                errorMessage = "Failed to create area responsibility. Please check your input and try again.";
            }
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task Close()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        
        // Reset form
        model = new CreateAreaResponsibilityModel();
        errorMessage = string.Empty;
    }
}
