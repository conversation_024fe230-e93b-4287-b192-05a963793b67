using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Models;



/// <summary>
/// Shared model for area responsibility statistics
/// </summary>
public class AreaResponsibilityStatisticsModel
{
    public int TotalResponsibilities { get; set; }
    public int ActiveResponsibilities { get; set; }
    public int InactiveResponsibilities { get; set; }
    public int AreasWithResponsibilities { get; set; }
    public int AreasWithoutResponsibilities { get; set; }
    public int TemplatesWithResponsibilities { get; set; }
    public double CoveragePercentage => (AreasWithResponsibilities + AreasWithoutResponsibilities) > 0 
        ? (double)AreasWithResponsibilities / (AreasWithResponsibilities + AreasWithoutResponsibilities) * 100 : 0;
}

/// <summary>
/// Shared model for audit execution context
/// </summary>
public class AuditExecutionContextModel
{
    public string AuditId { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public string AuditTemplateName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public string FactoryName { get; set; } = string.Empty;
    public bool EnableAreaBasedResponsibility { get; set; }
    public bool EnableFindingCategorization { get; set; }
    public List<AreaResponsibilityModel> AreaResponsibilities { get; set; } = new();
    public List<FindingCategorySummaryModel> AvailableCategories { get; set; } = new();
}

/// <summary>
/// Shared model for finding assignment request
/// </summary>
public class FindingAssignmentModel
{
    public string FindingId { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public string? ResponsibleUserId { get; set; }
    public string? RetrospectiveAnalystUserId { get; set; }
    public string? AssignmentReason { get; set; }
    public DateTime? DueDate { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
}

/// <summary>
/// Shared model for bulk assignment operations
/// </summary>
public class BulkAssignmentModel
{
    public List<string> FindingIds { get; set; } = new();
    public string OperationType { get; set; } = string.Empty; // "assign_category", "assign_responsible", "set_due_date"
    public int? CategoryId { get; set; }
    public string? ResponsibleUserId { get; set; }
    public string? RetrospectiveAnalystUserId { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Reason { get; set; }
}

/// <summary>
/// Shared model for audit completion summary
/// </summary>
public class AuditCompletionSummaryModel
{
    public string AuditId { get; set; } = string.Empty;
    public int TotalQuestions { get; set; }
    public int AnsweredQuestions { get; set; }
    public int TotalFindings { get; set; }
    public int CategorizedFindings { get; set; }
    public int FindingsWithResponsibleUsers { get; set; }
    public int CriticalFindings { get; set; }
    public int MajorFindings { get; set; }
    public int MinorFindings { get; set; }
    public int ObservationFindings { get; set; }
    public bool IsComplete => AnsweredQuestions == TotalQuestions;
    public double CompletionPercentage => TotalQuestions > 0 ? (double)AnsweredQuestions / TotalQuestions * 100 : 0;
    public double CategorizationPercentage => TotalFindings > 0 ? (double)CategorizedFindings / TotalFindings * 100 : 0;
    public double ResponsibilityAssignmentPercentage => TotalFindings > 0 ? (double)FindingsWithResponsibleUsers / TotalFindings * 100 : 0;
}

/// <summary>
/// Shared model for feature configuration
/// </summary>
public class FeatureConfigurationModel
{
    public bool AreaBasedResponsibilityEnabled { get; set; }
    public bool FindingCategorizationEnabled { get; set; }
    public bool AutomaticAssignmentEnabled { get; set; }
    public bool RequireCategoryForFindings { get; set; }
    public bool RequireResponsibleUserForFindings { get; set; }
    public bool EnableDueDateTracking { get; set; }
    public bool EnableEscalationWorkflow { get; set; }
    public int DefaultFindingDueDays { get; set; } = 30;
    public int EscalationThresholdDays { get; set; } = 7;
}

/// <summary>
/// Shared model for validation results
/// </summary>
public class ValidationResultModel
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, List<string>> FieldErrors { get; set; } = new();

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    public void AddFieldError(string field, string error)
    {
        if (!FieldErrors.ContainsKey(field))
        {
            FieldErrors[field] = new List<string>();
        }
        FieldErrors[field].Add(error);
        IsValid = false;
    }

    public bool HasErrors => Errors.Any() || FieldErrors.Any();
    public bool HasWarnings => Warnings.Any();
}

/// <summary>
/// Shared model for export requests
/// </summary>
public class ExportRequestModel
{
    public string ExportType { get; set; } = string.Empty; // "findings", "responsibilities", "categories"
    public string Format { get; set; } = "excel"; // "excel", "csv", "pdf"
    public Dictionary<string, object> Filters { get; set; } = new();
    public List<string> IncludeFields { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeStatistics { get; set; } = false;
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
}

/// <summary>
/// Shared model for notification preferences
/// </summary>
public class NotificationPreferencesModel
{
    public bool EmailNotifications { get; set; } = true;
    public bool FindingAssignmentNotifications { get; set; } = true;
    public bool DueDateReminderNotifications { get; set; } = true;
    public bool EscalationNotifications { get; set; } = true;
    public bool AuditCompletionNotifications { get; set; } = true;
    public int ReminderDaysBefore { get; set; } = 3;
    public string PreferredNotificationTime { get; set; } = "09:00";
    public List<string> NotificationDays { get; set; } = new() { "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" };
}
