using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Users;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Entities.Users;

public class TemplateAccessAssignmentTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateAssignment()
    {
        // Arrange
        var userId = "user-123";
        var templateId = 456;
        var assignedByUserId = "admin-789";
        var description = "Test access";
        var expiresAt = DateTime.UtcNow.AddDays(30);

        // Act
        var assignment = TemplateAccessAssignment.Create(
            userId,
            templateId,
            assignedByUserId,
            description,
            expiresAt);

        // Assert
        assignment.Should().NotBeNull();
        assignment.UserId.Should().Be(userId);
        assignment.AuditTemplateId.Should().Be(templateId);
        assignment.AssignedByUserId.Should().Be(assignedByUserId);
        assignment.Description.Should().Be(description);
        assignment.ExpiresAt.Should().Be(expiresAt);
        assignment.IsActive.Should().BeTrue();
        assignment.CreatedByUserId.Should().Be(assignedByUserId);
        assignment.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        assignment.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Create_WithMinimalParameters_ShouldCreateAssignment()
    {
        // Arrange
        var userId = "user-123";
        var templateId = 456;
        var assignedByUserId = "admin-789";

        // Act
        var assignment = TemplateAccessAssignment.Create(userId, templateId, assignedByUserId);

        // Assert
        assignment.Should().NotBeNull();
        assignment.UserId.Should().Be(userId);
        assignment.AuditTemplateId.Should().Be(templateId);
        assignment.AssignedByUserId.Should().Be(assignedByUserId);
        assignment.Description.Should().BeNull();
        assignment.ExpiresAt.Should().BeNull();
        assignment.IsActive.Should().BeTrue();
    }

    [Theory]
    [InlineData(true, null, true)] // Active, no expiration
    [InlineData(true, 30, true)] // Active, expires in future
    [InlineData(true, -1, false)] // Active, expired
    [InlineData(false, null, false)] // Inactive, no expiration
    [InlineData(false, 30, false)] // Inactive, expires in future
    [InlineData(false, -1, false)] // Inactive, expired
    public void IsCurrentlyValid_WithDifferentStates_ShouldReturnExpectedResult(
        bool isActive, int? daysToExpiration, bool expectedValid)
    {
        // Arrange
        var expiresAt = daysToExpiration.HasValue 
            ? DateTime.UtcNow.AddDays(daysToExpiration.Value) 
            : (DateTime?)null;

        var assignment = TemplateAccessAssignment.Create("user-123", 456, "admin-789", null, expiresAt);
        assignment.IsActive = isActive;

        // Act
        var isValid = assignment.IsCurrentlyValid();

        // Assert
        isValid.Should().Be(expectedValid);
    }

    [Fact]
    public void Revoke_ShouldSetInactiveAndUpdateMetadata()
    {
        // Arrange
        var assignment = TemplateAccessAssignment.Create("user-123", 456, "admin-789");
        var revokedByUserId = "admin-999";
        var beforeRevoke = DateTime.UtcNow;

        // Act
        assignment.Revoke(revokedByUserId);

        // Assert
        assignment.IsActive.Should().BeFalse();
        assignment.UpdatedByUserId.Should().Be(revokedByUserId);
        assignment.UpdatedAt.Should().BeOnOrAfter(beforeRevoke);
    }

    [Fact]
    public void ExtendAccess_WithNewExpirationDate_ShouldUpdateExpirationAndMetadata()
    {
        // Arrange
        var assignment = TemplateAccessAssignment.Create("user-123", 456, "admin-789");
        var newExpirationDate = DateTime.UtcNow.AddDays(60);
        var updatedByUserId = "admin-999";
        var beforeUpdate = DateTime.UtcNow;

        // Act
        assignment.ExtendAccess(newExpirationDate, updatedByUserId);

        // Assert
        assignment.ExpiresAt.Should().Be(newExpirationDate);
        assignment.UpdatedByUserId.Should().Be(updatedByUserId);
        assignment.UpdatedAt.Should().BeOnOrAfter(beforeUpdate);
    }

    [Fact]
    public void ExtendAccess_WithNullExpiration_ShouldRemoveExpirationAndUpdateMetadata()
    {
        // Arrange
        var assignment = TemplateAccessAssignment.Create("user-123", 456, "admin-789", null, DateTime.UtcNow.AddDays(30));
        var updatedByUserId = "admin-999";
        var beforeUpdate = DateTime.UtcNow;

        // Act
        assignment.ExtendAccess(null, updatedByUserId);

        // Assert
        assignment.ExpiresAt.Should().BeNull();
        assignment.UpdatedByUserId.Should().Be(updatedByUserId);
        assignment.UpdatedAt.Should().BeOnOrAfter(beforeUpdate);
    }
}
