using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Application.Scheduling.Commands.ToggleRecurringAuditSetting;

/// <summary>
/// Handler for ToggleRecurringAuditSettingCommand
/// </summary>
public class ToggleRecurringAuditSettingCommandHandler : BaseCommandHandler<ToggleRecurringAuditSettingCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IRecurrenceCalculationService _recurrenceCalculationService;

    public ToggleRecurringAuditSettingCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IRecurrenceCalculationService recurrenceCalculationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _recurrenceCalculationService = recurrenceCalculationService;
    }

    public override async Task Handle(ToggleRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        // Get the existing recurring audit setting
        var recurringAuditSetting = await _context.RecurringAuditSettings
            .Include(r => r.RecurrenceRule)
            .FirstOrDefaultAsync(r => r.Id == request.Id && r.IsEnabled, cancellationToken);

        if (recurringAuditSetting == null)
        {
            throw new NotFoundException(nameof(RecurringAuditSetting), request.Id);
        }

        // Check for optimistic concurrency
        if (recurringAuditSetting.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The recurring audit setting has been modified by another user. Please refresh and try again.");
        }

        // Update the enabled status
        recurringAuditSetting.IsEnabled = request.IsEnabled;
        recurringAuditSetting.UpdatedAt = DateTime.UtcNow;
        recurringAuditSetting.UpdatedByUserId = _currentUserService.UserId;

        // If re-enabling, recalculate the next generation date
        if (request.IsEnabled && recurringAuditSetting.RecurrenceRule != null)
        {
            recurringAuditSetting.NextGenerationDate = _recurrenceCalculationService.CalculateNextGenerationDate(recurringAuditSetting.RecurrenceRule);
        }
        else if (!request.IsEnabled)
        {
            // Clear the next generation date when disabling
            recurringAuditSetting.NextGenerationDate = null;
        }

        await _context.SaveChangesAsync(cancellationToken);
    }


}
