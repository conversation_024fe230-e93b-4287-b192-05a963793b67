using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetAreaResponsibilities;

/// <summary>
/// Handler for getting area responsibilities
/// </summary>
public class GetAreaResponsibilitiesQueryHandler : BaseQueryHandler<GetAreaResponsibilitiesQuery, List<AreaResponsibilityDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetAreaResponsibilitiesQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<List<AreaResponsibilityDto>> Handle(GetAreaResponsibilitiesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.AreaResponsibilities
            .Include(ar => ar.Area)
                .ThenInclude(a => a.Factory)
            .Include(ar => ar.ResponsibleUser)
            .Include(ar => ar.AuditTemplate)
            .AsQueryable();

        // Apply filters
        if (request.AreaId.HasValue)
        {
            query = query.Where(ar => ar.AreaId == request.AreaId.Value);
        }

        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(ar => ar.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.ResponsibilityType.HasValue)
        {
            query = query.Where(ar => ar.ResponsibilityType == request.ResponsibilityType.Value);
        }

        if (!string.IsNullOrEmpty(request.ResponsibleUserId))
        {
            query = query.Where(ar => ar.ResponsibleUserId == request.ResponsibleUserId);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(ar => ar.IsActive == request.IsActive.Value);
        }

        // Filter by template specificity
        if (!request.IncludeTemplateSpecific && request.IncludeGeneral)
        {
            query = query.Where(ar => ar.AuditTemplateId == null);
        }
        else if (request.IncludeTemplateSpecific && !request.IncludeGeneral)
        {
            query = query.Where(ar => ar.AuditTemplateId != null);
        }

        var responsibilities = await query
            .OrderBy(ar => ar.Area.Factory.FactoryName)
            .ThenBy(ar => ar.Area.AreaName)
            .ThenBy(ar => ar.ResponsibilityType)
            .ThenBy(ar => ar.Priority)
            .ToListAsync(cancellationToken);

        return responsibilities.Select(ar => new AreaResponsibilityDto
        {
            Id = ar.Id,
            AreaId = ar.AreaId,
            AreaName = ar.Area.AreaName,
            ResponsibilityType = ar.ResponsibilityType,
            ResponsibilityTypeName = ar.ResponsibilityType.ToString(),
            ResponsibleUserId = ar.ResponsibleUserId,
            ResponsibleUserName = ar.ResponsibleUser.Username,
            ResponsibleUserFullName = $"{ar.ResponsibleUser.FirstName} {ar.ResponsibleUser.LastName}",
            AuditTemplateId = ar.AuditTemplateId,
            AuditTemplateName = ar.AuditTemplate?.TemplateName,
            IsActive = ar.IsActive,
            Description = ar.Description,
            Priority = ar.Priority,
            IsTemplateSpecific = ar.IsTemplateSpecific,
            IsEffective = ar.IsEffective,
            CreatedAt = ar.CreatedAt,
            UpdatedAt = ar.UpdatedAt,
            RecordVersion = ar.RecordVersion,
            CreatedByUserId = ar.CreatedByUserId,
            UpdatedByUserId = ar.UpdatedByUserId
        }).ToList();
    }
}
