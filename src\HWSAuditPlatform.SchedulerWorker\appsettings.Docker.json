{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "HWSAuditPlatform": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=hwsaudit-sqlserver,1433;Database=HWSAuditPlatformDb;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true", "SupportConnection": "Server=hwsaudit-sqlserver,1433;Database=HWSAuditPlatformSupport;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "ActiveDirectory": {"Domain": "hwsaudit.local", "Username": "<EMAIL>", "Password": "************", "SearchBase": "OU=HWSUsers,OU=HWSAudit,DC=hwsaudit,DC=local", "UseSSL": false, "Port": 389, "TimeoutSeconds": 30, "Server": "hwsaudit-ad"}, "FileStorage": {"Type": "Local"}, "LocalFileStorage": {"StoragePath": "/app/uploads", "BaseUrl": "https://localhost:5001"}, "AdSync": {"SyncIntervalMinutes": 30, "Enabled": true}}