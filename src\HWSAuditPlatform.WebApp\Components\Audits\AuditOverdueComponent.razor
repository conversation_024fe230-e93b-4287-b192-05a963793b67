@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditApiService
@inject ILogger<AuditOverdueComponent> <PERSON><PERSON>
@inject NavigationManager Navigation

<div class="audit-overdue">
    <div class="page-header">
        <h1>Overdue Audits</h1>
        <p class="text-muted">Audits that require immediate attention</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading overdue audits..." />

    @if (!isLoading)
    {
        @if (overdueAudits?.Any() == true)
        {
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>@overdueAudits.Count() overdue audit(s) found</strong> - These audits require immediate attention.
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Audit Template</th>
                                    <th>Location</th>
                                    <th>Due Date</th>
                                    <th>Days Overdue</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var audit in overdueAudits.OrderBy(a => a.DueDate))
                                {
                                    <tr class="table-danger">
                                        <td>
                                            <div class="fw-semibold">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</div>
                                        </td>
                                        <td>
                                            <div>@(audit.Factory?.FactoryName ?? "Unknown Factory")</div>
                                            @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                                            {
                                                <small class="text-muted">@audit.Area.AreaName</small>
                                            }
                                        </td>
                                        <td>
                                            @if (audit.DueDate.HasValue)
                                            {
                                                <span class="text-danger fw-bold">
                                                    @audit.DueDate.Value.ToString("MMM dd, yyyy")
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @if (audit.DueDate.HasValue)
                                            {
                                                var daysOverdue = (DateTime.Now - audit.DueDate.Value).Days;
                                                <span class="badge bg-danger">
                                                    @daysOverdue day(s)
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                                @audit.OverallStatus
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" @onclick="() => ViewAudit(audit.Id)"
                                                        title="View audit details">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                @if (audit.OverallStatus == AuditOverallStatus.Scheduled)
                                                {
                                                    <button class="btn btn-outline-success"
                                                            @onclick="() => StartAudit(audit.Id)"
                                                            title="Start audit">
                                                        <i class="bi bi-play"></i>
                                                    </button>
                                                }
                                                @if (audit.OverallStatus == AuditOverallStatus.PendingManagerReview)
                                                {
                                                    <button class="btn btn-outline-warning" @onclick="() => ReviewAudit(audit.Id)"
                                                            title="Review audit">
                                                        <i class="bi bi-check-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3 text-success">All Audits On Track!</h3>
                    <p class="text-muted">No overdue audits found. Great job keeping up with the schedule!</p>
                </div>
            </div>
        }
    }
</div>

<style>
    .audit-overdue {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>

@code {
    private bool isLoading = true;
    private IEnumerable<Audit>? overdueAudits;

    protected override async Task OnInitializedAsync()
    {
        await LoadOverdueAudits();
    }

    private async Task LoadOverdueAudits()
    {
        try
        {
            isLoading = true;
            
            var allAudits = await AuditApiService.GetAuditsAsync();
            if (allAudits != null)
            {
                overdueAudits = allAudits.Where(a => 
                    a.DueDate.HasValue && 
                    a.DueDate.Value < DateTime.Now && 
                    a.OverallStatus != AuditOverallStatus.Closed &&
                    a.OverallStatus != AuditOverallStatus.Cancelled);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading overdue audits");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private void ViewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}");
    }

    private void StartAudit(string auditId)
    {
        // Placeholder - requires comprehensive API client
        Logger.LogInformation("Start audit requested for {AuditId}", auditId);
    }

    private void ReviewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}/review");
    }
}
