using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.ValueObjects;

namespace HWSAuditPlatform.Application.Organization.DTOs;

/// <summary>
/// Data Transfer Object for Factory entity
/// </summary>
public class FactoryDto : AuditableDto<int>
{
    public string FactoryName { get; set; } = string.Empty;
    public string? FactoryProcess { get; set; }
    public int LocationId { get; set; }
    public string? LocationName { get; set; }
    public string? LocationCountry { get; set; }
    public Address? Address { get; set; }
    public bool IsActive { get; set; }
    
    public List<AreaSummaryDto> Areas { get; set; } = new();
    public int AreaCount => Areas.Count;
}

/// <summary>
/// Simplified Factory DTO for lists and lookups
/// </summary>
public class FactorySummaryDto
{
    public int Id { get; set; }
    public string FactoryName { get; set; } = string.Empty;
    public string? LocationName { get; set; }
    public string? LocationCountry { get; set; }
    public bool IsActive { get; set; }
    public int AreaCount { get; set; }
}
