using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Organization.Queries.GetLocations;

/// <summary>
/// Query to get all locations with their factory counts
/// </summary>
public class GetLocationsQuery : BaseQuery<List<LocationSummaryDto>>
{
    public bool? IncludeInactive { get; set; } = false;
    public string? OwnerGroupId { get; set; }
}
