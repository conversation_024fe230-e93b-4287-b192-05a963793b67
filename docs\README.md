# HWS Audit Platform - Documentation

## Overview

The HWS Audit Platform is a comprehensive audit management system designed for manufacturing quality audits with offline capabilities, Active Directory integration, and enterprise-scale requirements. This documentation provides complete guidance for understanding, developing, and deploying the platform.

## Documentation Structure

### 📋 **Core Documentation**

## Documentation Sections

### 🏗️ [Architecture](architecture/README.md)
Comprehensive system architecture and design documentation.
- [System Overview](architecture/system-overview.md) - High-level architecture and technology stack
- [Application Layer](architecture/application-layer.md) - CQRS, MediatR, and application workflows
- [Infrastructure Layer](architecture/infrastructure-layer.md) - Database, external services, and repositories
- [User Hierarchy](architecture/user-hierarchy.md) - User roles, permissions, and access control

### 🔌 [API Documentation](api/README.md)
Complete API reference and integration guides.
- [Authentication](api/authentication.md) - User authentication and management APIs
- [Audit Management](api/audit-management.md) - Complete audit lifecycle APIs
- [Organization Management](api/organization-management.md) - Organizational structure APIs
- [Templates & Scheduling](api/templates-scheduling.md) - Template and scheduling APIs
- [Files & Health](api/files-health.md) - File operations and health monitoring
- [Integration Guide](api/integration-guide.md) - Complete API integration guide

### 🚀 [Deployment](deployment/README.md)
Deployment and setup documentation.
- [Docker Setup](deployment/docker-setup.md) - Containerized development environment
- [External Storage](deployment/external-storage.md) - External file storage configuration
- [Scalar UI Setup](deployment/scalar-ui-setup.md) - API documentation interface setup

### 👨‍💻 [Development](development/README.md)
Developer resources and guidelines.
- [Getting Started](development/getting-started.md) - Complete developer onboarding guide
- [Testing Guide](development/testing-guide.md) - Testing procedures and guidelines
- [Debugging](development/debugging.md) - Debugging tools and procedures
- [Error Handling](development/error-handling.md) - Error handling patterns and best practices

### 📖 [User Guides](user-guides/README.md)
End-user documentation and system guides.
- [System Overview](user-guides/system-overview.md) - System capabilities and features
- [Technical Requirements](user-guides/technical-requirements.md) - Technical requirements specification

## Quick Start

### Prerequisites

- .NET 10 SDK
- SQL Server (LocalDB, Express, or full version)
- Visual Studio 2024 or VS Code
- Git
- External drive (D:) for file storage (recommended)

### Setup Instructions

```bash
# 1. Clone the repository
git clone https://github.com/company/hws-audit-platform.git
cd hws-audit-platform

# 2. Set up external file storage (recommended)
.\scripts\setup-external-storage.ps1

# 3. Restore packages
dotnet restore

# 4. Set up database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# 5. Run the application
dotnet run --project src/HWSAuditPlatform.ApiService
```

### Configuration

Update `appsettings.Development.json` with your local settings:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "D:\\uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

## Architecture Summary

### Clean Architecture Layers

```
┌─────────────────────────────────────┐
│        Presentation (API)           │  ← Controllers, HTTP handling
├─────────────────────────────────────┤
│         Application                 │  ← Use cases, CQRS, validation
├─────────────────────────────────────┤
│           Domain                    │  ← Business logic, entities
├─────────────────────────────────────┤
│       Infrastructure               │  ← Database, external services
└─────────────────────────────────────┘
```

### Key Features

- **CQRS with MediatR**: Clean separation of read/write operations
- **Entity Framework Core**: Advanced ORM with migrations and configurations
- **Offline Sync**: PWA support with CUID and conflict resolution
- **File Storage**: Local file system storage
- **Active Directory**: Enterprise user and group synchronization
- **Health Monitoring**: Comprehensive health checks and logging
- **Clean Architecture**: Testable, maintainable, and scalable design

## Technology Stack

### Backend

- **.NET 10**: Latest framework with performance improvements
- **ASP.NET Core**: Web API framework
- **Entity Framework Core 8**: Object-relational mapping
- **SQL Server**: Primary database
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Input validation

### Infrastructure

- **Local File Storage**: Secure file system storage
- **System.DirectoryServices**: Active Directory integration
- **Serilog**: Structured logging

### Development

- **xUnit**: Unit testing framework
- **FluentAssertions**: Test assertions
- **Moq**: Mocking framework
- **Swagger/OpenAPI**: API documentation

## Project Structure

```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns
│   └── HWSAuditPlatform.ApiService/      # Web API controllers
├── tests/
│   ├── HWSAuditPlatform.Domain.Tests/
│   ├── HWSAuditPlatform.Application.Tests/
│   ├── HWSAuditPlatform.Infrastructure.Tests/
│   └── HWSAuditPlatform.Integration.Tests/
├── docs/                                 # Documentation
└── scripts/                             # Deployment scripts
```

## Key Concepts

### Domain-Driven Design

- **Entities**: Core business objects with identity
- **Value Objects**: Immutable objects without identity
- **Domain Events**: Business events that trigger side effects
- **Aggregates**: Consistency boundaries for related entities

### CQRS Pattern

- **Commands**: Modify application state (CreateUser, UpdateAudit)
- **Queries**: Return data without side effects (GetUsers, GetAudits)
- **Handlers**: Process commands and queries
- **DTOs**: Data transfer objects for API communication

### Offline Synchronization

- **CUID**: Client-generated unique identifiers
- **Optimistic Concurrency**: RecordVersion for conflict detection
- **Conflict Resolution**: Automatic and manual conflict handling
- **PWA Support**: Progressive Web App capabilities

## Development Workflow

### Adding New Features

1. **Domain First**: Create or modify domain entities
2. **Application Layer**: Add commands/queries, handlers, DTOs
3. **Infrastructure**: Add entity configurations, migrations
4. **API Layer**: Create controllers and endpoints
5. **Testing**: Unit tests, integration tests, validation

### Database Changes

1. Modify entity or add new entity
2. Add/update entity configuration
3. Create migration: `dotnet ef migrations add <MigrationName>`
4. Review generated migration
5. Update database: `dotnet ef database update`

### Testing Strategy

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows
- **API Tests**: Test HTTP endpoints and responses
- **Performance Tests**: Load testing for scalability

## Deployment

### Development

- Local SQL Server or LocalDB
- Local file storage
- Development Active Directory (optional)

### Production

- SQL Server Database
- Local file storage with backup strategy
- Production Active Directory
- IIS deployment
- Application Insights monitoring

## Security

### Authentication & Authorization

- JWT token-based authentication
- Role-based access control (Admin, Manager, Auditor)
- Claims-based authorization
- Active Directory integration

### Data Protection

- Encryption at rest and in transit
- Audit trails for all changes
- Data retention policies
- Secure file storage

## Monitoring & Observability

### Health Checks

- Database connectivity
- Active Directory connectivity
- File storage accessibility
- External service dependencies

### Logging

- Structured logging with Serilog
- File-based and console logging
- Performance monitoring
- Error tracking and alerting

## Support & Contributing

### Getting Help

- Review documentation thoroughly
- Check existing issues and solutions
- Follow coding standards and conventions
- Write comprehensive tests

### Contributing Guidelines

- Follow clean architecture principles
- Maintain test coverage above 80%
- Document new features and changes
- Follow Git workflow and PR process

## Related Resources

- **Domain Model**: See `src/HWSAuditPlatform.Domain/` for business entities
- **Database Schema**: See `docs/database.dbml` for complete schema
- **API Documentation**: Scalar UI available at `/scalar/v1` endpoint
- **Technical Requirements**: See [Technical Requirements](user-guides/technical-requirements.md)

---

This documentation provides comprehensive guidance for understanding, developing, and maintaining the HWS Audit Platform. For specific implementation details, refer to the individual layer documentation files.
