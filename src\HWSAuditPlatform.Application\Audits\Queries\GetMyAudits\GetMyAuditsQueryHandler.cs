using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Queries.GetMyAudits;

/// <summary>
/// Handler for GetMyAuditsQuery
/// </summary>
public class GetMyAuditsQueryHandler : BaseQueryHandler<GetMyAuditsQuery, PaginatedResult<AuditSummaryDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetMyAuditsQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<PaginatedResult<AuditSummaryDto>> Handle(GetMyAuditsQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.AssignedToUserId == currentUserId)
            .AsQueryable();

        // Apply filters
        if (request.Status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == request.Status.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(a => a.AreaId == request.AreaId.Value);
        }

        if (request.IsOverdue.HasValue && request.IsOverdue.Value)
        {
            var now = DateTime.UtcNow;
            query = query.Where(a => a.DueDate.HasValue && a.DueDate.Value < now && 
                a.OverallStatus != Domain.Enums.AuditOverallStatus.Closed && 
                a.OverallStatus != Domain.Enums.AuditOverallStatus.Cancelled);
        }

        // Apply sorting
        var ascending = request.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase);
        query = request.SortBy.ToLower() switch
        {
            "scheduleddate" => ascending ? query.OrderBy(a => a.ScheduledDate) : query.OrderByDescending(a => a.ScheduledDate),
            "duedate" => ascending ? query.OrderBy(a => a.DueDate) : query.OrderByDescending(a => a.DueDate),
            "status" => ascending ? query.OrderBy(a => a.OverallStatus) : query.OrderByDescending(a => a.OverallStatus),
            "templatename" => ascending ? query.OrderBy(a => a.AuditTemplate.TemplateName) : query.OrderByDescending(a => a.AuditTemplate.TemplateName),
            "factoryname" => ascending ? query.OrderBy(a => a.Factory.FactoryName) : query.OrderByDescending(a => a.Factory.FactoryName),
            _ => ascending ? query.OrderBy(a => a.ScheduledDate) : query.OrderByDescending(a => a.ScheduledDate)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var audits = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(a => new AuditSummaryDto
            {
                Id = a.Id,
                AuditTemplateName = a.AuditTemplate.TemplateName,
                AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                ScheduledDate = a.ScheduledDate,
                DueDate = a.DueDate,
                OverallStatus = a.OverallStatus,
                FactoryName = a.Factory.FactoryName,
                AreaName = a.Area.AreaName,
                OverallScore = a.OverallScore,
                IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow && 
                    a.OverallStatus != Domain.Enums.AuditOverallStatus.Closed && 
                    a.OverallStatus != Domain.Enums.AuditOverallStatus.Cancelled
            })
            .ToListAsync(cancellationToken);

        return new PaginatedResult<AuditSummaryDto>(audits, totalCount, request.PageNumber, request.PageSize);
    }
}
