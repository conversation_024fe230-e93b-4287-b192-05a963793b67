#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Tests the HWS Audit Platform application connections to Docker containers
.DESCRIPTION
    This script tests the application's ability to connect to SQL Server and Active Directory
    containers using the application's configuration settings.
#>

# Set error action preference
$ErrorActionPreference = "Stop"

# Helper function for colored console output
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Test SQL Server connection using application connection string
function Test-ApplicationSqlConnection {
    try {
        Write-ColorOutput "Testing SQL Server connection with application settings..." "Yellow"
        
        # Set environment to Docker
        $env:ASPNETCORE_ENVIRONMENT = "Docker"
        
        # Test connection using Entity Framework
        $testResult = dotnet run --project src/HWSAuditPlatform.ApiService --no-build -- --test-db-connection 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "  [✓] Application can connect to SQL Server" "Green"
            return $true
        }
        else {
            Write-ColorOutput "  [✗] Application failed to connect to SQL Server" "Red"
            Write-ColorOutput "  Error: $testResult" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "  [✗] Error testing SQL Server connection: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Test Active Directory connection using application settings
function Test-ApplicationAdConnection {
    try {
        Write-ColorOutput "Testing Active Directory connection with application settings..." "Yellow"
        
        # Set environment to Docker
        $env:ASPNETCORE_ENVIRONMENT = "Docker"
        
        # Test AD connection using the application
        $testResult = dotnet run --project src/HWSAuditPlatform.ApiService --no-build -- --test-ad-connection 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "  [✓] Application can connect to Active Directory" "Green"
            return $true
        }
        else {
            Write-ColorOutput "  [✗] Application failed to connect to Active Directory" "Red"
            Write-ColorOutput "  Error: $testResult" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "  [✗] Error testing Active Directory connection: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Test container network connectivity
function Test-ContainerNetworkConnectivity {
    try {
        Write-ColorOutput "Testing container network connectivity..." "Yellow"
        
        # Test SQL Server container connectivity
        $sqlTest = docker exec hwsaudit-sqlserver ping -c 1 hwsaudit-ad 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "  [✓] SQL Server can reach Active Directory container" "Green"
        }
        else {
            Write-ColorOutput "  [✗] SQL Server cannot reach Active Directory container" "Red"
            return $false
        }
        
        # Test AD container connectivity
        $adTest = docker exec hwsaudit-ad ping -c 1 hwsaudit-sqlserver 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "  [✓] Active Directory can reach SQL Server container" "Green"
        }
        else {
            Write-ColorOutput "  [✗] Active Directory cannot reach SQL Server container" "Red"
            return $false
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "  [✗] Error testing container network connectivity: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Verify configuration files
function Test-ConfigurationFiles {
    try {
        Write-ColorOutput "Verifying Docker configuration files..." "Yellow"
        
        $configFiles = @(
            "src/HWSAuditPlatform.ApiService/appsettings.Docker.json",
            "src/HWSAuditPlatform.Web/appsettings.Docker.json",
            "src/HWSAuditPlatform.SchedulerWorker/appsettings.Docker.json"
        )
        
        $allValid = $true
        
        foreach ($configFile in $configFiles) {
            if (Test-Path $configFile) {
                $content = Get-Content $configFile -Raw | ConvertFrom-Json
                
                # Check SQL Server connection string
                if ($content.ConnectionStrings.DefaultConnection -like "*hwsaudit-sqlserver*") {
                    Write-ColorOutput "  [✓] $configFile has correct SQL Server container name" "Green"
                }
                else {
                    Write-ColorOutput "  [✗] $configFile missing correct SQL Server container name" "Red"
                    $allValid = $false
                }
                
                # Check Active Directory configuration
                if ($content.ActiveDirectory.Server -eq "hwsaudit-ad") {
                    Write-ColorOutput "  [✓] $configFile has correct AD container name" "Green"
                }
                elseif ($content.ActiveDirectory) {
                    Write-ColorOutput "  [!] $configFile missing AD Server configuration" "Yellow"
                }
            }
            else {
                Write-ColorOutput "  [✗] Configuration file not found: $configFile" "Red"
                $allValid = $false
            }
        }
        
        return $allValid
    }
    catch {
        Write-ColorOutput "  [✗] Error verifying configuration files: $($_.Exception.Message)" "Red"
        return $false
    }
}

# --- Main Execution ---
try {
    Write-ColorOutput "HWS Audit Platform Docker Connection Test" "Blue"
    Write-ColorOutput "=========================================" "Blue"
    Write-Host ""
    
    $allTestsPassed = $true
    
    # Check if containers are running first
    Write-ColorOutput "1. Checking Container Status:" "Blue"
    $sqlRunning = docker inspect --format='{{.State.Running}}' hwsaudit-sqlserver 2>$null
    $adRunning = docker inspect --format='{{.State.Running}}' hwsaudit-ad 2>$null
    
    if ($sqlRunning -eq "true") {
        Write-ColorOutput "  [✓] SQL Server container is running" "Green"
    }
    else {
        Write-ColorOutput "  [✗] SQL Server container is not running" "Red"
        $allTestsPassed = $false
    }
    
    if ($adRunning -eq "true") {
        Write-ColorOutput "  [✓] Active Directory container is running" "Green"
    }
    else {
        Write-ColorOutput "  [✗] Active Directory container is not running" "Red"
        $allTestsPassed = $false
    }
    Write-Host ""
    
    # Test configuration files
    Write-ColorOutput "2. Configuration Verification:" "Blue"
    if (-not (Test-ConfigurationFiles)) { $allTestsPassed = $false }
    Write-Host ""
    
    # Test network connectivity
    Write-ColorOutput "3. Network Connectivity:" "Blue"
    if (-not (Test-ContainerNetworkConnectivity)) { $allTestsPassed = $false }
    Write-Host ""
    
    # Build the application first
    Write-ColorOutput "4. Building Application:" "Blue"
    Write-ColorOutput "Building application for testing..." "Yellow"
    dotnet build src/HWSAuditPlatform.ApiService --configuration Debug --no-restore
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "  [✓] Application built successfully" "Green"
    }
    else {
        Write-ColorOutput "  [✗] Application build failed" "Red"
        $allTestsPassed = $false
    }
    Write-Host ""
    
    # Overall status
    if ($allTestsPassed) {
        Write-ColorOutput "✅ All Docker connection tests passed!" "Green"
        Write-Host ""
        Write-ColorOutput "The application is properly configured to connect to Docker containers." "Blue"
        Write-ColorOutput "You can now run the application with:" "Blue"
        Write-Host "  `$env:ASPNETCORE_ENVIRONMENT='Docker'"
        Write-Host "  dotnet run --project src/HWSAuditPlatform.ApiService"
        Write-Host ""
        Write-Host "Or use the AppHost:"
        Write-Host "  dotnet run --project src/HWSAuditPlatform.AppHost"
        exit 0
    }
    else {
        Write-ColorOutput "❌ Some connection tests failed." "Red"
        Write-Host ""
        Write-ColorOutput "Troubleshooting steps:" "Blue"
        Write-Host "  1. Ensure Docker containers are running: ./docker-start.ps1"
        Write-Host "  2. Check container health: ./docker-health-check.ps1"
        Write-Host "  3. Review container logs: docker-compose logs -f"
        Write-Host "  4. For a clean restart: ./docker-start.ps1 -Clean"
        exit 1
    }
}
catch {
    Write-ColorOutput "A critical error occurred during connection testing: $($_.Exception.Message)" "Red"
    exit 1
}
