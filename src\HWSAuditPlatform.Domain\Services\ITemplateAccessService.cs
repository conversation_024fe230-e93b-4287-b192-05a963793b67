using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Services;

/// <summary>
/// Domain service for managing template access control
/// </summary>
public interface ITemplateAccessService
{
    /// <summary>
    /// Checks if a user has access to a specific audit template
    /// </summary>
    /// <param name="userId">User ID to check</param>
    /// <param name="templateId">Template ID to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has access, false otherwise</returns>
    Task<bool> HasTemplateAccessAsync(string userId, int templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all templates accessible to a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of accessible templates</returns>
    Task<IEnumerable<AuditTemplate>> GetAccessibleTemplatesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all users who have access to a specific template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of users with access</returns>
    Task<IEnumerable<User>> GetUsersWithTemplateAccessAsync(int templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Grants template access to a user
    /// </summary>
    /// <param name="userId">User ID to grant access to</param>
    /// <param name="templateId">Template ID to grant access to</param>
    /// <param name="assignedByUserId">User granting the access</param>
    /// <param name="description">Optional description</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created access assignment</returns>
    Task<TemplateAccessAssignment> GrantTemplateAccessAsync(
        string userId, 
        int templateId, 
        string assignedByUserId, 
        string? description = null, 
        DateTime? expiresAt = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Revokes template access from a user
    /// </summary>
    /// <param name="userId">User ID to revoke access from</param>
    /// <param name="templateId">Template ID to revoke access to</param>
    /// <param name="revokedByUserId">User revoking the access</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if access was revoked, false if no access existed</returns>
    Task<bool> RevokeTemplateAccessAsync(string userId, int templateId, string revokedByUserId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates that a user can create audits in a specific organizational scope
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="factoryId">Factory ID</param>
    /// <param name="areaId">Area ID (optional)</param>
    /// <param name="subAreaId">SubArea ID (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has access to the scope, false otherwise</returns>
    Task<bool> HasOrganizationalAccessAsync(
        string userId, 
        int factoryId, 
        int? areaId = null, 
        int? subAreaId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all organizational scopes accessible to a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of process owner assignments</returns>
    Task<IEnumerable<ProcessOwnerAssignment>> GetAccessibleScopesAsync(string userId, CancellationToken cancellationToken = default);
}
