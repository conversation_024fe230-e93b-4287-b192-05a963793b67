using Blazored.LocalStorage;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.WebAuditPWA.Models;
using HWSAuditPlatform.Domain.Entities.Audits;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Implementation of sync service for offline data synchronization
/// </summary>
public class SyncServiceImplementation : ISyncService
{
    private readonly IAuditApiService _auditService;
    private readonly IOfflineStorageService _offlineStorage;
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<SyncServiceImplementation> _logger;
    private readonly IPwaService _pwaService;
    
    private const string SyncStatusKey = "sync_status";
    private const string LastSyncKey = "last_sync_time";
    private const string SyncInProgressKey = "sync_in_progress";
    
    private bool _isSyncInProgress = false;

    private readonly IBackgroundSyncService _backgroundSyncService;

    public SyncServiceImplementation(
        IAuditApiService auditService,
        IOfflineStorageService offlineStorage,
        ILocalStorageService localStorage,
        IPwaService pwaService,
        IBackgroundSyncService backgroundSyncService,
        ILogger<SyncServiceImplementation> logger)
    {
        _auditService = auditService;
        _offlineStorage = offlineStorage;
        _localStorage = localStorage;
        _pwaService = pwaService;
        _backgroundSyncService = backgroundSyncService;
        _logger = logger;
    }

    public async Task<Result> SyncAsync()
    {
        if (_isSyncInProgress)
        {
            return Result.Failure("Sync is already in progress");
        }

        try
        {
            _isSyncInProgress = true;
            await _localStorage.SetItemAsync(SyncInProgressKey, true);
            
            _logger.LogInformation("Starting data synchronization");

            // Check if we're online
            var isOnline = await _pwaService.IsOnlineAsync();
            if (!isOnline)
            {
                return Result.Failure("Cannot sync while offline");
            }

            var syncResult = new SyncResult();

            // Sync audit answers
            await SyncAuditAnswers(syncResult);

            // Sync attachments
            await SyncAttachments(syncResult);

            // Sync completed audits
            await SyncCompletedAudits(syncResult);

            // Update last sync time
            await _localStorage.SetItemAsync(LastSyncKey, DateTime.UtcNow);

            _logger.LogInformation("Sync completed successfully. Synced: {AnswerCount} answers, {AttachmentCount} attachments, {AuditCount} audits",
                syncResult.SyncedAnswers, syncResult.SyncedAttachments, syncResult.SyncedAudits);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data synchronization");
            return Result.Failure($"Sync failed: {ex.Message}");
        }
        finally
        {
            _isSyncInProgress = false;
            await _localStorage.SetItemAsync(SyncInProgressKey, false);
        }
    }

    public async Task<bool> IsSyncInProgressAsync()
    {
        try
        {
            var inProgress = await _localStorage.GetItemAsync<bool>(SyncInProgressKey);
            return inProgress || _isSyncInProgress;
        }
        catch
        {
            return _isSyncInProgress;
        }
    }

    public async Task<int> GetPendingSyncItemsCountAsync()
    {
        try
        {
            var count = 0;

            // Count pending audit answers
            var offlineAudits = await _offlineStorage.GetOfflineAuditsAsync();
            foreach (var audit in offlineAudits)
            {
                var answers = await _offlineStorage.GetOfflineAuditAnswersAsync(audit.Id);
                count += answers.Count;
            }

            // Count pending attachments
            var pendingAttachments = await GetPendingAttachmentsAsync();
            count += pendingAttachments.Count;

            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending sync items count");
            return 0;
        }
    }

    private async Task SyncAuditAnswers(SyncResult syncResult)
    {
        try
        {
            var offlineAudits = await _offlineStorage.GetOfflineAuditsAsync();
            
            foreach (var audit in offlineAudits)
            {
                var answers = await _offlineStorage.GetOfflineAuditAnswersAsync(audit.Id);
                if (answers.Any())
                {
                    _logger.LogInformation("Syncing {Count} answers for audit {AuditId}", answers.Count, audit.Id);
                    
                    var result = await _auditService.SubmitAuditAnswersAsync(audit.Id, answers);
                    if (result.IsSuccess)
                    {
                        // Remove synced answers from offline storage
                        await _offlineStorage.StoreAuditAnswersAsync(audit.Id, new List<AuditAnswer>());
                        syncResult.SyncedAnswers += answers.Count;
                        
                        _logger.LogInformation("Successfully synced {Count} answers for audit {AuditId}", answers.Count, audit.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to sync answers for audit {AuditId}: {Error}", audit.Id, result.ErrorMessage);
                        syncResult.FailedAnswers += answers.Count;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing audit answers");
        }
    }

    private async Task SyncAttachments(SyncResult syncResult)
    {
        try
        {
            var pendingAttachments = await GetPendingAttachmentsAsync();
            
            foreach (var attachment in pendingAttachments)
            {
                _logger.LogInformation("Syncing attachment {AttachmentId} for audit {AuditId}", attachment.Id, attachment.AuditId);
                
                var result = await _auditService.UploadAttachmentAsync(
                    attachment.AuditId, 
                    attachment.QuestionId.ToString(), 
                    attachment.Data, 
                    attachment.FileName, 
                    attachment.ContentType);
                
                if (result.IsSuccess && !string.IsNullOrEmpty(result.Data))
                {
                    // Mark attachment as uploaded
                    attachment.IsUploaded = true;
                    attachment.UploadedFileId = result.Data;
                    await UpdateAttachmentAsync(attachment);
                    
                    syncResult.SyncedAttachments++;
                    _logger.LogInformation("Successfully synced attachment {AttachmentId}", attachment.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to sync attachment {AttachmentId}: {Error}", attachment.Id, result.ErrorMessage);
                    syncResult.FailedAttachments++;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing attachments");
        }
    }

    private async Task SyncCompletedAudits(SyncResult syncResult)
    {
        try
        {
            var offlineAudits = await _offlineStorage.GetOfflineAuditsAsync();
            var completedAudits = offlineAudits.Where(a => a.OverallStatus == Domain.Enums.AuditOverallStatus.Submitted).ToList();
            
            foreach (var audit in completedAudits)
            {
                _logger.LogInformation("Syncing completed audit {AuditId}", audit.Id);
                
                var result = await _auditService.CompleteAuditAsync(audit.Id);
                if (result.IsSuccess)
                {
                    // Remove from offline storage
                    await _offlineStorage.RemoveOfflineAuditAsync(audit.Id);
                    syncResult.SyncedAudits++;
                    
                    _logger.LogInformation("Successfully synced completed audit {AuditId}", audit.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to sync completed audit {AuditId}: {Error}", audit.Id, result.ErrorMessage);
                    syncResult.FailedAudits++;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing completed audits");
        }
    }

    private async Task<List<OfflineAttachment>> GetPendingAttachmentsAsync()
    {
        return await _offlineStorage.GetPendingAttachmentsAsync();
    }

    private async Task UpdateAttachmentAsync(OfflineAttachment attachment)
    {
        await _offlineStorage.UpdateAttachmentAsync(attachment);
    }

    public async Task<SyncStatus> GetSyncStatusAsync()
    {
        try
        {
            var isOnline = await _pwaService.IsOnlineAsync();
            var isSyncInProgress = await IsSyncInProgressAsync();
            var pendingItems = await GetPendingSyncItemsCountAsync();
            var lastSyncTime = await _localStorage.GetItemAsync<DateTime?>(LastSyncKey);
            
            return new SyncStatus
            {
                IsOnline = isOnline,
                IsSyncInProgress = isSyncInProgress,
                PendingAudits = 0, // TODO: Implement
                PendingAnswers = pendingItems,
                PendingAttachments = (await GetPendingAttachmentsAsync()).Count,
                LastSyncTime = lastSyncTime,
                LastSyncError = null // TODO: Store and retrieve last sync error
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync status");
            return new SyncStatus
            {
                IsOnline = false,
                IsSyncInProgress = false,
                LastSyncError = ex.Message
            };
        }
    }

    public async Task<Result> QueueForBackgroundSyncAsync<T>(T data, string syncType)
    {
        try
        {
            _logger.LogInformation("Queuing data for background sync: {SyncType}", syncType);

            var success = await _backgroundSyncService.QueueForBackgroundSyncAsync(data, syncType);

            if (success)
            {
                _logger.LogInformation("Successfully queued data for background sync: {SyncType}", syncType);
                return Result.Success();
            }
            else
            {
                _logger.LogWarning("Failed to queue data for background sync: {SyncType}", syncType);
                return Result.Failure("Failed to queue data for background sync");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queuing data for background sync: {SyncType}", syncType);
            return Result.Failure($"Error queuing data for background sync: {ex.Message}");
        }
    }

    public async Task<Result> RequestManualSyncAsync(string syncType)
    {
        try
        {
            _logger.LogInformation("Requesting manual sync: {SyncType}", syncType);

            await _backgroundSyncService.RequestManualSyncAsync(syncType);

            _logger.LogInformation("Manual sync requested successfully: {SyncType}", syncType);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting manual sync: {SyncType}", syncType);
            return Result.Failure($"Error requesting manual sync: {ex.Message}");
        }
    }

    public async Task<List<BackgroundSyncItem>> GetPendingSyncItemsAsync(string? syncType = null)
    {
        try
        {
            return await _backgroundSyncService.GetPendingSyncItemsAsync(syncType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending sync items");
            return new List<BackgroundSyncItem>();
        }
    }
}

/// <summary>
/// Sync operation result
/// </summary>
public class SyncResult
{
    public int SyncedAnswers { get; set; }
    public int FailedAnswers { get; set; }
    public int SyncedAttachments { get; set; }
    public int FailedAttachments { get; set; }
    public int SyncedAudits { get; set; }
    public int FailedAudits { get; set; }
    
    public int TotalSynced => SyncedAnswers + SyncedAttachments + SyncedAudits;
    public int TotalFailed => FailedAnswers + FailedAttachments + FailedAudits;
    public bool HasFailures => TotalFailed > 0;
}
