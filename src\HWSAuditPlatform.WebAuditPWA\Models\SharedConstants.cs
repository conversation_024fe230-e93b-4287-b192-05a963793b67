namespace HWSAuditPlatform.WebAuditPWA.Models;

/// <summary>
/// Shared constants for PWA-specific functionality
/// </summary>
public static class SharedConstants
{
    /// <summary>
    /// Constants for offline storage
    /// </summary>
    public static class OfflineStorage
    {
        public const string AuditDataKey = "audit_data";
        public const string AttachmentsKey = "attachments";
        public const string SyncQueueKey = "sync_queue";
        public const string UserPreferencesKey = "user_preferences";
        public const string CacheKey = "cache_data";
        public const int MaxOfflineAudits = 50;
        public const int MaxOfflineAttachmentSizeMB = 100;
        public const int DataRetentionDays = 30;
    }

    /// <summary>
    /// Constants for camera functionality
    /// </summary>
    public static class Camera
    {
        public const int DefaultImageQuality = 80;
        public const int MaxImageWidth = 1920;
        public const int MaxImageHeight = 1080;
        public const int ThumbnailWidth = 200;
        public const int ThumbnailHeight = 150;
        public const string DefaultImageFormat = "image/jpeg";
        public const long MaxImageSizeBytes = 10 * 1024 * 1024; // 10MB

        public static class Quality
        {
            public const int Low = 50;
            public const int Medium = 75;
            public const int High = 90;
        }
    }

    /// <summary>
    /// Constants for sync operations
    /// </summary>
    public static class Sync
    {
        public const int DefaultSyncIntervalMinutes = 15;
        public const int MinSyncIntervalMinutes = 5;
        public const int MaxSyncIntervalMinutes = 60;
        public const int MaxRetryAttempts = 3;
        public const int RetryDelaySeconds = 30;
        public const int SyncTimeoutSeconds = 120;
        public const int BatchSize = 10;
    }

    /// <summary>
    /// Constants for PWA notifications
    /// </summary>
    public static class Notifications
    {
        public const int MaxNotificationDisplayTime = 5000; // 5 seconds
        public const int MaxNotificationQueue = 10;
        public const string DefaultNotificationIcon = "/icon-192.png";
        public const string DefaultNotificationBadge = "/icon-192.png";

        public static class Actions
        {
            public const string View = "view";
            public const string Dismiss = "dismiss";
            public const string Sync = "sync";
            public const string Retry = "retry";
        }
    }

    /// <summary>
    /// Constants for finding categorization in PWA
    /// </summary>
    public static class FindingCategories
    {
        public const int MaxCategoriesDisplayed = 20;
        public const int CategoryGridColumns = 2;
        public const string DefaultCategoryColor = "#6c757d";
        public const string DefaultCategoryIcon = "fas fa-tag";

        /// <summary>
        /// Icon mapping for PWA display
        /// </summary>
        public static readonly Dictionary<string, string> IconMapping = new()
        {
            ["warning-triangle"] = "fas fa-exclamation-triangle",
            ["exclamation-circle"] = "fas fa-exclamation-circle",
            ["info-circle"] = "fas fa-info-circle",
            ["shield-alt"] = "fas fa-shield-alt",
            ["tools"] = "fas fa-tools",
            ["cog"] = "fas fa-cog",
            ["bug"] = "fas fa-bug",
            ["fire"] = "fas fa-fire",
            ["bolt"] = "fas fa-bolt",
            ["eye"] = "fas fa-eye",
            ["wrench"] = "fas fa-wrench",
            ["clipboard-check"] = "fas fa-clipboard-check",
            ["hard-hat"] = "fas fa-hard-hat",
            ["industry"] = "fas fa-industry",
            ["thermometer"] = "fas fa-thermometer-half"
        };
    }

    /// <summary>
    /// Constants for responsibility display in PWA
    /// </summary>
    public static class Responsibilities
    {
        public const int MaxResponsibilitiesDisplayed = 10;
        public const bool ShowCompactViewByDefault = true;
        public const bool ShowEmptyStateByDefault = true;

        /// <summary>
        /// Responsibility type icons for PWA
        /// </summary>
        public static readonly Dictionary<string, string> ResponsibilityIcons = new()
        {
            ["CorrectiveActionOwner"] = "fas fa-user-check",
            ["RetrospectiveAnalyst"] = "fas fa-chart-line",
            ["FindingReviewer"] = "fas fa-user-edit",
            ["EscalationContact"] = "fas fa-exclamation-triangle"
        };

        /// <summary>
        /// Responsibility type display names
        /// </summary>
        public static readonly Dictionary<string, string> ResponsibilityDisplayNames = new()
        {
            ["CorrectiveActionOwner"] = "Corrective Actions",
            ["RetrospectiveAnalyst"] = "Retrospective Analysis",
            ["FindingReviewer"] = "Finding Review",
            ["EscalationContact"] = "Escalation Contact"
        };
    }

    /// <summary>
    /// Constants for audit execution in PWA
    /// </summary>
    public static class AuditExecution
    {
        public const int AutoSaveIntervalSeconds = 30;
        public const int MaxFindingsPerQuestion = 10;
        public const int MaxFindingLength = 2000;
        public const int MaxCommentsLength = 1000;
        public const bool RequireFindingsForNoAnswers = true;
        public const bool AutoAdvanceOnAnswer = false;
        public const bool ShowProgressIndicator = true;

        /// <summary>
        /// Touch gesture constants
        /// </summary>
        public static class Gestures
        {
            public const int SwipeThreshold = 100; // pixels
            public const double SwipeVelocityThreshold = 0.3; // pixels per ms
            public const int LongPressThreshold = 500; // ms
            public const int DoubleTapThreshold = 300; // ms
        }
    }

    /// <summary>
    /// Constants for UI theming in PWA
    /// </summary>
    public static class Theming
    {
        public const string DefaultTheme = "industrial";
        public const string DefaultAccentColor = "#14b8a6"; // Teal
        public const string DefaultBackgroundColor = "#1a1a1a"; // Dark
        public const string DefaultTextColor = "#ffffff"; // White

        /// <summary>
        /// Available themes
        /// </summary>
        public static readonly Dictionary<string, string> AvailableThemes = new()
        {
            ["light"] = "Light Theme",
            ["dark"] = "Dark Theme",
            ["industrial"] = "Industrial Theme",
            ["high-contrast"] = "High Contrast Theme"
        };

        /// <summary>
        /// Severity level colors
        /// </summary>
        public static readonly Dictionary<string, string> SeverityColors = new()
        {
            ["Critical"] = "#dc3545",
            ["Major"] = "#fd7e14",
            ["Minor"] = "#ffc107",
            ["Observation"] = "#6c757d"
        };
    }

    /// <summary>
    /// Constants for validation in PWA
    /// </summary>
    public static class Validation
    {
        public const int MaxValidationErrors = 10;
        public const int ValidationDebounceMs = 500;
        public const bool ShowValidationSummary = true;
        public const bool ValidateOnChange = true;
        public const bool ValidateOnBlur = true;

        /// <summary>
        /// Validation error messages
        /// </summary>
        public static class ErrorMessages
        {
            public const string RequiredField = "This field is required.";
            public const string InvalidFormat = "Invalid format.";
            public const string TooLong = "Text is too long.";
            public const string TooShort = "Text is too short.";
            public const string InvalidNumber = "Invalid number.";
            public const string InvalidDate = "Invalid date.";
            public const string RequiredFindings = "At least one finding is required for 'No' answers.";
            public const string RequiredCategory = "Category selection is required.";
            public const string RequiredResponsible = "Responsible user assignment is required.";
        }
    }

    /// <summary>
    /// Constants for performance optimization
    /// </summary>
    public static class Performance
    {
        public const int VirtualScrollThreshold = 50;
        public const int ImageLazyLoadThreshold = 200; // pixels
        public const int ComponentUpdateDebounceMs = 100;
        public const int SearchDebounceMs = 300;
        public const int MaxConcurrentRequests = 5;
        public const int RequestTimeoutMs = 30000; // 30 seconds
    }

    /// <summary>
    /// Constants for accessibility
    /// </summary>
    public static class Accessibility
    {
        public const int MinTouchTargetSize = 44; // pixels
        public const double MinContrastRatio = 4.5;
        public const int FocusIndicatorWidth = 2; // pixels
        public const string FocusIndicatorColor = "#007bff";
        public const bool ReduceMotionByDefault = false;
        public const bool HighContrastByDefault = false;

        /// <summary>
        /// ARIA labels for common elements
        /// </summary>
        public static class AriaLabels
        {
            public const string NextQuestion = "Go to next question";
            public const string PreviousQuestion = "Go to previous question";
            public const string SaveAndExit = "Save progress and exit audit";
            public const string CompleteAudit = "Complete and submit audit";
            public const string CapturePhoto = "Capture photo evidence";
            public const string SelectCategory = "Select finding category";
            public const string ViewResponsibilities = "View assigned responsibilities";
        }
    }
}
