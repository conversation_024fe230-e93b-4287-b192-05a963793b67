using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;

/// <summary>
/// Handler for GetAuditTemplatesQuery
/// </summary>
public class GetAuditTemplatesQueryHandler : BaseQueryHandler<GetAuditTemplatesQuery, PaginatedResult<AuditTemplateSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAuditTemplatesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<AuditTemplateSummaryDto>> Handle(GetAuditTemplatesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.AuditTemplates
            .Include(t => t.Questions.Where(q => q.IsActive))
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(t =>
                EF.Functions.Like(t.TemplateName, searchPattern) ||
                (t.Description != null && EF.Functions.Like(t.Description, searchPattern)));
        }

        if (request.IsPublished.HasValue)
        {
            query = query.Where(t => t.IsPublished == request.IsPublished.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(t => t.IsActive == request.IsActive.Value);
        }

        if (request.CanBeUsed.HasValue && request.CanBeUsed.Value)
        {
            query = query.Where(t => t.IsPublished && t.IsActive);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "description" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.Description)
                : query.OrderBy(t => t.Description),
            "version" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.Version)
                : query.OrderBy(t => t.Version),
            "ispublished" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.IsPublished)
                : query.OrderBy(t => t.IsPublished),
            "createdat" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.CreatedAt)
                : query.OrderBy(t => t.CreatedAt),
            "updatedat" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.UpdatedAt)
                : query.OrderBy(t => t.UpdatedAt),
            _ => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(t => t.TemplateName)
                : query.OrderBy(t => t.TemplateName)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and project to DTO
        var templates = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(t => new AuditTemplateSummaryDto
            {
                Id = t.Id,
                TemplateName = t.TemplateName,
                Description = t.Description,
                Version = t.Version,
                IsPublished = t.IsPublished,
                IsActive = t.IsActive,
                FullName = $"{t.TemplateName} v{t.Version}",
                CanBeUsed = t.IsPublished && t.IsActive,
                QuestionCount = t.Questions.Count(q => q.IsActive),
                CreatedAt = t.CreatedAt,
                UpdatedAt = t.UpdatedAt
            })
            .ToListAsync(cancellationToken);

        return new PaginatedResult<AuditTemplateSummaryDto>(templates, totalCount, request.PageNumber, request.PageSize);
    }
}
