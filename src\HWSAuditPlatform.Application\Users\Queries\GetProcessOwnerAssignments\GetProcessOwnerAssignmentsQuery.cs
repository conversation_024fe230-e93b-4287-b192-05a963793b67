using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.Application.Users.Queries.GetProcessOwnerAssignments;

/// <summary>
/// Query to get process owner assignments with optional filtering
/// </summary>
public class GetProcessOwnerAssignmentsQuery : BaseQuery<PaginatedResult<ProcessOwnerAssignmentDto>>
{
    public string? ProcessOwnerUserId { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public bool? IsActive { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}
