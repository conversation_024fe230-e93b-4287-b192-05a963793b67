#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration Variables ---
DOMAIN_NAME="hwsaudit.local"
USER_PASSWORD="HWSAudit123!"

# Derive the LDAP Base DN from the domain name (e.g., hwsaudit.local -> DC=hwsaudit,DC=local)
BASE_DN=$(echo "$DOMAIN_NAME" | sed 's/\./,DC=/g' | sed 's/^/DC=/')

# --- Path Definitions ---
# This is the fix: We define two types of paths.

# 1. Full Distinguished Names (DNs) - Used for CREATING the OUs themselves.
HWS_OU_DN="OU=HWSAudit,${BASE_DN}"
USERS_OU_DN="OU=HWSUsers,${HWS_OU_DN}"
GROUPS_OU_DN="OU=HWSGroups,${HWS_OU_DN}"
SERVICE_ACCOUNTS_OU_DN="OU=ServiceAccounts,${HWS_OU_DN}"

# 2. Relative Distinguished Names (RDNs) - Used for ADDING users/groups TO the OUs.
#    This is the path relative to the domain root.
HWS_OU_RDN="OU=HWSAudit"
USERS_OU_RDN="OU=HWSUsers,${HWS_OU_RDN}"
GROUPS_OU_RDN="OU=HWSGroups,${HWS_OU_RDN}"
SERVICE_ACCOUNTS_OU_RDN="OU=ServiceAccounts,${HWS_OU_RDN}"


# --- Wait for Samba to be Ready ---
echo "Waiting for Samba AD to be ready..."
check_samba_ready() {
    samba-tool domain info "${DOMAIN_NAME}" >/dev/null 2>&1
    return $?
}
for i in {1..30}; do
    if check_samba_ready; then
        echo "Samba AD is ready!"
        break
    fi
    echo "Waiting for Samba AD... (attempt $i/30)"
    sleep 10
done
if ! check_samba_ready; then
    echo "ERROR: Samba AD failed to start properly after 5 minutes."
    exit 1
fi

echo "Setting up HWS Audit Platform users and groups..."

# --- Create Organizational Units (using the full DNs) ---
echo "Creating Organizational Units..."
samba-tool ou create "${HWS_OU_DN}" || true
samba-tool ou create "${USERS_OU_DN}" || true
samba-tool ou create "${GROUPS_OU_DN}" || true
samba-tool ou create "${SERVICE_ACCOUNTS_OU_DN}" || true

# --- Create Groups (using the relative RDNs) ---
echo "Creating Groups..."
samba-tool group add "HWSAudit-Administrators" --groupou="${GROUPS_OU_RDN}" || true
samba-tool group add "HWSAudit-Auditors" --groupou="${GROUPS_OU_RDN}" || true
samba-tool group add "HWSAudit-Managers" --groupou="${GROUPS_OU_RDN}" || true
samba-tool group add "HWSAudit-Viewers" --groupou="${GROUPS_OU_RDN}" || true

# --- Create Service Account (using the relative RDN) ---
echo "Creating Service Account..."
samba-tool user create hwsaudit-service "${USER_PASSWORD}" \
    --userou="${SERVICE_ACCOUNTS_OU_RDN}" \
    --given-name="HWS Audit" \
    --surname="Service Account" \
    --mail-address="hwsaudit-service@${DOMAIN_NAME}" \
    --description="Service account for HWS Audit Platform" || true
samba-tool user setexpiry hwsaudit-service --noexpiry || true

# --- Create Test Users (using the relative RDN) ---
echo "Creating Test Users..."
samba-tool user create admin.user "${USER_PASSWORD}" --userou="${USERS_OU_RDN}" --given-name="Admin" --surname="User" --mail-address="admin.user@${DOMAIN_NAME}" --description="Test Administrator User" || true
samba-tool user create auditor.user "${USER_PASSWORD}" --userou="${USERS_OU_RDN}" --given-name="Auditor" --surname="User" --mail-address="auditor.user@${DOMAIN_NAME}" --description="Test Auditor User" || true
samba-tool user create manager.user "${USER_PASSWORD}" --userou="${USERS_OU_RDN}" --given-name="Manager" --surname="User" --mail-address="manager.user@${DOMAIN_NAME}" --description="Test Manager User" || true
samba-tool user create viewer.user "${USER_PASSWORD}" --userou="${USERS_OU_RDN}" --given-name="Viewer" --surname="User" --mail-address="viewer.user@${DOMAIN_NAME}" --description="Test Viewer User" || true

# --- Add Users to Groups ---
echo "Adding users to groups..."
samba-tool group addmembers "HWSAudit-Administrators" admin.user || true
samba-tool group addmembers "HWSAudit-Auditors" auditor.user || true
samba-tool group addmembers "HWSAudit-Managers" manager.user || true
samba-tool group addmembers "HWSAudit-Viewers" viewer.user || true

# --- Final Output ---
echo ""
echo "✅ Active Directory setup completed successfully!"
echo "================================================="
echo ""
echo "Domain: ${DOMAIN_NAME}"
echo "Administrator: Administrator / ${USER_PASSWORD}"
echo ""
echo "Test Users Created:"
echo "  • admin.user@${DOMAIN_NAME} (Password: ${USER_PASSWORD})"
echo "  • auditor.user@${DOMAIN_NAME} (Password: ${USER_PASSWORD})"
echo "  • manager.user@${DOMAIN_NAME} (Password: ${USER_PASSWORD})"
echo "  • viewer.user@${DOMAIN_NAME} (Password: ${USER_PASSWORD})"
echo ""
echo "Service Account: hwsaudit-service@${DOMAIN_NAME} (Password: ${USER_PASSWORD})"