#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Starts the HWS Audit Platform Docker environment
.DESCRIPTION
    This script starts the Docker containers for SQL Server and Active Directory,
    initializes the databases, and sets up the development environment.
#>

param(
    [switch]$Clean,
    [switch]$SkipBuild,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Helper function for colored console output
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Checks if the Docker daemon is running
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Waits for a container to report a 'healthy' status
function Wait-ForContainer {
    param(
        [string]$ContainerName,
        [int]$TimeoutSeconds = 240 # Increased timeout for safety
    )

    Write-ColorOutput "Waiting for $ContainerName to be healthy..." "Yellow"

    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    $interval = 5

    while ($stopwatch.Elapsed.TotalSeconds -lt $TimeoutSeconds) {
        try {
            $health = docker inspect --format='{{.State.Health.Status}}' $ContainerName
            
            # --- THIS IS THE FIX ---
            # Use .Trim() to remove any leading/trailing whitespace or newlines
            # from the command output before comparing the string.
            if ($health.Trim() -eq "healthy") {
                $stopwatch.Stop()
                Write-ColorOutput "`n$ContainerName is healthy! (Took $($stopwatch.Elapsed.ToString('mm\:ss')))" "Green"
                return $true
            }
        }
        catch {
            # This can happen if the container is still starting up and not inspectable
            Write-Host "." -NoNewline
        }

        Start-Sleep $interval
        Write-Host "." -NoNewline
    }

    Write-ColorOutput "`nTimeout waiting for $ContainerName to be healthy. Check logs with 'docker-compose logs $ContainerName'" "Red"
    return $false
}


# --- Main Execution ---
try {
    Write-ColorOutput "Starting HWS Audit Platform Docker Environment" "Blue"
    Write-ColorOutput "=================================================" "Blue"

    if (-not (Test-DockerRunning)) {
        Write-ColorOutput "Docker is not running. Please start Docker Desktop and try again." "Red"
        exit 1
    }

    if ($Clean) {
        Write-ColorOutput "Cleaning up existing containers, networks, and volumes..." "Yellow"
        docker-compose down -v --remove-orphans
    }

    if (Test-Path ".env.docker") {
        Write-ColorOutput "Found .env.docker file (will be used by docker-compose)" "Yellow"
    }

    Write-ColorOutput "Starting Docker containers via docker-compose..." "Yellow"
    docker-compose up -d

    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Failed to start Docker containers. Check the output above." "Red"
        exit 1
    }

    # With the fix, this wait will now succeed once the container is healthy.
    if (-not (Wait-ForContainer "hwsaudit-sqlserver" 240)) {
        Write-ColorOutput "A required container failed to become healthy. Please check the logs." "Red"
        docker-compose logs
        exit 1
    }

    Write-ColorOutput "All containers are healthy and running." "Green"

    # ... (rest of your script remains the same) ...
    
    # Run database migrations
    Write-ColorOutput "Running database migrations..." "Yellow"
    $env:ASPNETCORE_ENVIRONMENT = "Docker"
    dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService --configuration Docker

    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Database migration failed. The environment is running, but the database may be out of date." "Yellow"
    }
    else {
        Write-ColorOutput "Database migrations completed successfully." "Green"
    }

    # Setup Active Directory users
    Write-ColorOutput "Setting up Active Directory users..." "Yellow"
    docker exec hwsaudit-ad bash /scripts/setup-ad.sh

    # --- Final Success Message ---
    Write-Host ""
    Write-ColorOutput "HWS Audit Platform Docker Environment Started Successfully!" "Green"
    # ... (rest of your script) ...

}
catch {
    Write-ColorOutput "AN ERROR OCCURRED: $($_.Exception.Message)" "Red"
    Write-ColorOutput "-------------------------------------------------" "Red"
    Write-ColorOutput "Attempting to show container status and logs..." "Yellow"
    docker-compose ps
    docker-compose logs --tail=50
    exit 1
}