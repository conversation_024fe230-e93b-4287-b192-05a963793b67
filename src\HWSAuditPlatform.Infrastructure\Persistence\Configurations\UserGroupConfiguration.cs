using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for UserGroup entity
/// </summary>
public class UserGroupConfiguration : IEntityTypeConfiguration<UserGroup>
{
    public void Configure(EntityTypeBuilder<UserGroup> builder)
    {
        builder.ToTable("hwsap_usr_groups");

        // Primary Key
        builder.HasKey(ug => ug.Id);
        builder.Property(ug => ug.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(ug => ug.GroupName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(ug => ug.Description)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ug => ug.AdObjectGuid)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(ug => ug.IsAdSynced)
            .IsRequired()
            .HasDefaultValue(false);

        // Auditable properties
        builder.Property(ug => ug.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ug => ug.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ug => ug.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(ug => ug.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(ug => ug.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(ug => ug.GroupName)
            .IsUnique()
            .HasDatabaseName("IX_UserGroups_GroupName");

        builder.HasIndex(ug => ug.AdObjectGuid)
            .IsUnique()
            .HasDatabaseName("IX_UserGroups_AdObjectGuid")
            .HasFilter("[AdObjectGuid] IS NOT NULL");

        // Relationships
        builder.HasOne(ug => ug.CreatedByUser)
            .WithMany()
            .HasForeignKey(ug => ug.CreatedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(ug => ug.UpdatedByUser)
            .WithMany()
            .HasForeignKey(ug => ug.UpdatedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Ignore domain events and duplicate navigation properties
        builder.Ignore(ug => ug.DomainEvents);
        builder.Ignore(ug => ug.UserGroupMembers); // Use Members instead
    }
}

/// <summary>
/// Entity Framework configuration for UserGroupMember entity
/// </summary>
public class UserGroupMemberConfiguration : IEntityTypeConfiguration<UserGroupMember>
{
    public void Configure(EntityTypeBuilder<UserGroupMember> builder)
    {
        builder.ToTable("hwsap_usr_group_members");

        // Composite Primary Key (ignore the Id property and use composite key)
        builder.HasKey(ugm => new { ugm.UserGroupId, ugm.UserId });

        // Ignore the Id property since we're using composite key
        builder.Ignore(ugm => ugm.Id);

        // Properties
        builder.Property(ugm => ugm.UserGroupId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(ugm => ugm.UserId)
            .HasMaxLength(25)
            .IsRequired();

        // Relationships
        builder.HasOne(ugm => ugm.UserGroup)
            .WithMany(ug => ug.Members)
            .HasForeignKey(ugm => ugm.UserGroupId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ugm => ugm.User)
            .WithMany(u => u.UserGroupMemberships)
            .HasForeignKey(ugm => ugm.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ugm => ugm.UserGroupId)
            .HasDatabaseName("IX_UserGroupMembers_UserGroupId");

        builder.HasIndex(ugm => ugm.UserId)
            .HasDatabaseName("IX_UserGroupMembers_UserId");
    }
}
