<div class="audit-execution-footer">
    <div class="container-fluid py-2">
        <div class="footer-content">
            <!-- Left Side - Save & Exit -->
            <div class="footer-left">
                <button class="btn btn-outline-secondary btn-save-exit" @onclick="HandleSaveAndExit">
                    <i class="fas fa-save me-2"></i>
                    <span class="btn-text">Save & Exit</span>
                </button>
            </div>
            
            <!-- Right Side - Progress & Actions -->
            <div class="footer-right">
                @if (IsAuditComplete)
                {
                    <button class="btn btn-success btn-complete" @onclick="HandleCompleteAudit" disabled="@IsSubmitting">
                        @if (IsSubmitting)
                        {
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        }
                        <i class="fas fa-check-circle me-2"></i>
                        <span class="btn-text">Complete Audit</span>
                    </button>
                }
                else
                {
                    <div class="completion-info">
                        @if (RemainingRequiredQuestions > 0)
                        {
                            <div class="remaining-info">
                                <span class="remaining-count">@RemainingRequiredQuestions</span>
                                <span class="remaining-text">required remaining</span>
                            </div>
                        }
                        <button class="btn btn-primary btn-next" 
                                @onclick="HandleNextQuestion" 
                                disabled="@IsLastQuestion">
                            <span class="btn-text">Next Question</span>
                            <i class="fas fa-chevron-right ms-2"></i>
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public bool IsAuditComplete { get; set; }
    [Parameter] public bool IsSubmitting { get; set; }
    [Parameter] public bool IsLastQuestion { get; set; }
    [Parameter] public int RemainingRequiredQuestions { get; set; }
    [Parameter] public EventCallback OnSaveAndExit { get; set; }
    [Parameter] public EventCallback OnCompleteAudit { get; set; }
    [Parameter] public EventCallback OnNextQuestion { get; set; }

    private async Task HandleSaveAndExit()
    {
        if (OnSaveAndExit.HasDelegate)
        {
            await OnSaveAndExit.InvokeAsync();
        }
    }

    private async Task HandleCompleteAudit()
    {
        if (OnCompleteAudit.HasDelegate)
        {
            await OnCompleteAudit.InvokeAsync();
        }
    }

    private async Task HandleNextQuestion()
    {
        if (OnNextQuestion.HasDelegate)
        {
            await OnNextQuestion.InvokeAsync();
        }
    }
}

<style>
    .audit-execution-footer {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-top: 2px solid var(--industrial-teal);
        box-shadow: 0 -4px 12px var(--industrial-shadow);
        /* Remove sticky positioning to give more space for content */
        position: static;
        flex-shrink: 0;
        /* Remove margin-top: auto to let footer follow content naturally */
    }

    /* Only use sticky positioning on very large screens where space is abundant */
    @@media (min-width: 1200px) {
        .audit-execution-footer {
            position: sticky;
            bottom: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            margin-top: auto; /* Push to bottom on large screens with sticky positioning */
        }
    }

    /* Responsive adjustments */
    @@media (max-width: 767px) {
        .audit-execution-footer {
            position: static;
            bottom: auto;
            z-index: auto;
            backdrop-filter: none;
            margin-top: 0;
        }
    }

    .footer-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .footer-left,
    .footer-right {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Save & Exit Button */
    .btn-save-exit {
        background: transparent;
        border: 2px solid var(--industrial-border-light);
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        padding: 1rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        min-height: 56px;
        display: flex;
        align-items: center;
    }

    .btn-save-exit:hover {
        background: var(--industrial-light-gray);
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--industrial-shadow);
    }

    /* Complete Audit Button */
    .btn-complete {
        background: linear-gradient(135deg, var(--industrial-green) 0%, #2f855a 100%);
        border: 2px solid var(--industrial-green);
        color: white;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        padding: 1rem 2.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        min-height: 56px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
    }

    .btn-complete:hover:not(:disabled) {
        background: linear-gradient(135deg, #2f855a 0%, var(--industrial-green) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(56, 161, 105, 0.4);
    }

    .btn-complete:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    /* Next Question Button */
    .btn-next {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        border: 2px solid var(--industrial-teal);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        padding: 1rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        min-height: 56px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 12px var(--industrial-glow);
    }

    .btn-next:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--industrial-teal-dark) 0%, var(--industrial-teal) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px var(--industrial-glow-heavy);
    }

    .btn-next:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* Completion Info */
    .completion-info {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .remaining-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 0.5rem 1rem;
        background: rgba(229, 62, 62, 0.1);
        border: 1px solid var(--industrial-red);
        border-radius: 8px;
    }

    .remaining-count {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--industrial-red);
        line-height: 1;
    }

    .remaining-text {
        font-size: 0.8rem;
        color: var(--industrial-text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 0.25rem;
    }

    /* Loading Spinner */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.125rem;
    }

    /* Responsive Design */
    @@media (max-width: 991px) {
        .footer-content {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .footer-left,
        .footer-right {
            justify-content: center;
        }

        .completion-info {
            flex-direction: column;
            gap: 0.75rem;
            align-items: stretch;
        }

        .btn-save-exit,
        .btn-complete,
        .btn-next {
            width: 100%;
            justify-content: center;
        }
    }

    @@media (max-width: 767px) {
        .audit-execution-footer .container-fluid {
            padding: 0.5rem 1rem;
        }

        .footer-content {
            gap: 0.75rem;
        }

        .btn-save-exit,
        .btn-complete,
        .btn-next {
            padding: 1rem 1.5rem;
            font-size: 0.95rem;
            min-height: 48px;
        }

        .remaining-info {
            padding: 0.5rem 1rem;
        }

        .remaining-count {
            font-size: 1.1rem;
        }

        .remaining-text {
            font-size: 0.75rem;
        }

        .btn-text {
            font-size: 0.9rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .btn-save-exit,
        .btn-complete,
        .btn-next {
            min-height: 48px;
            padding: 1.25rem 2rem;
            font-size: 1rem;
        }

        .remaining-info {
            padding: 0.75rem 1.25rem;
        }
    }

    /* Extra small devices - very compact */
    @@media (max-width: 480px) {
        .audit-execution-footer .container-fluid {
            padding: 0.5rem 0.75rem;
        }

        .footer-content {
            gap: 0.5rem;
        }

        .btn-save-exit,
        .btn-complete,
        .btn-next {
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            min-height: 52px;
        }

        /* Ensure button text and icons are always visible */
        .btn-save-exit .btn-text,
        .btn-complete .btn-text,
        .btn-next .btn-text {
            display: inline !important;
        }

        .btn-save-exit i,
        .btn-complete i,
        .btn-next i {
            display: inline !important;
        }

        .remaining-info {
            padding: 0.375rem 0.75rem;
        }

        .remaining-count {
            font-size: 1rem;
        }

        .remaining-text {
            font-size: 0.7rem;
        }

        .btn-text {
            font-size: 0.9rem;
        }
    }

    /* Animation for completion state */
    .btn-complete {
        animation: pulse-success 2s infinite;
    }

    @@keyframes pulse-success {
        0% {
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }
        50% {
            box-shadow: 0 4px 20px rgba(56, 161, 105, 0.5);
        }
        100% {
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }
    }
</style>
