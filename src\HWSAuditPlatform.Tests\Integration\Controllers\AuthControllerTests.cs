using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using HWSAuditPlatform.ApiService.Controllers;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using Microsoft.VisualStudio.TestPlatform.TestHost;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Tests.Integration.Controllers;

public class AuthControllerTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ApplicationDbContext _context;

    public AuthControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the real database context
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb_Auth_" + Guid.NewGuid());
                });
            });
        });

        _client = _factory.CreateClient();
        
        // Get the test database context
        var scope = _factory.Services.CreateScope();
        _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Seed test data
        SeedTestData().Wait();
    }

    private async Task SeedTestData()
    {
        // Create test roles
        var devAdminRole = new Role
        {
            RoleName = UserRole.DevAdmin,
            Description = "Developer Administrator"
        };
        var auditorRole = new Role
        {
            RoleName = UserRole.Auditor,
            Description = "Auditor"
        };

        await _context.Roles.AddRangeAsync(devAdminRole, auditorRole);
        
        // Create test user
        var testUser = User.Create(
            username: "test.user",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: auditorRole.Id,
            factoryId: 1,
            isActive: true,
            adObjectGuid: "test-user-guid-123",
            adDistinguishedName: "CN=Test User,OU=Users,DC=hwsaudit,DC=local",
            createdByUserId: "system");

        await _context.Users.AddAsync(testUser);
        await _context.SaveChangesAsync();
    }

    [Fact]
    public async Task TestLogin_WithValidCredentials_ShouldReturnToken()
    {
        // Arrange
        var loginRequest = new LoginRequest
        {
            Username = "admin.user",
            Password = "HWSAudit123!"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/auth/test-login", loginRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var jsonDoc = JsonDocument.Parse(content);
        
        jsonDoc.RootElement.GetProperty("success").GetBoolean().Should().BeTrue();
        jsonDoc.RootElement.GetProperty("data").GetProperty("token").GetString().Should().NotBeNullOrEmpty();
        jsonDoc.RootElement.GetProperty("data").GetProperty("username").GetString().Should().Be("admin.user");
    }

    [Fact]
    public async Task RefreshToken_WithValidToken_ShouldReturnNewToken()
    {
        // Arrange - First login to get a token
        var loginRequest = new LoginRequest
        {
            Username = "admin.user",
            Password = "HWSAudit123!"
        };

        var loginResponse = await _client.PostAsJsonAsync("/api/v1/auth/test-login", loginRequest);
        loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var loginContent = await loginResponse.Content.ReadAsStringAsync();
        var loginJsonDoc = JsonDocument.Parse(loginContent);
        var token = loginJsonDoc.RootElement.GetProperty("data").GetProperty("token").GetString();

        // Add authorization header
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        // Act - Call refresh token endpoint
        var refreshResponse = await _client.PostAsync("/api/v1/auth/refresh", null);

        // Assert
        refreshResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var refreshContent = await refreshResponse.Content.ReadAsStringAsync();
        var refreshJsonDoc = JsonDocument.Parse(refreshContent);
        
        refreshJsonDoc.RootElement.GetProperty("success").GetBoolean().Should().BeTrue();
        var newToken = refreshJsonDoc.RootElement.GetProperty("data").GetProperty("token").GetString();
        newToken.Should().NotBeNullOrEmpty();
        newToken.Should().NotBe(token); // New token should be different from old token
        
        // Verify the response contains expected user information
        refreshJsonDoc.RootElement.GetProperty("data").GetProperty("username").GetString().Should().Be("admin.user");
        refreshJsonDoc.RootElement.GetProperty("data").GetProperty("role").GetString().Should().Be("DevAdmin");
    }

    [Fact]
    public async Task RefreshToken_WithoutToken_ShouldReturnUnauthorized()
    {
        // Act - Call refresh token endpoint without authorization header
        var response = await _client.PostAsync("/api/v1/auth/refresh", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task RefreshToken_WithInvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange - Add invalid authorization header
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "invalid-token");

        // Act - Call refresh token endpoint
        var response = await _client.PostAsync("/api/v1/auth/refresh", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _client?.Dispose();
    }
}
