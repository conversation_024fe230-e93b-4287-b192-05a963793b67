namespace HWSAuditPlatform.ApiService.Models;

/// <summary>
/// Standard API error response model
/// </summary>
public class ApiErrorResponse
{
    /// <summary>
    /// HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Error title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Detailed error message
    /// </summary>
    public string Detail { get; set; } = string.Empty;

    /// <summary>
    /// Validation errors (if applicable)
    /// </summary>
    public IDictionary<string, string[]>? Errors { get; set; }

    /// <summary>
    /// Trace identifier for debugging
    /// </summary>
    public string? TraceId { get; set; }

    /// <summary>
    /// Timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a bad request error response
    /// </summary>
    public static ApiErrorResponse BadRequest(string detail, IDictionary<string, string[]>? errors = null)
    {
        return new ApiErrorResponse
        {
            StatusCode = 400,
            Title = "Bad Request",
            Detail = detail,
            Errors = errors
        };
    }

    /// <summary>
    /// Creates a not found error response
    /// </summary>
    public static ApiErrorResponse NotFound(string detail)
    {
        return new ApiErrorResponse
        {
            StatusCode = 404,
            Title = "Not Found",
            Detail = detail
        };
    }

    /// <summary>
    /// Creates a conflict error response
    /// </summary>
    public static ApiErrorResponse Conflict(string detail)
    {
        return new ApiErrorResponse
        {
            StatusCode = 409,
            Title = "Conflict",
            Detail = detail
        };
    }
}

/// <summary>
/// Standard API success response model
/// </summary>
/// <typeparam name="T">Type of data being returned</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// The response data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Success message
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Timestamp of the response
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a successful response
    /// </summary>
    public static ApiResponse<T> SuccessResult(T data, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        };
    }

    /// <summary>
    /// Creates a successful response without data
    /// </summary>
    public static ApiResponse<T> SuccessResult(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message
        };
    }
}

/// <summary>
/// File upload result model
/// </summary>
public class FileUploadResult
{
    /// <summary>
    /// Path to the uploaded file
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// Original filename
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Content type of the file
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Upload timestamp
    /// </summary>
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
}
