using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateTemplateVersion;

/// <summary>
/// Handler for CreateTemplateVersionCommand
/// </summary>
public class CreateTemplateVersionCommandHandler : BaseCommandHandler<CreateTemplateVersionCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateTemplateVersionCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateTemplateVersionCommand request, CancellationToken cancellationToken)
    {
        // Get the original template with all its questions and question groups
        var originalTemplate = await _context.AuditTemplates
            .Include(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(t => t.QuestionGroups.Where(g => g.IsActive))
                .ThenInclude(g => g.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .FirstOrDefaultAsync(t => t.Id == request.OriginalTemplateId && t.IsActive, cancellationToken);

        if (originalTemplate == null)
        {
            throw new NotFoundException(nameof(AuditTemplate), request.OriginalTemplateId);
        }

        // Check if the original template is published (only published templates should be versioned)
        if (!originalTemplate.IsPublished)
        {
            throw new InvalidOperationException("Cannot create a new version of an unpublished template. Edit the template directly instead.");
        }

        // Find the highest version number for this template name
        var maxVersion = await _context.AuditTemplates
            .Where(t => t.TemplateName == originalTemplate.TemplateName && t.IsActive)
            .MaxAsync(t => t.Version, cancellationToken);

        // Create the new template version
        var newTemplate = new AuditTemplate
        {
            TemplateName = request.NewTemplateName ?? originalTemplate.TemplateName,
            Description = request.NewDescription ?? originalTemplate.Description,
            Version = maxVersion + 1,
            IsPublished = false, // New versions start as unpublished
            IsActive = true,
            CreatedByUserId = _currentUserService.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.AuditTemplates.AddAsync(newTemplate, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Copy question groups
        var questionGroupMapping = new Dictionary<int, int>();
        foreach (var originalGroup in originalTemplate.QuestionGroups.Where(g => g.IsActive))
        {
            var newGroup = new QuestionGroup
            {
                AuditTemplateId = newTemplate.Id,
                GroupName = originalGroup.GroupName,
                Description = originalGroup.Description,
                DisplayOrder = originalGroup.DisplayOrder,
                IsActive = true,
                CreatedByUserId = _currentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.QuestionGroups.AddAsync(newGroup, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
            
            questionGroupMapping[originalGroup.Id] = newGroup.Id;
        }

        // Copy questions and their options
        var questionMapping = new Dictionary<int, int>();
        var questionsToProcess = originalTemplate.Questions.Where(q => q.IsActive).OrderBy(q => q.DisplayOrder).ToList();
        
        foreach (var originalQuestion in questionsToProcess)
        {
            var newQuestion = new Question
            {
                AuditTemplateId = newTemplate.Id,
                QuestionGroupId = originalQuestion.QuestionGroupId.HasValue && questionGroupMapping.ContainsKey(originalQuestion.QuestionGroupId.Value) 
                    ? questionGroupMapping[originalQuestion.QuestionGroupId.Value] 
                    : null,
                QuestionText = originalQuestion.QuestionText,
                QuestionType = originalQuestion.QuestionType,
                DisplayOrder = originalQuestion.DisplayOrder,
                IsRequired = originalQuestion.IsRequired,
                Weight = originalQuestion.Weight,
                HelpText = originalQuestion.HelpText,
                ParentQuestionId = null, // Will be set in second pass for conditional questions
                TriggerAnswerValue = originalQuestion.TriggerAnswerValue,
                SeverityLevel = originalQuestion.SeverityLevel,
                EvidenceRequired = originalQuestion.EvidenceRequired,
                AllowedEvidenceTypes = originalQuestion.AllowedEvidenceTypes,
                IsActive = true,
                CreatedByUserId = _currentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.Questions.AddAsync(newQuestion, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
            
            questionMapping[originalQuestion.Id] = newQuestion.Id;

            // Copy question options
            foreach (var originalOption in originalQuestion.Options.Where(o => o.IsActive))
            {
                var newOption = new QuestionOption
                {
                    QuestionId = newQuestion.Id,
                    OptionText = originalOption.OptionText,
                    OptionValue = originalOption.OptionValue,
                    IsCorrectOption = originalOption.IsCorrectOption,
                    DisplayOrder = originalOption.DisplayOrder,
                    IsActive = true,
                    CreatedByUserId = _currentUserService.UserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _context.QuestionOptions.AddAsync(newOption, cancellationToken);
            }
        }

        // Second pass: Update parent question IDs for conditional questions
        foreach (var originalQuestion in questionsToProcess.Where(q => q.ParentQuestionId.HasValue))
        {
            if (questionMapping.ContainsKey(originalQuestion.Id) && 
                questionMapping.ContainsKey(originalQuestion.ParentQuestionId.Value))
            {
                var newQuestion = await _context.Questions.FindAsync(questionMapping[originalQuestion.Id]);
                if (newQuestion != null)
                {
                    newQuestion.ParentQuestionId = questionMapping[originalQuestion.ParentQuestionId.Value];
                }
            }
        }

        await _context.SaveChangesAsync(cancellationToken);

        return newTemplate.Id;
    }
}
