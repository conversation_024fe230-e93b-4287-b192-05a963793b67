#!/usr/bin/env pwsh
# Script to start only the Active Directory container for development testing

Write-Host "Starting Active Directory container for development..." -ForegroundColor Green

# Check if Dock<PERSON> is running
try {
    docker version | Out-Null
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop first."
    exit 1
}

# Stop any existing AD container
Write-Host "Stopping existing AD container..." -ForegroundColor Yellow
docker stop hwsaudit-ad 2>$null
docker rm hwsaudit-ad 2>$null

# Create network if it doesn't exist
Write-Host "Creating Docker network..." -ForegroundColor Yellow
docker network create hwsaudit-network --subnet=**********/16 2>$null

# Start only the AD container
Write-Host "Starting Active Directory container..." -ForegroundColor Yellow
docker run -d `
    --name hwsaudit-ad `
    --network hwsaudit-network `
    --ip *********** `
    -p 389:389 `
    -p 636:636 `
    -p 88:88 `
    -p 88:88/udp `
    -p 464:464 `
    -p 464:464/udp `
    -p 53:53 `
    -p 53:53/udp `
    -e DOMAIN=hwsaudit.local `
    -e DOMAINPASS=HWSAudit123! `
    -e DNSFORWARDER=******* `
    -e NOCOMPLEXITY=true `
    -e INSECURELDAP=true `
    -v samba_data:/var/lib/samba `
    -v samba_config:/etc/samba/external `
    --restart unless-stopped `
    nowsci/samba-domain:latest

if ($LASTEXITCODE -eq 0) {
    Write-Host "Active Directory container started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Container Details:" -ForegroundColor Cyan
    Write-Host "  Name: hwsaudit-ad" -ForegroundColor White
    Write-Host "  Domain: hwsaudit.local" -ForegroundColor White
    Write-Host "  LDAP Port: 389" -ForegroundColor White
    Write-Host "  LDAPS Port: 636" -ForegroundColor White
    Write-Host "  Admin Password: HWSAudit123!" -ForegroundColor White
    Write-Host ""
    Write-Host "The AD server may take 2-3 minutes to fully initialize." -ForegroundColor Yellow
    Write-Host "You can check the logs with: docker logs -f hwsaudit-ad" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To test AD connectivity:" -ForegroundColor Cyan
    Write-Host "  docker exec hwsaudit-ad samba-tool domain info hwsaudit.local" -ForegroundColor White
} else {
    Write-Error "Failed to start Active Directory container"
    exit 1
}
