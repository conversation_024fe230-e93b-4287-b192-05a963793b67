using FluentValidation;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteQuestion;

/// <summary>
/// Validator for DeleteQuestionCommand
/// </summary>
public class DeleteQuestionCommandValidator : AbstractValidator<DeleteQuestionCommand>
{
    private readonly IApplicationDbContext _context;

    public DeleteQuestionCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.QuestionId)
            .GreaterThan(0).WithMessage("Question ID must be greater than 0")
            .MustAsync(BeValidQuestion).WithMessage("Question does not exist");

        RuleFor(x => x.TemplateId)
            .GreaterThan(0).WithMessage("Template ID must be greater than 0")
            .MustAsync(BeValidTemplate).WithMessage("Template does not exist or is published");

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0).WithMessage("Record version must be greater than 0");

        RuleFor(x => x)
            .MustAsync(QuestionBelongsToTemplate).WithMessage("Question does not belong to the specified template");
    }

    private async Task<bool> BeValidQuestion(int questionId, CancellationToken cancellationToken)
    {
        return await _context.Questions
            .AnyAsync(q => q.Id == questionId && q.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidTemplate(int templateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates
            .AnyAsync(t => t.Id == templateId && t.IsActive && !t.IsPublished, cancellationToken);
    }

    private async Task<bool> QuestionBelongsToTemplate(DeleteQuestionCommand command, CancellationToken cancellationToken)
    {
        return await _context.Questions
            .AnyAsync(q => q.Id == command.QuestionId && 
                          q.AuditTemplateId == command.TemplateId && 
                          q.IsActive, cancellationToken);
    }
}
