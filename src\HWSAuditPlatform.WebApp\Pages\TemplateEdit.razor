@page "/templates/{id:int}/edit"
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "SystemManager,DevAdmin")]
@inject ITemplateApiService TemplateService
@inject ITemplateValidationService ValidationService
@inject NavigationManager Navigation
@inject ILogger<TemplateEdit> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Edit Template - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading template...</p>
        </div>
    }
    else if (template == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Template Not Found</h4>
            <p>The requested template could not be found or you don't have permission to edit it.</p>
            <hr>
            <button class="btn btn-outline-danger" @onclick="BackToTemplates">
                <i class="bi bi-arrow-left me-2"></i>Back to Templates
            </button>
        </div>
    }
    else if (template.IsPublished)
    {
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading">Published Template</h4>
            <p>This template is published and cannot be edited directly. You can create a new version to make changes.</p>
            <hr>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" @onclick="CreateNewVersion">
                    <i class="bi bi-plus-circle me-2"></i>Create New Version
                </button>
                <button class="btn btn-outline-secondary" @onclick="ViewTemplate">
                    <i class="bi bi-eye me-2"></i>View Template
                </button>
                <button class="btn btn-outline-secondary" @onclick="BackToTemplates">
                    <i class="bi bi-arrow-left me-2"></i>Back to Templates
                </button>
            </div>
        </div>
    }
    else
    {
        <!-- Template Edit Form -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="/templates" class="text-decoration-none">Templates</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="/templates/@Id" class="text-decoration-none">@template.TemplateName</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="h2 mb-2">Edit Template</h1>
                        <div class="d-flex align-items-center gap-3 mb-3">
                            <span class="badge bg-warning fs-6">Draft</span>
                            <span class="badge bg-secondary fs-6">Version @template.Version</span>
                        </div>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button class="btn btn-success" @onclick="SaveTemplate" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-check-circle me-2"></i>
                            }
                            Save Changes
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="CancelEdit">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @errorMessage
                <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                @successMessage
                <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
            </div>
        }

        <!-- Template Information Form -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>Template Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="@editModel" OnValidSubmit="@SaveTemplate">
                            <DataAnnotationsValidator />
                            
                            <div class="mb-3">
                                <label for="templateName" class="form-label">Template Name <span class="text-danger">*</span></label>
                                <InputText id="templateName" class="form-control" @bind-Value="editModel.TemplateName" />
                                <ValidationMessage For="@(() => editModel.TemplateName)" />
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <InputTextArea id="description" class="form-control" rows="4" @bind-Value="editModel.Description" />
                                <ValidationMessage For="@(() => editModel.Description)" />
                                <div class="form-text">Provide a detailed description of the template's purpose and scope.</div>
                            </div>
                            
                            <div class="form-check">
                                <InputCheckbox id="isActive" class="form-check-input" @bind-Value="editModel.IsActive" />
                                <label class="form-check-label" for="isActive">
                                    Active Template
                                </label>
                                <div class="form-text">Inactive templates are hidden from users and cannot be used for new audits.</div>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary mb-1">@GetTotalQuestionCount()</h4>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info mb-1">@template.QuestionGroups.Count(g => g.IsActive)</h4>
                                <small class="text-muted">Groups</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" @onclick="ManageQuestions">
                                <i class="bi bi-gear me-1"></i>Manage Questions
                            </button>
                            @if (CanPublish())
                            {
                                <button class="btn btn-outline-success btn-sm" @onclick="PublishTemplate">
                                    <i class="bi bi-check-circle me-1"></i>Publish Template
                                </button>
                            }
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-clock me-2"></i>Template History
                        </h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted d-block">Created</small>
                        <span class="small">@template.CreatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                        
                        <small class="text-muted d-block mt-2">Last Updated</small>
                        <span class="small">@template.UpdatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Questions Preview -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle me-2"></i>Questions Preview
                </h5>
                <button class="btn btn-sm btn-outline-primary" @onclick="ManageQuestions">
                    <i class="bi bi-gear me-1"></i>Manage Questions
                </button>
            </div>
            <div class="card-body">
                @if (GetTotalQuestionCount() > 0)
                {
                    <div class="row">
                        @foreach (var question in template.Questions.Where(q => q.IsActive).OrderBy(q => q.DisplayOrder).Take(5))
                        {
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="mb-2">@question.QuestionText</h6>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="badge bg-light text-dark small">@question.QuestionType</span>
                                        @if (question.IsRequired)
                                        {
                                            <span class="badge bg-danger small">Required</span>
                                        }
                                        @if (question.EvidenceRequired)
                                        {
                                            <span class="badge bg-warning small">Evidence</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                    
                    @if (GetTotalQuestionCount() > 5)
                    {
                        <div class="text-center mt-3">
                            <small class="text-muted">Showing 5 of @GetTotalQuestionCount() questions</small>
                            <br>
                            <button class="btn btn-sm btn-link" @onclick="ManageQuestions">
                                View all questions
                            </button>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle display-4 text-muted mb-3"></i>
                        <h6>No Questions</h6>
                        <p class="text-muted">This template doesn't have any questions yet.</p>
                        <button class="btn btn-primary" @onclick="ManageQuestions">
                            <i class="bi bi-plus-circle me-2"></i>Add Questions
                        </button>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }

    private AuditTemplate? template;
    private UpdateAuditTemplateRequest editModel = new();
    private bool isLoading = true;
    private bool isSaving = false;
    private string? errorMessage = null;
    private string? successMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplate();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id > 0)
        {
            await LoadTemplate();
        }
    }

    private async Task LoadTemplate()
    {
        try
        {
            isLoading = true;
            errorMessage = null;
            template = await TemplateService.GetTemplateByIdAsync(Id);

            if (template != null)
            {
                // Initialize edit model with current template data
                editModel = new UpdateAuditTemplateRequest
                {
                    TemplateName = template.TemplateName,
                    Description = template.Description,
                    IsActive = template.IsActive
                };
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading template {TemplateId}", Id);
            errorMessage = "Failed to load template. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private int GetTotalQuestionCount()
    {
        if (template?.QuestionGroups?.Any() == true)
        {
            return template.QuestionGroups.Sum(g => g.Questions?.Count(q => q.IsActive) ?? 0) +
                   template.Questions.Count(q => q.IsActive && !q.QuestionGroupId.HasValue);
        }
        return template?.Questions?.Count(q => q.IsActive) ?? 0;
    }

    private bool CanPublish()
    {
        return template != null && !template.IsPublished && GetTotalQuestionCount() > 0;
    }

    private async Task SaveTemplate()
    {
        try
        {
            isSaving = true;
            errorMessage = null;
            successMessage = null;

            var success = await TemplateService.UpdateTemplateAsync(Id, editModel);
            if (success)
            {
                successMessage = "Template updated successfully!";
                await LoadTemplate(); // Refresh template data
            }
            else
            {
                errorMessage = "Failed to update template. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating template {TemplateId}", Id);
            errorMessage = "An error occurred while updating the template.";
        }
        finally
        {
            isSaving = false;
        }
    }

    private void BackToTemplates()
    {
        Navigation.NavigateTo("/templates");
    }

    private void ViewTemplate()
    {
        Navigation.NavigateTo($"/templates/{Id}");
    }

    private void CancelEdit()
    {
        Navigation.NavigateTo($"/templates/{Id}");
    }

    private void ManageQuestions()
    {
        Navigation.NavigateTo($"/templates/{Id}/questions");
    }

    private async Task CreateNewVersion()
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Create a new version of this template? This will copy all questions and settings to a new draft version.");

            if (confirmed)
            {
                var newTemplateId = await TemplateService.CreateTemplateVersionAsync(Id);
                if (newTemplateId.HasValue)
                {
                    Navigation.NavigateTo($"/templates/{newTemplateId.Value}/edit");
                }
                else
                {
                    errorMessage = "Failed to create template version. Please try again.";
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating template version for template {TemplateId}", Id);
            errorMessage = "An error occurred while creating the template version.";
        }
    }

    private async Task PublishTemplate()
    {
        try
        {
            if (template == null) return;

            // Validate template before publishing
            var validationResult = await ValidationService.ValidateForPublishingAsync(template);

            if (!validationResult.IsValid)
            {
                var errorList = string.Join("\n• ", validationResult.Errors);
                await JSRuntime.InvokeVoidAsync("alert", $"Cannot publish template due to the following errors:\n\n• {errorList}");
                return;
            }

            // Show warnings if any
            if (validationResult.Warnings.Any())
            {
                var warningList = string.Join("\n• ", validationResult.Warnings);
                var proceedWithWarnings = await JSRuntime.InvokeAsync<bool>("confirm",
                    $"The following warnings were found:\n\n• {warningList}\n\nDo you want to proceed with publishing?");

                if (!proceedWithWarnings)
                {
                    return;
                }
            }

            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Publish this template? Once published, it cannot be edited directly. You'll need to create a new version for changes.");

            if (confirmed)
            {
                var result = await TemplateService.PublishTemplateWithDetailsAsync(Id);
                if (result.IsSuccess)
                {
                    Navigation.NavigateTo($"/templates/{Id}");
                }
                else
                {
                    if (result.IsValidationError)
                    {
                        // Show validation errors in a more user-friendly way
                        var errorList = string.Join("\n• ", result.ErrorMessages);
                        await JSRuntime.InvokeVoidAsync("alert", $"Cannot publish template due to validation errors:\n\n• {errorList}");
                    }
                    else
                    {
                        errorMessage = result.GetErrorMessage();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error publishing template {TemplateId}", Id);
            errorMessage = "An error occurred while publishing the template.";
        }
    }
}
