using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.StartAudit;

/// <summary>
/// Validator for StartAuditCommand
/// </summary>
public class StartAuditCommandValidator : AbstractValidator<StartAuditCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public StartAuditCommandValidator(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;

        RuleFor(x => x.AuditId)
            .NotEmpty().WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit).WithMessage("Audit does not exist")
            .MustAsync(BeScheduledStatus).WithMessage("Audit must be in Scheduled status to start")
            //.MustAsync(BeAssignedToCurrentUser).WithMessage("Audit is not assigned to current user")
            ;
    }

    private async Task<bool> BeValidAudit(string auditId, CancellationToken cancellationToken)
    {
        return await _context.Audits.AnyAsync(a => a.Id == auditId, cancellationToken);
    }

    private async Task<bool> BeScheduledStatus(string auditId, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);
        return audit?.OverallStatus == AuditOverallStatus.Scheduled;
    }

    private async Task<bool> BeAssignedToCurrentUser(string auditId, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId)) return false;

        var audit = await _context.Audits
            .Include(a => a.AssignedToUserGroup)
            .ThenInclude(g => g!.UserGroupMembers)
            .FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);

        if (audit == null) return false;

        // Check direct assignment
        if (audit.AssignmentType == AssignmentType.Individual && audit.AssignedToUserId == currentUserId)
            return true;

        // Check group assignment
        if (audit.AssignmentType == AssignmentType.GroupAny && audit.AssignedToUserGroup != null)
        {
            return audit.AssignedToUserGroup.UserGroupMembers.Any(m => m.UserId == currentUserId);
        }

        return false;
    }
}
