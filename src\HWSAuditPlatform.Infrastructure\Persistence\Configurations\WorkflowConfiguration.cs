using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Workflow;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for AuditLog entity
/// </summary>
public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.ToTable("hwsap_wfl_audit_logs");

        // Primary Key
        builder.HasKey(al => al.Id);
        builder.Property(al => al.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(al => al.EventTimestamp)
            .IsRequired();

        builder.Property(al => al.ServerReceivedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(al => al.UserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(al => al.EntityType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(al => al.EntityId)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(al => al.ActionType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(al => al.OldValues)
            .HasColumnType("nvarchar(max)")
            .IsRequired(false);

        builder.Property(al => al.NewValues)
            .HasColumnType("nvarchar(max)")
            .IsRequired(false);

        builder.Property(al => al.Details)
            .HasColumnType("nvarchar(max)")
            .IsRequired(false);

        builder.Property(al => al.IPAddress)
            .HasMaxLength(45)
            .IsRequired(false);

        builder.Property(al => al.AppVersion)
            .HasMaxLength(50)
            .IsRequired(false);

        // Base entity properties
        builder.Property(al => al.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(al => al.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(al => al.UserId)
            .HasDatabaseName("IX_AuditLogs_UserId");

        builder.HasIndex(al => al.EntityType)
            .HasDatabaseName("IX_AuditLogs_EntityType");

        builder.HasIndex(al => al.EntityId)
            .HasDatabaseName("IX_AuditLogs_EntityId");

        builder.HasIndex(al => al.EventTimestamp)
            .HasDatabaseName("IX_AuditLogs_EventTimestamp");

        builder.HasIndex(al => new { al.EntityType, al.EntityId })
            .HasDatabaseName("IX_AuditLogs_EntityType_EntityId");

        // Relationships
        builder.HasOne(al => al.User)
            .WithMany()
            .HasForeignKey(al => al.UserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(al => al.DomainEvents);
    }
}
