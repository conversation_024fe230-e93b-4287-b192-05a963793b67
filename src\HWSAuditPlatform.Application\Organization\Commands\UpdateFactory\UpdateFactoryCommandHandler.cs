using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.UpdateFactory;

/// <summary>
/// Handler for UpdateFactoryCommand
/// </summary>
public class UpdateFactoryCommandHandler : BaseCommandHandler<UpdateFactoryCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateFactoryCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(UpdateFactoryCommand request, CancellationToken cancellationToken)
    {
        // Get the factory
        var factory = await _context.Factories
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (factory == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Organization.Factory), request.Id);
        }

        // Check for concurrency conflicts
        if (factory.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The factory has been modified by another user. Please refresh and try again.");
        }

        // Create address value object if address details are provided
        Address? address = null;
        if (!string.IsNullOrEmpty(request.AddressLine1) || !string.IsNullOrEmpty(request.City) || !string.IsNullOrEmpty(request.PostalCode))
        {
            address = new Address(request.AddressLine1, request.City, request.PostalCode);
        }

        // Update the factory
        factory.FactoryName = request.FactoryName;
        factory.FactoryProcess = request.FactoryProcess;
        factory.LocationId = request.LocationId;
        factory.Address = address;
        factory.IsActive = request.IsActive;
        factory.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
