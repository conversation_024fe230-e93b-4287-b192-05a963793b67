using HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Tests.Common;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;

namespace HWSAuditPlatform.Tests.Application.Organization.Commands;

public class CreateAreaResponsibilityCommandHandlerTests : BaseDbTestClass
{
    private readonly Mock<IAreaResponsibilityService> _mockAreaResponsibilityService;
    private readonly CreateAreaResponsibilityCommandHandler _handler;

    public CreateAreaResponsibilityCommandHandlerTests()
    {
        _mockAreaResponsibilityService = new Mock<IAreaResponsibilityService>();
        _handler = new CreateAreaResponsibilityCommandHandler(
            Context,
            MockCurrentUserService.Object,
            _mockAreaResponsibilityService.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreateAreaResponsibility_WhenValidRequest()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "12345678-1234-1234-1234-123456789012",
            Username = "test.user",
            FirstName = "Test",
            LastName = "User"
        };

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012",
            IsActive = true,
            Description = "Test responsibility",
            Priority = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.AreaId);
        Assert.Equal("Test Area", result.AreaName);
        Assert.Equal(ResponsibilityType.CorrectiveActionOwner, result.ResponsibilityType);
        Assert.Equal("12345678-1234-1234-1234-123456789012", result.ResponsibleUserId);
        Assert.Equal("test.user", result.ResponsibleUserName);
        Assert.Equal("Test User", result.ResponsibleUserFullName);
        Assert.True(result.IsActive);
        Assert.Equal("Test responsibility", result.Description);
        Assert.Equal(1, result.Priority);

        // Verify it was saved to database
        var savedResponsibility = await Context.AreaResponsibilities.FirstOrDefaultAsync();
        Assert.NotNull(savedResponsibility);
        Assert.Equal(command.AreaId, savedResponsibility.AreaId);
        Assert.Equal(command.ResponsibilityType, savedResponsibility.ResponsibilityType);
    }

    [Fact]
    public async Task Handle_ShouldCreateAreaResponsibility_WithAuditTemplate()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "12345678-1234-1234-1234-123456789012",
            Username = "test.user",
            FirstName = "Test",
            LastName = "User"
        };
        var template = new AuditTemplate 
        { 
            Id = 1, 
            TemplateName = "Test Template", 
            IsActive = true,
            EnableAreaBasedResponsibility = true
        };

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);
        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012",
            AuditTemplateId = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.AuditTemplateId);
        Assert.True(result.IsTemplateSpecific);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenAreaNotFound()
    {
        // Arrange
        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 999, // Non-existent area
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenUserNotFound()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "99999999-9999-9999-9999-999999999999" // Non-existent user
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenTemplateNotFound()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "12345678-1234-1234-1234-123456789012",
            Username = "test.user"
        };

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012",
            AuditTemplateId = 999 // Non-existent template
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowValidationException_WhenTemplateDoesNotEnableAreaBasedResponsibility()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "12345678-1234-1234-1234-123456789012",
            Username = "test.user"
        };
        var template = new AuditTemplate 
        { 
            Id = 1, 
            TemplateName = "Test Template", 
            IsActive = true,
            EnableAreaBasedResponsibility = false // Not enabled
        };

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);
        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012",
            AuditTemplateId = 1
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ValidationException>(() => 
            _handler.Handle(command, CancellationToken.None));
        
        Assert.Contains("Area-based responsibility is not enabled", exception.Message);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenAreaIsInactive()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = false }; // Inactive

        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        await Context.SaveChangesAsync();

        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }
}
