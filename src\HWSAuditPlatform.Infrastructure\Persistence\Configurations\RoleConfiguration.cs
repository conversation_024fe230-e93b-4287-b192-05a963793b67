using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Role entity
/// </summary>
public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("hwsap_usr_roles");

        // Primary Key
        builder.HasKey(r => r.Id);
        builder.Property(r => r.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(r => r.RoleName)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.Description)
            .HasMaxLength(255)
            .IsRequired(false);

        // Base entity properties
        builder.Property(r => r.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(r => r.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(r => r.RoleName)
            .IsUnique()
            .HasDatabaseName("IX_Roles_RoleName");

        // Ignore domain events
        builder.Ignore(r => r.DomainEvents);

        // Seed data - using static dates to avoid migration issues
        builder.HasData(
            new Role { Id = 1, RoleName = UserRole.DevAdmin, Description = "Developer Administrator with full system access", CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new Role { Id = 2, RoleName = UserRole.SystemManager, Description = "System Manager with template creation and user management capabilities", CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new Role { Id = 3, RoleName = UserRole.ProcessOwner, Description = "Process Owner with audit assignment and results management capabilities", CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new Role { Id = 4, RoleName = UserRole.Auditor, Description = "Auditor who performs audit activities", CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new Role { Id = 5, RoleName = UserRole.Viewer, Description = "Viewer with read-only access", CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) }
        );
    }
}

/// <summary>
/// Entity Framework configuration for AdGroupRoleMapping entity
/// </summary>
public class AdGroupRoleMappingConfiguration : IEntityTypeConfiguration<AdGroupRoleMapping>
{
    public void Configure(EntityTypeBuilder<AdGroupRoleMapping> builder)
    {
        builder.ToTable("hwsap_usr_ad_group_role_mappings");

        // Primary Key
        builder.HasKey(agrm => agrm.Id);
        builder.Property(agrm => agrm.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(agrm => agrm.AdGroupName)
            .HasMaxLength(256)
            .IsRequired();

        builder.Property(agrm => agrm.RoleId)
            .IsRequired();

        builder.Property(agrm => agrm.Description)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(agrm => agrm.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Base entity properties
        builder.Property(agrm => agrm.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(agrm => agrm.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(agrm => agrm.AdGroupName)
            .IsUnique()
            .HasDatabaseName("IX_AdGroupRoleMappings_AdGroupName");

        builder.HasIndex(agrm => agrm.RoleId)
            .HasDatabaseName("IX_AdGroupRoleMappings_RoleId");

        // Relationships
        builder.HasOne(agrm => agrm.Role)
            .WithMany(r => r.AdGroupRoleMappings)
            .HasForeignKey(agrm => agrm.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(agrm => agrm.DomainEvents);

        // Seed data for AD group role mappings - using static dates to avoid migration issues
        builder.HasData(
            new AdGroupRoleMapping { Id = 1, AdGroupName = "HWSAudit-DevAdministrators", RoleId = 1, Description = "Maps AD DevAdministrators group to DevAdmin role", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new AdGroupRoleMapping { Id = 2, AdGroupName = "HWSAudit-SystemManagers", RoleId = 2, Description = "Maps AD SystemManagers group to SystemManager role", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new AdGroupRoleMapping { Id = 3, AdGroupName = "HWSAudit-ProcessOwners", RoleId = 3, Description = "Maps AD ProcessOwners group to ProcessOwner role", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new AdGroupRoleMapping { Id = 4, AdGroupName = "HWSAudit-Auditors", RoleId = 4, Description = "Maps AD Auditors group to Auditor role", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
            new AdGroupRoleMapping { Id = 5, AdGroupName = "HWSAudit-Viewers", RoleId = 5, Description = "Maps AD Viewers group to Viewer role", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc), UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) }
        );
    }
}
