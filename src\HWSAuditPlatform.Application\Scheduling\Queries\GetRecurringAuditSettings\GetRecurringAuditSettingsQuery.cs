using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.DTOs;

namespace HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSettings;

/// <summary>
/// Query to get a paginated list of recurring audit settings
/// </summary>
public class GetRecurringAuditSettingsQuery : BaseQuery<PaginatedResult<RecurringAuditSettingSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Search term to filter by setting name or template name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by enabled status
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// Filter by audit template ID
    /// </summary>
    public int? AuditTemplateId { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Filter by sub-area ID
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Filter by assignment type
    /// </summary>
    public Domain.Enums.AssignmentType? AssignmentType { get; set; }

    /// <summary>
    /// Filter by assigned user ID
    /// </summary>
    public string? AssignedToUserId { get; set; }

    /// <summary>
    /// Filter by assigned user group ID
    /// </summary>
    public string? AssignedToUserGroupId { get; set; }

    /// <summary>
    /// Whether to include inactive settings
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "SettingName";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "asc";
}
