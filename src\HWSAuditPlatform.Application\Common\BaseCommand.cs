using MediatR;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for commands that don't return a value
/// </summary>
public abstract class BaseCommand : IRequest
{
}

/// <summary>
/// Base class for commands that return a value
/// </summary>
/// <typeparam name="TResponse">The type of response returned by the command</typeparam>
public abstract class BaseCommand<TResponse> : IRequest<TResponse>
{
}
