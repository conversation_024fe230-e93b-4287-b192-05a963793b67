using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFindingCategories;

/// <summary>
/// Handler for getting finding categories
/// </summary>
public class GetFindingCategoriesQueryHandler : BaseQueryHandler<GetFindingCategoriesQuery, List<FindingCategoryDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetFindingCategoriesQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<List<FindingCategoryDto>> Handle(GetFindingCategoriesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.FindingCategories
            .Include(fc => fc.AuditTemplate)
            .AsQueryable();

        // Apply filters
        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(fc => fc.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(fc => fc.IsActive == request.IsActive.Value);
        }

        var categories = await query
            .OrderBy(fc => fc.AuditTemplate.TemplateName)
            .ThenBy(fc => fc.DisplayOrder)
            .ThenBy(fc => fc.CategoryName)
            .ToListAsync(cancellationToken);

        var result = new List<FindingCategoryDto>();

        foreach (var category in categories)
        {
            var dto = new FindingCategoryDto
            {
                Id = category.Id,
                CategoryName = category.CategoryName,
                Description = category.Description,
                AuditTemplateId = category.AuditTemplateId,
                AuditTemplateName = category.AuditTemplate.TemplateName,
                DisplayOrder = category.DisplayOrder,
                IsActive = category.IsActive,
                ColorCode = category.ColorCode,
                IconName = category.IconName,
                RequiresDocumentation = category.RequiresDocumentation,
                IsUsable = category.IsUsable,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
                RecordVersion = category.RecordVersion,
                CreatedByUserId = category.CreatedByUserId,
                UpdatedByUserId = category.UpdatedByUserId
            };

            // Include usage statistics if requested
            if (request.IncludeUsageStatistics)
            {
                var findingCount = await _context.Findings
                    .CountAsync(f => f.FindingCategoryId == category.Id, cancellationToken);

                var openFindingCount = await _context.Findings
                    .CountAsync(f => f.FindingCategoryId == category.Id && f.IsOpen, cancellationToken);

                dto.FindingCount = findingCount;
                dto.OpenFindingCount = openFindingCount;
            }

            result.Add(dto);
        }

        return result;
    }
}
