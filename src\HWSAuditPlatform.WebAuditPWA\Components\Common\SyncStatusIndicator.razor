@using HWSAuditPlatform.WebAuditPWA.Models
@inject ISyncService SyncService
@inject IOfflineStorageService OfflineStorage
@inject IPwaService PwaService
@inject IJSRuntime JSRuntime
@inject ILogger<SyncStatusIndicator> <PERSON><PERSON>
@implements IDisposable

<div class="sync-status-indicator @(isVisible ? "visible" : "")">
    <div class="sync-status-content">
        @if (syncStatus.IsSyncing)
        {
            <div class="sync-item syncing">
                <div class="sync-icon">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Syncing...</span>
                    </div>
                </div>
                <div class="sync-text">
                    <div class="sync-title">Syncing...</div>
                    <div class="sync-details">@syncStatus.CurrentOperation</div>
                </div>
            </div>
        }
        else if (syncStatus.HasPendingItems)
        {
            <div class="sync-item pending" @onclick="StartSync">
                <div class="sync-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="sync-text">
                    <div class="sync-title">@syncStatus.PendingCount items to sync</div>
                    <div class="sync-details">Tap to sync now</div>
                </div>
            </div>
        }
        else if (syncStatus.LastSyncTime.HasValue)
        {
            <div class="sync-item synced">
                <div class="sync-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="sync-text">
                    <div class="sync-title">All synced</div>
                    <div class="sync-details">Last sync: @FormatLastSync(syncStatus.LastSyncTime.Value)</div>
                </div>
            </div>
        }

        @if (!syncStatus.IsOnline)
        {
            <div class="sync-item offline">
                <div class="sync-icon">
                    <i class="fas fa-wifi-slash"></i>
                </div>
                <div class="sync-text">
                    <div class="sync-title">Offline</div>
                    <div class="sync-details">Data will sync when online</div>
                </div>
            </div>
        }
    </div>
</div>

<style>
    .sync-status-indicator {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1000;
        max-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    }

    .sync-status-indicator.visible {
        opacity: 1;
        transform: translateX(0);
    }

    .sync-status-content {
        background: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
    }

    .sync-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid var(--industrial-border);
        transition: background-color 0.2s ease;
    }

    .sync-item:last-child {
        border-bottom: none;
    }

    .sync-item.pending {
        cursor: pointer;
        background: var(--industrial-light-gray);
    }

    .sync-item.pending:hover {
        background: var(--industrial-steel);
    }

    .sync-item.syncing {
        background: linear-gradient(90deg, var(--industrial-light-gray) 0%, var(--industrial-steel) 50%, var(--industrial-light-gray) 100%);
        background-size: 200% 100%;
        animation: syncPulse 2s ease-in-out infinite;
    }

    .sync-item.synced {
        background: rgba(var(--industrial-green-rgb), 0.1);
        border-left: 3px solid var(--industrial-green);
    }

    .sync-item.offline {
        background: rgba(255, 193, 7, 0.1);
        border-left: 3px solid #ffc107;
    }

    .sync-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .sync-item.syncing .sync-icon {
        color: var(--industrial-teal);
    }

    .sync-item.pending .sync-icon {
        color: var(--industrial-orange);
    }

    .sync-item.synced .sync-icon {
        color: var(--industrial-green);
    }

    .sync-item.offline .sync-icon {
        color: #ffc107;
    }

    .sync-text {
        flex: 1;
        min-width: 0;
    }

    .sync-title {
        font-weight: 600;
        color: var(--industrial-text);
        font-size: 0.875rem;
        line-height: 1.2;
    }

    .sync-details {
        font-size: 0.75rem;
        color: var(--industrial-steel);
        line-height: 1.2;
        margin-top: 0.125rem;
    }

    @@keyframes syncPulse {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    @@media (max-width: 768px) {
        .sync-status-indicator {
            top: 0.5rem;
            right: 0.5rem;
            left: 0.5rem;
            max-width: none;
        }

        .sync-item {
            padding: 0.5rem;
        }

        .sync-icon {
            width: 28px;
            height: 28px;
            margin-right: 0.5rem;
        }
    }
</style>

@code {
    private SyncStatusInfo syncStatus = new();
    private bool isVisible = false;
    private Timer? statusTimer;
    private Timer? visibilityTimer;

    protected override async Task OnInitializedAsync()
    {
        await UpdateSyncStatus();
        
        // Update status every 5 seconds
        statusTimer = new Timer(async _ => await InvokeAsync(UpdateSyncStatus), null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
    }

    private async Task UpdateSyncStatus()
    {
        try
        {
            var isOnline = await PwaService.IsOnlineAsync();
            var pendingAttachments = await OfflineStorage.GetPendingAttachmentsAsync();
            var pendingAudits = await OfflineStorage.GetOfflineAuditsAsync();

            var newStatus = new SyncStatusInfo
            {
                IsOnline = isOnline,
                PendingCount = pendingAttachments.Count + pendingAudits.Count,
                HasPendingItems = pendingAttachments.Any() || pendingAudits.Any(),
                IsSyncing = false, // This would be set by the sync service
                CurrentOperation = string.Empty,
                LastSyncTime = await GetLastSyncTime()
            };

            var shouldShow = newStatus.HasPendingItems || !newStatus.IsOnline || newStatus.IsSyncing;
            
            if (shouldShow != isVisible || !syncStatus.Equals(newStatus))
            {
                syncStatus = newStatus;
                isVisible = shouldShow;
                
                if (isVisible)
                {
                    // Auto-hide after 10 seconds if not syncing and no pending items
                    if (!syncStatus.IsSyncing && !syncStatus.HasPendingItems)
                    {
                        visibilityTimer?.Dispose();
                        visibilityTimer = new Timer(_ => InvokeAsync(() => 
                        {
                            isVisible = false;
                            StateHasChanged();
                        }), null, TimeSpan.FromSeconds(10), Timeout.InfiniteTimeSpan);
                    }
                }
                
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating sync status");
        }
    }

    private Task<DateTime?> GetLastSyncTime()
    {
        try
        {
            // This would be implemented to get the last sync time from storage
            // For now, return null
            return Task.FromResult<DateTime?>(null);
        }
        catch
        {
            return Task.FromResult<DateTime?>(null);
        }
    }

    private async Task StartSync()
    {
        try
        {
            if (!syncStatus.IsOnline)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Cannot sync while offline. Please check your internet connection.");
                return;
            }

            syncStatus.IsSyncing = true;
            syncStatus.CurrentOperation = "Starting sync...";
            StateHasChanged();

            var result = await SyncService.SyncAsync();
            
            if (result.IsSuccess)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "Sync completed successfully");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Sync failed: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting sync");
            await JSRuntime.InvokeVoidAsync("alert", "An error occurred while syncing. Please try again.");
        }
        finally
        {
            syncStatus.IsSyncing = false;
            syncStatus.CurrentOperation = string.Empty;
            await UpdateSyncStatus();
        }
    }

    private string FormatLastSync(DateTime lastSync)
    {
        var timeSpan = DateTime.UtcNow - lastSync;
        
        if (timeSpan.TotalMinutes < 1)
            return "Just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes}m ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours}h ago";
        
        return lastSync.ToString("MMM d, HH:mm");
    }

    public void Dispose()
    {
        statusTimer?.Dispose();
        visibilityTimer?.Dispose();
    }

    private class SyncStatusInfo
    {
        public bool IsOnline { get; set; }
        public int PendingCount { get; set; }
        public bool HasPendingItems { get; set; }
        public bool IsSyncing { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public DateTime? LastSyncTime { get; set; }

        public bool Equals(SyncStatusInfo other)
        {
            return IsOnline == other.IsOnline &&
                   PendingCount == other.PendingCount &&
                   HasPendingItems == other.HasPendingItems &&
                   IsSyncing == other.IsSyncing &&
                   CurrentOperation == other.CurrentOperation &&
                   LastSyncTime == other.LastSyncTime;
        }
    }
}
