using Asp.Versioning;
using HWSAuditPlatform.ApiService.Controllers;
using HWSAuditPlatform.ApiService.Models;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.Commands.CreateTemplateAccessAssignment;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// API controller for managing template access assignments
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/template-access")]
[Authorize(Policy = "SystemManagerOrAbove")]
public class TemplateAccessController : BaseController
{
    private readonly ITemplateAccessService _templateAccessService;

    public TemplateAccessController(
        IMediator mediator, 
        ILogger<TemplateAccessController> logger,
        ITemplateAccessService templateAccessService)
        : base(mediator, logger)
    {
        _templateAccessService = templateAccessService;
    }

    /// <summary>
    /// Grant template access to a user
    /// </summary>
    /// <param name="command">Command to create the template access assignment</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ID of the created assignment</returns>
    [HttpPost("grant")]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status409Conflict)]
    public async Task<ActionResult<int>> GrantTemplateAccess(
        [FromBody] CreateTemplateAccessAssignmentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            var assignmentId = await Mediator.Send(command, cancellationToken);
            return Created(nameof(GetUserTemplateAccess), new { userId = command.UserId }, assignmentId, "Template access granted successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized attempt to grant template access");
            return Forbid();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation when granting template access");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error granting template access");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Revoke template access from a user
    /// </summary>
    /// <param name="userId">User ID to revoke access from</param>
    /// <param name="templateId">Template ID to revoke access to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    [HttpDelete("revoke/{userId}/{templateId}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<bool>> RevokeTemplateAccess(
        string userId,
        int templateId,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get current user ID from claims or service
            var currentUserId = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return Unauthorized();
            }

            var success = await _templateAccessService.RevokeTemplateAccessAsync(userId, templateId, currentUserId, cancellationToken);
            
            if (!success)
            {
                return NotFound("No active template access found for the specified user and template");
            }

            return Success(true, "Template access revoked successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized attempt to revoke template access");
            return Forbid();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error revoking template access for user {UserId} and template {TemplateId}", userId, templateId);
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get all templates accessible to a specific user
    /// </summary>
    /// <param name="userId">User ID to check access for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of accessible templates</returns>
    [HttpGet("user/{userId}/templates")]
    [ProducesResponseType(typeof(ApiResponse<List<object>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<object>>> GetUserTemplateAccess(
        string userId,
        CancellationToken cancellationToken)
    {
        try
        {
            var templates = await _templateAccessService.GetAccessibleTemplatesAsync(userId, cancellationToken);
            var templateDtos = templates.Select(t => new
            {
                t.Id,
                t.TemplateName,
                t.FullName,
                t.Description,
                t.IsPublished,
                t.IsActive
            }).ToList();

            return Success(templateDtos.Cast<object>().ToList());
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized access to user template access");
            return Forbid();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving template access for user {UserId}", userId);
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get all users who have access to a specific template
    /// </summary>
    /// <param name="templateId">Template ID to check access for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of users with access</returns>
    [HttpGet("template/{templateId}/users")]
    [ProducesResponseType(typeof(ApiResponse<List<object>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<object>>> GetTemplateUserAccess(
        int templateId,
        CancellationToken cancellationToken)
    {
        try
        {
            var users = await _templateAccessService.GetUsersWithTemplateAccessAsync(templateId, cancellationToken);
            var userDtos = users.Select(u => new
            {
                u.AdObjectGuid,
                u.Username,
                u.FullName,
                u.Email,
                Role = u.Role.RoleName.ToString()
            }).ToList();

            return Success(userDtos.Cast<object>().ToList());
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized access to template user access");
            return Forbid();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving user access for template {TemplateId}", templateId);
            return BadRequest(ex.Message);
        }
    }
}
