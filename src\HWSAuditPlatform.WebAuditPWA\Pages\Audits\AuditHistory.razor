@page "/audit-history"
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IAuditApiService AuditService
@inject NavigationManager Navigation
@inject ILogger<AuditHistory> Logger

<PageTitle>Audit History - HWS Audit PWA</PageTitle>

<div class="audit-history-container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-history me-2"></i>
                Audit History
            </h2>
            <p class="text-muted mb-0">View completed and submitted audits</p>
        </div>
        <button class="btn btn-outline-secondary" @onclick="RefreshHistory">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Status Filter</label>
                    <select class="form-select" @bind="statusFilter" @bind:after="ApplyFilters">
                        <option value="">All Statuses</option>
                        <option value="Submitted">Submitted</option>
                        <option value="Closed">Closed</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control" @bind="dateFrom" @bind:after="ApplyFilters" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control" @bind="dateTo" @bind:after="ApplyFilters" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search audits..." 
                               @bind="searchTerm" @onkeypress="HandleSearchKeyPress" />
                        <button class="btn btn-outline-secondary" type="button" @onclick="ApplyFilters">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading audit history...</p>
        </div>
    }
    else if (audits?.Items?.Any() != true)
    {
        <div class="text-center p-5">
            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Completed Audits Found</h5>
            <p class="text-muted">
                @if (HasActiveFilters())
                {
                    <span>No audits match your current filters.</span>
                }
                else
                {
                    <span>You haven't completed any audits yet.</span>
                }
            </p>
            @if (HasActiveFilters())
            {
                <button class="btn btn-outline-primary" @onclick="ClearFilters">
                    <i class="fas fa-times me-2"></i>Clear Filters
                </button>
            }
        </div>
    }
    else
    {
        <!-- Summary Stats -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card industrial-stats-card industrial-stats-primary">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">@audits.TotalCount</div>
                        <h6 class="card-title">Total Audits</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card industrial-stats-card industrial-stats-success">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">@GetCompletedCount()</div>
                        <h6 class="card-title">Completed</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card industrial-stats-card industrial-stats-info">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">@GetAverageScore().ToString("F1")%</div>
                        <h6 class="card-title">Avg Score</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card industrial-stats-card industrial-stats-warning">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">@GetNonConformanceCount()</div>
                        <h6 class="card-title">Non-Conformances</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit List -->
        <div class="row">
            @foreach (var audit in audits.Items)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 audit-history-card" @onclick="() => ViewAuditResults(audit.Id)">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="@GetStatusIcon(audit.OverallStatus) me-2"></i>
                                <h6 class="mb-0 fw-bold text-truncate">@audit.AuditTemplateName</h6>
                            </div>
                            <span class="badge bg-@GetStatusBadgeClass(audit.OverallStatus)">
                                @audit.OverallStatus
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted d-block">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    @audit.LocationName
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-calendar me-1"></i>
                                    Completed: @audit.CompletedAt?.ToString("MMM dd, yyyy")
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-user me-1"></i>
                                    @audit.AssignedToUserName
                                </small>
                            </div>

                            @if (audit.OverallScore.HasValue)
                            {
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="text-muted">Overall Score</small>
                                        <small class="fw-bold text-@GetScoreTextClass(audit.OverallScore.Value)">
                                            @audit.OverallScore.Value.ToString("F1")%
                                        </small>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-@GetScoreProgressClass(audit.OverallScore.Value)" 
                                             style="width: @audit.OverallScore.Value%"></div>
                                    </div>
                                </div>
                            }

                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="small text-muted">Questions</div>
                                    <div class="fw-bold">@GetQuestionCount(audit)</div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">Evidence</div>
                                    <div class="fw-bold">@GetEvidenceCount(audit)</div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">Issues</div>
                                    <div class="fw-bold text-warning">@GetIssueCount(audit)</div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-fill" @onclick:stopPropagation="true" 
                                        @onclick="() => ViewAuditResults(audit.Id)">
                                    <i class="fas fa-eye me-1"></i>View Results
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" @onclick:stopPropagation="true" 
                                        @onclick="() => ShareAudit(audit.Id)">
                                    <i class="fas fa-share me-1"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (audits.TotalPages > 1)
        {
            <nav aria-label="Audit history pagination">
                <ul class="pagination justify-content-center">
                    <li class="page-item @(currentPage <= 1 ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage <= 1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </li>
                    
                    @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(audits.TotalPages, currentPage + 2); i++)
                    {
                        <li class="page-item @(i == currentPage ? "active" : "")">
                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                        </li>
                    }
                    
                    <li class="page-item @(currentPage >= audits.TotalPages ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage >= audits.TotalPages)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </li>
                </ul>
            </nav>
        }
    }
</div>

<style>
    .audit-history-card {
        cursor: pointer;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 12px;
    }

    .audit-history-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--industrial-shadow-heavy);
        border-color: var(--industrial-teal);
    }

    .progress {
        border-radius: 10px;
        background-color: var(--industrial-light-gray);
    }

    .progress-bar {
        border-radius: 10px;
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }

    /* Industrial stats cards */
    .industrial-stats-card {
        border-radius: 12px;
        border: 1px solid var(--industrial-border);
        box-shadow: 0 4px 12px var(--industrial-shadow);
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        overflow: hidden;
    }

    .industrial-stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--industrial-shadow-heavy);
    }

    .industrial-stats-primary {
        background: linear-gradient(135deg, var(--industrial-blue) 0%, var(--industrial-steel) 100%);
        color: var(--industrial-text-light);
    }

    .industrial-stats-success {
        background: linear-gradient(135deg, var(--industrial-green) 0%, var(--industrial-teal-dark) 100%);
        color: var(--industrial-text-light);
    }

    .industrial-stats-info {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
        color: var(--industrial-text-light);
    }

    .industrial-stats-warning {
        background: linear-gradient(135deg, var(--industrial-orange) 0%, #d97706 100%);
        color: var(--industrial-text-light);
    }

    .industrial-stats-card .card-body {
        padding: 1.5rem;
        text-align: center;
    }

    .industrial-stats-card .display-6 {
        font-weight: 700;
        text-shadow: 0 2px 4px var(--industrial-shadow);
        font-family: 'Roboto Condensed', sans-serif;
    }

    .industrial-stats-card .card-title {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Badge styling */
    .badge.bg-primary { background-color: var(--industrial-blue) !important; }
    .badge.bg-success { background-color: var(--industrial-green) !important; }
    .badge.bg-secondary { background-color: var(--industrial-steel) !important; }
</style>

@code {
    private PagedResult<AuditSummaryDto>? audits;
    private bool isLoading = true;
    private int currentPage = 1;
    private int pageSize = 12;
    
    // Filters
    private string statusFilter = "";
    private DateTime? dateFrom;
    private DateTime? dateTo;
    private string searchTerm = "";
    
    protected override async Task OnInitializedAsync()
    {
        await LoadAuditHistory();
    }
    
    private async Task LoadAuditHistory()
    {
        try
        {
            isLoading = true;
            Logger.LogInformation("Loading audit history - Page: {Page}, Status: {Status}", currentPage, statusFilter);
            
            // Load completed audits (Submitted and Closed)
            var completedStatuses = string.IsNullOrEmpty(statusFilter) ? "Submitted,Closed,Cancelled" : statusFilter;
            
            var result = await AuditService.GetAssignedAuditsAsync();
            
            if (result.IsSuccess)
            {
                // Convert to PagedResult for compatibility
                var completedAudits = result.Data?.Where(a =>
                    a.OverallStatus == AuditOverallStatus.Submitted ||
                    a.OverallStatus == AuditOverallStatus.Closed ||
                    a.OverallStatus == AuditOverallStatus.Cancelled).ToList() ?? new List<Audit>();

                // Convert to DTOs
                var auditDtos = completedAudits.Select(a => new AuditSummaryDto
                {
                    Id = a.Id,
                    AuditTemplateName = a.AuditTemplate?.TemplateName ?? "Unknown",
                    AssignedToUserName = "Current User", // Would need to get from user context
                    ScheduledDate = a.ScheduledDate,
                    DueDate = a.DueDate,
                    OverallStatus = a.OverallStatus,
                    FactoryName = a.Factory?.FactoryName,
                    AreaName = a.Area?.AreaName,
                    SubAreaName = a.SubArea?.SubAreaName,
                    OverallScore = a.OverallScore.HasValue ? (double?)a.OverallScore.Value : null,
                    StartedAt = a.StartedAt,
                    CompletedAt = a.CompletedAt,
                    TotalQuestions = 0, // Would need to calculate
                    AnsweredQuestions = 0 // Would need to calculate
                }).ToList();

                audits = new PagedResult<AuditSummaryDto>
                {
                    Items = auditDtos,
                    TotalCount = auditDtos.Count,
                    PageNumber = currentPage,
                    PageSize = pageSize
                };

                Logger.LogInformation("Successfully loaded {Count} audit history items", auditDtos.Count);
            }
            else
            {
                Logger.LogWarning("Failed to load audit history: {Error}", result.ErrorMessage);
                audits = new PagedResult<AuditSummaryDto>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit history");
            audits = new PagedResult<AuditSummaryDto>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshHistory()
    {
        await LoadAuditHistory();
    }

    private async Task ApplyFilters()
    {
        currentPage = 1; // Reset to first page when filtering
        await LoadAuditHistory();
    }

    private async Task HandleSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyFilters();
        }
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= (audits?.TotalPages ?? 1))
        {
            currentPage = page;
            await LoadAuditHistory();
        }
    }

    private bool HasActiveFilters()
    {
        return !string.IsNullOrEmpty(statusFilter) ||
               dateFrom.HasValue ||
               dateTo.HasValue ||
               !string.IsNullOrEmpty(searchTerm);
    }

    private async Task ClearFilters()
    {
        statusFilter = "";
        dateFrom = null;
        dateTo = null;
        searchTerm = "";
        currentPage = 1;
        await LoadAuditHistory();
    }

    private void ViewAuditResults(string auditId)
    {
        Navigation.NavigateTo($"/audit-results/{auditId}");
    }

    private Task ShareAudit(string auditId)
    {
        // TODO: Implement share functionality
        Logger.LogInformation("Share requested for audit: {AuditId}", auditId);

        return Task.CompletedTask;
    }

    // Summary calculation methods
    private int GetCompletedCount()
    {
        return audits?.Items?.Count(a => a.OverallStatus == AuditOverallStatus.Closed) ?? 0;
    }

    private double GetAverageScore()
    {
        var scoresWithValues = audits?.Items?.Where(a => a.OverallScore.HasValue).Select(a => a.OverallScore!.Value);
        return scoresWithValues?.Any() == true ? scoresWithValues.Average() : 0;
    }

    private int GetNonConformanceCount()
    {
        // This would need to be calculated based on actual audit answers
        // For now, return a placeholder
        return audits?.Items?.Count(a => a.OverallScore < 80) ?? 0;
    }

    // Card display methods
    private string GetStatusIcon(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Submitted => "fas fa-paper-plane text-primary",
            AuditOverallStatus.Closed => "fas fa-check-circle text-success",
            AuditOverallStatus.Cancelled => "fas fa-times-circle text-secondary",
            _ => "fas fa-clipboard text-muted"
        };
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.Cancelled => "secondary",
            _ => "secondary"
        };
    }

    private string GetScoreTextClass(double score)
    {
        return score switch
        {
            >= 90 => "success",
            >= 80 => "info",
            >= 70 => "warning",
            _ => "danger"
        };
    }

    private string GetScoreProgressClass(double score)
    {
        return score switch
        {
            >= 90 => "success",
            >= 80 => "info",
            >= 70 => "warning",
            _ => "danger"
        };
    }

    private int GetQuestionCount(AuditSummaryDto audit)
    {
        // This would need to be provided by the API or calculated
        return audit.TotalQuestions;
    }

    private int GetEvidenceCount(AuditSummaryDto audit)
    {
        // This would need to be provided by the API or calculated
        return audit.EvidenceCount ?? 0;
    }

    private int GetIssueCount(AuditSummaryDto audit)
    {
        // This would need to be provided by the API or calculated
        return audit.NonConformanceCount ?? 0;
    }
}
