using FluentValidation;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Templates.Queries.GetTemplateQuestions;

/// <summary>
/// Validator for GetTemplateQuestionsQuery
/// </summary>
public class GetTemplateQuestionsQueryValidator : AbstractValidator<GetTemplateQuestionsQuery>
{
    private readonly IApplicationDbContext _context;

    public GetTemplateQuestionsQueryValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.TemplateId)
            .GreaterThan(0).WithMessage("Template ID must be greater than 0")
            .MustAsync(BeValidTemplate).WithMessage("Template does not exist");

        RuleFor(x => x.QuestionGroupId)
            .MustAsync(BeValidQuestionGroup).WithMessage("Question group does not exist or does not belong to the template")
            .When(x => x.QuestionGroupId.HasValue);
    }

    private async Task<bool> BeValidTemplate(int templateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates
            .AnyAsync(t => t.Id == templateId && t.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidQuestionGroup(GetTemplateQuestionsQuery query, int? questionGroupId, CancellationToken cancellationToken)
    {
        if (!questionGroupId.HasValue)
            return true;

        return await _context.QuestionGroups
            .AnyAsync(g => g.Id == questionGroupId.Value && 
                          g.AuditTemplateId == query.TemplateId && 
                          g.IsActive, cancellationToken);
    }
}
