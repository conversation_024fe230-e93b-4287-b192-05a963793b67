using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAuditAnswers;

/// <summary>
/// Handler for GetAuditAnswersQuery
/// </summary>
public class GetAuditAnswersQueryHandler : BaseQueryHandler<GetAuditAnswersQuery, List<AuditAnswerDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAuditAnswersQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<AuditAnswerDto>> Handle(GetAuditAnswersQuery request, CancellationToken cancellationToken)
    {
        // Verify audit exists
        var auditExists = await _context.Audits
            .AnyAsync(a => a.Id == request.AuditId, cancellationToken);

        if (!auditExists)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        var query = _context.AuditAnswers
            .Include(a => a.Question)
            .Include(a => a.SelectedOption)
            .Include(a => a.SelectedOptions)
                .ThenInclude(so => so.QuestionOption)
            .Include(a => a.FailureReasons)
            .Include(a => a.Attachments)
            .Where(a => a.AuditId == request.AuditId);

        // Apply question filter if specified
        if (request.QuestionId.HasValue)
        {
            query = query.Where(a => a.QuestionId == request.QuestionId.Value);
        }

        var answers = await query
            .OrderBy(a => a.Question.DisplayOrder)
            .ThenBy(a => a.QuestionId)
            .Select(a => new AuditAnswerDto
            {
                Id = a.Id,
                AuditId = a.AuditId,
                QuestionId = a.QuestionId,
                QuestionText = a.Question.QuestionText,
                QuestionType = a.Question.QuestionType,
                AnswerValue = a.GetDisplayValue(),
                IsNotApplicable = a.IsNotApplicable,
                Comments = a.Comments,
                SeverityLevel = a.SeverityLevel,
                CreatedAt = a.CreatedAt,
                UpdatedAt = a.UpdatedAt,
                CreatedByUserId = a.CreatedByUserId,
                UpdatedByUserId = a.UpdatedByUserId,
                SelectedOptions = a.SelectedOptions.Select(so => new AuditAnswerSelectedOptionDto
                {
                    Id = so.Id,
                    AuditAnswerId = so.AuditAnswerId,
                    QuestionOptionId = so.QuestionOptionId,
                    OptionText = so.QuestionOption.OptionText,
                    OptionValue = so.QuestionOption.OptionValue
                }).ToList(),
                FailureReasons = a.FailureReasons.Select(fr => new AuditAnswerFailureReasonDto
                {
                    Id = fr.Id,
                    AuditAnswerId = fr.AuditAnswerId,
                    FailureReasonText = fr.ReasonText
                }).ToList(),
                Attachments = a.Attachments.Select(att => new AuditAttachmentDto
                {
                    Id = att.Id,
                    AuditAnswerId = att.AuditAnswerId,
                    FileName = att.FileName,
                    OriginalFileName = att.OriginalFileName,
                    ContentType = att.ContentType,
                    FileSize = att.FileSize ?? 0,
                    FilePath = att.FilePath,
                    Description = att.Description,
                    CreatedAt = att.UploadedAt,
                    CreatedByUserId = att.UploadedByUserId
                }).ToList()
            })
            .ToListAsync(cancellationToken);

        return answers;
    }
}
