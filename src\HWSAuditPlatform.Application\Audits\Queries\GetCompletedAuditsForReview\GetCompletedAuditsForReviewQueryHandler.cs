using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetCompletedAuditsForReview;

/// <summary>
/// Handler for GetCompletedAuditsForReviewQuery
/// </summary>
public class GetCompletedAuditsForReviewQueryHandler : BaseQueryHandler<GetCompletedAuditsForReviewQuery, PaginatedResult<AuditReviewDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetCompletedAuditsForReviewQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<PaginatedResult<AuditReviewDto>> Handle(GetCompletedAuditsForReviewQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        var currentUserRole = _currentUserService.Role;

        // Get user's accessible factories/areas based on role
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .Where(a => a.OverallStatus == AuditOverallStatus.Submitted ||
                       a.OverallStatus == AuditOverallStatus.ManagerReviewed ||
                       a.OverallStatus == AuditOverallStatus.PendingManagerReview ||
                       a.OverallStatus == AuditOverallStatus.Closed)
            .AsQueryable();

        // Apply role-based filtering - ProcessOwners can only see audits in their assigned areas
        if (currentUserRole == UserRole.ProcessOwner)
        {
            // Get user's primary factory to limit access
            var currentUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == currentUserId, cancellationToken);

            if (currentUser?.FactoryId.HasValue == true)
            {
                query = query.Where(a => a.FactoryId == currentUser.FactoryId.Value);
            }
        }

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(a =>
                EF.Functions.Like(a.AuditTemplate.TemplateName, searchPattern) ||
                (a.AssignedToUser != null && EF.Functions.Like(a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName, searchPattern)) ||
                EF.Functions.Like(a.Factory.FactoryName, searchPattern) ||
                EF.Functions.Like(a.Area.AreaName, searchPattern));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == request.Status.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(a => a.AreaId == request.AreaId.Value);
        }

        if (request.SubAreaId.HasValue)
        {
            query = query.Where(a => a.SubAreaId == request.SubAreaId.Value);
        }

        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(a => a.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.CompletedDateFrom.HasValue)
        {
            query = query.Where(a => a.CompletedAt >= request.CompletedDateFrom.Value);
        }

        if (request.CompletedDateTo.HasValue)
        {
            query = query.Where(a => a.CompletedAt <= request.CompletedDateTo.Value);
        }

        if (!string.IsNullOrEmpty(request.AssignedToUserId))
        {
            query = query.Where(a => a.AssignedToUserId == request.AssignedToUserId);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "templatename" => request.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.AuditTemplate.TemplateName)
                : query.OrderBy(a => a.AuditTemplate.TemplateName),
            "assigneduser" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.AssignedToUser!.FirstName + " " + a.AssignedToUser.LastName)
                : query.OrderBy(a => a.AssignedToUser!.FirstName + " " + a.AssignedToUser.LastName),
            "factory" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.Factory.FactoryName)
                : query.OrderBy(a => a.Factory.FactoryName),
            "area" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.Area.AreaName)
                : query.OrderBy(a => a.Area.AreaName),
            "status" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.OverallStatus)
                : query.OrderBy(a => a.OverallStatus),
            "score" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.OverallScore)
                : query.OrderBy(a => a.OverallScore),
            _ => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.CompletedAt)
                : query.OrderBy(a => a.CompletedAt)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and select data
        var audits = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(a => new AuditReviewDto
            {
                Id = a.Id,
                AuditTemplateName = a.AuditTemplate.TemplateName,
                AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                AssignedToUserId = a.AssignedToUserId,
                ScheduledDate = a.ScheduledDate,
                DueDate = a.DueDate,
                StartedAt = a.StartedAt,
                CompletedAt = a.CompletedAt,
                OverallStatus = a.OverallStatus,
                FactoryName = a.Factory.FactoryName,
                AreaName = a.Area.AreaName,
                SubAreaName = a.SubArea != null ? a.SubArea.SubAreaName : null,
                OverallScore = a.OverallScore,
                ManagerComments = a.ManagerComments,
                ReviewedByUserName = a.ReviewedByUser != null ? $"{a.ReviewedByUser.FirstName} {a.ReviewedByUser.LastName}".Trim() : null,
                ReviewedAt = a.ReviewedAt,
                IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow && 
                    a.OverallStatus != AuditOverallStatus.Closed && 
                    a.OverallStatus != AuditOverallStatus.Cancelled,
                // Basic counts - will be populated separately if needed
                TotalQuestions = 0,
                AnsweredQuestions = 0,
                PassedQuestions = 0,
                FailedQuestions = 0,
                NotApplicableQuestions = 0,
                QuestionsWithFindings = 0,
                TotalAttachments = 0,
                Answers = new List<AuditAnswerDto>()
            })
            .ToListAsync(cancellationToken);

        // If detailed answers are requested, load them separately to avoid complex joins
        if (request.IncludeAnswers && audits.Any())
        {
            var auditIds = audits.Select(a => a.Id).ToList();
            await LoadDetailedAnswers(auditIds, audits, cancellationToken);
        }
        else
        {
            // Load basic statistics for each audit
            await LoadBasicStatistics(audits.Select(a => a.Id).ToList(), audits, cancellationToken);
        }

        return new PaginatedResult<AuditReviewDto>(audits, totalCount, request.PageNumber, request.PageSize);
    }

    private async Task LoadBasicStatistics(List<string> auditIds, List<AuditReviewDto> audits, CancellationToken cancellationToken)
    {
        if (!auditIds.Any()) return;

        var statistics = await _context.AuditAnswers
            .Where(aa => auditIds.Contains(aa.AuditId))
            .GroupBy(aa => aa.AuditId)
            .Select(g => new
            {
                AuditId = g.Key,
                TotalAnswers = g.Count(),
                PassedAnswers = g.Count(aa => aa.AnswerBoolean == true),
                FailedAnswers = g.Count(aa => aa.AnswerBoolean == false),
                NotApplicableAnswers = g.Count(aa => aa.IsNotApplicable),
                AnswersWithFindings = g.Count(aa => aa.FailureReasons.Any()),
                TotalAttachments = g.Sum(aa => aa.Attachments.Count())
            })
            .ToListAsync(cancellationToken);

        foreach (var audit in audits)
        {
            var stat = statistics.FirstOrDefault(s => s.AuditId == audit.Id);
            if (stat != null)
            {
                audit.AnsweredQuestions = stat.TotalAnswers;
                audit.PassedQuestions = stat.PassedAnswers;
                audit.FailedQuestions = stat.FailedAnswers;
                audit.NotApplicableQuestions = stat.NotApplicableAnswers;
                audit.QuestionsWithFindings = stat.AnswersWithFindings;
                audit.TotalAttachments = stat.TotalAttachments;
            }
        }
    }

    private async Task LoadDetailedAnswers(List<string> auditIds, List<AuditReviewDto> audits, CancellationToken cancellationToken)
    {
        if (!auditIds.Any()) return;

        var answers = await _context.AuditAnswers
            .Include(a => a.Question)
            .Include(a => a.SelectedOption)
            .Include(a => a.SelectedOptions)
                .ThenInclude(so => so.QuestionOption)
            .Include(a => a.FailureReasons)
            .Include(a => a.Attachments)
            .Where(a => auditIds.Contains(a.AuditId))
            .OrderBy(a => a.Question.DisplayOrder)
            .ThenBy(a => a.QuestionId)
            .Select(a => new AuditAnswerDto
            {
                Id = a.Id,
                AuditId = a.AuditId,
                QuestionId = a.QuestionId,
                QuestionText = a.Question.QuestionText,
                QuestionType = a.Question.QuestionType,
                AnswerValue = a.GetDisplayValue(),
                IsNotApplicable = a.IsNotApplicable,
                Comments = a.Comments,
                SeverityLevel = a.SeverityLevel,
                CreatedAt = a.CreatedAt,
                UpdatedAt = a.UpdatedAt,
                CreatedByUserId = a.CreatedByUserId,
                UpdatedByUserId = a.UpdatedByUserId,
                SelectedOptions = a.SelectedOptions.Select(so => new AuditAnswerSelectedOptionDto
                {
                    Id = so.Id,
                    AuditAnswerId = so.AuditAnswerId,
                    QuestionOptionId = so.QuestionOptionId,
                    OptionText = so.QuestionOption.OptionText,
                    OptionValue = so.QuestionOption.OptionValue
                }).ToList(),
                FailureReasons = a.FailureReasons.Select(fr => new AuditAnswerFailureReasonDto
                {
                    Id = fr.Id,
                    AuditAnswerId = fr.AuditAnswerId,
                    FailureReasonText = fr.ReasonText
                }).ToList(),
                Attachments = a.Attachments.Select(att => new AuditAttachmentDto
                {
                    Id = att.Id,
                    AuditAnswerId = att.AuditAnswerId,
                    FileName = att.FileName,
                    OriginalFileName = att.OriginalFileName,
                    ContentType = att.ContentType,
                    FileSize = att.FileSize ?? 0,
                    FilePath = att.FilePath,
                    Description = att.Description,
                    CreatedAt = att.UploadedAt,
                    CreatedByUserId = att.UploadedByUserId
                }).ToList()
            })
            .ToListAsync(cancellationToken);

        // Group answers by audit and populate the audit DTOs
        var answersByAudit = answers.GroupBy(a => a.AuditId).ToDictionary(g => g.Key, g => g.ToList());

        foreach (var audit in audits)
        {
            if (answersByAudit.TryGetValue(audit.Id, out var auditAnswers))
            {
                audit.Answers = auditAnswers;
                audit.AnsweredQuestions = auditAnswers.Count;
                audit.PassedQuestions = auditAnswers.Count(a => a.AnswerValue == "Yes" || a.AnswerValue == "True");
                audit.FailedQuestions = auditAnswers.Count(a => a.AnswerValue == "No" || a.AnswerValue == "False");
                audit.NotApplicableQuestions = auditAnswers.Count(a => a.IsNotApplicable);
                audit.QuestionsWithFindings = auditAnswers.Count(a => a.FailureReasons.Any());
                audit.TotalAttachments = auditAnswers.Sum(a => a.Attachments.Count);
            }
        }
    }
}
