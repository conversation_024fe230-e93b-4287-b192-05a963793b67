# HWS Audit Platform - Developer Getting Started Guide

Welcome to the HWS Audit Platform development team! This comprehensive guide will help you get up and running with the project quickly and efficiently.

## Project Overview

### What is the HWS Audit Platform?

The **HWS Audit Platform** is a comprehensive manufacturing quality audit management system built with .NET 9, implementing Clean Architecture principles with Domain-Driven Design (DDD). The platform supports offline-capable Progressive Web Apps (PWA), Active Directory integration, and enterprise-scale audit workflows.

### Key Business Objectives

- **Improve Audit Efficiency**: Reduce audit completion time by 50% through digital workflows
- **Enhance Data Quality**: Eliminate transcription errors and ensure consistent data collection
- **Enable Offline Operations**: Support auditing in manufacturing environments with limited connectivity
- **Provide Real-time Insights**: Instant visibility into audit progress and results
- **Ensure Compliance**: Meet regulatory requirements with comprehensive audit trails

### Target Users

- **Auditors**: Conduct audits using mobile devices and tablets
- **Audit Managers**: Oversee audit programs and review results
- **System Administrators**: Manage users, templates, and system configuration
- **Quality Managers**: Analyze audit data and generate reports

## System Architecture

### Clean Architecture Layers

The platform follows Clean Architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────┐
│        Presentation (API)           │  ← Controllers, HTTP handling
├─────────────────────────────────────┤
│         Application                 │  ← Use cases, CQRS, validation
├─────────────────────────────────────┤
│           Domain                    │  ← Business logic, entities
├─────────────────────────────────────┤
│       Infrastructure               │  ← Database, external services
└─────────────────────────────────────┘
```

### Layer Responsibilities

- **Domain Layer**: Contains business entities, rules, and domain logic (no dependencies)
- **Application Layer**: Orchestrates business workflows using CQRS pattern (depends only on Domain)
- **Infrastructure Layer**: Implements external concerns like database access and file storage
- **Presentation Layer**: Handles HTTP requests, authentication, and user interfaces

### Project Structure

```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration (CQRS)
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns implementation
│   ├── HWSAuditPlatform.ApiService/      # Web API controllers
│   ├── HWSAuditPlatform.Web/            # Blazor web application
│   ├── HWSAuditPlatform.AppHost/        # .NET Aspire orchestration
│   ├── HWSAuditPlatform.ServiceDefaults/ # Shared service configuration
│   ├── HWSAuditPlatform.SchedulerWorker/ # Background services
│   └── HWSAuditPlatform.Tests/          # Unit and integration tests
├── docs/                                 # Documentation
└── scripts/                             # Deployment and utility scripts
```

## Development Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

- **.NET 9 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Visual Studio 2024** or **VS Code** with C# extension
- **SQL Server** - LocalDB, Express, or full version
- **Git** - For version control
- **Docker Desktop** - For containerized development (optional)

### Quick Setup

#### 1. Clone the Repository

```bash
git clone [repository-url]
cd hws-audit-platform
```

#### 2. Restore NuGet Packages

```bash
dotnet restore
```

#### 3. Configure Database Connection

Create or update `src/HWSAuditPlatform.ApiService/appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true",
    "SupportConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformSupport_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  }
}
```

#### 4. Create and Update Database

```bash
# Create main database migration
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Create support database (if migrations exist)
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService --context SupportDbContext
```

#### 5. Run the Application

```bash
# Option 1: Run API Service directly
dotnet run --project src/HWSAuditPlatform.ApiService

# Option 2: Use AppHost for orchestrated startup
dotnet run --project src/HWSAuditPlatform.AppHost
```

#### 6. Verify Setup

- **API Documentation (Scalar UI)**: `https://localhost:7490/scalar/v1`
- **Swagger UI (fallback)**: `https://localhost:7490/swagger`
- **Health Check**: `https://localhost:7490/health`

### Alternative: Docker Environment

For a complete containerized development environment:

```powershell
# Start Docker environment (includes SQL Server and Active Directory)
./docker-start.ps1

# Run the application with Docker environment
$env:ASPNETCORE_ENVIRONMENT="Docker"
dotnet run --project src/HWSAuditPlatform.ApiService
```

## Key Technical Concepts

### Domain-Driven Design (DDD)

The project follows DDD principles:

- **Entities**: Core business objects with identity (User, Audit, AuditTemplate)
- **Value Objects**: Immutable objects without identity (Address, AuditStatus)
- **Domain Events**: Business events that trigger side effects
- **Aggregates**: Consistency boundaries for related entities
- **Domain Services**: Complex business logic that doesn't belong to a single entity

### CQRS Pattern

- **Commands**: Modify application state (CreateUser, UpdateAudit)
- **Queries**: Return data without side effects (GetUsers, GetAudits)
- **Handlers**: Process commands and queries using MediatR
- **DTOs**: Data transfer objects for API communication

### Active Directory Integration

All users in the HWS Audit Platform are synchronized from Active Directory:

- **No Offline Registration**: Users cannot register directly in the application
- **Automatic Sync**: Background service synchronizes users and groups from AD
- **Role Mapping**: AD groups are mapped to application roles (Admin, Manager, Auditor)
- **Authentication**: Users authenticate using their AD credentials

### Database Architecture

#### Main Business Database
- Stores core entities: Users, Audits, AuditTemplates, Factories, etc.
- Uses CUID (25-character) identifiers for offline-capable entities
- Implements soft delete and audit trails
- Optimized for business operations and reporting

#### Support Database
- **API Keys**: User-generated API keys for programmatic access
- **Event Store**: Domain events and system events for auditing
- **Feature Flags**: Dynamic feature toggling and A/B testing
- **Telemetry**: Application usage analytics and performance metrics
- **Caching**: Application-level caching for performance

### Offline-First Architecture

The platform supports offline operations through:

- **Progressive Web App (PWA)**: Installable web application
- **Local Storage**: Client-side data caching
- **Sync Mechanisms**: Conflict resolution and data synchronization
- **CUID Identifiers**: Client-generated unique IDs for offline creation

## Technology Stack

### Core Technologies
- **.NET 9**: Latest framework with performance improvements
- **ASP.NET Core**: Web API framework
- **Entity Framework Core 8**: Object-relational mapping
- **SQL Server**: Primary database
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Input validation

### Infrastructure Technologies
- **Local File Storage**: File storage implementation
- **System.DirectoryServices**: Active Directory integration
- **Serilog**: Structured logging

### Development Tools
- **Visual Studio 2024**: Primary IDE
- **xUnit**: Unit testing framework
- **FluentAssertions**: Test assertions
- **Moq**: Mocking framework
- **Scalar UI**: API documentation (preferred over Swagger UI)

## Next Steps

Now that you have the development environment set up, here are your next steps:

1. **Explore the Codebase**: Start with the Domain layer to understand the business entities
2. **Review Architecture Documentation**: Read the [Architecture Overview](../architecture/README.md)
3. **Understand Testing**: Review the [Testing Guide](testing-guide.md)
4. **Learn the API**: Explore the [API Documentation](../api/README.md)
5. **Practice Development**: Try adding a simple entity following our [Coding Standards](coding-standards.md)

## Getting Help

- **Documentation**: Comprehensive documentation in the `/docs` folder
- **Code Examples**: Look at existing entities and handlers for patterns
- **Team Resources**: Reach out to team members for guidance
- **Testing**: Run tests frequently to ensure your changes work correctly

Welcome to the team! 🚀
