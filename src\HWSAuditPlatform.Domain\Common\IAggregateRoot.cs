namespace HWSAuditPlatform.Domain.Common;

/// <summary>
/// Marker interface for aggregate roots in the domain
/// </summary>
public interface IAggregateRoot
{
    /// <summary>
    /// Gets the domain events that have been raised by this aggregate
    /// </summary>
    IReadOnlyCollection<IDomainEvent> DomainEvents { get; }

    /// <summary>
    /// Clears all domain events from this aggregate
    /// </summary>
    void ClearDomainEvents();
}
