using FluentAssertions;
using FluentValidation.TestHelper;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class CreateAuditTemplateCommandValidatorTests : BaseDbTestClass
{
    private readonly CreateAuditTemplateCommandValidator _validator;

    public CreateAuditTemplateCommandValidatorTests()
    {
        _validator = new CreateAuditTemplateCommandValidator(Context);
    }

    [Fact]
    public async Task Validate_WithValidCommand_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "New Safety Template",
            Description = "A comprehensive safety audit template",
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithEmptyTemplateName_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = string.Empty,
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.TemplateName)
            .WithErrorMessage("Template name is required");
    }

    [Fact]
    public async Task Validate_WithTooLongTemplateName_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = new string('A', 256), // Exceeds 255 character limit
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.TemplateName)
            .WithErrorMessage("Template name cannot exceed 255 characters");
    }

    [Fact]
    public async Task Validate_WithTooLongDescription_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Valid Template Name",
            Description = new string('A', 2001), // Exceeds 2000 character limit
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Description)
            .WithErrorMessage("Description cannot exceed 2000 characters");
    }

    [Fact]
    public async Task Validate_WithZeroVersion_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Valid Template Name",
            Version = 0
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Version)
            .WithErrorMessage("Version must be greater than 0");
    }

    [Fact]
    public async Task Validate_WithNegativeVersion_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Valid Template Name",
            Version = -1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Version)
            .WithErrorMessage("Version must be greater than 0");
    }

    [Fact]
    public async Task Validate_WithDuplicateTemplateNameAndVersion_ShouldHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateExistingTemplateAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Existing Template", // Same name as existing
            Version = 1 // Same version as existing
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.TemplateName)
            .WithErrorMessage("A template with this name and version already exists");
    }

    [Fact]
    public async Task Validate_WithSameNameButDifferentVersion_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateExistingTemplateAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Existing Template", // Same name as existing
            Version = 2 // Different version
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithNullDescription_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Template Without Description",
            Description = null,
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithMaxLengthTemplateName_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = new string('A', 255), // Exactly 255 characters
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithMaxLengthDescription_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Valid Template Name",
            Description = new string('A', 2000), // Exactly 2000 characters
            Version = 1
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public async Task Validate_WithHighVersion_ShouldNotHaveValidationError()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "High Version Template",
            Version = 999
        };

        // Act
        var result = await _validator.TestValidateAsync(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    private async Task CreateExistingTemplateAsync()
    {
        var existingTemplate = new AuditTemplate
        {
            TemplateName = "Existing Template",
            Version = 1,
            IsPublished = false,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(existingTemplate);
        await Context.SaveChangesAsync();
    }
}
