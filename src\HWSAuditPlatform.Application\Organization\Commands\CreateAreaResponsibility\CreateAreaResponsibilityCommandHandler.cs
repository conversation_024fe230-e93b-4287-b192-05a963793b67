using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;

/// <summary>
/// Handler for creating area responsibility assignments
/// </summary>
public class CreateAreaResponsibilityCommandHandler : BaseCommandHandler<CreateAreaResponsibilityCommand, AreaResponsibilityDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAreaResponsibilityService _areaResponsibilityService;

    public CreateAreaResponsibilityCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IAreaResponsibilityService areaResponsibilityService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _areaResponsibilityService = areaResponsibilityService;
    }

    public override async Task<AreaResponsibilityDto> Handle(CreateAreaResponsibilityCommand request, CancellationToken cancellationToken)
    {
        // Validate that the area exists
        var area = await _context.Areas
            .Include(a => a.Factory)
            .FirstOrDefaultAsync(a => a.Id == request.AreaId && a.IsActive, cancellationToken);

        if (area == null)
        {
            throw new NotFoundException(nameof(Area), request.AreaId);
        }

        // Validate that the responsible user exists
        var responsibleUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == request.ResponsibleUserId, cancellationToken);

        if (responsibleUser == null)
        {
            throw new NotFoundException("User", request.ResponsibleUserId);
        }

        // Validate audit template if specified
        if (request.AuditTemplateId.HasValue)
        {
            var template = await _context.AuditTemplates
                .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId.Value && t.IsActive, cancellationToken);

            if (template == null)
            {
                throw new NotFoundException("AuditTemplate", request.AuditTemplateId.Value);
            }

            if (!template.EnableAreaBasedResponsibility)
            {
                throw new ValidationException("AuditTemplateId", "Area-based responsibility is not enabled for this audit template.");
            }
        }

        // Create the area responsibility
        var areaResponsibility = new AreaResponsibility
        {
            Id = CuidGenerator.Generate(),
            AreaId = request.AreaId,
            ResponsibilityType = request.ResponsibilityType,
            ResponsibleUserId = request.ResponsibleUserId,
            AuditTemplateId = request.AuditTemplateId,
            IsActive = request.IsActive,
            Description = request.Description,
            Priority = request.Priority
        };

        _context.AreaResponsibilities.Add(areaResponsibility);
        await _context.SaveChangesAsync(cancellationToken);

        // Return the created responsibility as DTO
        return new AreaResponsibilityDto
        {
            Id = areaResponsibility.Id,
            AreaId = areaResponsibility.AreaId,
            AreaName = area.AreaName,
            ResponsibilityType = areaResponsibility.ResponsibilityType,
            ResponsibilityTypeName = areaResponsibility.ResponsibilityType.ToString(),
            ResponsibleUserId = areaResponsibility.ResponsibleUserId,
            ResponsibleUserName = responsibleUser.Username,
            ResponsibleUserFullName = $"{responsibleUser.FirstName} {responsibleUser.LastName}",
            AuditTemplateId = areaResponsibility.AuditTemplateId,
            IsActive = areaResponsibility.IsActive,
            Description = areaResponsibility.Description,
            Priority = areaResponsibility.Priority,
            IsTemplateSpecific = areaResponsibility.IsTemplateSpecific,
            IsEffective = areaResponsibility.IsEffective,
            CreatedAt = areaResponsibility.CreatedAt,
            UpdatedAt = areaResponsibility.UpdatedAt,
            RecordVersion = areaResponsibility.RecordVersion,
            CreatedByUserId = areaResponsibility.CreatedByUserId,
            UpdatedByUserId = areaResponsibility.UpdatedByUserId
        };
    }
}
