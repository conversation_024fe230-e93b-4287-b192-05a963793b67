﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveFailureReasonId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AuditAnswerFailureReasons_FailureReasonId",
                table: "hwsap_aud_answer_failure_reasons");

            migrationBuilder.DropColumn(
                name: "FailureReasonId",
                table: "hwsap_aud_answer_failure_reasons");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FailureReasonId",
                table: "hwsap_aud_answer_failure_reasons",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswerFailureReasons_FailureReasonId",
                table: "hwsap_aud_answer_failure_reasons",
                column: "FailureReasonId");
        }
    }
}
