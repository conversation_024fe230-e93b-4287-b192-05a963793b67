@if (HasError)
{
    <div class="alert alert-@AlertType alert-dismissible fade show" role="alert">
        <i class="bi @IconClass me-2"></i>
        <strong>@Title</strong>
        @if (!string.IsNullOrEmpty(Message))
        {
            <div class="mt-1">@Message</div>
        }
        @if (ShowRetry && OnRetry.HasDelegate)
        {
            <div class="mt-2">
                <button type="button" class="btn btn-sm btn-outline-@AlertType" @onclick="OnRetry">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
            </div>
        }
        @if (Dismissible)
        {
            <button type="button" class="btn-close" @onclick="Dismiss" aria-label="Close"></button>
        }
    </div>
}

<style>
    .alert {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
    }

    .alert-warning {
        background-color: #fff3cd;
        color: #856404;
    }

    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
    }
</style>

@code {
    [Parameter] public bool HasError { get; set; }
    [Parameter] public string Title { get; set; } = "Error";
    [Parameter] public string? Message { get; set; }
    [Parameter] public string AlertType { get; set; } = "danger"; // danger, warning, info
    [Parameter] public bool Dismissible { get; set; } = true;
    [Parameter] public bool ShowRetry { get; set; } = false;
    [Parameter] public EventCallback OnRetry { get; set; }
    [Parameter] public EventCallback OnDismiss { get; set; }

    private string IconClass => AlertType switch
    {
        "danger" => "bi-exclamation-triangle-fill",
        "warning" => "bi-exclamation-triangle",
        "info" => "bi-info-circle",
        _ => "bi-exclamation-triangle-fill"
    };

    private async Task Dismiss()
    {
        HasError = false;
        if (OnDismiss.HasDelegate)
        {
            await OnDismiss.InvokeAsync();
        }
    }
}
