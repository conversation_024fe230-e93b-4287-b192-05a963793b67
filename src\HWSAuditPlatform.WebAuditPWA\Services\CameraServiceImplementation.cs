using Microsoft.JSInterop;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Implementation of camera service using JavaScript interop
/// </summary>
public class CameraServiceImplementation : ICameraService
{
    protected readonly IJSRuntime _jsRuntime;
    protected readonly ILogger<CameraServiceImplementation> _logger;

    public CameraServiceImplementation(IJSRuntime jsRuntime, ILogger<CameraServiceImplementation> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task<bool> IsCameraAvailableAsync()
    {
        try
        {
            var isAvailable = await _jsRuntime.InvokeAsync<bool>("isCameraAvailable");
            _logger.LogDebug("Camera availability check: {IsAvailable}", isAvailable);
            return isAvailable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking camera availability");
            return false;
        }
    }

    public async Task<Result<byte[]>> CapturePhotoAsync()
    {
        try
        {
            _logger.LogInformation("Starting photo capture");

            var isAvailable = await IsCameraAvailableAsync();
            if (!isAvailable)
            {
                return Result<byte[]>.Failure("Camera is not available on this device");
            }

            var imageData = await _jsRuntime.InvokeAsync<byte[]>("capturePhoto");
            
            if (imageData != null && imageData.Length > 0)
            {
                _logger.LogInformation("Successfully captured photo: {Size} bytes", imageData.Length);
                return Result<byte[]>.Success(imageData);
            }
            else
            {
                return Result<byte[]>.Failure("Failed to capture photo or user cancelled");
            }
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error during photo capture");
            return Result<byte[]>.Failure($"Camera error: {jsEx.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing photo");
            return Result<byte[]>.Failure("An unexpected error occurred while capturing photo");
        }
    }

    public async Task<Result<byte[]>> SelectPhotoAsync()
    {
        try
        {
            _logger.LogInformation("Starting photo selection");

            var imageData = await _jsRuntime.InvokeAsync<byte[]>("selectPhoto");
            
            if (imageData != null && imageData.Length > 0)
            {
                _logger.LogInformation("Successfully selected photo: {Size} bytes", imageData.Length);
                return Result<byte[]>.Success(imageData);
            }
            else
            {
                return Result<byte[]>.Failure("No photo selected or user cancelled");
            }
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error during photo selection");
            return Result<byte[]>.Failure($"Photo selection error: {jsEx.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error selecting photo");
            return Result<byte[]>.Failure("An unexpected error occurred while selecting photo");
        }
    }

    public async Task<byte[]> ResizeImageAsync(byte[] imageData, int maxWidth, int maxHeight, int quality = 80)
    {
        try
        {
            _logger.LogInformation("Resizing image: {OriginalSize} bytes to max {MaxWidth}x{MaxHeight} at {Quality}% quality", 
                imageData.Length, maxWidth, maxHeight, quality);

            var resizedData = await _jsRuntime.InvokeAsync<byte[]>("resizeImage", imageData, maxWidth, maxHeight, quality / 100.0);
            
            if (resizedData != null && resizedData.Length > 0)
            {
                _logger.LogInformation("Successfully resized image: {OriginalSize} -> {NewSize} bytes", 
                    imageData.Length, resizedData.Length);
                return resizedData;
            }
            else
            {
                _logger.LogWarning("Image resize failed, returning original image");
                return imageData;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resizing image, returning original");
            return imageData;
        }
    }
}

/// <summary>
/// Camera capture options
/// </summary>
public class CameraCaptureOptions
{
    public int MaxWidth { get; set; } = 1920;
    public int MaxHeight { get; set; } = 1080;
    public double Quality { get; set; } = 0.8;
    public string FacingMode { get; set; } = "environment";
    public bool AllowGallerySelection { get; set; } = true;
    public long MaxFileSizeBytes { get; set; } = 10 * 1024 * 1024; // 10MB
}

/// <summary>
/// Camera capture options for JavaScript interop
/// </summary>
public class CameraCaptureOptionsJs
{
    public int MaxWidth { get; set; } = 1920;
    public int MaxHeight { get; set; } = 1080;
    public double Quality { get; set; } = 0.8;
    public string FacingMode { get; set; } = "environment";
    public bool AllowGallerySelection { get; set; } = true;
}

/// <summary>
/// Captured image info for JavaScript interop
/// </summary>
public class CapturedImageInfoJs
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = "image/jpeg";
    public long Size { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string CapturedAt { get; set; } = string.Empty;
}

/// <summary>
/// File information for captured/selected images
/// </summary>
public class CapturedImageInfo
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = "image/jpeg";
    public long Size { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public DateTime CapturedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Enhanced camera service with additional features
/// </summary>
public interface IEnhancedCameraService : ICameraService
{
    /// <summary>
    /// Captures a photo with specific options
    /// </summary>
    Task<Result<CapturedImageInfo>> CapturePhotoWithOptionsAsync(CameraCaptureOptions options);

    /// <summary>
    /// Selects a photo with specific options
    /// </summary>
    Task<Result<CapturedImageInfo>> SelectPhotoWithOptionsAsync(CameraCaptureOptions options);

    /// <summary>
    /// Gets camera capabilities
    /// </summary>
    Task<CameraCapabilities> GetCameraCapabilitiesAsync();

    /// <summary>
    /// Validates image file
    /// </summary>
    Task<Result<bool>> ValidateImageAsync(byte[] imageData, long maxSizeBytes = 10 * 1024 * 1024);
}

/// <summary>
/// Camera capabilities information
/// </summary>
public class CameraCapabilities
{
    public bool HasCamera { get; set; }
    public bool HasFrontCamera { get; set; }
    public bool HasBackCamera { get; set; }
    public bool SupportsFlash { get; set; }
    public bool SupportsZoom { get; set; }
    public List<string> SupportedFormats { get; set; } = new();
    public List<CameraResolution> SupportedResolutions { get; set; } = new();
}

/// <summary>
/// Camera resolution information
/// </summary>
public class CameraResolution
{
    public int Width { get; set; }
    public int Height { get; set; }
    public string Label => $"{Width}x{Height}";
}

/// <summary>
/// Enhanced camera service implementation
/// </summary>
public class EnhancedCameraService : CameraServiceImplementation, IEnhancedCameraService
{
    public EnhancedCameraService(IJSRuntime jsRuntime, ILogger<CameraServiceImplementation> logger) 
        : base(jsRuntime, logger)
    {
    }

    public async Task<Result<CapturedImageInfo>> CapturePhotoWithOptionsAsync(CameraCaptureOptions options)
    {
        try
        {
            _logger.LogInformation("Starting photo capture with options");

            var isAvailable = await IsCameraAvailableAsync();
            if (!isAvailable)
            {
                return Result<CapturedImageInfo>.Failure("Camera is not available on this device");
            }

            var jsOptions = new CameraCaptureOptionsJs
            {
                MaxWidth = options.MaxWidth,
                MaxHeight = options.MaxHeight,
                Quality = options.Quality,
                FacingMode = options.FacingMode,
                AllowGallerySelection = options.AllowGallerySelection
            };

            var result = await _jsRuntime.InvokeAsync<CapturedImageInfoJs>("capturePhotoWithOptions", jsOptions);

            if (result?.Data != null && result.Data.Length > 0)
            {
                var imageInfo = new CapturedImageInfo
                {
                    Data = result.Data,
                    FileName = result.FileName ?? $"photo_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                    ContentType = result.ContentType ?? "image/jpeg",
                    Size = result.Size,
                    Width = result.Width,
                    Height = result.Height,
                    CapturedAt = DateTime.TryParse(result.CapturedAt, out var capturedAt) ? capturedAt : DateTime.UtcNow
                };

                _logger.LogInformation("Successfully captured photo with options: {Size} bytes, {Width}x{Height}",
                    imageInfo.Size, imageInfo.Width, imageInfo.Height);

                return Result<CapturedImageInfo>.Success(imageInfo);
            }
            else
            {
                return Result<CapturedImageInfo>.Failure("Failed to capture photo or user cancelled");
            }
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error during photo capture with options");
            return Result<CapturedImageInfo>.Failure($"Camera error: {jsEx.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing photo with options");
            return Result<CapturedImageInfo>.Failure($"Error capturing photo: {ex.Message}");
        }
    }

    public async Task<Result<CapturedImageInfo>> SelectPhotoWithOptionsAsync(CameraCaptureOptions options)
    {
        try
        {
            _logger.LogInformation("Starting photo selection with options");

            var jsOptions = new CameraCaptureOptionsJs
            {
                MaxWidth = options.MaxWidth,
                MaxHeight = options.MaxHeight,
                Quality = options.Quality,
                AllowGallerySelection = options.AllowGallerySelection
            };

            var result = await _jsRuntime.InvokeAsync<CapturedImageInfoJs>("selectPhotoWithOptions", jsOptions);

            if (result?.Data != null && result.Data.Length > 0)
            {
                var imageInfo = new CapturedImageInfo
                {
                    Data = result.Data,
                    FileName = result.FileName ?? $"selected_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                    ContentType = result.ContentType ?? "image/jpeg",
                    Size = result.Size,
                    Width = result.Width,
                    Height = result.Height,
                    CapturedAt = DateTime.TryParse(result.CapturedAt, out var capturedAt) ? capturedAt : DateTime.UtcNow
                };

                _logger.LogInformation("Successfully selected photo with options: {Size} bytes, {Width}x{Height}",
                    imageInfo.Size, imageInfo.Width, imageInfo.Height);

                return Result<CapturedImageInfo>.Success(imageInfo);
            }
            else
            {
                return Result<CapturedImageInfo>.Failure("No photo selected or user cancelled");
            }
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error during photo selection with options");
            return Result<CapturedImageInfo>.Failure($"Photo selection error: {jsEx.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error selecting photo with options");
            return Result<CapturedImageInfo>.Failure($"Error selecting photo: {ex.Message}");
        }
    }

    public async Task<CameraCapabilities> GetCameraCapabilitiesAsync()
    {
        try
        {
            var capabilities = await _jsRuntime.InvokeAsync<CameraCapabilities>("getCameraCapabilities");
            return capabilities ?? new CameraCapabilities();
        }
        catch (Exception)
        {
            return new CameraCapabilities();
        }
    }

    public async Task<Result<bool>> ValidateImageAsync(byte[] imageData, long maxSizeBytes = 10485760)
    {
        try
        {
            if (imageData == null || imageData.Length == 0)
            {
                return Result<bool>.Failure("Image data is empty");
            }

            if (imageData.Length > maxSizeBytes)
            {
                return Result<bool>.Failure($"Image size ({imageData.Length} bytes) exceeds maximum allowed size ({maxSizeBytes} bytes)");
            }

            // Basic image format validation
            var isValidFormat = await _jsRuntime.InvokeAsync<bool>("validateImageFormat", imageData);
            
            if (!isValidFormat)
            {
                return Result<bool>.Failure("Invalid image format");
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Error validating image: {ex.Message}");
        }
    }
}
