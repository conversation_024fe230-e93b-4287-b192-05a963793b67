using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Service for mapping Active Directory groups to application roles
/// </summary>
public class AdRoleMappingService : IAdRoleMappingService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<AdRoleMappingService> _logger;

    public AdRoleMappingService(IApplicationDbContext context, ILogger<AdRoleMappingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Gets user role by first checking database for existing user, then falling back to AD group mapping
    /// </summary>
    /// <param name="adObjectGuid">AD Object GUID of the user</param>
    /// <param name="adGroupMemberships">AD group memberships for fallback mapping</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User role</returns>
    public async Task<UserRole> GetUserRoleAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default)
    {
        // First, try to find the user in the database by AD Object GUID
        var existingUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == adObjectGuid, cancellationToken);

        if (existingUser != null)
        {
            _logger.LogDebug("Found existing user {Username} with role {Role} from database",
                existingUser.Username, existingUser.Role.RoleName);
            return existingUser.Role.RoleName;
        }

        // If user doesn't exist in database, fall back to AD group mapping
        _logger.LogDebug("User with AD Object GUID {AdObjectGuid} not found in database, falling back to AD group mapping", adObjectGuid);
        return await MapAdGroupsToRoleAsync(adGroupMemberships, cancellationToken);
    }

    /// <summary>
    /// Gets user information including role by first checking database, then falling back to AD group mapping
    /// </summary>
    /// <param name="adObjectGuid">AD Object GUID of the user</param>
    /// <param name="adGroupMemberships">AD group memberships for fallback mapping</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tuple containing user role and whether user exists in database</returns>
    public async Task<(UserRole role, bool userExistsInDb)> GetUserRoleWithExistenceAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default)
    {
        // First, try to find the user in the database by AD Object GUID
        var existingUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == adObjectGuid, cancellationToken);

        if (existingUser != null)
        {
            _logger.LogDebug("Found existing user {Username} with role {Role} from database",
                existingUser.Username, existingUser.Role.RoleName);
            return (existingUser.Role.RoleName, true);
        }

        // If user doesn't exist in database, fall back to AD group mapping
        _logger.LogDebug("User with AD Object GUID {AdObjectGuid} not found in database, falling back to AD group mapping", adObjectGuid);
        var role = await MapAdGroupsToRoleAsync(adGroupMemberships, cancellationToken);
        return (role, false);
    }

    public async Task<UserRole> MapAdGroupsToRoleAsync(IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default)
    {
        var groupMemberships = adGroupMemberships.ToList();

        if (!groupMemberships.Any())
        {
            _logger.LogWarning("User has no AD group memberships, defaulting to Viewer role");
            return UserRole.Viewer;
        }

        // Use database-driven mapping
        var dbMappings = await GetAdGroupRoleMappingsAsync(cancellationToken);

        if (dbMappings.Any())
        {
            var mappedRole = MapUsingDatabaseMappings(groupMemberships, dbMappings);
            if (mappedRole.HasValue)
            {
                _logger.LogDebug("Mapped user to role {Role} using database mappings", mappedRole.Value);
                return mappedRole.Value;
            }
        }

        // If no database mappings exist or no matches found, default to Viewer
        _logger.LogWarning("No role mapping found for user's AD groups, defaulting to Viewer role. Groups: {Groups}",
            string.Join(", ", groupMemberships));
        return UserRole.Viewer;
    }

    public async Task<Dictionary<string, UserRole>> GetAdGroupRoleMappingsAsync(CancellationToken cancellationToken = default)
    {
        var mappings = await _context.AdGroupRoleMappings
            .Include(agrm => agrm.Role)
            .Where(agrm => agrm.IsActive)
            .ToListAsync(cancellationToken);

        return mappings.ToDictionary(
            mapping => mapping.AdGroupName,
            mapping => mapping.Role.RoleName,
            StringComparer.OrdinalIgnoreCase);
    }

    private UserRole? MapUsingDatabaseMappings(IList<string> groupMemberships, Dictionary<string, UserRole> dbMappings)
    {
        // Check for exact matches first
        foreach (var group in groupMemberships)
        {
            if (dbMappings.TryGetValue(group, out var exactRole))
            {
                return GetHighestPrivilegeRole(exactRole, null) ?? exactRole;
            }
        }

        // Check for partial matches (group DN contains mapping key)
        UserRole? highestRole = null;
        foreach (var group in groupMemberships)
        {
            foreach (var mapping in dbMappings)
            {
                if (group.Contains(mapping.Key, StringComparison.OrdinalIgnoreCase))
                {
                    highestRole = GetHighestPrivilegeRole(highestRole, mapping.Value);
                }
            }
        }

        return highestRole;
    }



    private static UserRole? GetHighestPrivilegeRole(UserRole? current, UserRole? candidate)
    {
        if (!current.HasValue) return candidate;
        if (!candidate.HasValue) return current;

        // Role hierarchy: DevAdmin > SystemManager > ProcessOwner > Auditor > Viewer
        var roleHierarchy = new Dictionary<UserRole, int>
        {
            { UserRole.DevAdmin, 5 },
            { UserRole.SystemManager, 4 },
            { UserRole.ProcessOwner, 3 },
            { UserRole.Auditor, 2 },
            { UserRole.Viewer, 1 }
        };

        var currentPriority = roleHierarchy.GetValueOrDefault(current.Value, 0);
        var candidatePriority = roleHierarchy.GetValueOrDefault(candidate.Value, 0);

        return candidatePriority > currentPriority ? candidate : current;
    }
}
