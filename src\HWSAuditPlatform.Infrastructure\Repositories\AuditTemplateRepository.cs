using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Infrastructure.Persistence;

namespace HWSAuditPlatform.Infrastructure.Repositories;

/// <summary>
/// Specialized repository for AuditTemplate entities with template-specific operations
/// </summary>
public class AuditTemplateRepository : Repository<AuditTemplate, int>, IAuditTemplateRepository
{
    private new readonly ApplicationDbContext _context;

    public AuditTemplateRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<IReadOnlyList<AuditTemplate>> GetTemplatesWithDetailsAsync(
        Expression<Func<AuditTemplate, bool>>? predicate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditTemplates
            .Include(t => t.QuestionGroups.Where(g => g.IsActive))
                .ThenInclude(g => g.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .AsQueryable();

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<AuditTemplate?> GetTemplateWithFullDetailsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.AuditTemplates
            .Include(t => t.QuestionGroups.Where(g => g.IsActive))
                .ThenInclude(g => g.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.ChildQuestions.Where(cq => cq.IsActive))
                    .ThenInclude(cq => cq.Options.Where(o => o.IsActive))
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<IReadOnlyList<AuditTemplate>> GetPublishedTemplatesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.AuditTemplates
            .Where(t => t.IsPublished && t.IsActive)
            .OrderBy(t => t.TemplateName)
            .ThenByDescending(t => t.Version)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<AuditTemplate>> GetTemplateVersionsAsync(
        string templateName,
        CancellationToken cancellationToken = default)
    {
        return await _context.AuditTemplates
            .Where(t => t.TemplateName == templateName)
            .OrderByDescending(t => t.Version)
            .ToListAsync(cancellationToken);
    }

    public async Task<AuditTemplate?> GetLatestVersionAsync(
        string templateName,
        bool publishedOnly = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditTemplates
            .Where(t => t.TemplateName == templateName && t.IsActive);

        if (publishedOnly)
        {
            query = query.Where(t => t.IsPublished);
        }

        return await query
            .OrderByDescending(t => t.Version)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> TemplateNameVersionExistsAsync(
        string templateName,
        int version,
        int? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditTemplates
            .Where(t => t.TemplateName == templateName && t.Version == version);

        if (excludeId.HasValue)
        {
            query = query.Where(t => t.Id != excludeId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<int> GetQuestionCountAsync(int templateId, CancellationToken cancellationToken = default)
    {
        return await _context.Questions
            .Where(q => q.AuditTemplateId == templateId && q.IsActive)
            .CountAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<AuditTemplate>> GetTemplatesUsedInAuditsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.AuditTemplates
            .Where(t => _context.Audits.Any(a => a.AuditTemplateId == t.Id))
            .OrderBy(t => t.TemplateName)
            .ThenByDescending(t => t.Version)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IReadOnlyList<AuditTemplate> Items, int TotalCount)> GetPagedTemplatesWithFiltersAsync(
        int pageNumber,
        int pageSize,
        string? searchTerm = null,
        bool? isPublished = null,
        bool? isActive = null,
        bool? canBeUsed = null,
        string sortBy = "TemplateName",
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditTemplates.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var searchPattern = $"%{searchTerm}%";
            query = query.Where(t =>
                EF.Functions.Like(t.TemplateName, searchPattern) ||
                (t.Description != null && EF.Functions.Like(t.Description, searchPattern)));
        }

        if (isPublished.HasValue)
        {
            query = query.Where(t => t.IsPublished == isPublished.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(t => t.IsActive == isActive.Value);
        }

        if (canBeUsed.HasValue && canBeUsed.Value)
        {
            query = query.Where(t => t.IsPublished && t.IsActive);
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = sortBy.ToLower() switch
        {
            "description" => ascending
                ? query.OrderBy(t => t.Description)
                : query.OrderByDescending(t => t.Description),
            "version" => ascending
                ? query.OrderBy(t => t.Version)
                : query.OrderByDescending(t => t.Version),
            "ispublished" => ascending
                ? query.OrderBy(t => t.IsPublished)
                : query.OrderByDescending(t => t.IsPublished),
            "createdat" => ascending
                ? query.OrderBy(t => t.CreatedAt)
                : query.OrderByDescending(t => t.CreatedAt),
            "updatedat" => ascending
                ? query.OrderBy(t => t.UpdatedAt)
                : query.OrderByDescending(t => t.UpdatedAt),
            _ => ascending
                ? query.OrderBy(t => t.TemplateName)
                : query.OrderByDescending(t => t.TemplateName)
        };

        // Apply pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }
}

/// <summary>
/// Specialized repository for Question entities with question-specific operations
/// </summary>
public class QuestionRepository : Repository<Question, int>, IQuestionRepository
{
    private new readonly ApplicationDbContext _context;

    public QuestionRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<IReadOnlyList<Question>> GetQuestionsByTemplateAsync(
        int templateId,
        bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Questions
            .Include(q => q.QuestionGroup)
            .Include(q => q.Options.Where(o => o.IsActive))
            .Include(q => q.ParentQuestion)
            .Include(q => q.ChildQuestions.Where(cq => cq.IsActive))
            .Where(q => q.AuditTemplateId == templateId);

        if (!includeInactive)
        {
            query = query.Where(q => q.IsActive);
        }

        return await query
            .OrderBy(q => q.DisplayOrder)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Question>> GetQuestionsByGroupAsync(
        int groupId,
        bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Questions
            .Include(q => q.Options.Where(o => o.IsActive))
            .Include(q => q.ParentQuestion)
            .Include(q => q.ChildQuestions.Where(cq => cq.IsActive))
            .Where(q => q.QuestionGroupId == groupId);

        if (!includeInactive)
        {
            query = query.Where(q => q.IsActive);
        }

        return await query
            .OrderBy(q => q.DisplayOrder)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Question>> GetRootQuestionsAsync(
        int templateId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Questions
            .Include(q => q.Options.Where(o => o.IsActive))
            .Include(q => q.ChildQuestions.Where(cq => cq.IsActive))
                .ThenInclude(cq => cq.Options.Where(o => o.IsActive))
            .Where(q => q.AuditTemplateId == templateId && 
                       q.ParentQuestionId == null && 
                       q.IsActive)
            .OrderBy(q => q.DisplayOrder)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Question>> GetConditionalQuestionsAsync(
        int parentQuestionId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Questions
            .Include(q => q.Options.Where(o => o.IsActive))
            .Where(q => q.ParentQuestionId == parentQuestionId && q.IsActive)
            .OrderBy(q => q.DisplayOrder)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasDependentQuestionsAsync(int questionId, CancellationToken cancellationToken = default)
    {
        return await _context.Questions
            .AnyAsync(q => q.ParentQuestionId == questionId && q.IsActive, cancellationToken);
    }

    public async Task<bool> IsQuestionUsedInAuditsAsync(int questionId, CancellationToken cancellationToken = default)
    {
        return await _context.AuditAnswers
            .AnyAsync(aa => aa.QuestionId == questionId, cancellationToken);
    }

    public async Task<int> GetMaxDisplayOrderAsync(
        int templateId,
        int? groupId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Questions
            .Where(q => q.AuditTemplateId == templateId && q.IsActive);

        if (groupId.HasValue)
        {
            query = query.Where(q => q.QuestionGroupId == groupId.Value);
        }
        else
        {
            query = query.Where(q => q.QuestionGroupId == null);
        }

        if (!await query.AnyAsync(cancellationToken))
        {
            return 0;
        }

        return await query.MaxAsync(q => q.DisplayOrder, cancellationToken);
    }
}
