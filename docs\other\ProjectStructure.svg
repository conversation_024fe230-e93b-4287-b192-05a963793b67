<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1574.93359375 862.4264526367188" style="max-width: 1574.93px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph3" class="cluster"><rect height="160.42647552490234" width="1502.08203125" y="694" x="8" style=""/><g transform="translate(670.720703125, 694)" class="cluster-label"><foreignObject height="24" width="176.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data &amp; External Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="232" width="488.62890625" y="388" x="967.34375" style=""/><g transform="translate(1174.095703125, 388)" class="cluster-label"><foreignObject height="24" width="75.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Core Logic</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="128" width="732.5" y="210" x="834.43359375" style=""/><g transform="translate(1139.26953125, 210)" class="cluster-label"><foreignObject height="24" width="122.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="128" width="640" y="8" x="771.93359375" style=""/><g transform="translate(1037.66015625, 8)" class="cluster-label"><foreignObject height="24" width="108.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Interfaces</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PWA_API_0" d="M936.934,111L936.934,115.167C936.934,119.333,936.934,127.667,936.934,138C936.934,148.333,936.934,160.667,936.934,173C936.934,185.333,936.934,197.667,946.409,207.746C955.883,217.824,974.833,225.649,984.308,229.561L993.783,233.473"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_API_0" d="M1246.934,111L1246.934,115.167C1246.934,119.333,1246.934,127.667,1246.934,138C1246.934,148.333,1246.934,160.667,1246.934,173C1246.934,185.333,1246.934,197.667,1237.459,207.746C1227.984,217.824,1209.034,225.649,1199.559,229.561L1190.084,233.473"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_AppLayer_0" d="M1091.934,313L1091.934,317.167C1091.934,321.333,1091.934,329.667,1091.934,338C1091.934,346.333,1091.934,354.667,1091.934,363C1091.934,371.333,1091.934,379.667,1096.742,387.59C1101.551,395.513,1111.169,403.025,1115.977,406.781L1120.786,410.538"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_AppLayer_0" d="M1401.934,313L1401.934,317.167C1401.934,321.333,1401.934,329.667,1401.934,338C1401.934,346.333,1401.934,354.667,1401.934,363C1401.934,371.333,1401.934,379.667,1377.454,389.063C1352.974,398.458,1304.015,408.917,1279.536,414.146L1255.056,419.375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppLayer_DomainLayer_0" d="M1161.177,467L1161.787,473.167C1162.398,479.333,1163.619,491.667,1164.229,503.333C1164.84,515,1164.84,526,1164.84,531.5L1164.84,537"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppLayer_InfraLayer_0" d="M1118.642,467L1109.537,473.167C1100.433,479.333,1082.224,491.667,989.64,507.184C897.056,522.701,730.095,541.401,646.615,550.752L563.135,560.102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InfraLayer_DB_0" d="M426.082,578.268L381.012,585.224C335.943,592.179,245.803,606.089,200.734,619.211C155.664,632.333,155.664,644.667,155.664,657C155.664,669.333,155.664,681.667,155.664,696.036C155.664,710.404,155.664,726.809,155.664,735.011L155.664,743.213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InfraLayer_ADInt_0" d="M447.799,595L440.882,599.167C433.965,603.333,420.131,611.667,413.214,622C406.297,632.333,406.297,644.667,406.297,657C406.297,669.333,406.297,681.667,406.297,696.036C406.297,710.404,406.297,726.809,406.297,735.011L406.297,743.213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InfraLayer_LocalFS_0" d="M559.16,587.048L578.344,592.54C597.529,598.032,635.897,609.016,655.081,620.675C674.266,632.333,674.266,644.667,674.266,657C674.266,669.333,674.266,681.667,674.266,694.036C674.266,706.404,674.266,718.809,674.266,725.011L674.266,731.213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InfraLayer_ExtOrgDB_0" d="M559.16,575.609L623.857,583.008C688.555,590.406,817.949,605.203,882.646,618.768C947.344,632.333,947.344,644.667,947.344,657C947.344,669.333,947.344,681.667,947.344,693.546C947.344,705.426,947.344,716.852,947.344,722.565L947.344,728.278"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppLayer_ExtAM_0" d="M1246.709,467L1266.854,473.167C1287,479.333,1327.291,491.667,1347.436,508.5C1367.582,525.333,1367.582,546.667,1367.582,566C1367.582,585.333,1367.582,602.667,1367.582,617.5C1367.582,632.333,1367.582,644.667,1367.582,657C1367.582,669.333,1367.582,681.667,1367.582,691.333C1367.582,701,1367.582,708,1367.582,711.5L1367.582,715"/></g><g class="edgeLabels"><g transform="translate(936.93359375, 173)" class="edgeLabel"><g transform="translate(-22.8359375, -12)" class="label"><foreignObject height="24" width="45.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g transform="translate(1246.93359375, 173)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1064.015625, 504)" class="edgeLabel"><g transform="translate(-41.9375, -12)" class="label"><foreignObject height="24" width="83.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Data Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(947.34375, 657)" class="edgeLabel"><g transform="translate(-65.578125, -12)" class="label"><foreignObject height="24" width="131.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Future Integration</p></span></div></foreignObject></g></g><g transform="translate(1367.58203125, 568)" class="edgeLabel"><g transform="translate(-65.578125, -12)" class="label"><foreignObject height="24" width="131.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Future Integration</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(936.93359375, 72)" id="flowchart-PWA-0" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Blazor PWA, Auditor Field App</p></span></div></foreignObject></g></g><g transform="translate(1246.93359375, 72)" id="flowchart-Portal-1" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Blazor Server, Management Portal</p></span></div></foreignObject></g></g><g transform="translate(1091.93359375, 274)" id="flowchart-API-2" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web API, e.g., ASP.NET Core</p></span></div></foreignObject></g></g><g transform="translate(1401.93359375, 274)" id="flowchart-Scheduler-3" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SchedulerWorker, Background Service</p></span></div></foreignObject></g></g><g transform="translate(1158.50390625, 440)" id="flowchart-AppLayer-4" class="node default"><rect height="54" width="185.28125" y="-27" x="-92.640625" style="" class="basic label-container"/><g transform="translate(-62.640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="125.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Layer</p></span></div></foreignObject></g></g><g transform="translate(1164.83984375, 568)" id="flowchart-DomainLayer-5" class="node default"><rect height="54" width="157.5625" y="-27" x="-78.78125" style="" class="basic label-container"/><g transform="translate(-48.78125, -12)" style="" class="label"><rect/><foreignObject height="24" width="97.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Layer</p></span></div></foreignObject></g></g><g transform="translate(155.6640625, 774.2132377624512)" id="flowchart-DB-6" class="node default"><rect height="54" width="225.328125" y="-27" x="-112.6640625" style="" class="basic label-container"/><g transform="translate(-82.6640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="165.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HWSAP DB - SQL Server</p></span></div></foreignObject></g></g><g transform="translate(406.296875, 774.2132377624512)" id="flowchart-ADInt-7" class="node default"><rect height="54" width="175.9375" y="-27" x="-87.96875" style="" class="basic label-container"/><g transform="translate(-57.96875, -12)" style="" class="label"><rect/><foreignObject height="24" width="115.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Active Directory</p></span></div></foreignObject></g></g><g transform="translate(674.265625, 774.2132377624512)" id="flowchart-LocalFS-8" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Local File System, for Attachments</p></span></div></foreignObject></g></g><g transform="translate(947.34375, 774.2132377624512)" id="flowchart-ExtOrgDB-9" class="node default"><path transform="translate(-93.078125, -41.935221452244654)" style="" class="basic label-container" d="M0,14.956814301496436 a93.078125,14.956814301496436 0,0,0 186.15625,0 a93.078125,14.956814301496436 0,0,0 -186.15625,0 l0,53.956814301496436 a93.078125,14.956814301496436 0,0,0 186.15625,0 l0,-53.956814301496436"/><g transform="translate(-85.578125, -2)" style="" class="label"><rect/><foreignObject height="24" width="171.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Future: External Org DB</p></span></div></foreignObject></g></g><g transform="translate(1367.58203125, 774.2132377624512)" id="flowchart-ExtAM-10" class="node default"><path transform="translate(-107.5, -55.21323529411765)" style="" class="basic label-container" d="M0,15.808823529411764 a107.5,15.808823529411764 0,0,0 215,0 a107.5,15.808823529411764 0,0,0 -215,0 l0,78.80882352941177 a107.5,15.808823529411764 0,0,0 215,0 l0,-78.80882352941177"/><g transform="translate(-100, -14)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Future: Action Manager - AM</p></span></div></foreignObject></g></g><g transform="translate(492.62109375, 568)" id="flowchart-InfraLayer-22" class="node default"><rect height="54" width="133.078125" y="-27" x="-66.5390625" style="" class="basic label-container"/><g transform="translate(-36.5390625, -12)" style="" class="label"><rect/><foreignObject height="24" width="73.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>InfraLayer</p></span></div></foreignObject></g></g></g></g></g></svg>