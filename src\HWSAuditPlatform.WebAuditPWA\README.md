# HWS Audit Platform PWA

A Progressive Web Application (PWA) for audit execution and management in manufacturing environments, built with Blazor WebAssembly.

## Features

### 🏭 Industrial Design
- **Teal accent color** theme optimized for manufacturing environments
- **Tablet-optimized** responsive design with touch-friendly controls
- **Industrial color palette** with dark themes for reduced eye strain
- **Accessibility compliant** with WCAG 2.1 guidelines

### 📱 Progressive Web App
- **Offline functionality** - Complete audits without internet connection
- **Camera integration** - Capture evidence photos during audits
- **Install prompts** - Add to home screen on mobile devices
- **Service worker** - Background sync and caching
- **Push notifications** - Real-time audit assignments (future)

### 🔐 Authentication & Security
- **JWT token authentication** with automatic refresh
- **Active Directory integration** via HWS Audit Platform API
- **Role-based access control** (Auditor, Process Owner, System Manager)
- **Secure token storage** in browser local storage

### 📋 Audit Management
- **Audit assignment view** with filtering and search
- **Interactive audit execution** with all question types
- **Progress tracking** with visual indicators
- **Evidence capture** with camera integration
- **Offline completion** with automatic sync

### 🔄 Offline & Sync
- **Offline audit storage** in browser local storage
- **Automatic synchronization** when connection restored
- **Manual sync triggers** for immediate data upload
- **Conflict resolution** for concurrent edits
- **Network status monitoring** with visual indicators

## Technology Stack

- **Frontend**: Blazor WebAssembly (.NET 8)
- **Styling**: Bootstrap 5 with custom industrial theme
- **Icons**: Font Awesome 6
- **Storage**: Browser Local Storage via Blazored.LocalStorage
- **Camera**: JavaScript MediaDevices API
- **PWA**: Service Worker with Workbox
- **Authentication**: JWT tokens with custom auth provider

## Getting Started

### Prerequisites

- .NET 8 SDK
- Node.js (for PWA tools)
- Modern web browser with PWA support
- HWS Audit Platform API running

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd HWSAuditPlatform
   ```

2. **Configure API endpoint**
   ```bash
   # Update src/HWSAuditPlatform.WebAuditPWA/wwwroot/appsettings.Development.json
   {
     "ApiBaseUrl": "https://localhost:7490/"
   }
   ```

3. **Run the application**
   ```bash
   cd src/HWSAuditPlatform.WebAuditPWA
   dotnet run
   ```

4. **Access the application**
   - Open browser to `https://localhost:7002`
   - Login with your HWS Audit Platform credentials

### Production Deployment

#### Docker Deployment

1. **Build Docker image**
   ```bash
   docker build -t hws-audit-pwa -f src/HWSAuditPlatform.WebAuditPWA/Dockerfile .
   ```

2. **Run with Docker Compose**
   ```bash
   cd src/HWSAuditPlatform.WebAuditPWA
   docker-compose up -d
   ```

3. **Configure production settings**
   ```bash
   # Update appsettings.Production.json
   {
     "ApiBaseUrl": "https://your-production-api.com/"
   }
   ```

#### Manual Deployment

1. **Publish the application**
   ```bash
   dotnet publish -c Release -o ./publish
   ```

2. **Deploy to web server**
   - Copy `publish/wwwroot` contents to web server
   - Configure HTTPS and proper MIME types
   - Set up reverse proxy for API calls

## Configuration

### Application Settings

```json
{
  "ApiBaseUrl": "https://api.example.com/",
  "PWA": {
    "EnableOfflineMode": true,
    "SyncIntervalMinutes": 15,
    "MaxOfflineAudits": 50,
    "CacheExpirationHours": 24
  },
  "Camera": {
    "MaxImageWidth": 1920,
    "MaxImageHeight": 1080,
    "ImageQuality": 0.8,
    "MaxFileSizeMB": 10
  }
}
```

### Environment Variables

- `ASPNETCORE_ENVIRONMENT` - Development/Production
- `API_BASE_URL` - Override API endpoint
- `PWA_CACHE_VERSION` - Force cache refresh

## Usage

### For Auditors

1. **Login** with your credentials
2. **View assigned audits** on the dashboard
3. **Start an audit** by clicking the Start button
4. **Answer questions** using the interactive interface
5. **Capture evidence** using the camera integration
6. **Complete audit** when all required questions answered
7. **Sync data** when back online (if completed offline)

### For Process Owners

1. **Monitor audit progress** on the dashboard
2. **View completed audits** and results
3. **Access audit reports** and analytics

### Offline Usage

1. **Download audits** while online
2. **Complete audits** without internet connection
3. **Capture photos** and evidence offline
4. **Automatic sync** when connection restored
5. **Manual sync** using the sync button

## Architecture

### Project Structure

```
src/HWSAuditPlatform.WebAuditPWA/
├── Components/           # Reusable Blazor components
│   ├── Audit/           # Audit-specific components
│   ├── Common/          # Shared UI components
│   └── Navigation/      # Navigation components
├── Pages/               # Blazor pages/routes
│   ├── Audits/         # Audit management pages
│   └── Offline/        # Offline functionality
├── Services/           # Business logic services
├── Models/             # Data transfer objects
├── Layout/             # Application layout
└── wwwroot/           # Static assets
    ├── css/           # Stylesheets
    ├── js/            # JavaScript files
    └── icons/         # PWA icons
```

### Service Architecture

- **Authentication Service** - JWT token management
- **Audit API Service** - API communication
- **Offline Storage Service** - Local data persistence
- **Sync Service** - Data synchronization
- **Camera Service** - Photo capture
- **PWA Service** - Progressive web app features

## Testing

### Unit Tests

```bash
# Run unit tests
dotnet test src/HWSAuditPlatform.Tests/

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Integration Tests

```bash
# Run integration tests
dotnet test src/HWSAuditPlatform.IntegrationTests/
```

### Manual Testing

1. **PWA Installation** - Test add to home screen
2. **Offline Functionality** - Disconnect network and test
3. **Camera Integration** - Test photo capture on mobile
4. **Responsive Design** - Test on various screen sizes
5. **Authentication** - Test login/logout flows

## Troubleshooting

### Common Issues

1. **Camera not working**
   - Check browser permissions
   - Ensure HTTPS is enabled
   - Verify device has camera

2. **Offline sync failing**
   - Check network connectivity
   - Verify API endpoint accessibility
   - Clear browser storage if corrupted

3. **PWA not installing**
   - Ensure HTTPS is enabled
   - Check manifest.json is accessible
   - Verify service worker registration

4. **Authentication issues**
   - Check API endpoint configuration
   - Verify JWT token format
   - Clear browser storage and re-login

### Debug Mode

Enable debug logging in `appsettings.Development.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "HWSAuditPlatform.WebAuditPWA": "Debug"
    }
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is proprietary software owned by HWS Audit Platform.

## Support

For technical support, contact the development team or create an issue in the repository.
