namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the frequency type for recurring audit schedules.
/// Maps to the frequency_type enum in the database.
/// </summary>
public enum FrequencyType
{
    /// <summary>
    /// Daily frequency
    /// </summary>
    DAILY,

    /// <summary>
    /// Weekly frequency
    /// </summary>
    WEEKLY,

    /// <summary>
    /// Monthly frequency
    /// </summary>
    MONTHLY,

    /// <summary>
    /// Yearly frequency
    /// </summary>
    YEARLY,

    /// <summary>
    /// Custom frequency (note: typo in DBML - COSTUM)
    /// </summary>
    COSTUM
}
