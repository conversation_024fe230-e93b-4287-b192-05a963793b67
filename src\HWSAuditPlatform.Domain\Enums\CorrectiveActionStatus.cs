namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the status of a corrective action throughout its lifecycle.
/// Maps to the corrective_action_status enum in the database.
/// </summary>
public enum CorrectiveActionStatus
{
    /// <summary>
    /// Action has been assigned to a user
    /// </summary>
    Assigned,

    /// <summary>
    /// Action is currently in progress
    /// </summary>
    InProgress,

    /// <summary>
    /// Action has been completed and is pending verification
    /// </summary>
    CompletedPendingVerification,

    /// <summary>
    /// Action has been verified and closed
    /// </summary>
    VerifiedClosed,

    /// <summary>
    /// Action has been cancelled
    /// </summary>
    Cancelled,

    /// <summary>
    /// Action taken did not resolve the finding
    /// </summary>
    Ineffective
}
