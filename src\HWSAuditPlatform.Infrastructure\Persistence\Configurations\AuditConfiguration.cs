using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Audit entity
/// </summary>
public class AuditConfiguration : IEntityTypeConfiguration<Audit>
{
    public void Configure(EntityTypeBuilder<Audit> builder)
    {
        builder.ToTable("hwsap_aud_audits");

        // Primary Key
        builder.HasKey(a => a.Id);
        builder.Property(a => a.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(a => a.AssignmentType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.AssignedToUserGroupId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(a => a.AssignedToUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(a => a.ScheduledDate)
            .IsRequired();

        builder.Property(a => a.DueDate)
            .IsRequired(false);

        builder.Property(a => a.StartedAt)
            .IsRequired(false);

        builder.Property(a => a.CompletedAt)
            .IsRequired(false);

        builder.Property(a => a.OverallStatus)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.OverallScore)
            .HasColumnType("decimal(7,2)")
            .IsRequired(false);

        builder.Property(a => a.ManagerComments)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(a => a.ReviewedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(a => a.ReviewedAt)
            .IsRequired(false);

        builder.Property(a => a.RecurringAuditSettingId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Auditable properties
        builder.Property(a => a.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(a => a.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(a => a.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(a => a.AuditTemplateId)
            .HasDatabaseName("IX_Audits_AuditTemplateId");

        builder.HasIndex(a => a.AssignedToUserId)
            .HasDatabaseName("IX_Audits_AssignedToUserId");

        builder.HasIndex(a => a.AssignedToUserGroupId)
            .HasDatabaseName("IX_Audits_AssignedToUserGroupId");

        builder.HasIndex(a => a.OverallStatus)
            .HasDatabaseName("IX_Audits_OverallStatus");

        builder.HasIndex(a => a.ScheduledDate)
            .HasDatabaseName("IX_Audits_ScheduledDate");

        builder.HasIndex(a => a.DueDate)
            .HasDatabaseName("IX_Audits_DueDate");

        builder.HasIndex(a => a.FactoryId)
            .HasDatabaseName("IX_Audits_FactoryId");

        builder.HasIndex(a => a.AreaId)
            .HasDatabaseName("IX_Audits_AreaId");

        // Relationships
        builder.HasOne(a => a.AuditTemplate)
            .WithMany()
            .HasForeignKey(a => a.AuditTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.AssignedToUserGroup)
            .WithMany()
            .HasForeignKey(a => a.AssignedToUserGroupId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(a => a.AssignedToUser)
            .WithMany()
            .HasForeignKey(a => a.AssignedToUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(a => a.Factory)
            .WithMany()
            .HasForeignKey(a => a.FactoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.Area)
            .WithMany()
            .HasForeignKey(a => a.AreaId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.SubArea)
            .WithMany()
            .HasForeignKey(a => a.SubAreaId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(a => a.ReviewedByUser)
            .WithMany()
            .HasForeignKey(a => a.ReviewedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(a => a.RecurringAuditSetting)
            .WithMany()
            .HasForeignKey(a => a.RecurringAuditSettingId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Configure the CorrectionRequests navigation property explicitly
        builder.HasMany(a => a.CorrectionRequests)
            .WithOne(acr => acr.Audit)
            .HasForeignKey(acr => acr.AuditId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(a => a.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for AuditAnswerSelectedOption entity
/// </summary>
public class AuditAnswerSelectedOptionConfiguration : IEntityTypeConfiguration<AuditAnswerSelectedOption>
{
    public void Configure(EntityTypeBuilder<AuditAnswerSelectedOption> builder)
    {
        builder.ToTable("hwsap_aud_answer_selected_options");

        // Primary Key
        builder.HasKey(aaso => aaso.Id);
        builder.Property(aaso => aaso.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(aaso => aaso.AuditAnswerId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(aaso => aaso.QuestionOptionId)
            .IsRequired();

        // Relationships
        builder.HasOne(aaso => aaso.AuditAnswer)
            .WithMany(aa => aa.SelectedOptions)
            .HasForeignKey(aaso => aaso.AuditAnswerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(aaso => aaso.QuestionOption)
            .WithMany()
            .HasForeignKey(aaso => aaso.QuestionOptionId)
            .OnDelete(DeleteBehavior.Restrict);

        // Auditable properties
        builder.Property(aaso => aaso.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(aaso => aaso.AuditAnswerId)
            .HasDatabaseName("IX_AuditAnswerSelectedOptions_AuditAnswerId");

        builder.HasIndex(aaso => aaso.QuestionOptionId)
            .HasDatabaseName("IX_AuditAnswerSelectedOptions_QuestionOptionId");

        // Ignore domain events (inherited from BaseEntity)
        builder.Ignore(aaso => aaso.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for AuditAnswer entity
/// </summary>
public class AuditAnswerConfiguration : IEntityTypeConfiguration<AuditAnswer>
{
    public void Configure(EntityTypeBuilder<AuditAnswer> builder)
    {
        builder.ToTable("hwsap_aud_answers");

        // Primary Key
        builder.HasKey(aa => aa.Id);
        builder.Property(aa => aa.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(aa => aa.AuditId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(aa => aa.QuestionId)
            .IsRequired();

        // Ignore computed property - AnswerValue is calculated from other properties
        builder.Ignore(aa => aa.AnswerValue);

        builder.Property(aa => aa.IsNotApplicable)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(aa => aa.Comments)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(aa => aa.SeverityLevel)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired(false);

        // Auditable properties
        builder.Property(aa => aa.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(aa => aa.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(aa => aa.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(aa => aa.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(aa => aa.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(aa => aa.AuditId)
            .HasDatabaseName("IX_AuditAnswers_AuditId");

        builder.HasIndex(aa => aa.QuestionId)
            .HasDatabaseName("IX_AuditAnswers_QuestionId");

        builder.HasIndex(aa => new { aa.AuditId, aa.QuestionId })
            .IsUnique()
            .HasDatabaseName("IX_AuditAnswers_AuditId_QuestionId");

        // Relationships
        builder.HasOne(aa => aa.Audit)
            .WithMany(a => a.Answers)
            .HasForeignKey(aa => aa.AuditId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(aa => aa.Question)
            .WithMany()
            .HasForeignKey(aa => aa.QuestionId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore domain events
        builder.Ignore(aa => aa.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for AuditAnswerFailureReason entity
/// </summary>
public class AuditAnswerFailureReasonConfiguration : IEntityTypeConfiguration<AuditAnswerFailureReason>
{
    public void Configure(EntityTypeBuilder<AuditAnswerFailureReason> builder)
    {
        builder.ToTable("hwsap_aud_answer_failure_reasons");

        // Primary Key
        builder.HasKey(aafr => aafr.Id);
        builder.Property(aafr => aafr.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(aafr => aafr.AuditAnswerId)
            .HasMaxLength(25)
            .IsRequired();



        // Auditable properties
        builder.Property(aafr => aafr.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(aafr => aafr.AuditAnswerId)
            .HasDatabaseName("IX_AuditAnswerFailureReasons_AuditAnswerId");



        // Relationships
        builder.HasOne(aafr => aafr.AuditAnswer)
            .WithMany(aa => aa.FailureReasons)
            .HasForeignKey(aafr => aafr.AuditAnswerId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(aafr => aafr.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for AuditAttachment entity
/// </summary>
public class AuditAttachmentConfiguration : IEntityTypeConfiguration<AuditAttachment>
{
    public void Configure(EntityTypeBuilder<AuditAttachment> builder)
    {
        builder.ToTable("hwsap_aud_attachments");

        // Primary Key
        builder.HasKey(aa => aa.Id);
        builder.Property(aa => aa.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(aa => aa.AuditAnswerId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(aa => aa.FileName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(aa => aa.OriginalFileName)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(aa => aa.ContentType)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(aa => aa.FileSize)
            .IsRequired();

        builder.Property(aa => aa.FilePath)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(aa => aa.Description)
            .HasMaxLength(1000)
            .IsRequired(false);

        // Auditable properties
        builder.Property(aa => aa.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(aa => aa.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(aa => aa.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(aa => aa.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(aa => aa.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(aa => aa.AuditAnswerId)
            .HasDatabaseName("IX_AuditAttachments_AuditAnswerId");

        builder.HasIndex(aa => aa.FileName)
            .HasDatabaseName("IX_AuditAttachments_FileName");

        // Relationships
        builder.HasOne(aa => aa.AuditAnswer)
            .WithMany(a => a.Attachments)
            .HasForeignKey(aa => aa.AuditAnswerId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(aa => aa.DomainEvents);
    }
}
