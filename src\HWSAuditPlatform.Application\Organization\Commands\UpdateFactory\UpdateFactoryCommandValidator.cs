using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.UpdateFactory;

/// <summary>
/// Validator for UpdateFactoryCommand
/// </summary>
public class UpdateFactoryCommandValidator : AbstractValidator<UpdateFactoryCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateFactoryCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.Id)
            .GreaterThan(0).WithMessage("Factory ID must be greater than 0");

        RuleFor(x => x.FactoryName)
            .NotEmpty().WithMessage("Factory name is required")
            .MaximumLength(150).WithMessage("Factory name must not exceed 150 characters");

        RuleFor(x => x.FactoryProcess)
            .MaximumLength(2000).WithMessage("Factory process must not exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.FactoryProcess));

        RuleFor(x => x.LocationId)
            .GreaterThan(0).WithMessage("Location ID must be greater than 0")
            .MustAsync(BeValidLocation).WithMessage("Location does not exist");

        RuleFor(x => x.AddressLine1)
            .MaximumLength(255).WithMessage("Address line 1 must not exceed 255 characters")
            .When(x => !string.IsNullOrEmpty(x.AddressLine1));

        RuleFor(x => x.City)
            .MaximumLength(100).WithMessage("City must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.City));

        RuleFor(x => x.PostalCode)
            .MaximumLength(20).WithMessage("Postal code must not exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.PostalCode));

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0).WithMessage("Record version must be greater than 0");
    }

    private async Task<bool> BeValidLocation(int locationId, CancellationToken cancellationToken)
    {
        return await _context.Locations.AnyAsync(l => l.Id == locationId, cancellationToken);
    }
}
