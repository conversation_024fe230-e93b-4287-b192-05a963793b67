using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateArea;

/// <summary>
/// Handler for CreateAreaCommand
/// </summary>
public class CreateAreaCommandHandler : BaseCommandHandler<CreateAreaCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateAreaCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateAreaCommand request, CancellationToken cancellationToken)
    {
        // Create the area entity
        var area = new Area
        {
            AreaName = request.AreaName,
            FactoryId = request.FactoryId,
            Description = request.Description,
            IsActive = request.IsActive,
            CreatedByUserId = _currentUserService.UserId
        };

        // Add to context
        await _context.Areas.AddAsync(area, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return area.Id;
    }
}
