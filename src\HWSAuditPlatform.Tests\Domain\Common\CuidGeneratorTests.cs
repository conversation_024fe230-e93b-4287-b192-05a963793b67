using System.Collections.Concurrent;
using FluentAssertions;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Tests.Domain.Common;

public class CuidGeneratorTests
{
    [Fact]
    public void Generate_ShouldReturnValidCuid()
    {
        // Act
        var cuid = CuidGenerator.Generate();

        // Assert
        cuid.Should().NotBeNullOrEmpty();
        cuid.Should().HaveLength(25);
        cuid.Should().StartWith("c");
        CuidGenerator.IsValidCuid(cuid).Should().BeTrue();
    }

    [Fact]
    public void Generate_ShouldReturnUniqueCuids()
    {
        // Act
        var cuids = new HashSet<string>();
        for (int i = 0; i < 1000; i++)
        {
            cuids.Add(CuidGenerator.Generate());
        }

        // Assert
        cuids.Should().HaveCount(1000, "all generated CUIDs should be unique");
    }

    [Fact]
    public void Generate_ShouldContainOnlyValidCharacters()
    {
        // Act
        var cuid = CuidGenerator.Generate();

        // Assert
        for (int i = 1; i < cuid.Length; i++) // Skip the 'c' prefix
        {
            var c = cuid[i];
            (char.IsDigit(c) || (c >= 'a' && c <= 'z')).Should().BeTrue($"character '{c}' at position {i} should be a valid base36 character");
        }
    }

    [Theory]
    [InlineData("c1234567890abcdef12345678", true)]
    [InlineData("c000000000000000000000000", true)]
    [InlineData("czzzzzzzzzzzzzzzzzzzzzzzz", true)]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("1234567890abcdef12345678", false)] // Missing 'c' prefix
    [InlineData("c1234567890abcdef1234567", false)] // Too short
    [InlineData("c1234567890abcdef123456789", false)] // Too long
    [InlineData("c1234567890ABCDEF12345678", false)] // Contains uppercase
    [InlineData("c1234567890abcdef1234567!", false)] // Contains invalid character
    public void IsValidCuid_ShouldValidateCorrectly(string? input, bool expected)
    {
        // Act
        var result = CuidGenerator.IsValidCuid(input);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Generate_ShouldBeSequentiallyOrdered()
    {
        // Act - Generate CUIDs with small delay to ensure different timestamps
        var cuid1 = CuidGenerator.Generate();
        Thread.Sleep(1); // Ensure different timestamp
        var cuid2 = CuidGenerator.Generate();

        // Assert - CUIDs should be lexicographically ordered due to timestamp component
        string.Compare(cuid1, cuid2, StringComparison.Ordinal).Should().BeLessThan(0, 
            "CUIDs should be roughly sequential due to timestamp component");
    }

    [Fact]
    public async Task Generate_ShouldHandleConcurrentGeneration()
    {
        // Arrange
        var cuids = new ConcurrentBag<string>();
        var tasks = new List<Task>();

        // Act - Generate CUIDs concurrently
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < 100; j++)
                {
                    cuids.Add(CuidGenerator.Generate());
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        cuids.Should().HaveCount(1000);
        cuids.Distinct().Should().HaveCount(1000, "all CUIDs should be unique even when generated concurrently");
        
        // Verify all are valid CUIDs
        foreach (var cuid in cuids)
        {
            CuidGenerator.IsValidCuid(cuid).Should().BeTrue($"CUID '{cuid}' should be valid");
        }
    }
}
