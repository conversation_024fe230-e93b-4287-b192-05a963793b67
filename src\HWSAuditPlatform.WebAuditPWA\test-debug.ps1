# Simple test script for PWA Debug Mode
Write-Host "Testing HWS Audit Platform PWA Debug Mode" -ForegroundColor Cyan

# Test API
Write-Host "1. Testing API..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "https://localhost:7490/api/v1/health" -Method GET
    Write-Host "   API is running" -ForegroundColor Green
} catch {
    Write-Host "   API is not running" -ForegroundColor Red
}

# Test Authentication
Write-Host "2. Testing Authentication..." -ForegroundColor Yellow
try {
    $body = '{"username":"admin.user","password":"HWSAudit123!"}'
    $auth = Invoke-RestMethod -Uri "https://localhost:7490/api/v1/auth/login" -Method POST -ContentType "application/json" -Body $body
    if ($auth.success) {
        Write-Host "   Authentication working" -ForegroundColor Green
    }
} catch {
    Write-Host "   Authentication failed" -ForegroundColor Red
}

# Test PWA
Write-Host "3. Testing PWA..." -ForegroundColor Yellow
try {
    $pwa = Invoke-WebRequest -Uri "http://localhost:5112" -Method GET
    if ($pwa.StatusCode -eq 200) {
        Write-Host "   PWA is accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "   PWA is not accessible" -ForegroundColor Red
}

Write-Host "Test completed!" -ForegroundColor Green
