﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Components.WebAssembly.Http
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using Microsoft.JSInterop
@using Blazored.LocalStorage
@using HWSAuditPlatform.WebApp
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Components
@using HWSAuditPlatform.WebApp.Components.Auth
@using HWSAuditPlatform.WebApp.Components.Dashboard
@using HWSAuditPlatform.WebApp.Components.Shared
@using HWSAuditPlatform.WebApp.Layout
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Entities.Users
@using HWSAuditPlatform.Domain.Entities.Organization
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.Application.Audits.DTOs
@using HWSAuditPlatform.Application.Templates.DTOs
@using HWSAuditPlatform.Application.Users.DTOs
