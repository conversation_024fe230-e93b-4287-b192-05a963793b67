@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Users
@using HWSAuditPlatform.Domain.Entities.Templates
@inject IUserApiService UserApiService
@inject ITemplateApiService TemplateApiService
@inject ILogger<TemplateAccessManagementComponent> Logger

<div class="template-access-management">
    <div class="page-header">
        <h1>Template Access Management</h1>
        <p class="text-muted">Manage user and group access to audit templates</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading template access data..." />

    @if (!isLoading)
    {
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-ruled me-2"></i>Templates
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (templates?.Any() == true)
                        {
                            <div class="list-group">
                                @foreach (var template in templates)
                                {
                                    <div class="list-group-item @(selectedTemplateId == template.Id ? "active" : "")" 
                                         @onclick="() => SelectTemplate(template.Id)"
                                         style="cursor: pointer;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">@template.TemplateName</h6>
                                                <small class="@(selectedTemplateId == template.Id ? "text-light" : "text-muted")">
                                                    Version @template.Version
                                                </small>
                                            </div>
                                            @if (template.IsActive)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Inactive</span>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="bi bi-file-earmark-x text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No templates found</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                @if (selectedTemplateId.HasValue)
                {
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-people me-2"></i>Access Management for @GetSelectedTemplateName()
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Feature Under Development</strong>
                                <p class="mb-0 mt-2">
                                    Template access management functionality will be implemented when the comprehensive API client is available.
                                    This will include:
                                </p>
                                <ul class="mt-2 mb-0">
                                    <li>Assign template access to individual users</li>
                                    <li>Assign template access to user groups</li>
                                    <li>Set permission levels (View, Use, Edit)</li>
                                    <li>Bulk access management operations</li>
                                    <li>Access inheritance from organizational hierarchy</li>
                                </ul>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h6>Current Access Summary</h6>
                                    <div class="access-summary">
                                        <div class="summary-item">
                                            <span class="summary-label">Users with Access:</span>
                                            <span class="summary-value">0</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Groups with Access:</span>
                                            <span class="summary-value">0</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Public Access:</span>
                                            <span class="summary-value">No</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Quick Actions</h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" disabled>
                                            <i class="bi bi-person-plus me-2"></i>Add User Access
                                        </button>
                                        <button class="btn btn-outline-secondary" disabled>
                                            <i class="bi bi-people-fill me-2"></i>Add Group Access
                                        </button>
                                        <button class="btn btn-outline-info" disabled>
                                            <i class="bi bi-eye me-2"></i>View Access Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-arrow-left text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">Select a Template</h5>
                            <p class="text-muted">Choose a template from the list to manage its access permissions.</p>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

<style>
    .template-access-management {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .list-group-item {
        border: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
    }

    .list-group-item.active {
        background-color: #007bff;
        border-color: #007bff;
    }

    .access-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-label {
        font-weight: 600;
        color: #495057;
    }

    .summary-value {
        font-weight: 700;
        color: #007bff;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
    }
</style>

@code {
    private bool isLoading = true;
    private IEnumerable<AuditTemplate>? templates;
    private int? selectedTemplateId;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            templates = await TemplateApiService.GetTemplatesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading template access data");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void SelectTemplate(int templateId)
    {
        selectedTemplateId = templateId;
        StateHasChanged();
    }

    private string GetSelectedTemplateName()
    {
        if (!selectedTemplateId.HasValue || templates == null)
            return "Unknown Template";

        var template = templates.FirstOrDefault(t => t.Id == selectedTemplateId.Value);
        return template?.TemplateName ?? "Unknown Template";
    }
}
