using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.ValueObjects;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Infrastructure.Persistence;

public class ApplicationDbContextTests : BaseDbTestClass
{
    [Fact]
    public async Task SaveChangesAsync_ShouldSetAuditFields()
    {
        // Arrange
        var user = User.Create(
            username: "testuser",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        var beforeSave = DateTime.UtcNow;

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().BeAfter(beforeSave.AddSeconds(-1));
        user.UpdatedAt.Should().BeAfter(beforeSave.AddSeconds(-1));
        user.RecordVersion.Should().Be(1);
    }

    [Fact]
    public async Task SaveChangesAsync_OnUpdate_ShouldUpdateTimestampAndVersion()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user = await Context.Users.FirstAsync();
        var originalCreatedAt = user.CreatedAt;
        var originalVersion = user.RecordVersion;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);

        // Act
        user.Update(user.FirstName, user.LastName, "<EMAIL>", user.RoleId, user.FactoryId, user.IsActive);
        await Context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(originalCreatedAt); // Should not change
        user.UpdatedAt.Should().BeAfter(originalCreatedAt);
        user.RecordVersion.Should().Be(originalVersion + 1);
    }

    [Fact]
    public async Task Users_ShouldEnforceUniqueUsername()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var duplicateUser = User.Create(
            username: "admin", // Duplicate username
            firstName: "Duplicate",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(duplicateUser);

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateException>(() => Context.SaveChangesAsync());
    }

    [Fact]
    public async Task Users_ShouldAllowDuplicateEmails()
    {
        // Arrange
        await SeedTestDataAsync();

        var duplicateUser = User.Create(
            username: "newuser",
            firstName: "New",
            lastName: "User",
            email: "<EMAIL>", // Duplicate email should be allowed
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(duplicateUser);

        // Act & Assert
        await Context.SaveChangesAsync(); // Should not throw

        var usersWithSameEmail = await Context.Users
            .Where(u => u.Email == "<EMAIL>")
            .CountAsync();
        usersWithSameEmail.Should().BeGreaterThan(1);
    }

    [Fact]
    public async Task Users_ShouldAllowNullEmails()
    {
        // Arrange
        var userWithNullEmail = User.Create(
            username: "nullemailuser",
            firstName: "Null",
            lastName: "Email",
            email: null, // Null email should be allowed
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        Context.Users.Add(userWithNullEmail);
        await Context.SaveChangesAsync(); // Should not throw

        // Assert
        var savedUser = await Context.Users.FirstAsync(u => u.Username == "nullemailuser");
        savedUser.Email.Should().BeNull();
    }

    [Fact]
    public async Task Factories_ShouldStoreLocationInformation()
    {
        // Arrange
        var location = new Location
        {
            Id = 1,
            LocationName = "Test Location",
            LocationCountry = "Test Country",
            LocationCountryCode = "TC",
            OwnerGroupId = "TestGroup",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var factory = new Factory
        {
            Id = 1,
            FactoryName = "Test Factory",
            FactoryProcess = "Test Process",
            LocationId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Act
        Context.Locations.Add(location);
        Context.Factories.Add(factory);
        await Context.SaveChangesAsync();

        // Assert
        var savedLocation = await Context.Locations.FirstAsync();
        savedLocation.LocationName.Should().Be("Test Location");
        savedLocation.LocationCountry.Should().Be("Test Country");
        savedLocation.LocationCountryCode.Should().Be("TC");
        savedLocation.OwnerGroupId.Should().Be("TestGroup");
    }

    [Fact]
    public async Task Roles_ShouldHaveSeedData()
    {
        // Arrange & Act
        var roles = await Context.Roles.ToListAsync();

        // Assert
        roles.Should().HaveCount(3);
        roles.Should().Contain(r => r.RoleName == UserRole.DevAdmin);
        roles.Should().Contain(r => r.RoleName == UserRole.ProcessOwner);
        roles.Should().Contain(r => r.RoleName == UserRole.Auditor);
    }

    [Fact]
    public async Task Users_ShouldLoadRelatedData()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var users = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .ToListAsync();

        // Assert
        users.Should().NotBeEmpty();
        users.Should().OnlyContain(u => u.Role != null);
        users.Where(u => u.FactoryId.HasValue).Should().OnlyContain(u => u.Factory != null);
    }

    [Fact]
    public async Task Context_ShouldSupportConcurrentAccess()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user1 = await Context.Users.FirstAsync();
        var user2 = await Context.Users.FirstAsync();

        // Act
        user1.Update(user1.FirstName, user1.LastName, "<EMAIL>", user1.RoleId, user1.FactoryId, user1.IsActive);
        user2.Update(user2.FirstName, user2.LastName, "<EMAIL>", user2.RoleId, user2.FactoryId, user2.IsActive);

        await Context.SaveChangesAsync();

        // Assert - Second update should fail due to concurrency
        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(() => Context.SaveChangesAsync());
    }

    [Fact]
    public async Task Context_ShouldHandleNullValues()
    {
        // Arrange
        var user = User.Create(
            username: "nulltest",
            firstName: null, // Null first name
            lastName: null,  // Null last name
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null, // Null factory
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Assert
        var savedUser = await Context.Users.FirstAsync(u => u.Username == "nulltest");
        savedUser.FirstName.Should().Be("");
        savedUser.LastName.Should().Be("");
        savedUser.FactoryId.Should().BeNull();
        savedUser.AdObjectGuid.Should().Be("");
        savedUser.AdDistinguishedName.Should().BeNull();
    }

    [Fact]
    public async Task Context_ShouldSupportTransactions()
    {
        // Arrange
        using var transaction = await Context.Database.BeginTransactionAsync();
        
        var user = User.Create(
            username: "transactiontest",
            firstName: "Transaction",
            lastName: "Test",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();
        
        // Rollback transaction
        await transaction.RollbackAsync();

        // Assert
        var userExists = await Context.Users.AnyAsync(u => u.Username == "transactiontest");
        userExists.Should().BeFalse();
    }

    [Fact]
    public async Task Context_ShouldHandleLargeDataSets()
    {
        // Arrange
        var users = new List<User>();
        for (int i = 0; i < 100; i++)
        {
            var user = User.Create(
                username: $"user{i}",
                firstName: $"First{i}",
                lastName: $"Last{i}",
                email: $"user{i}@test.com",
                roleId: 1,
                factoryId: null,
                isActive: true,
                adObjectGuid: null,
                adDistinguishedName: null,
                createdByUserId: "creator"
            );
            users.Add(user);
        }

        // Act
        Context.Users.AddRange(users);
        await Context.SaveChangesAsync();

        // Assert
        var userCount = await Context.Users.CountAsync();
        userCount.Should().Be(100);
    }
}
