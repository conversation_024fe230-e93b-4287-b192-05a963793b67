using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAuditForReview;

/// <summary>
/// Handler for GetAuditForReviewQuery
/// </summary>
public class GetAuditForReviewQueryHandler : BaseQueryHandler<GetAuditForReviewQuery, AuditReviewDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetAuditForReviewQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<AuditReviewDto> Handle(GetAuditForReviewQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        var currentUserRole = _currentUserService.Role;

        var audit = await _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.Id);
        }

        // Apply role-based access control
        if (currentUserRole == UserRole.ProcessOwner)
        {
            var currentUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == currentUserId, cancellationToken);

            if (currentUser?.FactoryId.HasValue == true && audit.FactoryId != currentUser.FactoryId.Value)
            {
                throw new UnauthorizedAccessException("You can only view audits in your assigned factory");
            }
        }

        // Load detailed answers
        var answers = await _context.AuditAnswers
            .Include(a => a.Question)
            .Include(a => a.SelectedOption)
            .Include(a => a.SelectedOptions)
                .ThenInclude(so => so.QuestionOption)
            .Include(a => a.FailureReasons)
            .Include(a => a.Attachments)
            .Where(a => a.AuditId == request.Id)
            .OrderBy(a => a.Question.DisplayOrder)
            .ThenBy(a => a.QuestionId)
            .Select(a => new AuditAnswerDto
            {
                Id = a.Id,
                AuditId = a.AuditId,
                QuestionId = a.QuestionId,
                QuestionText = a.Question.QuestionText,
                QuestionType = a.Question.QuestionType,
                AnswerValue = a.GetDisplayValue(),
                IsNotApplicable = a.IsNotApplicable,
                Comments = a.Comments,
                SeverityLevel = a.SeverityLevel,
                CreatedAt = a.CreatedAt,
                UpdatedAt = a.UpdatedAt,
                CreatedByUserId = a.CreatedByUserId,
                UpdatedByUserId = a.UpdatedByUserId,
                SelectedOptions = a.SelectedOptions.Select(so => new AuditAnswerSelectedOptionDto
                {
                    Id = so.Id,
                    AuditAnswerId = so.AuditAnswerId,
                    QuestionOptionId = so.QuestionOptionId,
                    OptionText = so.QuestionOption.OptionText,
                    OptionValue = so.QuestionOption.OptionValue
                }).ToList(),
                FailureReasons = a.FailureReasons.Select(fr => new AuditAnswerFailureReasonDto
                {
                    Id = fr.Id,
                    AuditAnswerId = fr.AuditAnswerId,
                    FailureReasonText = fr.ReasonText
                }).ToList(),
                Attachments = a.Attachments.Select(att => new AuditAttachmentDto
                {
                    Id = att.Id,
                    AuditAnswerId = att.AuditAnswerId,
                    FileName = att.FileName,
                    OriginalFileName = att.OriginalFileName,
                    ContentType = att.ContentType,
                    FileSize = att.FileSize ?? 0,
                    FilePath = att.FilePath,
                    Description = att.Description,
                    CreatedAt = att.UploadedAt,
                    CreatedByUserId = att.UploadedByUserId
                }).ToList()
            })
            .ToListAsync(cancellationToken);

        // Get total questions count from template
        var totalQuestions = await _context.Questions
            .Where(q => q.AuditTemplateId == audit.AuditTemplateId && q.IsActive)
            .CountAsync(cancellationToken);

        return new AuditReviewDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate.TemplateName,
            AssignedToUserName = audit.AssignedToUser != null ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() : null,
            AssignedToUserId = audit.AssignedToUserId,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory.FactoryName,
            AreaName = audit.Area.AreaName,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserName = audit.ReviewedByUser != null ? $"{audit.ReviewedByUser.FirstName} {audit.ReviewedByUser.LastName}".Trim() : null,
            ReviewedAt = audit.ReviewedAt,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.UtcNow && 
                audit.OverallStatus != AuditOverallStatus.Closed && 
                audit.OverallStatus != AuditOverallStatus.Cancelled,
            Answers = answers,
            TotalQuestions = totalQuestions,
            AnsweredQuestions = answers.Count,
            PassedQuestions = answers.Count(a => a.AnswerValue == "Yes" || a.AnswerValue == "True"),
            FailedQuestions = answers.Count(a => a.AnswerValue == "No" || a.AnswerValue == "False"),
            NotApplicableQuestions = answers.Count(a => a.IsNotApplicable),
            QuestionsWithFindings = answers.Count(a => a.FailureReasons.Any()),
            TotalAttachments = answers.Sum(a => a.Attachments.Count)
        };
    }
}
