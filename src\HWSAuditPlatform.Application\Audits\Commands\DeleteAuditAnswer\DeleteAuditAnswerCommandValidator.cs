using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.DeleteAuditAnswer;

/// <summary>
/// Validator for DeleteAuditAnswerCommand
/// </summary>
public class DeleteAuditAnswerCommandValidator : AbstractValidator<DeleteAuditAnswerCommand>
{
    private readonly IApplicationDbContext _context;

    public DeleteAuditAnswerCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AnswerId)
            .NotEmpty().WithMessage("Answer ID is required")
            .MustAsync(BeValidAnswer).WithMessage("Audit answer does not exist");

        RuleFor(x => x.AuditId)
            .NotEmpty().WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit).WithMessage("Audit does not exist");

        RuleFor(x => x)
            .MustAsync(AnswerBelongsToAudit).WithMessage("The audit answer does not belong to the specified audit");
    }

    private async Task<bool> BeValidAnswer(string answerId, CancellationToken cancellationToken)
    {
        return await _context.AuditAnswers.AnyAsync(a => a.Id == answerId, cancellationToken);
    }

    private async Task<bool> BeValidAudit(string auditId, CancellationToken cancellationToken)
    {
        return await _context.Audits.AnyAsync(a => a.Id == auditId, cancellationToken);
    }

    private async Task<bool> AnswerBelongsToAudit(DeleteAuditAnswerCommand command, CancellationToken cancellationToken)
    {
        return await _context.AuditAnswers.AnyAsync(
            a => a.Id == command.AnswerId && a.AuditId == command.AuditId,
            cancellationToken);
    }
}
