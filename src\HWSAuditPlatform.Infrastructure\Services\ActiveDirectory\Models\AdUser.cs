namespace HWSAuditPlatform.Infrastructure.Services.ActiveDirectory.Models;

/// <summary>
/// Represents a user from Active Directory
/// </summary>
public class AdUser
{
    public string ObjectGuid { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string DistinguishedName { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime? LastLogon { get; set; }
    public DateTime? WhenCreated { get; set; }
    public DateTime? WhenChanged { get; set; }
    public string? Department { get; set; }
    public string? Title { get; set; }
    public string? Manager { get; set; }
    public string? Phone { get; set; }
    public List<string> MemberOf { get; set; } = new();

    public string FullName => $"{FirstName} {LastName}".Trim();
}

/// <summary>
/// Represents a group from Active Directory
/// </summary>
public class AdGroup
{
    public string ObjectGuid { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string DistinguishedName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string GroupType { get; set; } = string.Empty;
    public DateTime? WhenCreated { get; set; }
    public DateTime? WhenChanged { get; set; }
    public List<string> Members { get; set; } = new();
}
