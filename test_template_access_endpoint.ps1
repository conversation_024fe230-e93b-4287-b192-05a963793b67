# PowerShell script to test the TemplateAccess endpoint
# This script tests the GET /api/v1/template-access/template/{templateId}/users endpoint

$baseUrl = "https://localhost:7001"
$templateId = 1  # Assuming template ID 1 exists

# Test credentials
$username = "admin.user"
$password = "HWSAudit123!"

Write-Host "Testing TemplateAccess endpoint..." -ForegroundColor Green

try {
    # First, authenticate to get a token
    Write-Host "Authenticating..." -ForegroundColor Yellow
    
    $authBody = @{
        username = $username
        password = $password
    } | ConvertTo-Json
    
    $authResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/auth/login" -Method Post -Body $authBody -ContentType "application/json" -SkipCertificateCheck
    
    if ($authResponse.success) {
        Write-Host "Authentication successful" -ForegroundColor Green
        
        # Extract the token from the response
        $token = $authResponse.data.token
        
        # Test the template access endpoint
        Write-Host "Testing GET /api/v1/template-access/template/$templateId/users" -ForegroundColor Yellow
        
        $headers = @{
            "Authorization" = "Bearer $token"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/template-access/template/$templateId/users" -Method Get -Headers $headers -SkipCertificateCheck
        
        Write-Host "Response received:" -ForegroundColor Green
        Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Cyan
        
        if ($response.success) {
            Write-Host "✅ Endpoint is working correctly!" -ForegroundColor Green
            Write-Host "Number of users with access: $($response.data.Count)" -ForegroundColor Green
        } else {
            Write-Host "❌ Endpoint returned success=false" -ForegroundColor Red
            Write-Host "Message: $($response.message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Authentication failed" -ForegroundColor Red
        Write-Host "Message: $($authResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
}

Write-Host "Test completed." -ForegroundColor Green
