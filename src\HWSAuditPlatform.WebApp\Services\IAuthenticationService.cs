using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for authentication services
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticates a user with username and password
    /// </summary>
    /// <param name="username">Username</param>
    /// <param name="password">Password</param>
    /// <returns>Authentication result</returns>
    Task<AuthenticationResult> LoginAsync(string username, string password);

    /// <summary>
    /// Logs out the current user
    /// </summary>
    Task LogoutAsync();

    /// <summary>
    /// Gets the current authentication token
    /// </summary>
    /// <returns>JWT token or null if not authenticated</returns>
    Task<string?> GetTokenAsync();

    /// <summary>
    /// Gets the current user information
    /// </summary>
    /// <returns>User information or null if not authenticated</returns>
    Task<UserInfo?> GetCurrentUserAsync();

    /// <summary>
    /// Refreshes the authentication token
    /// </summary>
    /// <returns>New authentication result or null if refresh failed</returns>
    Task<AuthenticationResult?> RefreshTokenAsync();

    /// <summary>
    /// Checks if the user is currently authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    Task<bool> IsAuthenticatedAsync();
}
