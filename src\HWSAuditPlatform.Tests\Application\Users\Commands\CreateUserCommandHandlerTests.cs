using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Users.Commands.CreateUser;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Application.Exceptions;

namespace HWSAuditPlatform.Tests.Application.Users.Commands;

public class CreateUserCommandHandlerTests : BaseDbTestClass
{
    private readonly CreateUserCommandHandler _handler;

    public CreateUserCommandHandlerTests()
    {
        _handler = new CreateUserCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateUser()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            AdObjectGuid = Guid.NewGuid().ToString(),
            AdDistinguishedName = "CN=New User,OU=Users,DC=test,DC=com"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        var user = await Context.Users.FirstOrDefaultAsync(u => u.Id == result);
        user.Should().NotBeNull();
        user!.Username.Should().Be(command.Username);
        user.FirstName.Should().Be(command.FirstName);
        user.LastName.Should().Be(command.LastName);
        user.Email.Should().Be(command.Email);
        user.IsActive.Should().Be(command.IsActive);
        user.AdObjectGuid.Should().Be(command.AdObjectGuid);
        user.AdDistinguishedName.Should().Be(command.AdDistinguishedName);
    }

    [Fact]
    public async Task Handle_WithInvalidRole_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = (UserRole)999, // Invalid role
            FactoryId = 1,
            IsActive = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithDuplicateUsername_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "admin", // Existing username
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithDuplicateEmail_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>", // Existing email
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithNullFactoryId_ShouldCreateUser()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.DevAdmin,
            FactoryId = null, // Admin might not be assigned to a factory
            IsActive = true
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        var user = await Context.Users.FirstOrDefaultAsync(u => u.Id == result);
        user.Should().NotBeNull();
        user!.FactoryId.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldSetCreatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var expectedCreatedBy = "test-creator-id";
        MockCurrentUserService.Setup(x => x.UserId).Returns(expectedCreatedBy);
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var user = await Context.Users.FirstOrDefaultAsync(u => u.Id == result);
        user.Should().NotBeNull();
        user!.CreatedByUserId.Should().Be(expectedCreatedBy);
    }

    [Fact]
    public async Task Handle_ShouldGenerateCuidId()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveLength(25); // CUID length
        result.Should().StartWith("c"); // CUID prefix
    }

    [Fact]
    public async Task Handle_ShouldSetAuditFields()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var beforeCreate = DateTime.UtcNow;
        
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var user = await Context.Users.FirstOrDefaultAsync(u => u.Id == result);
        user.Should().NotBeNull();
        user!.CreatedAt.Should().BeAfter(beforeCreate);
        user.UpdatedAt.Should().BeAfter(beforeCreate);
        user.RecordVersion.Should().Be(1);
    }
}
