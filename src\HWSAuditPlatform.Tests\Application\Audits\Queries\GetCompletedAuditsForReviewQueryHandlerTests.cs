using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using HWSAuditPlatform.Application.Audits.Queries.GetCompletedAuditsForReview;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Audits.Queries;

public class GetCompletedAuditsForReviewQueryHandlerTests : BaseDbTestClass
{
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly GetCompletedAuditsForReviewQueryHandler _handler;

    public GetCompletedAuditsForReviewQueryHandlerTests()
    {
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _handler = new GetCompletedAuditsForReviewQueryHandler(Context, _mockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidQuery_ReturnsCompletedAudits()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe", FactoryId = 1 };

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1),
            OverallScore = 85.5m
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("manager1");
        _mockCurrentUserService.Setup(x => x.Role).Returns(UserRole.SystemManager);

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        
        var auditReview = result.Items.First();
        Assert.Equal("audit1", auditReview.Id);
        Assert.Equal("Test Template", auditReview.AuditTemplateName);
        Assert.Equal("John Doe", auditReview.AssignedToUserName);
        Assert.Equal("Test Factory", auditReview.FactoryName);
        Assert.Equal("Test Area", auditReview.AreaName);
        Assert.Equal(85.5m, auditReview.OverallScore);
        Assert.Equal(AuditOverallStatus.Submitted, auditReview.OverallStatus);
    }

    [Fact]
    public async Task Handle_ProcessOwnerRole_FiltersAuditsByFactory()
    {
        // Arrange
        var factory1 = new Factory { Id = 1, FactoryName = "Factory 1" };
        var factory2 = new Factory { Id = 2, FactoryName = "Factory 2" };
        var area1 = new Area { Id = 1, AreaName = "Area 1", FactoryId = 1 };
        var area2 = new Area { Id = 2, AreaName = "Area 2", FactoryId = 2 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        
        var processOwner = new User 
        { 
            Id = "processowner1", 
            FirstName = "Process", 
            LastName = "Owner",
            FactoryId = 1 // Only has access to Factory 1
        };
        
        var user1 = new User { Id = "user1", FirstName = "User", LastName = "One" };
        var user2 = new User { Id = "user2", FirstName = "User", LastName = "Two" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1, // Factory 1 - should be visible
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 1,
            FactoryId = 2, // Factory 2 - should NOT be visible
            AreaId = 2,
            AssignedToUserId = "user2",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddRangeAsync(factory1, factory2);
        await Context.Areas.AddRangeAsync(area1, area2);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddRangeAsync(processOwner, user1, user2);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("processowner1");
        _mockCurrentUserService.Setup(x => x.Role).Returns(UserRole.ProcessOwner);

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount); // Only audit1 should be visible
        Assert.Single(result.Items);
        Assert.Equal("audit1", result.Items.First().Id);
        Assert.Equal("Factory 1", result.Items.First().FactoryName);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_FiltersResults()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template1 = new AuditTemplate { Id = 1, TemplateName = "Safety Audit" };
        var template2 = new AuditTemplate { Id = 2, TemplateName = "Quality Audit" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 2,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddRangeAsync(template1, template2);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("manager1");
        _mockCurrentUserService.Setup(x => x.Role).Returns(UserRole.SystemManager);

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "Safety"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal("audit1", result.Items.First().Id);
        Assert.Equal("Safety Audit", result.Items.First().AuditTemplateName);
    }

    [Fact]
    public async Task Handle_WithStatusFilter_FiltersResults()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.ManagerReviewed,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("manager1");
        _mockCurrentUserService.Setup(x => x.Role).Returns(UserRole.SystemManager);

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = 1,
            PageSize = 10,
            Status = AuditOverallStatus.Submitted
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal("audit1", result.Items.First().Id);
        Assert.Equal(AuditOverallStatus.Submitted, result.Items.First().OverallStatus);
    }

    [Fact]
    public async Task Handle_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audits = new List<Audit>();
        for (int i = 1; i <= 15; i++)
        {
            audits.Add(new Audit
            {
                Id = $"audit{i}",
                AuditTemplateId = 1,
                FactoryId = 1,
                AreaId = 1,
                AssignedToUserId = "user1",
                OverallStatus = AuditOverallStatus.Submitted,
                CompletedAt = DateTime.UtcNow.AddDays(-i)
            });
        }

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audits);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("manager1");
        _mockCurrentUserService.Setup(x => x.Role).Returns(UserRole.SystemManager);

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = 2,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(5, result.Items.Count); // Second page should have 5 items
        Assert.Equal(2, result.PageNumber);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
    }
}
