@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditApiService
@inject ILogger<AuditOverviewComponent> Logger

<div class="audit-overview">
    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading audit overview..." />

    @if (!isLoading)
    {
        <!-- Key Metrics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card metric-primary">
                    <div class="metric-icon">
                        <i class="bi bi-clipboard-data"></i>
                    </div>
                    <div class="metric-content">
                        <h3>@totalAudits</h3>
                        <p>Total Audits</p>
                        <small class="text-muted">All time</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-warning">
                    <div class="metric-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                    <div class="metric-content">
                        <h3>@inProgressAudits</h3>
                        <p>In Progress</p>
                        <small class="text-muted">Active audits</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-info">
                    <div class="metric-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div class="metric-content">
                        <h3>@pendingReview</h3>
                        <p>Pending Review</p>
                        <small class="text-muted">Awaiting approval</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card metric-success">
                    <div class="metric-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="metric-content">
                        <h3>@completedThisMonth</h3>
                        <p>Completed</p>
                        <small class="text-muted">This month</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Quick Stats -->
        <div class="row">
            <div class="col-md-8">
                <div class="overview-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-activity me-2"></i>Recent Audit Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (recentAudits?.Any() == true)
                        {
                            <div class="activity-timeline">
                                @foreach (var audit in recentAudits.Take(8))
                                {
                                    <div class="timeline-item">
                                        <div class="timeline-marker @GetStatusMarkerClass(audit.OverallStatus)">
                                            <i class="@GetStatusIcon(audit.OverallStatus)"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-header">
                                                <h6 class="timeline-title">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</h6>
                                                <span class="timeline-date">@audit.ScheduledDate.ToString("MMM dd")</span>
                                            </div>
                                            <div class="timeline-details">
                                                <span class="location">@(audit.Factory?.FactoryName ?? "Unknown Factory")</span>
                                                @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                                                {
                                                    <span class="area"> • @audit.Area.AreaName</span>
                                                }
                                            </div>
                                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                                @audit.OverallStatus
                                            </span>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="bi bi-clipboard-x text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No recent audit activity</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="overview-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-exclamation-triangle me-2"></i>Attention Required
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (overdueAudits?.Any() == true)
                        {
                            <div class="attention-list">
                                @foreach (var audit in overdueAudits.Take(5))
                                {
                                    <div class="attention-item">
                                        <div class="attention-icon">
                                            <i class="bi bi-exclamation-triangle text-danger"></i>
                                        </div>
                                        <div class="attention-content">
                                            <h6 class="attention-title">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</h6>
                                            <p class="attention-details">
                                                @(audit.Factory?.FactoryName ?? "Unknown Factory")
                                                @if (audit.DueDate.HasValue)
                                                {
                                                    <br><small class="text-danger">Due: @audit.DueDate.Value.ToString("MMM dd, yyyy")</small>
                                                }
                                            </p>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="bi bi-check-circle-fill text-success" style="font-size: 2.5rem;"></i>
                                <p class="text-muted mt-2">All audits on track!</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="overview-card mt-3">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-lightning-fill me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" @onclick="CreateNewAudit">
                                <i class="bi bi-plus-circle me-2"></i>Create Audit
                            </button>
                            <button class="btn btn-outline-warning" @onclick="ReviewPendingAudits">
                                <i class="bi bi-clipboard-check me-2"></i>Review Pending (@pendingReview)
                            </button>
                            <button class="btn btn-outline-info" @onclick="ViewAnalytics">
                                <i class="bi bi-bar-chart me-2"></i>View Analytics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .audit-overview {
        padding: 0;
    }

    .metric-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .metric-card:hover::before {
        opacity: 1;
    }

    .metric-primary::before { background: #007bff; }
    .metric-warning::before { background: #ffc107; }
    .metric-info::before { background: #17a2b8; }
    .metric-success::before { background: #28a745; }

    .metric-icon {
        width: 70px;
        height: 70px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 28px;
        color: white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .metric-primary .metric-icon { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
    .metric-warning .metric-icon { background: linear-gradient(135deg, #ffc107 0%, #d69e2e 100%); }
    .metric-info .metric-icon { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
    .metric-success .metric-icon { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }

    .metric-content h3 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
        color: #495057;
    }

    .metric-content p {
        margin: 5px 0 0 0;
        color: #495057;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.9rem;
    }

    .overview-card {
        background: white;
        border: 1px solid #dee2e6;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .overview-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
        border-radius: 12px 12px 0 0 !important;
        padding: 20px;
    }

    .overview-card .card-title {
        color: #495057;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin: 0;
    }

    .activity-timeline {
        max-height: 500px;
        overflow-y: auto;
    }

    .timeline-item {
        display: flex;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .timeline-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .timeline-marker {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
        font-size: 16px;
        color: white;
    }

    .timeline-content {
        flex: 1;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }

    .timeline-title {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
    }

    .timeline-date {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .timeline-details {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 8px;
    }

    .attention-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .attention-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        padding: 15px;
        background: rgba(220, 53, 69, 0.1);
        border-radius: 8px;
        border-left: 4px solid #dc3545;
    }

    .attention-icon {
        margin-right: 12px;
        font-size: 18px;
    }

    .attention-title {
        margin: 0 0 5px 0;
        font-size: 0.95rem;
        font-weight: 600;
        color: #495057;
    }

    .attention-details {
        margin: 0;
        font-size: 0.85rem;
        color: #6c757d;
    }
</style>

@code {
    private bool isLoading = true;
    private int totalAudits = 0;
    private int inProgressAudits = 0;
    private int pendingReview = 0;
    private int completedThisMonth = 0;
    private List<Audit>? recentAudits;
    private List<Audit>? overdueAudits;

    protected override async Task OnInitializedAsync()
    {
        await LoadOverviewData();
    }

    private async Task LoadOverviewData()
    {
        try
    {
            isLoading = true;

            var audits = await AuditApiService.GetAuditsAsync();
            if (audits != null)
            {
                var allAudits = audits.ToList();

                totalAudits = allAudits.Count;
                inProgressAudits = allAudits.Count(a => a.OverallStatus == AuditOverallStatus.InProgress);
                pendingReview = allAudits.Count(a => a.OverallStatus == AuditOverallStatus.PendingManagerReview);
                completedThisMonth = allAudits.Count(a =>
                    a.OverallStatus == AuditOverallStatus.Closed &&
                    a.ScheduledDate.Month == DateTime.UtcNow.Month &&
                    a.ScheduledDate.Year == DateTime.UtcNow.Year);

                recentAudits = allAudits
                    .OrderByDescending(a => a.ScheduledDate)
                    .ToList();

                overdueAudits = allAudits
                    .Where(a => a.DueDate.HasValue && a.DueDate.Value < DateTime.Now && a.OverallStatus != AuditOverallStatus.Closed)
                    .OrderBy(a => a.DueDate)
                    .ToList();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading overview data");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetStatusMarkerClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private string GetStatusIcon(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bi bi-calendar",
            AuditOverallStatus.InProgress => "bi bi-play-circle",
            AuditOverallStatus.PendingManagerReview => "bi bi-clock",
            AuditOverallStatus.Closed => "bi bi-check-circle",
            AuditOverallStatus.Cancelled => "bi bi-x-circle",
            _ => "bi bi-circle"
        };
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private void CreateNewAudit()
    {
        // Navigate to create audit functionality - placeholder
    }

    private void ReviewPendingAudits()
    {
        // Navigate to pending review tab - placeholder
    }

    private void ViewAnalytics()
    {
        // Navigate to analytics tab - placeholder
    }
}
