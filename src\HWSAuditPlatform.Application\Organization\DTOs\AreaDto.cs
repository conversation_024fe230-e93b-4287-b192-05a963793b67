using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Organization.DTOs;

/// <summary>
/// Data Transfer Object for Area entity
/// </summary>
public class AreaDto : AuditableDto<int>
{
    public string AreaName { get; set; } = string.Empty;
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    
    public List<SubAreaSummaryDto> SubAreas { get; set; } = new();
    public int SubAreaCount => SubAreas.Count;
}

/// <summary>
/// Simplified Area DTO for lists and lookups
/// </summary>
public class AreaSummaryDto
{
    public int Id { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public int SubAreaCount { get; set; }
}
