using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for Role entity
/// </summary>
public class RoleDto : BaseDto<int>
{
    public UserRole RoleName { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Data Transfer Object for AD Group Role Mapping
/// </summary>
public class AdGroupRoleMappingDto : BaseDto<int>
{
    public string AdGroupName { get; set; } = string.Empty;
    public int RoleId { get; set; }
    public UserRole RoleName { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}
