@page "/bootstrap-test"
@using Microsoft.AspNetCore.Components.Web

<PageTitle>Bootstrap Integration Test - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="display-4 text-primary mb-4">Bootstrap Integration Test</h1>
            <p class="lead">This page demonstrates that Bootstrap is properly integrated and working with local files.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Bootstrap Components Test</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testing various Bootstrap components:</p>
                    
                    <div class="mb-3">
                        <button class="btn btn-primary me-2">Primary Button</button>
                        <button class="btn btn-secondary me-2">Secondary Button</button>
                        <button class="btn btn-success">Success Button</button>
                    </div>

                    <div class="alert alert-info" role="alert">
                        <strong>Info:</strong> Bootstrap CSS is loading from local files!
                    </div>

                    <div class="progress mb-3">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                    </div>

                    <div class="form-group">
                        <label for="testInput" class="form-label">Test Input</label>
                        <input type="text" class="form-control" id="testInput" placeholder="Bootstrap form styling">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Font Test</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Testing local font loading:</p>
                    
                    <div class="mb-3">
                        <h6>Roboto Font Family (from local files):</h6>
                        <p style="font-family: 'Roboto', sans-serif; font-weight: 300;">Light weight text (300)</p>
                        <p style="font-family: 'Roboto', sans-serif; font-weight: 400;">Regular weight text (400)</p>
                        <p style="font-family: 'Roboto', sans-serif; font-weight: 500;">Medium weight text (500)</p>
                        <p style="font-family: 'Roboto', sans-serif; font-weight: 700;">Bold weight text (700)</p>
                    </div>

                    <div class="mb-3">
                        <h6>Roboto Condensed Font Family (from local files):</h6>
                        <p style="font-family: 'Roboto Condensed', sans-serif; font-weight: 400;">Regular condensed text</p>
                        <p style="font-family: 'Roboto Condensed', sans-serif; font-weight: 700;">Bold condensed text</p>
                    </div>

                    <div class="alert alert-success" role="alert">
                        <strong>Success:</strong> All fonts are loading from local files - no external CDN dependencies!
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Integration Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="badge bg-success fs-6 mb-2">✓ Bootstrap CSS</div>
                                <p class="small">Loaded from local files</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="badge bg-success fs-6 mb-2">✓ Bootstrap JS</div>
                                <p class="small">Loaded from local files</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="badge bg-success fs-6 mb-2">✓ Google Fonts</div>
                                <p class="small">Self-hosted locally</p>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>Files Downloaded:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">Bootstrap 5.3.2 CSS & JS (minified)</li>
                        <li class="list-group-item">Roboto Font Family (Light, Regular, Medium, Bold)</li>
                        <li class="list-group-item">Roboto Condensed Font Family (Regular, Bold)</li>
                        <li class="list-group-item">All fonts in WOFF2 and TTF formats for maximum compatibility</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        // Test page initialization
        await base.OnInitializedAsync();
    }
}
