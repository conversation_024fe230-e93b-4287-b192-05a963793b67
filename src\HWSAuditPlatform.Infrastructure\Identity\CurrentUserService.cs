using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using System.IdentityModel.Tokens.Jwt;

namespace HWSAuditPlatform.Infrastructure.Identity;

/// <summary>
/// Service for accessing current user information from HTTP context
/// </summary>
public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string? UserId => _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

    public string? Username => _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;

    public string? Email => _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Email)?.Value;

    public UserRole? Role
    {
        get
        {
            var roleString = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            if (string.IsNullOrEmpty(roleString))
                return null;

            return Enum.TryParse<UserRole>(roleString, out var role) ? role : null;
        }
    }

    public int? FactoryId
    {
        get
        {
            var factoryIdString = _httpContextAccessor.HttpContext?.User?.FindFirst("FactoryId")?.Value;
            if (string.IsNullOrEmpty(factoryIdString))
                return null;

            return int.TryParse(factoryIdString, out var factoryId) ? factoryId : null;
        }
    }

    public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    public bool HasRole(UserRole role)
    {
        return Role == role;
    }

    public bool HasAnyRole(params UserRole[] roles)
    {
        if (Role == null)
            return false;

        return roles.Contains(Role.Value);
    }
}
