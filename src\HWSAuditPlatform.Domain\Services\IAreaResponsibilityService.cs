using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Services;

/// <summary>
/// Domain service for managing area-based responsibility assignments
/// </summary>
public interface IAreaResponsibilityService
{
    /// <summary>
    /// Assigns responsible users to a finding based on the audit's area and template configuration
    /// </summary>
    /// <param name="finding">The finding to assign responsibilities to</param>
    /// <param name="audit">The audit that generated the finding</param>
    /// <param name="areaResponsibilities">Available area responsibilities</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the assignment operation</returns>
    Task AssignResponsibilitiesAsync(
        Finding finding, 
        Audit audit, 
        IEnumerable<AreaResponsibility> areaResponsibilities,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the responsible user for a specific responsibility type in an area
    /// </summary>
    /// <param name="areaId">The area ID</param>
    /// <param name="responsibilityType">The type of responsibility</param>
    /// <param name="auditTemplateId">Optional audit template ID for template-specific responsibilities</param>
    /// <param name="areaResponsibilities">Available area responsibilities</param>
    /// <returns>The responsible user ID if found, null otherwise</returns>
    string? GetResponsibleUser(
        int areaId, 
        ResponsibilityType responsibilityType, 
        int? auditTemplateId,
        IEnumerable<AreaResponsibility> areaResponsibilities);

    /// <summary>
    /// Validates that area responsibility assignments are consistent and complete
    /// </summary>
    /// <param name="areaResponsibilities">Area responsibilities to validate</param>
    /// <returns>Validation result with any issues found</returns>
    AreaResponsibilityValidationResult ValidateAreaResponsibilities(
        IEnumerable<AreaResponsibility> areaResponsibilities);
}

/// <summary>
/// Result of area responsibility validation
/// </summary>
public class AreaResponsibilityValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
}
