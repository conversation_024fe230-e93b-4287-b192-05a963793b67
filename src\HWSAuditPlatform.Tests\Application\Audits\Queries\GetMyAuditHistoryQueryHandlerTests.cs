using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;
using HWSAuditPlatform.Application.Audits.Queries.GetMyAuditHistory;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Audits.Queries;

public class GetMyAuditHistoryQueryHandlerTests : BaseDbTestClass
{
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly GetMyAuditHistoryQueryHandler _handler;

    public GetMyAuditHistoryQueryHandlerTests()
    {
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _handler = new GetMyAuditHistoryQueryHandler(Context, _mockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidQuery_ReturnsUserAuditHistory()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user1 = new User { Id = "user1", FirstName = "John", LastName = "Doe" };
        var user2 = new User { Id = "user2", FirstName = "Jane", LastName = "Smith" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1", // Assigned to current user
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1),
            OverallScore = 85.5m
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user2", // Assigned to different user - should not be returned
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1),
            OverallScore = 90.0m
        };

        var audit3 = new Audit
        {
            Id = "audit3",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1", // Assigned to current user
            OverallStatus = AuditOverallStatus.InProgress, // Not completed - should not be returned
            CompletedAt = null
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddRangeAsync(user1, user2);
        await Context.Audits.AddRangeAsync(audit1, audit2, audit3);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount); // Only audit1 should be returned
        Assert.Single(result.Items);
        
        var auditSummary = result.Items.First();
        Assert.Equal("audit1", auditSummary.Id);
        Assert.Equal("Test Template", auditSummary.AuditTemplateName);
        Assert.Equal("John Doe", auditSummary.AssignedToUserName);
        Assert.Equal("Test Factory", auditSummary.FactoryName);
        Assert.Equal("Test Area", auditSummary.AreaName);
        Assert.Equal(85.5m, auditSummary.OverallScore);
        Assert.Equal(AuditOverallStatus.Submitted, auditSummary.OverallStatus);
    }

    [Fact]
    public async Task Handle_WithUnauthenticatedUser_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        _mockCurrentUserService.Setup(x => x.UserId).Returns((string?)null);

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(() => 
            _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithSearchTerm_FiltersResults()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template1 = new AuditTemplate { Id = 1, TemplateName = "Safety Audit" };
        var template2 = new AuditTemplate { Id = 2, TemplateName = "Quality Audit" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 2,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddRangeAsync(template1, template2);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchTerm = "Safety"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal("audit1", result.Items.First().Id);
        Assert.Equal("Safety Audit", result.Items.First().AuditTemplateName);
    }

    [Fact]
    public async Task Handle_WithStatusFilter_FiltersResults()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.ManagerReviewed,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10,
            Status = AuditOverallStatus.Submitted
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal("audit1", result.Items.First().Id);
        Assert.Equal(AuditOverallStatus.Submitted, result.Items.First().OverallStatus);
    }

    [Fact]
    public async Task Handle_WithDateFilter_FiltersResults()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit1 = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-5) // 5 days ago
        };

        var audit2 = new Audit
        {
            Id = "audit2",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1) // 1 day ago
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audit1, audit2);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10,
            CompletedDateFrom = DateTime.UtcNow.AddDays(-3) // Only audits completed in last 3 days
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal("audit2", result.Items.First().Id); // Only the recent audit should be returned
    }

    [Theory]
    [InlineData(AuditOverallStatus.Submitted)]
    [InlineData(AuditOverallStatus.ManagerReviewed)]
    [InlineData(AuditOverallStatus.PendingManagerReview)]
    [InlineData(AuditOverallStatus.Closed)]
    [InlineData(AuditOverallStatus.PendingCorrection)]
    public async Task Handle_WithCompletedStatuses_ReturnsAudits(AuditOverallStatus status)
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit = new Audit
        {
            Id = $"audit_{status}",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = status,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Items);
        Assert.Equal($"audit_{status}", result.Items.First().Id);
        Assert.Equal(status, result.Items.First().OverallStatus);
    }

    [Fact]
    public async Task Handle_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audits = new List<Audit>();
        for (int i = 1; i <= 15; i++)
        {
            audits.Add(new Audit
            {
                Id = $"audit{i}",
                AuditTemplateId = 1,
                FactoryId = 1,
                AreaId = 1,
                AssignedToUserId = "user1",
                OverallStatus = AuditOverallStatus.Submitted,
                CompletedAt = DateTime.UtcNow.AddDays(-i)
            });
        }

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddRangeAsync(audits);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = 2,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(5, result.Items.Count); // Second page should have 5 items
        Assert.Equal(2, result.PageNumber);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
    }
}
