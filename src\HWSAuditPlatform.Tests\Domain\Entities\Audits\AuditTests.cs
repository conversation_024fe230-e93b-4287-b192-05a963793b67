using FluentAssertions;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Audits;

public class AuditTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var audit = new Audit();

        // Assert
        audit.AssignmentType.Should().Be(AssignmentType.Individual);
        audit.OverallStatus.Should().Be(AuditOverallStatus.Scheduled);
        audit.Answers.Should().NotBeNull().And.BeEmpty();
        audit.CorrectionRequests.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void IsOverdue_WithDueDateInPast_ShouldReturnTrue()
    {
        // Arrange
        var audit = new Audit
        {
            DueDate = DateTime.UtcNow.AddDays(-1),
            OverallStatus = AuditOverallStatus.InProgress
        };

        // Act & Assert
        audit.IsOverdue.Should().BeTrue();
    }

    [Fact]
    public void IsOverdue_WithDueDateInFuture_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            DueDate = DateTime.UtcNow.AddDays(1),
            OverallStatus = AuditOverallStatus.InProgress
        };

        // Act & Assert
        audit.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOverdue_WithNoDueDate_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            DueDate = null,
            OverallStatus = AuditOverallStatus.InProgress
        };

        // Act & Assert
        audit.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOverdue_WithClosedStatus_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            DueDate = DateTime.UtcNow.AddDays(-1),
            OverallStatus = AuditOverallStatus.Closed
        };

        // Act & Assert
        audit.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsOverdue_WithCancelledStatus_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            DueDate = DateTime.UtcNow.AddDays(-1),
            OverallStatus = AuditOverallStatus.Cancelled
        };

        // Act & Assert
        audit.IsOverdue.Should().BeFalse();
    }

    [Fact]
    public void IsInProgress_WithInProgressStatus_ShouldReturnTrue()
    {
        // Arrange
        var audit = new Audit
        {
            OverallStatus = AuditOverallStatus.InProgress
        };

        // Act & Assert
        audit.IsInProgress.Should().BeTrue();
    }

    [Fact]
    public void IsInProgress_WithOtherStatus_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            OverallStatus = AuditOverallStatus.Scheduled
        };

        // Act & Assert
        audit.IsInProgress.Should().BeFalse();
    }

    [Fact]
    public void IsCompleted_WithCompletedAt_ShouldReturnTrue()
    {
        // Arrange
        var audit = new Audit
        {
            CompletedAt = DateTime.UtcNow
        };

        // Act & Assert
        audit.IsCompleted.Should().BeTrue();
    }

    [Fact]
    public void IsCompleted_WithoutCompletedAt_ShouldReturnFalse()
    {
        // Arrange
        var audit = new Audit
        {
            CompletedAt = null
        };

        // Act & Assert
        audit.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public void LocationDescription_WithSubArea_ShouldIncludeAllLevels()
    {
        // Arrange
        var factory = new Factory { FactoryName = "Main Factory" };
        var area = new Area { AreaName = "Production Line A", Factory = factory };
        var subArea = new SubArea { SubAreaName = "Station 1", Area = area };
        
        var audit = new Audit
        {
            Factory = factory,
            Area = area,
            SubArea = subArea
        };

        // Act & Assert
        audit.LocationDescription.Should().Be("Main Factory - Production Line A - Station 1");
    }

    [Fact]
    public void LocationDescription_WithoutSubArea_ShouldIncludeFactoryAndArea()
    {
        // Arrange
        var factory = new Factory { FactoryName = "Main Factory" };
        var area = new Area { AreaName = "Production Line A", Factory = factory };
        
        var audit = new Audit
        {
            Factory = factory,
            Area = area,
            SubArea = null
        };

        // Act & Assert
        audit.LocationDescription.Should().Be("Main Factory - Production Line A");
    }

    [Fact]
    public void Audit_WithValidAssignment_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string userId = "clh7ckb0x0001qh08w5t6h5zy";
        var scheduledDate = DateTime.UtcNow.AddDays(1);
        var dueDate = DateTime.UtcNow.AddDays(7);

        // Act
        var audit = new Audit
        {
            AuditTemplateId = 1,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = userId,
            ScheduledDate = scheduledDate,
            DueDate = dueDate,
            FactoryId = 1,
            AreaId = 1
        };

        // Assert
        audit.AuditTemplateId.Should().Be(1);
        audit.AssignmentType.Should().Be(AssignmentType.Individual);
        audit.AssignedToUserId.Should().Be(userId);
        audit.ScheduledDate.Should().Be(scheduledDate);
        audit.DueDate.Should().Be(dueDate);
        audit.FactoryId.Should().Be(1);
        audit.AreaId.Should().Be(1);
    }

    [Fact]
    public void Audit_WithGroupAssignment_ShouldAllowGroupId()
    {
        // Arrange
        const string groupId = "clh7ckb0x0002qh08w5t6h5zz";

        // Act
        var audit = new Audit
        {
            AssignmentType = AssignmentType.GroupAny,
            AssignedToUserGroupId = groupId,
            AssignedToUserId = null
        };

        // Assert
        audit.AssignmentType.Should().Be(AssignmentType.GroupAny);
        audit.AssignedToUserGroupId.Should().Be(groupId);
        audit.AssignedToUserId.Should().BeNull();
    }

    [Fact]
    public void Audit_ImplementsIAggregateRoot()
    {
        // Arrange & Act
        var audit = new Audit();

        // Assert
        audit.Should().BeAssignableTo<IAggregateRoot>();
    }

    [Fact]
    public void Audit_InheritsFromAuditableEntity()
    {
        // Arrange & Act
        var audit = new Audit();

        // Assert
        audit.RecordVersion.Should().Be(1);
        audit.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        audit.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void OverallScore_CanBeSetAndRetrieved()
    {
        // Arrange
        var audit = new Audit();
        const decimal score = 85.5m;

        // Act
        audit.OverallScore = score;

        // Assert
        audit.OverallScore.Should().Be(score);
    }

    [Fact]
    public void ManagerComments_CanBeSetAndRetrieved()
    {
        // Arrange
        var audit = new Audit();
        const string comments = "Good audit, minor issues noted.";

        // Act
        audit.ManagerComments = comments;

        // Assert
        audit.ManagerComments.Should().Be(comments);
    }

    [Fact]
    public void ReviewedAt_CanBeSetAndRetrieved()
    {
        // Arrange
        var audit = new Audit();
        var reviewDate = DateTime.UtcNow;

        // Act
        audit.ReviewedAt = reviewDate;

        // Assert
        audit.ReviewedAt.Should().Be(reviewDate);
    }
}
