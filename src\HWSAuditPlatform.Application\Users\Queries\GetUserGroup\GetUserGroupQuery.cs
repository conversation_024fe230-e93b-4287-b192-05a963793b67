using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.Application.Users.Queries.GetUserGroup;

/// <summary>
/// Query to get a single user group by ID
/// </summary>
public class GetUserGroupQuery : BaseQuery<UserGroupDto>
{
    public string Id { get; set; } = string.Empty;

    public GetUserGroupQuery(string id)
    {
        Id = id;
    }
}
