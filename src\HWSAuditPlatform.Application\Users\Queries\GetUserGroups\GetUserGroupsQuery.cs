using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.Application.Users.Queries.GetUserGroups;

/// <summary>
/// Query to get a paginated list of user groups
/// </summary>
public class GetUserGroupsQuery : BaseQuery<PaginatedResult<UserGroupSummaryDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public bool? IsAdSynced { get; set; }
    public string? SortBy { get; set; } = "GroupName";
    public bool SortDescending { get; set; } = false;
}
