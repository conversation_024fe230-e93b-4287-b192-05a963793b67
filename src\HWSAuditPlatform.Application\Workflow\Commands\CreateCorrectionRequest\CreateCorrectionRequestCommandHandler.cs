using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Workflow.Commands.CreateCorrectionRequest;

/// <summary>
/// Handler for CreateCorrectionRequestCommand
/// </summary>
public class CreateCorrectionRequestCommandHandler : BaseCommandHandler<CreateCorrectionRequestCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateCorrectionRequestCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<string> Handle(CreateCorrectionRequestCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Verify audit exists and user has access to it
        var audit = await _context.Audits
            .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        // Verify user is the assigned auditor
        if (audit.AssignedToUserId != currentUserId)
        {
            throw new UnauthorizedAccessException("You can only request corrections for your own audits");
        }

        // Verify audit is in a state that allows correction requests
        if (audit.OverallStatus != AuditOverallStatus.Submitted && 
            audit.OverallStatus != AuditOverallStatus.ManagerReviewed &&
            audit.OverallStatus != AuditOverallStatus.PendingManagerReview)
        {
            throw new InvalidOperationException($"Cannot request correction for audit in {audit.OverallStatus} status");
        }

        // Check if there's already an active correction request for this audit
        var existingRequest = await _context.AuditCorrectionRequests
            .FirstOrDefaultAsync(cr => cr.AuditId == request.AuditId && 
                                      cr.Status == CorrectionRequestStatus.PendingApproval, 
                                 cancellationToken);

        if (existingRequest != null)
        {
            throw new InvalidOperationException("There is already a pending correction request for this audit");
        }

        // Create the correction request
        var correctionRequest = new AuditCorrectionRequest
        {
            Id = CuidGenerator.Generate(),
            AuditId = request.AuditId,
            RequestedByUserId = currentUserId,
            RequestReason = request.RequestReason,
            RequestedAt = DateTime.UtcNow,
            Status = CorrectionRequestStatus.PendingApproval,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = currentUserId,
            UpdatedByUserId = currentUserId
        };

        _context.AuditCorrectionRequests.Add(correctionRequest);
        await _context.SaveChangesAsync(cancellationToken);

        return correctionRequest.Id;
    }
}
