@using HWSAuditPlatform.WebApp.Models
@inject IAuthenticationService AuthService
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject ILogger<LoginComponent> Logger

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>HWS Audit Platform</h2>
            <p class="text-muted">Management Portal</p>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger" role="alert">
                @errorMessage
            </div>
        }

        <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
            <DataAnnotationsValidator />
            <ValidationSummary class="text-danger" />

            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <InputText id="username" class="form-control" @bind-Value="loginModel.Username" placeholder="Enter your username" />
                <ValidationMessage For="@(() => loginModel.Username)" />
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <InputText id="password" type="password" class="form-control" @bind-Value="loginModel.Password" placeholder="Enter your password" />
                <ValidationMessage For="@(() => loginModel.Password)" />
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        <span>Signing in...</span>
                    }
                    else
                    {
                        <span>Sign In</span>
                    }
                </button>
            </div>
        </EditForm>

        <div class="login-footer">
            <small class="text-muted">
                HWS Audit Platform &copy; @DateTime.Now.Year
            </small>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .login-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 0.5rem;
    }

    .login-footer {
        text-align: center;
        margin-top: 2rem;
    }
</style>

@code {
    private LoginRequest loginModel = new();
    private string? errorMessage;
    private bool isLoading = false;

    [Parameter]
    [SupplyParameterFromQuery]
    public string? ReturnUrl { get; set; }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = null;

            var result = await AuthService.LoginAsync(loginModel.Username, loginModel.Password);

            if (result.IsSuccess)
            {
                var userInfo = new UserInfo
                {
                    UserId = result.Username, // We'll improve this later
                    Username = result.Username,
                    Email = result.Email,
                    FullName = result.FullName,
                    Role = result.Role,
                    FactoryId = result.FactoryId,
                    IsActive = true
                };

                // Only call MarkUserAsAuthenticated if it's the custom provider
                if (AuthStateProvider is CustomAuthenticationStateProvider customProvider)
                {
                    customProvider.MarkUserAsAuthenticated(userInfo);
                }

                Logger.LogInformation("Login successful for user: {Username}", loginModel.Username);
                var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
                Navigation.NavigateTo(redirectUrl);
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "Login failed. Please check your credentials.";
                Logger.LogWarning("Login failed for user: {Username}. Error: {Error}", loginModel.Username, errorMessage);
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            Logger.LogError(ex, "Login error for user: {Username}", loginModel.Username);
        }
        finally
        {
            isLoading = false;
        }
    }
}
