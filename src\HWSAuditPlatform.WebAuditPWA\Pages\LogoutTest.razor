@page "/logout-test"
@using Microsoft.AspNetCore.Components.Authorization
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IAuthenticationService AuthService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ILogger<LogoutTest> Logger

<PageTitle>Logout Test - HWS Audit Platform PWA</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        Logout Functionality Test
                    </h4>
                </div>
                <div class="card-body">
                    <AuthorizeView>
                        <Authorized>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Currently Authenticated</h5>
                                <p><strong>User:</strong> @context.User.FindFirst("FullName")?.Value</p>
                                <p><strong>Role:</strong> @context.User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value</p>
                                <p><strong>Username:</strong> @context.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value</p>
                            </div>

                            <div class="d-grid gap-2">
                                <button class="btn btn-danger btn-lg" @onclick="TestLogout1">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Test Logout Method 1 (AuthService + StateProvider)
                                </button>
                                
                                <button class="btn btn-warning btn-lg" @onclick="TestLogout2">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Test Logout Method 2 (AuthService Only)
                                </button>
                                
                                <button class="btn btn-info btn-lg" @onclick="TestLogout3">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Test Logout Method 3 (StateProvider Only)
                                </button>
                                
                                <button class="btn btn-secondary btn-lg" @onclick="TestLogout4">
                                    <i class="fas fa-refresh me-2"></i>
                                    Test Force Reload
                                </button>
                            </div>
                        </Authorized>
                        <NotAuthorized>
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Not Authenticated</h5>
                                <p>You are not currently logged in. The logout test worked if you were previously authenticated!</p>
                                <a href="/login" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Go to Login
                                </a>
                            </div>
                        </NotAuthorized>
                    </AuthorizeView>

                    @if (!string.IsNullOrEmpty(lastAction))
                    {
                        <div class="alert alert-info mt-3">
                            <h6>Last Action:</h6>
                            <p>@lastAction</p>
                            <small class="text-muted">@lastActionTime</small>
                        </div>
                    }

                    @if (debugInfo.Any())
                    {
                        <div class="mt-4">
                            <h6>Debug Information:</h6>
                            <div class="bg-light p-3 rounded">
                                @foreach (var info in debugInfo)
                                {
                                    <div class="small text-muted">@info</div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string lastAction = "";
    private string lastActionTime = "";
    private List<string> debugInfo = new();

    private async Task TestLogout1()
    {
        try
        {
            lastAction = "Testing Method 1: AuthService.LogoutAsync() + AuthStateProvider.MarkUserAsLoggedOut()";
            lastActionTime = DateTime.Now.ToString("HH:mm:ss");
            debugInfo.Clear();
            
            Logger.LogInformation("Test Logout Method 1 started");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - Starting logout method 1");
            
            await AuthService.LogoutAsync();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - AuthService.LogoutAsync() completed");
            
            AuthStateProvider.MarkUserAsLoggedOut();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - AuthStateProvider.MarkUserAsLoggedOut() completed");
            
            StateHasChanged();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - StateHasChanged() called");
            
            Logger.LogInformation("Test Logout Method 1 completed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in Test Logout Method 1");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - ERROR: {ex.Message}");
        }
    }

    private async Task TestLogout2()
    {
        try
        {
            lastAction = "Testing Method 2: AuthService.LogoutAsync() only";
            lastActionTime = DateTime.Now.ToString("HH:mm:ss");
            debugInfo.Clear();
            
            Logger.LogInformation("Test Logout Method 2 started");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - Starting logout method 2");
            
            await AuthService.LogoutAsync();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - AuthService.LogoutAsync() completed");
            
            StateHasChanged();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - StateHasChanged() called");
            
            Logger.LogInformation("Test Logout Method 2 completed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in Test Logout Method 2");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - ERROR: {ex.Message}");
        }
    }

    private Task TestLogout3()
    {
        try
        {
            lastAction = "Testing Method 3: AuthStateProvider.MarkUserAsLoggedOut() only";
            lastActionTime = DateTime.Now.ToString("HH:mm:ss");
            debugInfo.Clear();

            Logger.LogInformation("Test Logout Method 3 started");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - Starting logout method 3");

            AuthStateProvider.MarkUserAsLoggedOut();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - AuthStateProvider.MarkUserAsLoggedOut() completed");

            StateHasChanged();
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - StateHasChanged() called");

            Logger.LogInformation("Test Logout Method 3 completed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in Test Logout Method 3");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - ERROR: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    private async Task TestLogout4()
    {
        try
        {
            lastAction = "Testing Method 4: Force page reload";
            lastActionTime = DateTime.Now.ToString("HH:mm:ss");
            debugInfo.Clear();
            
            Logger.LogInformation("Test Logout Method 4 started");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - Starting force reload");
            
            await JSRuntime.InvokeVoidAsync("location.reload");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in Test Logout Method 4");
            debugInfo.Add($"{DateTime.Now:HH:mm:ss} - ERROR: {ex.Message}");
        }
    }
}
