using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Audits;

/// <summary>
/// Represents a specific audit instance being performed.
/// Maps to the Audits table in the database.
/// </summary>
public class Audit : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// The template version used for this audit
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// Defines how the audit is assigned (Individual, GroupAny). GroupAllScheduled results in individual audits.
    /// </summary>
    public AssignmentType AssignmentType { get; set; } = AssignmentType.Individual;

    /// <summary>
    /// Target group if AssignmentType is GroupAny (CUID FK). Null otherwise.
    /// </summary>
    [MaxLength(25)]
    public string? AssignedToUserGroupId { get; set; }

    /// <summary>
    /// Navigation property for the assigned user group
    /// </summary>
    public virtual UserGroup? AssignedToUserGroup { get; set; }

    /// <summary>
    /// User specifically assigned (Individual type) OR user who claimed a GroupAny audit (AD ObjectGUID FK).
    /// Null initially for GroupAny.
    /// </summary>
    [MaxLength(36)]
    public string? AssignedToUserId { get; set; }

    /// <summary>
    /// Navigation property for the assigned user
    /// </summary>
    public virtual User? AssignedToUser { get; set; }

    /// <summary>
    /// The date and time the audit is scheduled to be performed
    /// </summary>
    public DateTime ScheduledDate { get; set; }

    /// <summary>
    /// The date and time by which the audit should be completed
    /// </summary>
    public DateTime? DueDate { get; set; }

    /// <summary>
    /// Timestamp when the auditor began working on this audit (first interaction)
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Timestamp when the auditor submitted the completed audit
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Current stage of the audit in its lifecycle
    /// </summary>
    public AuditOverallStatus OverallStatus { get; set; }

    /// <summary>
    /// The factory where the audit is conducted
    /// </summary>
    public int FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the factory
    /// </summary>
    public virtual Factory Factory { get; set; } = null!;

    /// <summary>
    /// The specific area within the factory being audited
    /// </summary>
    public int AreaId { get; set; }

    /// <summary>
    /// Navigation property for the area
    /// </summary>
    public virtual Area Area { get; set; } = null!;

    /// <summary>
    /// Optional: The specific sub-area being audited
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Navigation property for the sub-area
    /// </summary>
    public virtual SubArea? SubArea { get; set; }

    /// <summary>
    /// Calculated overall score for the audit, if applicable
    /// </summary>
    [Column(TypeName = "decimal(7,2)")]
    public decimal? OverallScore { get; set; }

    /// <summary>
    /// Comments provided by the manager during the review process
    /// </summary>
    public string? ManagerComments { get; set; }

    /// <summary>
    /// Manager who performed the final review or closed the audit (AD ObjectGUID FK)
    /// </summary>
    [MaxLength(36)]
    public string? ReviewedByUserId { get; set; }

    /// <summary>
    /// Navigation property for the reviewing manager
    /// </summary>
    public virtual User? ReviewedByUser { get; set; }

    /// <summary>
    /// Timestamp of when the manager's review was completed
    /// </summary>
    public DateTime? ReviewedAt { get; set; }

    /// <summary>
    /// Link to the recurring setting if this audit was generated automatically (CUID FK)
    /// </summary>
    [MaxLength(25)]
    public string? RecurringAuditSettingId { get; set; }

    /// <summary>
    /// Navigation property for the recurring audit setting
    /// </summary>
    public virtual RecurringAuditSetting? RecurringAuditSetting { get; set; }

    /// <summary>
    /// Navigation property for audit answers
    /// </summary>
    public virtual ICollection<AuditAnswer> Answers { get; set; } = new List<AuditAnswer>();

    /// <summary>
    /// Navigation property for audit correction requests
    /// </summary>
    public virtual ICollection<AuditCorrectionRequest> CorrectionRequests { get; set; } = new List<AuditCorrectionRequest>();

    /// <summary>
    /// Indicates if the audit is overdue
    /// </summary>
    public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow && OverallStatus != AuditOverallStatus.Closed && OverallStatus != AuditOverallStatus.Cancelled;

    /// <summary>
    /// Indicates if the audit is in progress
    /// </summary>
    public bool IsInProgress => OverallStatus == AuditOverallStatus.InProgress;

    /// <summary>
    /// Indicates if the audit is completed
    /// </summary>
    public bool IsCompleted => CompletedAt.HasValue;

    /// <summary>
    /// Gets the audit location description
    /// </summary>
    public string LocationDescription => SubArea != null ? $"{Factory.FactoryName} - {Area.AreaName} - {SubArea.SubAreaName}" : $"{Factory.FactoryName} - {Area.AreaName}";
}
