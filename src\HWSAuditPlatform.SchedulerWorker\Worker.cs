using HWSAuditPlatform.Application.Users.Commands.SyncUsersFromAd;
using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
using MediatR;
using Microsoft.Extensions.Options;

namespace HWSAuditPlatform.SchedulerWorker;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly AdSyncOptions _adSyncOptions;
    private readonly RecurringAuditOptions _recurringAuditOptions;

    public Worker(ILogger<Worker> logger, IServiceScopeFactory serviceScopeFactory,
        IOptions<AdSyncOptions> adSyncOptions, IOptions<RecurringAuditOptions> recurringAuditOptions)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _adSyncOptions = adSyncOptions.Value;
        _recurringAuditOptions = recurringAuditOptions.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Scheduler Worker started. AD Sync - Interval: {AdInterval} minutes, Enabled: {AdEnabled}. Recurring Audits - Interval: {AuditInterval} minutes, Enabled: {AuditEnabled}",
            _adSyncOptions.SyncIntervalMinutes, _adSyncOptions.Enabled, _recurringAuditOptions.GenerationIntervalMinutes, _recurringAuditOptions.Enabled);

        // Validate configuration
        if (!ValidateConfiguration())
        {
            _logger.LogError("Invalid configuration detected. Worker will not start.");
            return;
        }

        // Calculate the intervals
        var adSyncInterval = TimeSpan.FromMinutes(_adSyncOptions.SyncIntervalMinutes);
        var auditGenerationInterval = TimeSpan.FromMinutes(_recurringAuditOptions.GenerationIntervalMinutes);

        // Use a reasonable base interval (minimum 1 minute, maximum of the smaller configured interval)
        var baseInterval = TimeSpan.FromMinutes(Math.Max(1, Math.Min(_adSyncOptions.SyncIntervalMinutes, _recurringAuditOptions.GenerationIntervalMinutes)));

        var lastAdSync = DateTime.MinValue;
        var lastAuditGeneration = DateTime.MinValue;

        _logger.LogInformation("Worker loop starting with base interval: {BaseInterval} minutes", baseInterval.TotalMinutes);

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var now = DateTime.UtcNow;

                // Check if AD sync is due
                if (_adSyncOptions.Enabled && (now - lastAdSync) >= adSyncInterval)
                {
                    _logger.LogDebug("AD sync is due. Last sync: {LastSync}, Interval: {Interval} minutes",
                        lastAdSync == DateTime.MinValue ? "Never" : lastAdSync.ToString("yyyy-MM-dd HH:mm:ss"),
                        adSyncInterval.TotalMinutes);

                    await PerformAdSyncAsync(stoppingToken);
                    lastAdSync = now;
                }

                // Check if recurring audit generation is due
                if (_recurringAuditOptions.Enabled && (now - lastAuditGeneration) >= auditGenerationInterval)
                {
                    _logger.LogDebug("Recurring audit generation is due. Last generation: {LastGeneration}, Interval: {Interval} minutes",
                        lastAuditGeneration == DateTime.MinValue ? "Never" : lastAuditGeneration.ToString("yyyy-MM-dd HH:mm:ss"),
                        auditGenerationInterval.TotalMinutes);

                    await PerformRecurringAuditGenerationAsync(stoppingToken);
                    lastAuditGeneration = now;
                }

                // Wait for the base interval before checking again
                try
                {
                    await Task.Delay(baseInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Worker cancellation requested");
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Critical error in worker main loop");
            throw;
        }
        finally
        {
            _logger.LogInformation("Scheduler Worker stopped");
        }
    }

    private bool ValidateConfiguration()
    {
        var isValid = true;

        if (_adSyncOptions.SyncIntervalMinutes <= 0)
        {
            _logger.LogError("Invalid AD sync interval: {Interval} minutes. Must be greater than 0.", _adSyncOptions.SyncIntervalMinutes);
            isValid = false;
        }

        if (_recurringAuditOptions.GenerationIntervalMinutes <= 0)
        {
            _logger.LogError("Invalid recurring audit generation interval: {Interval} minutes. Must be greater than 0.", _recurringAuditOptions.GenerationIntervalMinutes);
            isValid = false;
        }

        if (_recurringAuditOptions.MaxAuditsPerBatch <= 0)
        {
            _logger.LogError("Invalid max audits per batch: {MaxAudits}. Must be greater than 0.", _recurringAuditOptions.MaxAuditsPerBatch);
            isValid = false;
        }

        if (_recurringAuditOptions.MaxAuditsPerBatch > 1000)
        {
            _logger.LogWarning("Max audits per batch is very high: {MaxAudits}. Consider reducing to avoid performance issues.", _recurringAuditOptions.MaxAuditsPerBatch);
        }

        return isValid;
    }

    private async Task PerformAdSyncAsync(CancellationToken stoppingToken)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            _logger.LogInformation("Starting scheduled AD user synchronization");

            using var scope = _serviceScopeFactory.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var command = new SyncUsersFromAdCommand
            {
                DryRun = false
            };

            var result = await mediator.Send(command, stoppingToken);
            var duration = DateTime.UtcNow - startTime;

            _logger.LogInformation("AD sync completed successfully in {Duration}ms. Result: {Result}",
                duration.TotalMilliseconds, result.ToString());
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AD sync was cancelled");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Error during scheduled AD user synchronization after {Duration}ms", duration.TotalMilliseconds);
        }
    }

    private async Task PerformRecurringAuditGenerationAsync(CancellationToken stoppingToken)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            _logger.LogInformation("Starting scheduled recurring audit generation (max batch size: {MaxBatch})",
                _recurringAuditOptions.MaxAuditsPerBatch);

            using var scope = _serviceScopeFactory.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var command = new GenerateRecurringAuditsCommand
            {
                DryRun = false,
                MaxAuditsToGenerate = _recurringAuditOptions.MaxAuditsPerBatch
            };

            var result = await mediator.Send(command, stoppingToken);
            var duration = DateTime.UtcNow - startTime;

            if (result.ErrorsEncountered > 0)
            {
                _logger.LogWarning("Recurring audit generation completed with {ErrorCount} errors in {Duration}ms. " +
                    "Generated {AuditCount} audits from {SettingCount} settings. Errors: {Errors}",
                    result.ErrorsEncountered, duration.TotalMilliseconds, result.GeneratedAuditIds.Count,
                    result.SettingsProcessed, string.Join("; ", result.Errors));
            }
            else
            {
                _logger.LogInformation("Recurring audit generation completed successfully in {Duration}ms. " +
                    "Generated {AuditCount} audits from {SettingCount} settings",
                    duration.TotalMilliseconds, result.GeneratedAuditIds.Count, result.SettingsProcessed);
            }

            // Log detailed statistics if any audits were generated
            if (result.GeneratedAuditIds.Count > 0)
            {
                _logger.LogInformation("Generated audit IDs: {AuditIds}", string.Join(", ", result.GeneratedAuditIds.Take(10)));
                if (result.GeneratedAuditIds.Count > 10)
                {
                    _logger.LogInformation("... and {RemainingCount} more audits", result.GeneratedAuditIds.Count - 10);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Recurring audit generation was cancelled");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Error during scheduled recurring audit generation after {Duration}ms", duration.TotalMilliseconds);
        }
    }
}

/// <summary>
/// Configuration options for AD synchronization
/// </summary>
public class AdSyncOptions
{
    public const string SectionName = "AdSync";

    /// <summary>
    /// Interval between AD synchronizations in minutes
    /// </summary>
    public int SyncIntervalMinutes { get; set; } = 30;

    /// <summary>
    /// Whether to enable AD synchronization
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// Configuration options for recurring audit generation
/// </summary>
public class RecurringAuditOptions
{
    public const string SectionName = "RecurringAudits";

    /// <summary>
    /// Interval between recurring audit generation checks in minutes
    /// </summary>
    public int GenerationIntervalMinutes { get; set; } = 15;

    /// <summary>
    /// Whether to enable recurring audit generation
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Maximum number of audits to generate in a single batch
    /// </summary>
    public int MaxAuditsPerBatch { get; set; } = 100;
}
