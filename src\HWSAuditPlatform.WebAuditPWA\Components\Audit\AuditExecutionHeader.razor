@using HWSAuditPlatform.Domain.Entities.Audits

<div class="audit-execution-header">
    <div class="container-fluid py-2">
        <div class="row align-items-center">
            <div class="col-12 col-lg-7">
                <div class="audit-info">
                    <h4 class="audit-title">@Audit?.AuditTemplate?.TemplateName</h4>
                    <div class="audit-subtitle">
                        <i class="fas fa-building text-primary me-2"></i>
                        <span class="factory-name">@Audit?.Factory?.FactoryName</span>
                        @if (Audit?.Area != null)
                        {
                            <i class="fas fa-chevron-right text-muted mx-2"></i>
                            <span class="area-name text-primary">@Audit.Area.AreaName</span>
                        }
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-5">
                <div class="header-actions">
                    <div class="progress-section">
                        <div class="progress-container">
                            <div class="progress">
                                <div class="progress-bar" 
                                     role="progressbar" 
                                     style="width: @ProgressPercentage%"
                                     aria-valuenow="@ProgressPercentage" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <div class="progress-text">
                                <span class="progress-numbers">@AnsweredCount of @TotalQuestions</span>
                                <span class="progress-label">completed</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-save" @onclick="HandleSaveAndExit">
                        <i class="fas fa-save me-2"></i>
                        <span class="btn-text">Save & Exit</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public Audit? Audit { get; set; }
    [Parameter] public double ProgressPercentage { get; set; }
    [Parameter] public int AnsweredCount { get; set; }
    [Parameter] public int TotalQuestions { get; set; }
    [Parameter] public EventCallback OnSaveAndExit { get; set; }

    private async Task HandleSaveAndExit()
    {
        if (OnSaveAndExit.HasDelegate)
        {
            await OnSaveAndExit.InvokeAsync();
        }
    }
}

<style>
    .audit-execution-header {
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        border-bottom: 2px solid var(--industrial-teal);
        box-shadow: 0 4px 12px var(--industrial-shadow);
        /* Remove sticky positioning to give more space for content */
        position: static;
        flex-shrink: 0;
    }

    /* Only use sticky positioning on very large screens where space is abundant */
    @@media (min-width: 1200px) {
        .audit-execution-header {
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }
    }

    .audit-info {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .audit-title {
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--industrial-text-light);
        text-transform: uppercase;
        letter-spacing: 1px;
        margin: 0;
        line-height: 1.2;
        text-shadow: 0 2px 4px var(--industrial-shadow);
    }

    .audit-subtitle {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
        font-size: 0.95rem;
        color: var(--industrial-text-muted);
    }

    .factory-name {
        font-weight: 600;
        color: var(--industrial-text);
    }

    .area-name {
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .progress-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 200px;
    }

    .progress-container {
        width: 100%;
        text-align: center;
    }

    .progress {
        height: 12px;
        background-color: var(--industrial-light-gray);
        border-radius: 6px;
        overflow: hidden;
        box-shadow: inset 0 2px 4px var(--industrial-shadow);
        margin-bottom: 0.5rem;
    }

    .progress-bar {
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 6px;
        box-shadow: 0 2px 4px var(--industrial-glow);
    }

    .progress-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.85rem;
    }

    .progress-numbers {
        font-weight: 700;
        color: var(--industrial-text-light);
    }

    .progress-label {
        color: var(--industrial-text-muted);
        font-size: 0.8rem;
    }

    .btn-save {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        padding: 0.875rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        min-height: 48px;
        white-space: nowrap;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px var(--industrial-glow);
    }

    /* Responsive Design */
    @@media (max-width: 991px) {
        .header-actions {
            justify-content: center;
            margin-top: 1rem;
        }

        .progress-section {
            min-width: 160px;
        }
    }

    @@media (max-width: 767px) {
        .audit-execution-header .container-fluid {
            padding: 0.5rem 1rem;
        }

        .audit-title {
            font-size: 1.1rem;
            line-height: 1.1;
        }

        .audit-subtitle {
            font-size: 0.85rem;
        }

        .header-actions {
            flex-direction: column;
            gap: 0.75rem;
            align-items: stretch;
            margin-top: 0.75rem;
        }

        .progress-section {
            min-width: auto;
            width: 100%;
        }

        .btn-save {
            width: 100%;
            justify-content: center;
            padding: 0.875rem 1.25rem;
            min-height: 48px;
            font-size: 0.95rem;
        }

        .btn-text {
            display: inline;
        }

        .audit-info {
            gap: 0.25rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .btn-save {
            min-height: 48px;
            padding: 1rem 1.5rem;
            font-size: 0.95rem;
        }
    }

    /* Extra small devices - very compact */
    @@media (max-width: 480px) {
        .audit-execution-header .container-fluid {
            padding: 0.5rem 0.75rem;
        }

        .audit-title {
            font-size: 1rem;
        }

        .audit-subtitle {
            font-size: 0.8rem;
        }

        .btn-save {
            padding: 0.875rem 1.25rem;
            min-height: 48px;
            font-size: 0.9rem;
        }

        /* Ensure button text and icons are always visible */
        .btn-save .btn-text {
            display: inline !important;
        }

        .btn-save i {
            display: inline !important;
            margin-right: 0.5rem;
        }

        .progress-text {
            font-size: 0.75rem;
        }

        .progress-numbers {
            font-size: 0.8rem;
        }

        .progress-label {
            font-size: 0.7rem;
        }
    }
</style>
