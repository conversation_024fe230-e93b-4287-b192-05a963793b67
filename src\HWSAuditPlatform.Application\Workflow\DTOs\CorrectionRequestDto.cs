using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Workflow.DTOs;

/// <summary>
/// Data Transfer Object for Audit Correction Request entity
/// </summary>
public class CorrectionRequestDto : AuditableDto<string>
{
    /// <summary>
    /// The audit being requested for correction
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Audit template name for display
    /// </summary>
    public string? AuditTemplateName { get; set; }

    /// <summary>
    /// Factory name for display
    /// </summary>
    public string? FactoryName { get; set; }

    /// <summary>
    /// Area name for display
    /// </summary>
    public string? AreaName { get; set; }

    /// <summary>
    /// Sub-area name for display
    /// </summary>
    public string? SubAreaName { get; set; }

    /// <summary>
    /// Auditor who submitted the request
    /// </summary>
    public string RequestedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Requesting user name for display
    /// </summary>
    public string? RequestedByUserName { get; set; }

    /// <summary>
    /// Justification provided by the auditor
    /// </summary>
    public string RequestReason { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the correction request was submitted
    /// </summary>
    public DateTime RequestedAt { get; set; }

    /// <summary>
    /// Current status of the correction request
    /// </summary>
    public CorrectionRequestStatus Status { get; set; }

    /// <summary>
    /// Manager who reviewed the correction request
    /// </summary>
    public string? ReviewedByUserId { get; set; }

    /// <summary>
    /// Reviewing manager name for display
    /// </summary>
    public string? ReviewedByUserName { get; set; }

    /// <summary>
    /// Timestamp when the manager's review was completed
    /// </summary>
    public DateTime? ReviewedAt { get; set; }

    /// <summary>
    /// Comments provided by the manager
    /// </summary>
    public string? ManagerComments { get; set; }

    /// <summary>
    /// Indicates if the request is pending approval
    /// </summary>
    public bool IsPending => Status == CorrectionRequestStatus.PendingApproval;

    /// <summary>
    /// Indicates if the request has been approved
    /// </summary>
    public bool IsApproved => Status == CorrectionRequestStatus.Approved;

    /// <summary>
    /// Indicates if the request has been denied
    /// </summary>
    public bool IsDenied => Status == CorrectionRequestStatus.Denied;
}

/// <summary>
/// Summary DTO for correction requests in lists
/// </summary>
public class CorrectionRequestSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditId { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public DateTime RequestedAt { get; set; }
    public CorrectionRequestStatus Status { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public bool IsPending => Status == CorrectionRequestStatus.PendingApproval;
}

/// <summary>
/// Request model for creating a correction request
/// </summary>
public class CreateCorrectionRequestDto
{
    /// <summary>
    /// The audit ID to request correction for
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Justification for the correction request
    /// </summary>
    public string RequestReason { get; set; } = string.Empty;
}

/// <summary>
/// Request model for reviewing a correction request
/// </summary>
public class ReviewCorrectionRequestDto
{
    /// <summary>
    /// Whether the request is approved or denied
    /// </summary>
    public bool Approved { get; set; }

    /// <summary>
    /// Manager's comments on the request
    /// </summary>
    public string? ManagerComments { get; set; }
}
