using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents the assignment of template access to users (typically Process Owners).
/// Maps to the TemplateAccessAssignments table in the database.
/// </summary>
public class TemplateAccessAssignment : AuditableEntity<int>
{
    /// <summary>
    /// The user ID who is granted access (typically a Process Owner, AD ObjectGUID FK)
    /// </summary>
    [Required]
    [MaxLength(36)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// The audit template ID being granted access to
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// User who granted this access (typically a SystemManager or DevAdmin)
    /// </summary>
    [Required]
    [MaxLength(36)]
    public string AssignedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user who granted access
    /// </summary>
    public virtual User AssignedByUser { get; set; } = null!;

    /// <summary>
    /// Indicates if this access assignment is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Optional description or notes about this access assignment
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional expiration date for this access (null means no expiration)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Creates a new template access assignment
    /// </summary>
    /// <param name="userId">User ID to grant access to</param>
    /// <param name="auditTemplateId">Template ID to grant access to</param>
    /// <param name="assignedByUserId">User granting the access</param>
    /// <param name="description">Optional description</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <returns>New TemplateAccessAssignment instance</returns>
    public static TemplateAccessAssignment Create(
        string userId,
        int auditTemplateId,
        string assignedByUserId,
        string? description = null,
        DateTime? expiresAt = null)
    {
        return new TemplateAccessAssignment
        {
            UserId = userId,
            AuditTemplateId = auditTemplateId,
            AssignedByUserId = assignedByUserId,
            Description = description,
            ExpiresAt = expiresAt,
            IsActive = true,
            CreatedByUserId = assignedByUserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Checks if this access assignment is currently valid (active and not expired)
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsCurrentlyValid()
    {
        if (!IsActive)
            return false;

        if (ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow)
            return false;

        return true;
    }

    /// <summary>
    /// Revokes this access assignment
    /// </summary>
    /// <param name="revokedByUserId">User revoking the access</param>
    public void Revoke(string revokedByUserId)
    {
        IsActive = false;
        UpdatedByUserId = revokedByUserId;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Extends the expiration date of this access assignment
    /// </summary>
    /// <param name="newExpirationDate">New expiration date (null for no expiration)</param>
    /// <param name="updatedByUserId">User making the change</param>
    public void ExtendAccess(DateTime? newExpirationDate, string updatedByUserId)
    {
        ExpiresAt = newExpirationDate;
        UpdatedByUserId = updatedByUserId;
        UpdatedAt = DateTime.UtcNow;
    }
}
