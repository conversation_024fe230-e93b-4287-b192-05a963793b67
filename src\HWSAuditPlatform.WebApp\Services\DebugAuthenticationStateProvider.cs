using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Debug authentication state provider that always returns an authenticated user
/// </summary>
public class DebugAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly ILogger<DebugAuthenticationStateProvider> _logger;
    private readonly IConfiguration _configuration;
    private ClaimsPrincipal _currentUser;

    public DebugAuthenticationStateProvider(
        ILogger<DebugAuthenticationStateProvider> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _currentUser = CreateMockUser();
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        _logger.LogDebug("🚨 DEBUG MODE: Returning mock authentication state");
        return Task.FromResult(new AuthenticationState(_currentUser));
    }

    private ClaimsPrincipal CreateMockUser()
    {
        var mockUser = _configuration.GetSection("Debug:MockUser");
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, mockUser["UserId"] ?? "debug-user-001"),
            new(ClaimTypes.Name, mockUser["Username"] ?? "debug.user"),
            new(ClaimTypes.Email, mockUser["Email"] ?? "<EMAIL>"),
            new(ClaimTypes.Role, mockUser["Role"] ?? "Admin"),
            new("FullName", mockUser["FullName"] ?? "Debug User")
        };

        var factoryId = mockUser["FactoryId"];
        if (!string.IsNullOrEmpty(factoryId))
        {
            claims.Add(new Claim("FactoryId", factoryId));
        }

        var identity = new ClaimsIdentity(claims, "debug");
        var user = new ClaimsPrincipal(identity);

        _logger.LogWarning("🚨 DEBUG MODE: Using mock authentication for user: {Username} with role: {Role}", 
            mockUser["Username"], mockUser["Role"]);

        return user;
    }

    /// <summary>
    /// Marks the user as authenticated (for debug mode compatibility)
    /// </summary>
    /// <param name="user">User information</param>
    public void MarkUserAsAuthenticated(Models.UserInfo user)
    {
        _logger.LogWarning("🚨 DEBUG MODE: MarkUserAsAuthenticated called for: {Username}", user.Username);
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.UserId),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new(ClaimTypes.Role, user.Role),
            new("FullName", user.FullName)
        };

        if (!string.IsNullOrEmpty(user.FactoryId))
        {
            claims.Add(new Claim("FactoryId", user.FactoryId));
        }

        var identity = new ClaimsIdentity(claims, "debug");
        _currentUser = new ClaimsPrincipal(identity);

        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
    }

    /// <summary>
    /// Marks the user as logged out (for debug mode compatibility)
    /// </summary>
    public void MarkUserAsLoggedOut()
    {
        _logger.LogWarning("🚨 DEBUG MODE: MarkUserAsLoggedOut called");
        
        _currentUser = CreateMockUser(); // Reset to default mock user
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
    }
}
