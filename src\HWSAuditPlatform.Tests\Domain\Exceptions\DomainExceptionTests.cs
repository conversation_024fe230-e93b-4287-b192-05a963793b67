using FluentAssertions;
using HWSAuditPlatform.Domain.Exceptions;

namespace HWSAuditPlatform.Tests.Domain.Exceptions;

public class DomainExceptionTests
{
    [Fact]
    public void BusinessRuleViolationException_WithMessage_ShouldSetMessage()
    {
        // Arrange
        const string message = "Business rule violated: Audit cannot be submitted without answers";

        // Act
        var exception = new BusinessRuleViolationException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Fact]
    public void BusinessRuleViolationException_WithMessageAndInnerException_ShouldSetBoth()
    {
        // Arrange
        const string message = "Business rule violated";
        var innerException = new InvalidOperationException("Inner exception");

        // Act
        var exception = new BusinessRuleViolationException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void EntityNotFoundException_WithStringId_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string entityType = "Audit";
        const string entityId = "clh7ckb0x0000qh08w5t6h5zx";

        // Act
        var exception = new EntityNotFoundException(entityType, entityId);

        // Assert
        exception.EntityType.Should().Be(entityType);
        exception.EntityId.Should().Be(entityId);
        exception.Message.Should().Be($"{entityType} with ID '{entityId}' was not found.");
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Fact]
    public void EntityNotFoundException_WithIntId_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string entityType = "AuditTemplate";
        const int entityId = 123;

        // Act
        var exception = new EntityNotFoundException(entityType, entityId);

        // Assert
        exception.EntityType.Should().Be(entityType);
        exception.EntityId.Should().Be(entityId.ToString());
        exception.Message.Should().Be($"{entityType} with ID '{entityId}' was not found.");
    }

    [Fact]
    public void InvalidOperationDomainException_WithMessage_ShouldSetMessage()
    {
        // Arrange
        const string message = "Cannot modify a submitted audit without correction request";

        // Act
        var exception = new InvalidOperationDomainException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Fact]
    public void InvalidOperationDomainException_WithMessageAndInnerException_ShouldSetBoth()
    {
        // Arrange
        const string message = "Invalid operation";
        var innerException = new ArgumentException("Argument exception");

        // Act
        var exception = new InvalidOperationDomainException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void ValidationException_WithSingleError_ShouldSetErrorsCorrectly()
    {
        // Arrange
        const string error = "Username is required";

        // Act
        var exception = new ValidationException(error);

        // Assert
        exception.Message.Should().Be(error);
        exception.Errors.Should().HaveCount(1);
        exception.Errors.Should().Contain(error);
        exception.Should().BeAssignableTo<DomainException>();
    }

    [Fact]
    public void ValidationException_WithMultipleErrors_ShouldSetErrorsCorrectly()
    {
        // Arrange
        var errors = new[] { "Username is required", "Email is invalid", "Password is too short" };

        // Act
        var exception = new ValidationException(errors);

        // Assert
        exception.Message.Should().Be("Validation failed");
        exception.Errors.Should().HaveCount(3);
        exception.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void ValidationException_WithEmptyErrors_ShouldHandleGracefully()
    {
        // Arrange
        var errors = Array.Empty<string>();

        // Act
        var exception = new ValidationException(errors);

        // Assert
        exception.Message.Should().Be("Validation failed");
        exception.Errors.Should().BeEmpty();
    }

    [Fact]
    public void ValidationException_ErrorsCollection_ShouldBeReadOnly()
    {
        // Arrange
        var errors = new[] { "Error 1", "Error 2" };
        var exception = new ValidationException(errors);

        // Act & Assert
        exception.Errors.Should().BeAssignableTo<IReadOnlyCollection<string>>();
        
        // Verify we can't modify the collection
        var act = () => ((ICollection<string>)exception.Errors).Add("New Error");
        act.Should().Throw<NotSupportedException>();
    }

    [Fact]
    public void AllDomainExceptions_ShouldInheritFromDomainException()
    {
        // Arrange & Act
        var businessRuleException = new BusinessRuleViolationException("test");
        var entityNotFoundException = new EntityNotFoundException("Entity", "123");
        var invalidOperationException = new InvalidOperationDomainException("test");
        var validationException = new ValidationException("test");

        // Assert
        businessRuleException.Should().BeAssignableTo<DomainException>();
        entityNotFoundException.Should().BeAssignableTo<DomainException>();
        invalidOperationException.Should().BeAssignableTo<DomainException>();
        validationException.Should().BeAssignableTo<DomainException>();
    }

    [Fact]
    public void AllDomainExceptions_ShouldInheritFromException()
    {
        // Arrange & Act
        var businessRuleException = new BusinessRuleViolationException("test");
        var entityNotFoundException = new EntityNotFoundException("Entity", "123");
        var invalidOperationException = new InvalidOperationDomainException("test");
        var validationException = new ValidationException("test");

        // Assert
        businessRuleException.Should().BeAssignableTo<Exception>();
        entityNotFoundException.Should().BeAssignableTo<Exception>();
        invalidOperationException.Should().BeAssignableTo<Exception>();
        validationException.Should().BeAssignableTo<Exception>();
    }
}
