@using HWSAuditPlatform.Domain.Entities.Templates

<div class="question-list">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5>Questions (@(Questions?.Count ?? 0))</h5>
        <button class="btn btn-sm btn-primary" @onclick="AddQuestion">
            <i class="bi bi-plus-circle me-1"></i>Add Question
        </button>
    </div>

    @if (Questions?.Any() == true)
    {
        <div class="questions-container">
            @foreach (var question in Questions.OrderBy(q => q.DisplayOrder))
            {
                <div class="question-item card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="question-content flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-secondary me-2">@question.DisplayOrder</span>
                                    <span class="badge @GetQuestionTypeBadgeClass(question.QuestionType.ToString()) me-2">
                                        @question.QuestionType.ToString()
                                    </span>
                                    @if (question.IsRequired)
                                    {
                                        <span class="badge bg-danger me-2">Required</span>
                                    }
                                    @if (question.EvidenceRequired)
                                    {
                                        <span class="badge bg-info me-2">Evidence Required</span>
                                    }
                                </div>
                                
                                <h6 class="question-text mb-2">@question.QuestionText</h6>
                                
                                @if (!string.IsNullOrEmpty(question.HelpText))
                                {
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-info-circle me-1"></i>@question.HelpText
                                    </p>
                                }

                                @if (question.Weight.HasValue)
                                {
                                    <small class="text-muted">
                                        <i class="bi bi-star me-1"></i>Weight: @question.Weight.Value
                                    </small>
                                }

                                @if (question.Options?.Any() == true)
                                {
                                    <div class="question-options mt-2">
                                        <small class="text-muted d-block mb-1">Options:</small>
                                        <div class="options-list">
                                            @foreach (var option in question.Options.OrderBy(o => o.DisplayOrder))
                                            {
                                                <span class="badge bg-light text-dark me-1 mb-1">
                                                    @option.OptionText
                                                    @if (!string.IsNullOrEmpty(option.OptionValue))
                                                    {
                                                        <span class="text-muted">(@option.OptionValue)</span>
                                                    }
                                                </span>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                            
                            <div class="question-actions ms-3">
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-outline-primary" 
                                            @onclick="() => EditQuestion(question)"
                                            title="Edit question">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" 
                                            @onclick="() => MoveQuestionUp(question)"
                                            disabled="@(question.DisplayOrder <= 1)"
                                            title="Move up">
                                        <i class="bi bi-arrow-up"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" 
                                            @onclick="() => MoveQuestionDown(question)"
                                            disabled="@(question.DisplayOrder >= (Questions?.Count ?? 0))"
                                            title="Move down">
                                        <i class="bi bi-arrow-down"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" 
                                            @onclick="() => DeleteQuestion(question)"
                                            title="Delete question">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-4">
            <i class="bi bi-question-circle text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">No Questions Added</h5>
            <p class="text-muted">Start building your template by adding questions.</p>
            <button class="btn btn-primary" @onclick="AddQuestion">
                <i class="bi bi-plus-circle me-2"></i>Add First Question
            </button>
        </div>
    }
</div>

<style>
    .question-list {
        padding: 0;
    }

    .question-item {
        border-left: 4px solid #007bff;
        transition: all 0.2s ease;
    }

    .question-item:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateX(2px);
    }

    .question-text {
        color: #495057;
        font-weight: 600;
    }

    .question-actions .btn-group-vertical .btn {
        border-radius: 0;
        border-bottom: none;
    }

    .question-actions .btn-group-vertical .btn:first-child {
        border-top-left-radius: 0.375rem;
        border-top-right-radius: 0.375rem;
    }

    .question-actions .btn-group-vertical .btn:last-child {
        border-bottom-left-radius: 0.375rem;
        border-bottom-right-radius: 0.375rem;
        border-bottom: 1px solid #dee2e6;
    }

    .options-list {
        max-height: 100px;
        overflow-y: auto;
    }

    .badge.bg-light {
        border: 1px solid #dee2e6;
    }
</style>

@code {
    [Parameter] public List<Question>? Questions { get; set; }
    [Parameter] public EventCallback OnAddQuestion { get; set; }
    [Parameter] public EventCallback<Question> OnEditQuestion { get; set; }
    [Parameter] public EventCallback<Question> OnDeleteQuestion { get; set; }
    [Parameter] public EventCallback<Question> OnMoveQuestionUp { get; set; }
    [Parameter] public EventCallback<Question> OnMoveQuestionDown { get; set; }

    private string GetQuestionTypeBadgeClass(string questionType)
    {
        return questionType?.ToLower() switch
        {
            "yesno" => "bg-primary",
            "numeric" => "bg-success",
            "shorttext" => "bg-info",
            "longtext" => "bg-info",
            "singleselect" => "bg-warning",
            "multiselect" => "bg-secondary",
            "date" => "bg-dark",
            _ => "bg-light text-dark"
        };
    }

    private async Task AddQuestion()
    {
        await OnAddQuestion.InvokeAsync();
    }

    private async Task EditQuestion(Question question)
    {
        await OnEditQuestion.InvokeAsync(question);
    }

    private async Task DeleteQuestion(Question question)
    {
        await OnDeleteQuestion.InvokeAsync(question);
    }

    private async Task MoveQuestionUp(Question question)
    {
        await OnMoveQuestionUp.InvokeAsync(question);
    }

    private async Task MoveQuestionDown(Question question)
    {
        await OnMoveQuestionDown.InvokeAsync(question);
    }
}
