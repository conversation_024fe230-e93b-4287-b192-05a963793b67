using FluentAssertions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.Commands.CreateProcessOwnerAssignment;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace HWSAuditPlatform.Tests.Application.Users.Commands;

public class CreateProcessOwnerAssignmentCommandHandlerTests
{
    private readonly Mock<IApplicationDbContext> _mockContext;
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly CreateProcessOwnerAssignmentCommandHandler _handler;

    public CreateProcessOwnerAssignmentCommandHandlerTests()
    {
        _mockContext = new Mock<IApplicationDbContext>();
        _mockCurrentUserService = new Mock<ICurrentUserService>();

        _handler = new CreateProcessOwnerAssignmentCommandHandler(
            _mockContext.Object,
            _mockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateAssignmentAndReturnId()
    {
        // Arrange
        var command = new CreateProcessOwnerAssignmentCommand
        {
            ProcessOwnerUserId = "process-owner-123",
            FactoryId = 1,
            AreaId = 2,
            SubAreaId = 3,
            Description = "Test assignment"
        };

        var currentUserId = "admin-456";
        var currentUserRole = UserRole.SystemManager;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        _mockCurrentUserService.Setup(x => x.Role).Returns(currentUserRole);

        var mockProcessOwnerAssignments = new Mock<Microsoft.EntityFrameworkCore.DbSet<ProcessOwnerAssignment>>();
        _mockContext.Setup(x => x.ProcessOwnerAssignments).Returns(mockProcessOwnerAssignments.Object);

        var mockUsers = new Mock<Microsoft.EntityFrameworkCore.DbSet<User>>();
        _mockContext.Setup(x => x.Users).Returns(mockUsers.Object);

        // Mock user existence check
        var processOwnerUser = new User { AdObjectGuid = "process-owner-123", Role = new Role { RoleName = UserRole.ProcessOwner } };
        mockUsers.Setup(x => x.FindAsync(It.IsAny<object[]>())).ReturnsAsync(processOwnerUser);

        _mockContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>())).ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeGreaterThan(0);
        mockProcessOwnerAssignments.Verify(x => x.Add(It.IsAny<ProcessOwnerAssignment>()), Times.Once);
        _mockContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentProcessOwner_ShouldThrowNotFoundException()
    {
        // Arrange
        var command = new CreateProcessOwnerAssignmentCommand
        {
            ProcessOwnerUserId = "non-existent-user",
            FactoryId = 1
        };

        var currentUserId = "admin-456";
        var currentUserRole = UserRole.SystemManager;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        _mockCurrentUserService.Setup(x => x.Role).Returns(currentUserRole);

        var mockUsers = new Mock<Microsoft.EntityFrameworkCore.DbSet<User>>();
        _mockContext.Setup(x => x.Users).Returns(mockUsers.Object);

        // Mock user not found
        mockUsers.Setup(x => x.FindAsync(It.IsAny<object[]>())).ReturnsAsync((User?)null);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*User not found*");
    }

    [Fact]
    public async Task Handle_WithUserNotProcessOwner_ShouldThrowValidationException()
    {
        // Arrange
        var command = new CreateProcessOwnerAssignmentCommand
        {
            ProcessOwnerUserId = "auditor-123",
            FactoryId = 1
        };

        var currentUserId = "admin-456";
        var currentUserRole = UserRole.SystemManager;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        _mockCurrentUserService.Setup(x => x.Role).Returns(currentUserRole);

        var mockUsers = new Mock<Microsoft.EntityFrameworkCore.DbSet<User>>();
        _mockContext.Setup(x => x.Users).Returns(mockUsers.Object);

        // Mock user with wrong role
        var auditorUser = new User { AdObjectGuid = "auditor-123", Role = new Role { RoleName = UserRole.Auditor } };
        mockUsers.Setup(x => x.FindAsync(It.IsAny<object[]>())).ReturnsAsync(auditorUser);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<HWSAuditPlatform.Application.Exceptions.ValidationException>()
            .WithMessage("*must have ProcessOwner role*");
    }

    [Theory]
    [InlineData(UserRole.Auditor)]
    [InlineData(UserRole.Viewer)]
    [InlineData(UserRole.ProcessOwner)]
    public async Task Handle_WithInsufficientPermissions_ShouldThrowUnauthorizedAccessException(UserRole userRole)
    {
        // Arrange
        var command = new CreateProcessOwnerAssignmentCommand
        {
            ProcessOwnerUserId = "process-owner-123",
            FactoryId = 1
        };

        var currentUserId = "user-456";

        _mockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        _mockCurrentUserService.Setup(x => x.Role).Returns(userRole);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<UnauthorizedAccessException>()
            .WithMessage("*insufficient permissions*");
    }

    [Theory]
    [InlineData(null, 2, null)] // Area without Factory
    [InlineData(null, null, 3)] // SubArea without Area
    [InlineData(1, null, 3)] // SubArea without Area
    public async Task Handle_WithInvalidHierarchy_ShouldThrowValidationException(
        int? factoryId, int? areaId, int? subAreaId)
    {
        // Arrange
        var command = new CreateProcessOwnerAssignmentCommand
        {
            ProcessOwnerUserId = "process-owner-123",
            FactoryId = factoryId,
            AreaId = areaId,
            SubAreaId = subAreaId
        };

        var currentUserId = "admin-456";
        var currentUserRole = UserRole.SystemManager;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        _mockCurrentUserService.Setup(x => x.Role).Returns(currentUserRole);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<HWSAuditPlatform.Application.Exceptions.ValidationException>()
            .WithMessage("*Invalid organizational hierarchy*");
    }
}
