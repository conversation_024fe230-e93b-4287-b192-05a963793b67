﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AuditTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TemplateName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Version = table.Column<int>(type: "int", nullable: false),
                    IsPublished = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Location",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LocationName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    LocationCountry = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    LocationCountryCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    OwnerGroupId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Location", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "QuestionGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    GroupName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    Description = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QuestionGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QuestionGroups_AuditTemplates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "AuditTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AdGroupRoleMappings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AdGroupName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdGroupRoleMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdGroupRoleMappings_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Questions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    QuestionGroupId = table.Column<int>(type: "int", nullable: true),
                    QuestionText = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    QuestionType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Weight = table.Column<decimal>(type: "decimal(7,2)", nullable: true),
                    HelpText = table.Column<string>(type: "text", nullable: true),
                    ParentQuestionId = table.Column<int>(type: "int", nullable: true),
                    TriggerAnswerValue = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SeverityLevel = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EvidenceRequired = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    EvidenceInstructions = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EvidenceTimingHint = table.Column<int>(type: "int", nullable: true),
                    AllowedEvidenceTypes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Questions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Questions_AuditTemplates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "AuditTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Questions_QuestionGroups_QuestionGroupId",
                        column: x => x.QuestionGroupId,
                        principalTable: "QuestionGroups",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Questions_Questions_ParentQuestionId",
                        column: x => x.ParentQuestionId,
                        principalTable: "Questions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "QuestionOptions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    QuestionId = table.Column<int>(type: "int", nullable: false),
                    OptionText = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    OptionValue = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    IsCorrectOption = table.Column<bool>(type: "bit", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QuestionOptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QuestionOptions_Questions_QuestionId",
                        column: x => x.QuestionId,
                        principalTable: "Questions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Areas",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AreaName = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: false),
                    FactoryId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Areas", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AuditAnswerFailureReasons",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditAnswerId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    FailureReasonId = table.Column<int>(type: "int", nullable: false),
                    ReasonText = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RecordVersion = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditAnswerFailureReasons", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AuditAnswers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    QuestionId = table.Column<int>(type: "int", nullable: false),
                    AnswerBoolean = table.Column<bool>(type: "bit", nullable: true),
                    AnswerText = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OriginalAnswerValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AnswerNumeric = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AnswerDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SelectedOptionId = table.Column<int>(type: "int", nullable: true),
                    IsNotApplicable = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    AnsweredAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SeverityLevel = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditAnswers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditAnswers_QuestionOptions_SelectedOptionId",
                        column: x => x.SelectedOptionId,
                        principalTable: "QuestionOptions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AuditAnswers_Questions_QuestionId",
                        column: x => x.QuestionId,
                        principalTable: "Questions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AuditAnswerSelectedOptions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditAnswerId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    QuestionOptionId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditAnswerSelectedOptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditAnswerSelectedOptions_AuditAnswers_AuditAnswerId",
                        column: x => x.AuditAnswerId,
                        principalTable: "AuditAnswers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AuditAnswerSelectedOptions_QuestionOptions_QuestionOptionId",
                        column: x => x.QuestionOptionId,
                        principalTable: "QuestionOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AuditAttachments",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditAnswerId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    OriginalFileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    FileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ContentType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    UploadedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UploadedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditAttachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditAttachments_AuditAnswers_AuditAnswerId",
                        column: x => x.AuditAnswerId,
                        principalTable: "AuditAnswers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AuditCorrectionRequests",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    RequestedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    RequestReason = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RequestedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    ReviewedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    ReviewedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ManagerComments = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AuditId1 = table.Column<string>(type: "nvarchar(25)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditCorrectionRequests", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AuditLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EventTimestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ServerReceivedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    EntityType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    EntityId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ActionType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    OldValues = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NewValues = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Details = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IPAddress = table.Column<string>(type: "nvarchar(45)", maxLength: 45, nullable: true),
                    AppVersion = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Audits",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    AssignmentType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    AssignedToUserGroupId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    AssignedToUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    ScheduledDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DueDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    StartedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OverallStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FactoryId = table.Column<int>(type: "int", nullable: false),
                    AreaId = table.Column<int>(type: "int", nullable: false),
                    SubAreaId = table.Column<int>(type: "int", nullable: true),
                    OverallScore = table.Column<decimal>(type: "decimal(7,2)", nullable: true),
                    ManagerComments = table.Column<string>(type: "text", nullable: true),
                    ReviewedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    ReviewedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RecurringAuditSettingId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Audits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Audits_Areas_AreaId",
                        column: x => x.AreaId,
                        principalTable: "Areas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Audits_AuditTemplates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "AuditTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CorrectiveActions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    FindingId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    ActionDescription = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AssignedToUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    DueDate = table.Column<DateOnly>(type: "date", nullable: false),
                    CompletionDate = table.Column<DateOnly>(type: "date", nullable: true),
                    EvidenceNotes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CorrectiveActions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Factories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FactoryName = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: false),
                    FactoryProcess = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    LocationId = table.Column<int>(type: "int", nullable: false),
                    Factory_AddressLine1 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Factory_City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Factory_PostalCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    AddressLine1 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    PostalCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Factories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Factories_Location_LocationId",
                        column: x => x.LocationId,
                        principalTable: "Location",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    Username = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    FactoryId = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    LastLoginDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdObjectGuid = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    AdDistinguishedName = table.Column<string>(type: "text", nullable: true),
                    AdSyncLastDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Users_Factories_FactoryId",
                        column: x => x.FactoryId,
                        principalTable: "Factories",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Users_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Findings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AuditAnswerId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    FindingCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FindingDescription = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FindingSeverityLevel = table.Column<int>(type: "int", nullable: false),
                    RootCauseAnalysis = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ImmediateActionTaken = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    ReportedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    DueDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Findings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Findings_AuditAnswers_AuditAnswerId",
                        column: x => x.AuditAnswerId,
                        principalTable: "AuditAnswers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Findings_Users_ReportedByUserId",
                        column: x => x.ReportedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SubAreas",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SubAreaName = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: false),
                    AreaId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubAreas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubAreas_Areas_AreaId",
                        column: x => x.AreaId,
                        principalTable: "Areas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SubAreas_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SubAreas_Users_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserGroups",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    GroupName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    AdObjectGuid = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    IsAdSynced = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserGroups_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_UserGroups_Users_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RecurringAuditSettings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    SettingName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    DeadlineDays = table.Column<int>(type: "int", nullable: false, defaultValue: 7),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    AssignmentType = table.Column<int>(type: "int", nullable: false),
                    AssignToUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    AssignToUserGroupId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    NextGenerationDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LastGeneratedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecurringAuditSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RecurringAuditSettings_AuditTemplates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "AuditTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RecurringAuditSettings_UserGroups_AssignToUserGroupId",
                        column: x => x.AssignToUserGroupId,
                        principalTable: "UserGroups",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RecurringAuditSettings_Users_AssignToUserId",
                        column: x => x.AssignToUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserGroupMembers",
                columns: table => new
                {
                    UserGroupId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserGroupMembers", x => new { x.UserGroupId, x.UserId });
                    table.ForeignKey(
                        name: "FK_UserGroupMembers_UserGroups_UserGroupId",
                        column: x => x.UserGroupId,
                        principalTable: "UserGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserGroupMembers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RecurrenceRules",
                columns: table => new
                {
                    RecurringAuditSettingId = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    FrequencyType = table.Column<int>(type: "int", nullable: false),
                    Interval = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    EndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    WeeklyDaysOfWeekMask = table.Column<int>(type: "int", nullable: true),
                    MonthlyDayOfMonth = table.Column<int>(type: "int", nullable: true),
                    MonthlyNthWeek = table.Column<int>(type: "int", nullable: true),
                    MonthlyDayOfWeek = table.Column<int>(type: "int", nullable: true),
                    YearlyMonth = table.Column<int>(type: "int", nullable: true),
                    YearlyDayOfMonth = table.Column<int>(type: "int", nullable: true),
                    YearlyNthWeek = table.Column<int>(type: "int", nullable: true),
                    YearlyDayOfWeek = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecurrenceRules", x => x.RecurringAuditSettingId);
                    table.ForeignKey(
                        name: "FK_RecurrenceRules_RecurringAuditSettings_RecurringAuditSettingId",
                        column: x => x.RecurringAuditSettingId,
                        principalTable: "RecurringAuditSettings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "CreatedAt", "Description", "RoleName", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "System Administrator with full access", "Admin", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) },
                    { 2, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Manager with audit oversight and approval capabilities", "Manager", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) },
                    { 3, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Auditor who performs audit activities", "Auditor", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdGroupRoleMappings_RoleId",
                table: "AdGroupRoleMappings",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Areas_CreatedByUserId",
                table: "Areas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Areas_Factory_AreaName",
                table: "Areas",
                columns: new[] { "FactoryId", "AreaName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Areas_FactoryId",
                table: "Areas",
                column: "FactoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Areas_UpdatedByUserId",
                table: "Areas",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswerFailureReasons_AuditAnswerId",
                table: "AuditAnswerFailureReasons",
                column: "AuditAnswerId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswerFailureReasons_FailureReasonId",
                table: "AuditAnswerFailureReasons",
                column: "FailureReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswers_AuditId",
                table: "AuditAnswers",
                column: "AuditId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswers_AuditId_QuestionId",
                table: "AuditAnswers",
                columns: new[] { "AuditId", "QuestionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswers_QuestionId",
                table: "AuditAnswers",
                column: "QuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswers_SelectedOptionId",
                table: "AuditAnswers",
                column: "SelectedOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswerSelectedOptions_AuditAnswerId",
                table: "AuditAnswerSelectedOptions",
                column: "AuditAnswerId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAnswerSelectedOptions_QuestionOptionId",
                table: "AuditAnswerSelectedOptions",
                column: "QuestionOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAttachments_AuditAnswerId",
                table: "AuditAttachments",
                column: "AuditAnswerId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAttachments_FileName",
                table: "AuditAttachments",
                column: "FileName");

            migrationBuilder.CreateIndex(
                name: "IX_AuditAttachments_UploadedByUserId",
                table: "AuditAttachments",
                column: "UploadedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_AuditId",
                table: "AuditCorrectionRequests",
                column: "AuditId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_AuditId1",
                table: "AuditCorrectionRequests",
                column: "AuditId1");

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_RequestedAt",
                table: "AuditCorrectionRequests",
                column: "RequestedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_RequestedByUserId",
                table: "AuditCorrectionRequests",
                column: "RequestedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_ReviewedByUserId",
                table: "AuditCorrectionRequests",
                column: "ReviewedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditCorrectionRequests_Status",
                table: "AuditCorrectionRequests",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId",
                table: "AuditLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_AreaId",
                table: "Audits",
                column: "AreaId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_AssignedToUserGroupId",
                table: "Audits",
                column: "AssignedToUserGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_AssignedToUserId",
                table: "Audits",
                column: "AssignedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_AuditTemplateId",
                table: "Audits",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_DueDate",
                table: "Audits",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_FactoryId",
                table: "Audits",
                column: "FactoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_OverallStatus",
                table: "Audits",
                column: "OverallStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_RecurringAuditSettingId",
                table: "Audits",
                column: "RecurringAuditSettingId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_ReviewedByUserId",
                table: "Audits",
                column: "ReviewedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_ScheduledDate",
                table: "Audits",
                column: "ScheduledDate");

            migrationBuilder.CreateIndex(
                name: "IX_Audits_SubAreaId",
                table: "Audits",
                column: "SubAreaId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditTemplates_IsActive",
                table: "AuditTemplates",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_AuditTemplates_IsPublished",
                table: "AuditTemplates",
                column: "IsPublished");

            migrationBuilder.CreateIndex(
                name: "IX_AuditTemplates_TemplateName",
                table: "AuditTemplates",
                column: "TemplateName");

            migrationBuilder.CreateIndex(
                name: "IX_AuditTemplates_TemplateName_Version",
                table: "AuditTemplates",
                columns: new[] { "TemplateName", "Version" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CorrectiveActions_AssignedToUserId",
                table: "CorrectiveActions",
                column: "AssignedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CorrectiveActions_CompletionDate",
                table: "CorrectiveActions",
                column: "CompletionDate");

            migrationBuilder.CreateIndex(
                name: "IX_CorrectiveActions_DueDate",
                table: "CorrectiveActions",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_CorrectiveActions_FindingId",
                table: "CorrectiveActions",
                column: "FindingId");

            migrationBuilder.CreateIndex(
                name: "IX_CorrectiveActions_Status",
                table: "CorrectiveActions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Factories_CreatedByUserId",
                table: "Factories",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Factories_LocationId",
                table: "Factories",
                column: "LocationId");

            migrationBuilder.CreateIndex(
                name: "IX_Factories_UpdatedByUserId",
                table: "Factories",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_AuditAnswerId",
                table: "Findings",
                column: "AuditAnswerId");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_DueDate",
                table: "Findings",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_FindingCode",
                table: "Findings",
                column: "FindingCode");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_FindingSeverityLevel",
                table: "Findings",
                column: "FindingSeverityLevel");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_ReportedByUserId",
                table: "Findings",
                column: "ReportedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_Status",
                table: "Findings",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Location_LocationCountryCode",
                table: "Location",
                column: "LocationCountryCode",
                unique: true,
                filter: "[LocationCountryCode] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Location_LocationName",
                table: "Location",
                column: "LocationName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_QuestionGroups_AuditTemplateId",
                table: "QuestionGroups",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionGroups_AuditTemplateId_DisplayOrder",
                table: "QuestionGroups",
                columns: new[] { "AuditTemplateId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_QuestionGroups_GroupName",
                table: "QuestionGroups",
                column: "GroupName");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionOptions_OptionText",
                table: "QuestionOptions",
                column: "OptionText");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionOptions_QuestionId",
                table: "QuestionOptions",
                column: "QuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionOptions_QuestionId_DisplayOrder",
                table: "QuestionOptions",
                columns: new[] { "QuestionId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_Questions_AuditTemplateId",
                table: "Questions",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_Questions_AuditTemplateId_DisplayOrder",
                table: "Questions",
                columns: new[] { "AuditTemplateId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_Questions_ParentQuestionId",
                table: "Questions",
                column: "ParentQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_Questions_QuestionGroupId",
                table: "Questions",
                column: "QuestionGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Questions_QuestionType",
                table: "Questions",
                column: "QuestionType");

            migrationBuilder.CreateIndex(
                name: "IX_RecurrenceRules_EndDate",
                table: "RecurrenceRules",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_RecurrenceRules_FrequencyType",
                table: "RecurrenceRules",
                column: "FrequencyType");

            migrationBuilder.CreateIndex(
                name: "IX_RecurrenceRules_StartDate",
                table: "RecurrenceRules",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_AssignToUserGroupId",
                table: "RecurringAuditSettings",
                column: "AssignToUserGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_AssignToUserId",
                table: "RecurringAuditSettings",
                column: "AssignToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_AuditTemplateId",
                table: "RecurringAuditSettings",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_IsEnabled",
                table: "RecurringAuditSettings",
                column: "IsEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_NextGenerationDate",
                table: "RecurringAuditSettings",
                column: "NextGenerationDate");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleName",
                table: "Roles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubAreas_Area_SubAreaName",
                table: "SubAreas",
                columns: new[] { "AreaId", "SubAreaName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubAreas_AreaId",
                table: "SubAreas",
                column: "AreaId");

            migrationBuilder.CreateIndex(
                name: "IX_SubAreas_CreatedByUserId",
                table: "SubAreas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubAreas_UpdatedByUserId",
                table: "SubAreas",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMembers_UserGroupId",
                table: "UserGroupMembers",
                column: "UserGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMembers_UserId",
                table: "UserGroupMembers",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroups_AdObjectGuid",
                table: "UserGroups",
                column: "AdObjectGuid",
                unique: true,
                filter: "[AdObjectGuid] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroups_CreatedByUserId",
                table: "UserGroups",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroups_GroupName",
                table: "UserGroups",
                column: "GroupName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserGroups_UpdatedByUserId",
                table: "UserGroups",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_AdObjectGuid",
                table: "Users",
                column: "AdObjectGuid",
                unique: true,
                filter: "[AdObjectGuid] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Users",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_FactoryId",
                table: "Users",
                column: "FactoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Users",
                column: "Username",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Factories_FactoryId",
                table: "Areas",
                column: "FactoryId",
                principalTable: "Factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Users_CreatedByUserId",
                table: "Areas",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Users_UpdatedByUserId",
                table: "Areas",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerFailureReasons_AuditAnswers_AuditAnswerId",
                table: "AuditAnswerFailureReasons",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_Audits_AuditId",
                table: "AuditAnswers",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAttachments_Users_UploadedByUserId",
                table: "AuditAttachments",
                column: "UploadedByUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId",
                table: "AuditCorrectionRequests",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId1",
                table: "AuditCorrectionRequests",
                column: "AuditId1",
                principalTable: "Audits",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Users_RequestedByUserId",
                table: "AuditCorrectionRequests",
                column: "RequestedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Users_ReviewedByUserId",
                table: "AuditCorrectionRequests",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditLogs_Users_UserId",
                table: "AuditLogs",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Factories_FactoryId",
                table: "Audits",
                column: "FactoryId",
                principalTable: "Factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_RecurringAuditSettings_RecurringAuditSettingId",
                table: "Audits",
                column: "RecurringAuditSettingId",
                principalTable: "RecurringAuditSettings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_SubAreas_SubAreaId",
                table: "Audits",
                column: "SubAreaId",
                principalTable: "SubAreas",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_UserGroups_AssignedToUserGroupId",
                table: "Audits",
                column: "AssignedToUserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Users_AssignedToUserId",
                table: "Audits",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Users_ReviewedByUserId",
                table: "Audits",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CorrectiveActions_Findings_FindingId",
                table: "CorrectiveActions",
                column: "FindingId",
                principalTable: "Findings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CorrectiveActions_Users_AssignedToUserId",
                table: "CorrectiveActions",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Factories_Users_CreatedByUserId",
                table: "Factories",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Factories_Users_UpdatedByUserId",
                table: "Factories",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Roles_RoleId",
                table: "Users");

            migrationBuilder.DropForeignKey(
                name: "FK_Users_Factories_FactoryId",
                table: "Users");

            migrationBuilder.DropTable(
                name: "AdGroupRoleMappings");

            migrationBuilder.DropTable(
                name: "AuditAnswerFailureReasons");

            migrationBuilder.DropTable(
                name: "AuditAnswerSelectedOptions");

            migrationBuilder.DropTable(
                name: "AuditAttachments");

            migrationBuilder.DropTable(
                name: "AuditCorrectionRequests");

            migrationBuilder.DropTable(
                name: "AuditLogs");

            migrationBuilder.DropTable(
                name: "CorrectiveActions");

            migrationBuilder.DropTable(
                name: "RecurrenceRules");

            migrationBuilder.DropTable(
                name: "UserGroupMembers");

            migrationBuilder.DropTable(
                name: "Findings");

            migrationBuilder.DropTable(
                name: "AuditAnswers");

            migrationBuilder.DropTable(
                name: "Audits");

            migrationBuilder.DropTable(
                name: "QuestionOptions");

            migrationBuilder.DropTable(
                name: "RecurringAuditSettings");

            migrationBuilder.DropTable(
                name: "SubAreas");

            migrationBuilder.DropTable(
                name: "Questions");

            migrationBuilder.DropTable(
                name: "UserGroups");

            migrationBuilder.DropTable(
                name: "Areas");

            migrationBuilder.DropTable(
                name: "QuestionGroups");

            migrationBuilder.DropTable(
                name: "AuditTemplates");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Factories");

            migrationBuilder.DropTable(
                name: "Location");

            migrationBuilder.DropTable(
                name: "Users");
        }
    }
}
