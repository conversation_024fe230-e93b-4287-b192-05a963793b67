using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.DTOs;

namespace HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSetting;

/// <summary>
/// Query to get a single recurring audit setting by ID
/// </summary>
public class GetRecurringAuditSettingQuery : BaseQuery<RecurringAuditSettingDto>
{
    /// <summary>
    /// The ID of the recurring audit setting to retrieve
    /// </summary>
    public string Id { get; set; } = string.Empty;

    public GetRecurringAuditSettingQuery(string id)
    {
        Id = id;
    }
}
