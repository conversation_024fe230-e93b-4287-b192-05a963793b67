using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;

/// <summary>
/// Handler for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandHandler : BaseCommandHandler<PublishTemplateCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public PublishTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(PublishTemplateCommand request, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .Include(t => t.Questions)
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId, cancellationToken);

        if (template == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.AuditTemplate), request.AuditTemplateId);
        }

        // Use domain method to validate and publish
        if (!template.TryPublish(_currentUserService.UserId))
        {
            var validationErrors = template.GetPublishValidationErrors();
            var errorMessage = validationErrors.Count == 1
                ? validationErrors[0]
                : $"Cannot publish template: {string.Join("; ", validationErrors)}";

            throw new InvalidOperationException(errorMessage);
        }

        await _context.SaveChangesAsync(cancellationToken);
    }
}
