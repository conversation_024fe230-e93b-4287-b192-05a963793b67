﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class MakeEmailOptionalAndNonUnique : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId1",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropIndex(
                name: "IX_hwsap_wfl_audit_correction_requests_AuditId1",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropIndex(
                name: "IX_Users_Email",
                table: "hwsap_usr_users");

            migrationBuilder.DropColumn(
                name: "AuditId1",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "hwsap_usr_users",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.AddColumn<bool>(
                name: "Address_HasValue",
                table: "hwsap_org_factories",
                type: "bit",
                nullable: true,
                computedColumnSql: "CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN 1 ELSE 0 END",
                stored: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Address_HasValue",
                table: "hwsap_org_factories");

            migrationBuilder.AddColumn<string>(
                name: "AuditId1",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(25)",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "hwsap_usr_users",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_wfl_audit_correction_requests_AuditId1",
                table: "hwsap_wfl_audit_correction_requests",
                column: "AuditId1");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "hwsap_usr_users",
                column: "Email",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId1",
                table: "hwsap_wfl_audit_correction_requests",
                column: "AuditId1",
                principalTable: "hwsap_aud_audits",
                principalColumn: "Id");
        }
    }
}
