@if (IsVisible)
{
    <div class="empty-state-container @ContainerClass">
        <div class="empty-state-content text-center">
            <!-- Icon -->
            <div class="empty-state-icon mb-4">
                @if (!string.IsNullOrEmpty(IconClass))
                {
                    <i class="@IconClass"></i>
                }
                else
                {
                    <i class="fas fa-inbox"></i>
                }
            </div>

            <!-- Title -->
            <h3 class="empty-state-title mb-3">
                @if (!string.IsNullOrEmpty(Title))
                {
                    @Title
                }
                else
                {
                    <span>No Items Found</span>
                }
            </h3>

            <!-- Description -->
            @if (!string.IsNullOrEmpty(Description))
            {
                <p class="empty-state-description text-muted mb-4">
                    @Description
                </p>
            }

            <!-- Action Button -->
            @if (!string.IsNullOrEmpty(ActionText) && OnActionClick.HasDelegate)
            {
                <button class="btn btn-primary" @onclick="HandleActionClick">
                    @if (!string.IsNullOrEmpty(ActionIcon))
                    {
                        <i class="@ActionIcon me-2"></i>
                    }
                    @ActionText
                </button>
            }

            <!-- Secondary Action -->
            @if (!string.IsNullOrEmpty(SecondaryActionText) && OnSecondaryActionClick.HasDelegate)
            {
                <button class="btn btn-outline-secondary ms-2" @onclick="HandleSecondaryActionClick">
                    @if (!string.IsNullOrEmpty(SecondaryActionIcon))
                    {
                        <i class="@SecondaryActionIcon me-2"></i>
                    }
                    @SecondaryActionText
                </button>
            }

            <!-- Custom Content -->
            @if (ChildContent != null)
            {
                <div class="empty-state-custom mt-4">
                    @ChildContent
                </div>
            }
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public string Title { get; set; } = string.Empty;
    [Parameter] public string Description { get; set; } = string.Empty;
    [Parameter] public string IconClass { get; set; } = string.Empty;
    [Parameter] public string ActionText { get; set; } = string.Empty;
    [Parameter] public string ActionIcon { get; set; } = string.Empty;
    [Parameter] public string SecondaryActionText { get; set; } = string.Empty;
    [Parameter] public string SecondaryActionIcon { get; set; } = string.Empty;
    [Parameter] public string ContainerClass { get; set; } = string.Empty;
    [Parameter] public EventCallback OnActionClick { get; set; }
    [Parameter] public EventCallback OnSecondaryActionClick { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }

    private async Task HandleActionClick()
    {
        if (OnActionClick.HasDelegate)
        {
            await OnActionClick.InvokeAsync();
        }
    }

    private async Task HandleSecondaryActionClick()
    {
        if (OnSecondaryActionClick.HasDelegate)
        {
            await OnSecondaryActionClick.InvokeAsync();
        }
    }
}

<style>
    .empty-state-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 300px;
        padding: 2rem;
    }

    .empty-state-content {
        max-width: 400px;
        width: 100%;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: var(--industrial-text-muted);
        opacity: 0.7;
    }

    .empty-state-title {
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 1.5rem;
    }

    .empty-state-description {
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 350px;
        margin: 0 auto;
    }

    .empty-state-custom {
        color: var(--industrial-text);
    }

    /* Variations */
    .empty-state-container.compact {
        min-height: 200px;
        padding: 1rem;
    }

    .empty-state-container.compact .empty-state-icon {
        font-size: 3rem;
        margin-bottom: 1rem !important;
    }

    .empty-state-container.compact .empty-state-title {
        font-size: 1.25rem;
        margin-bottom: 1rem !important;
    }

    .empty-state-container.compact .empty-state-description {
        font-size: 1rem;
        margin-bottom: 1.5rem !important;
    }

    .empty-state-container.large {
        min-height: 400px;
        padding: 3rem;
    }

    .empty-state-container.large .empty-state-icon {
        font-size: 5rem;
    }

    .empty-state-container.large .empty-state-title {
        font-size: 2rem;
    }

    .empty-state-container.large .empty-state-description {
        font-size: 1.2rem;
    }

    /* Mobile optimizations */
    @@media (max-width: 767px) {
        .empty-state-container {
            min-height: 250px;
            padding: 1.5rem;
        }
        
        .empty-state-icon {
            font-size: 3rem;
        }
        
        .empty-state-title {
            font-size: 1.25rem;
        }
        
        .empty-state-description {
            font-size: 1rem;
        }
        
        .btn {
            width: 100%;
            margin-bottom: 0.5rem;
        }
        
        .btn.ms-2 {
            margin-left: 0 !important;
        }
    }

    /* Tablet optimizations */
    @@media (min-width: 768px) and (max-width: 1024px) {
        .empty-state-container {
            min-height: 350px;
            padding: 2.5rem;
        }
        
        .empty-state-icon {
            font-size: 4.5rem;
        }
        
        .empty-state-title {
            font-size: 1.75rem;
        }
        
        .empty-state-description {
            font-size: 1.15rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
        }
    }

    /* Animation */
    .empty-state-content {
        animation: fadeInUp 0.6s ease-out;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
