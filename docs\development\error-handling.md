# Error Handling in HWS Audit Platform

## Overview

The HWS Audit Platform now includes a comprehensive error handling system with a styled error page that adapts based on the environment (Development vs Production).

## Features

### 🎨 Industrial-Themed Design
- Matches the existing HWS Audit Platform industrial theme
- Dark color scheme with orange accents
- Professional error presentation
- Responsive design for all screen sizes

### 🔧 Debug Mode (Development Environment)
When running in Development mode (`ASPNETCORE_ENVIRONMENT=Development`), the error page displays:

- **Exception Details**
  - Exception type and message
  - Full stack trace
  - Inner exception information (if present)

- **Request Information**
  - Request ID for tracking
  - Request path and query string
  - HTTP method
  - User agent
  - Timestamp

- **Enhanced Logging**
  - Automatic error logging with context
  - Request correlation for debugging

### 🛡️ Production Mode
When running in Production mode, the error page shows:

- **User-Friendly Messages**
  - Professional error message
  - No sensitive technical details
  - Clear next steps for users

- **Error Reference**
  - Request ID for support purposes
  - Contact information guidance

## Usage

### Accessing the Error Page
The error page is automatically triggered when:
- Unhandled exceptions occur in the application
- Users navigate to `/Error` directly
- The global exception handler redirects to the error page

### Testing Error Handling
In Development mode, you can test the error handling using the **Test Errors** page:

1. Navigate to `/test-error` or use the "Test Errors" link in the navigation menu (only visible in Development mode)
2. Choose from two testing methods:
   - **Direct Navigation**: Go directly to the error page to see styling and layout
   - **Interactive Demo**: Use `/test-error-display` to see the error page with different simulated exceptions
   - **Real Exceptions**: Test actual exception throwing (may break SignalR connection)

### Environment Configuration
The error page behavior is controlled by the `ASPNETCORE_ENVIRONMENT` variable:

```bash
# Development mode (shows debug information)
ASPNETCORE_ENVIRONMENT=Development

# Production mode (shows user-friendly messages)
ASPNETCORE_ENVIRONMENT=Production
```

## Error Page Components

### Files Modified/Created
- `src\HWSAuditPlatform.Web\Components\Pages\Error.razor` - Main error page component with `@rendermode InteractiveServer`
- `src\HWSAuditPlatform.Web\Components\Pages\Error.razor.css` - Industrial-themed styling
- `src\HWSAuditPlatform.Web\Components\Pages\TestError.razor` - Error testing page (Development only) with `@rendermode InteractiveServer`
- `src\HWSAuditPlatform.Web\Components\Pages\TestErrorDisplay.razor` - Interactive error demo page with simulated exceptions
- `src\HWSAuditPlatform.Web\Components\Layout\NavMenu.razor` - Added test error link for Development

### Key Features
- **Environment Detection**: Uses `IWebHostEnvironment.IsDevelopment()` to determine display mode
- **Exception Handling**: Captures and displays detailed exception information
- **Request Tracking**: Provides request IDs for error correlation
- **Responsive Design**: Works on desktop and mobile devices
- **Industrial Styling**: Matches the existing application theme
- **Interactive Components**: Uses `@rendermode InteractiveServer` for button functionality
- **Authentication Support**: Includes `CascadingAuthenticationState` for interactive components

## Action Buttons

The error page includes several action buttons:

1. **Return to Dashboard** - Takes users back to the main dashboard
2. **Go Back** - Uses browser history to return to the previous page
3. **Retry** - Reloads the current page to attempt the operation again

## Logging

All errors are automatically logged with:
- Exception details
- Request ID for correlation
- Request path information
- Timestamp

This helps with debugging and monitoring application health.

## Customization

### Styling
The error page uses CSS custom properties from the main application theme:
- `--industrial-dark`, `--industrial-darker`, `--industrial-gray`
- `--industrial-orange`, `--industrial-red`, `--industrial-blue`
- `--industrial-text`, `--industrial-text-muted`

### Messages
You can customize error messages by modifying the `Error.razor` component:
- Production mode messages in the `production-section`
- Debug information display in the `debug-section`

## Security Considerations

- **Production Safety**: Sensitive information (stack traces, internal paths) is only shown in Development mode
- **Error Logging**: All errors are logged for monitoring without exposing details to users
- **Request Tracking**: Request IDs allow support teams to correlate user reports with server logs

## Browser Compatibility

The error page is compatible with:
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers
- Responsive design adapts to different screen sizes
- Uses standard web technologies (HTML, CSS, JavaScript)
