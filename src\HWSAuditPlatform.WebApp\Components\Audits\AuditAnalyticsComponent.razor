@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditApiService
@inject ILogger<AuditAnalyticsComponent> Logger

<div class="audit-analytics">
    <div class="page-header">
        <h1>Audit Analytics</h1>
        <p class="text-muted">Comprehensive audit performance insights</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading analytics data..." />

    @if (!isLoading)
    {
        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="analytics-card">
                    <div class="analytics-icon bg-primary">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="analytics-content">
                        <h3>@averageScore.ToString("F1")%</h3>
                        <p>Average Score</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="analytics-card">
                    <div class="analytics-icon bg-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="analytics-content">
                        <h3>@completionRate.ToString("F1")%</h3>
                        <p>Completion Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="analytics-card">
                    <div class="analytics-icon bg-warning">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="analytics-content">
                        <h3>@averageDaysToComplete.ToString("F1")</h3>
                        <p>Avg Days to Complete</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="analytics-card">
                    <div class="analytics-icon bg-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="analytics-content">
                        <h3>@overduePercentage.ToString("F1")%</h3>
                        <p>Overdue Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Detailed Analytics -->
        <div class="row">
            <div class="col-md-6">
                <div class="analytics-chart-card">
                    <div class="card-header">
                        <h5>Audit Status Distribution</h5>
                    </div>
                    <div class="card-body">
                        <div class="status-distribution">
                            @foreach (var status in statusDistribution)
                            {
                                <div class="status-item">
                                    <div class="status-label">
                                        <span class="badge @GetStatusBadgeClass(status.Key)">@status.Key</span>
                                        <span class="status-count">@status.Value</span>
                                    </div>
                                    <div class="status-bar">
                                        <div class="status-fill @GetStatusBadgeClass(status.Key)" 
                                             style="width: @(totalAudits > 0 ? (status.Value * 100.0 / totalAudits).ToString("F1") : "0")%"></div>
                                    </div>
                                    <span class="status-percentage">@(totalAudits > 0 ? (status.Value * 100.0 / totalAudits).ToString("F1") : "0")%</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="analytics-chart-card">
                    <div class="card-header">
                        <h5>Score Distribution</h5>
                    </div>
                    <div class="card-body">
                        <div class="score-distribution">
                            <div class="score-range">
                                <span class="score-label">90-100%</span>
                                <div class="score-bar">
                                    <div class="score-fill bg-success" style="width: @excellentPercentage.ToString("F1")%"></div>
                                </div>
                                <span class="score-count">@excellentCount</span>
                            </div>
                            <div class="score-range">
                                <span class="score-label">80-89%</span>
                                <div class="score-bar">
                                    <div class="score-fill bg-primary" style="width: @goodPercentage.ToString("F1")%"></div>
                                </div>
                                <span class="score-count">@goodCount</span>
                            </div>
                            <div class="score-range">
                                <span class="score-label">70-79%</span>
                                <div class="score-bar">
                                    <div class="score-fill bg-warning" style="width: @fairPercentage.ToString("F1")%"></div>
                                </div>
                                <span class="score-count">@fairCount</span>
                            </div>
                            <div class="score-range">
                                <span class="score-label">Below 70%</span>
                                <div class="score-bar">
                                    <div class="score-fill bg-danger" style="width: @poorPercentage.ToString("F1")%"></div>
                                </div>
                                <span class="score-count">@poorCount</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Trends -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="analytics-chart-card">
                    <div class="card-header">
                        <h5>Recent Audit Trends</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">Advanced charting functionality will be implemented with a charting library</p>
                            <small class="text-muted">Consider integrating Chart.js or similar for detailed trend analysis</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .audit-analytics {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .analytics-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .analytics-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .analytics-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 24px;
        color: white;
    }

    .analytics-content h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: 700;
        color: #495057;
    }

    .analytics-content p {
        margin: 5px 0 0 0;
        color: #6c757d;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .analytics-chart-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .analytics-chart-card .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 12px 12px 0 0;
        padding: 15px 20px;
    }

    .analytics-chart-card .card-header h5 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .status-distribution, .score-distribution {
        padding: 10px 0;
    }

    .status-item, .score-range {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        gap: 15px;
    }

    .status-label {
        min-width: 120px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .score-label {
        min-width: 80px;
        font-weight: 600;
        color: #495057;
    }

    .status-bar, .score-bar {
        flex: 1;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .status-fill, .score-fill {
        height: 100%;
        transition: width 0.3s ease;
    }

    .status-percentage, .score-count {
        min-width: 50px;
        text-align: right;
        font-weight: 600;
        color: #495057;
    }

    .status-count {
        background: #f8f9fa;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85rem;
    }
</style>

@code {
    private bool isLoading = true;
    private int totalAudits = 0;
    private decimal averageScore = 0;
    private decimal completionRate = 0;
    private decimal averageDaysToComplete = 0;
    private decimal overduePercentage = 0;
    
    private Dictionary<AuditOverallStatus, int> statusDistribution = new();
    
    private int excellentCount = 0;
    private int goodCount = 0;
    private int fairCount = 0;
    private int poorCount = 0;
    
    private decimal excellentPercentage = 0;
    private decimal goodPercentage = 0;
    private decimal fairPercentage = 0;
    private decimal poorPercentage = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadAnalyticsData();
    }

    private async Task LoadAnalyticsData()
    {
        try
        {
            isLoading = true;
            
            var audits = await AuditApiService.GetAuditsAsync();
            if (audits != null)
            {
                var auditList = audits.ToList();
                totalAudits = auditList.Count;
                
                if (totalAudits > 0)
                {
                    // Calculate average score
                    var auditsWithScores = auditList.Where(a => a.OverallScore.HasValue).ToList();
                    if (auditsWithScores.Any())
                    {
                        averageScore = auditsWithScores.Average(a => a.OverallScore!.Value);
                    }
                    
                    // Calculate completion rate
                    var completedAudits = auditList.Count(a => a.OverallStatus == AuditOverallStatus.Closed);
                    completionRate = (decimal)completedAudits / totalAudits * 100;
                    
                    // Calculate average days to complete
                    var completedAuditsWithDates = auditList
                        .Where(a => a.OverallStatus == AuditOverallStatus.Closed && a.CompletedAt.HasValue)
                        .ToList();
                    if (completedAuditsWithDates.Any())
                    {
                        averageDaysToComplete = (decimal)completedAuditsWithDates
                            .Average(a => (a.CompletedAt!.Value - a.ScheduledDate).TotalDays);
                    }
                    
                    // Calculate overdue percentage
                    var overdueAudits = auditList.Count(a => 
                        a.DueDate.HasValue && 
                        a.DueDate.Value < DateTime.Now && 
                        a.OverallStatus != AuditOverallStatus.Closed);
                    overduePercentage = (decimal)overdueAudits / totalAudits * 100;
                    
                    // Status distribution
                    statusDistribution = auditList
                        .GroupBy(a => a.OverallStatus)
                        .ToDictionary(g => g.Key, g => g.Count());
                    
                    // Score distribution
                    if (auditsWithScores.Any())
                    {
                        excellentCount = auditsWithScores.Count(a => a.OverallScore >= 90);
                        goodCount = auditsWithScores.Count(a => a.OverallScore >= 80 && a.OverallScore < 90);
                        fairCount = auditsWithScores.Count(a => a.OverallScore >= 70 && a.OverallScore < 80);
                        poorCount = auditsWithScores.Count(a => a.OverallScore < 70);
                        
                        var totalWithScores = auditsWithScores.Count;
                        excellentPercentage = (decimal)excellentCount / totalWithScores * 100;
                        goodPercentage = (decimal)goodCount / totalWithScores * 100;
                        fairPercentage = (decimal)fairCount / totalWithScores * 100;
                        poorPercentage = (decimal)poorCount / totalWithScores * 100;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading analytics data");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }
}
