using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Infrastructure.Services.FileStorage;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for file upload and download operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class FilesController : BaseController
{
    private readonly IFileStorageService _fileStorageService;
    private static readonly string[] AllowedContentTypes = 
    {
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp",
        "application/pdf", "application/msword", 
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "text/plain", "text/csv"
    };

    private const long MaxFileSize = 10 * 1024 * 1024; // 10MB

    public FilesController(
        IMediator mediator, 
        ILogger<FilesController> logger,
        IFileStorageService fileStorageService) 
        : base(mediator, logger)
    {
        _fileStorageService = fileStorageService;
    }

    /// <summary>
    /// Upload a file
    /// </summary>
    /// <param name="file">File to upload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File upload result</returns>
    [HttpPost("upload")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(FileUploadResult), 200)]
    [ProducesResponseType(400)]
    [RequestSizeLimit(MaxFileSize)]
    public async Task<ActionResult<FileUploadResult>> UploadFile(
        IFormFile file,
        CancellationToken cancellationToken)
    {
        // Validate file
        if (file == null || file.Length == 0)
        {
            Logger.LogWarning("File upload attempted with no file");
            return BadRequest("No file provided");
        }

        if (file.Length > MaxFileSize)
        {
            Logger.LogWarning("File upload attempted with file size {FileSize} exceeding limit {MaxSize}", 
                file.Length, MaxFileSize);
            return BadRequest($"File size exceeds maximum allowed size of {MaxFileSize / (1024 * 1024)}MB");
        }

        if (!AllowedContentTypes.Contains(file.ContentType.ToLowerInvariant()))
        {
            Logger.LogWarning("File upload attempted with disallowed content type: {ContentType}", file.ContentType);
            return BadRequest($"File type '{file.ContentType}' is not allowed");
        }

        // Validate file extension matches content type
        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!IsValidFileExtension(extension, file.ContentType))
        {
            Logger.LogWarning("File extension {Extension} does not match content type {ContentType}", 
                extension, file.ContentType);
            return BadRequest("File extension does not match content type");
        }

        try
        {
            Logger.LogInformation("Uploading file: {FileName}, Size: {FileSize}, ContentType: {ContentType}", 
                file.FileName, file.Length, file.ContentType);

            using var stream = file.OpenReadStream();
            var uploadedFile = await _fileStorageService.UploadFileAsync(
                file.FileName,
                file.ContentType,
                stream,
                cancellationToken);

            var result = new FileUploadResult
            {
                FilePath = uploadedFile.StoragePath,
                FileName = uploadedFile.FileName,
                ContentType = uploadedFile.ContentType,
                Size = uploadedFile.Size
            };

            Logger.LogInformation("File uploaded successfully: {FilePath}", uploadedFile.StoragePath);
            return Success(result, "File uploaded successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error uploading file: {FileName}", file.FileName);
            throw;
        }
    }

    /// <summary>
    /// Download a file
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File content</returns>
    [HttpGet("download/{*filePath}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(FileResult), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DownloadFile(string filePath, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(filePath))
        {
            return BadRequest("File path is required");
        }

        try
        {
            Logger.LogInformation("Downloading file: {FilePath}", filePath);

            if (!await _fileStorageService.FileExistsAsync(filePath, cancellationToken))
            {
                Logger.LogWarning("File not found: {FilePath}", filePath);
                return NotFound("File not found");
            }

            var stream = await _fileStorageService.GetFileAsync(filePath, cancellationToken);
            var fileName = Path.GetFileName(filePath);
            var contentType = GetContentType(fileName);

            Logger.LogInformation("File downloaded successfully: {FilePath}", filePath);
            return File(stream, contentType, fileName);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error downloading file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Get a temporary download URL for a file
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="expiryHours">URL expiry time in hours (default: 1)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Temporary download URL</returns>
    [HttpGet("download-url/{*filePath}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(string), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<string>> GetDownloadUrl(
        string filePath, 
        [FromQuery] int expiryHours = 1,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(filePath))
        {
            return BadRequest("File path is required");
        }

        if (expiryHours < 1 || expiryHours > 24)
        {
            return BadRequest("Expiry hours must be between 1 and 24");
        }

        try
        {
            Logger.LogInformation("Generating download URL for file: {FilePath}", filePath);

            if (!await _fileStorageService.FileExistsAsync(filePath, cancellationToken))
            {
                Logger.LogWarning("File not found for URL generation: {FilePath}", filePath);
                return NotFound("File not found");
            }

            var url = await _fileStorageService.GenerateDownloadUrlAsync(
                filePath,
                TimeSpan.FromHours(expiryHours),
                cancellationToken);

            Logger.LogInformation("Download URL generated successfully for file: {FilePath}", filePath);
            return Success(url, "Download URL generated successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating download URL for file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Delete a file
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpDelete("{*filePath}")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteFile(string filePath, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(filePath))
        {
            return BadRequest("File path is required");
        }

        try
        {
            Logger.LogInformation("Deleting file: {FilePath}", filePath);

            if (!await _fileStorageService.FileExistsAsync(filePath, cancellationToken))
            {
                Logger.LogWarning("File not found for deletion: {FilePath}", filePath);
                return NotFound("File not found");
            }

            await _fileStorageService.DeleteFileAsync(filePath, cancellationToken);

            Logger.LogInformation("File deleted successfully: {FilePath}", filePath);
            return NoContentSuccess("File deleted successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
            throw;
        }
    }

    private static bool IsValidFileExtension(string extension, string contentType)
    {
        return extension switch
        {
            ".jpg" or ".jpeg" => contentType.StartsWith("image/jpeg") || contentType.StartsWith("image/jpg"),
            ".png" => contentType.StartsWith("image/png"),
            ".gif" => contentType.StartsWith("image/gif"),
            ".bmp" => contentType.StartsWith("image/bmp"),
            ".pdf" => contentType == "application/pdf",
            ".doc" => contentType == "application/msword",
            ".docx" => contentType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => contentType == "application/vnd.ms-excel",
            ".xlsx" => contentType == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".txt" => contentType == "text/plain",
            ".csv" => contentType == "text/csv",
            _ => false
        };
    }

    private static string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".txt" => "text/plain",
            ".csv" => "text/csv",
            _ => "application/octet-stream"
        };
    }
}
