using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAudit;

/// <summary>
/// Handler for GetAuditQuery
/// </summary>
public class GetAuditQueryHandler : BaseQueryHandler<GetAuditQuery, AuditDto>
{
    private readonly IApplicationDbContext _context;

    public GetAuditQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<AuditDto> Handle(GetAuditQuery request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .Include(a => a.AuditTemplate)
                .ThenInclude(t => t.QuestionGroups.Where(g => g.IsActive))
                .ThenInclude(g => g.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(a => a.AuditTemplate)
                .ThenInclude(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(a => a.AuditTemplate)
                .ThenInclude(t => t.Questions.Where(q => q.IsActive))
                .ThenInclude(q => q.AllowedEvidenceTypes.Where(aet => aet.IsActive))
            .Include(a => a.AssignedToUser)
            .Include(a => a.AssignedToUserGroup)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .Include(a => a.RecurringAuditSetting)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.Id);
        }

        return new AuditDto
        {
            Id = audit.Id,
            AuditTemplateId = audit.AuditTemplateId,
            AuditTemplateName = audit.AuditTemplate.TemplateName,
            AssignmentType = audit.AssignmentType,
            AssignedToUserGroupId = audit.AssignedToUserGroupId,
            AssignedToUserGroupName = audit.AssignedToUserGroup?.GroupName,
            AssignedToUserId = audit.AssignedToUserId,
            AssignedToUserName = audit.AssignedToUser != null 
                ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() 
                : null,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryId = audit.FactoryId,
            FactoryName = audit.Factory.FactoryName,
            AreaId = audit.AreaId,
            AreaName = audit.Area.AreaName,
            SubAreaId = audit.SubAreaId,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserId = audit.ReviewedByUserId,
            ReviewedByUserName = audit.ReviewedByUser != null 
                ? $"{audit.ReviewedByUser.FirstName} {audit.ReviewedByUser.LastName}".Trim() 
                : null,
            ReviewedAt = audit.ReviewedAt,
            RecurringAuditSettingId = audit.RecurringAuditSettingId,
            CreatedAt = audit.CreatedAt,
            UpdatedAt = audit.UpdatedAt,
            CreatedByUserId = audit.CreatedByUserId,
            UpdatedByUserId = audit.UpdatedByUserId,
            RecordVersion = audit.RecordVersion,

            // Template questions and options
            QuestionGroups = audit.AuditTemplate.QuestionGroups
                .OrderBy(g => g.DisplayOrder)
                .Select(g => new QuestionGroupDto
                {
                    Id = g.Id,
                    AuditTemplateId = g.AuditTemplateId,
                    GroupName = g.GroupName,
                    Description = g.Description,
                    DisplayOrder = g.DisplayOrder,
                    IsActive = g.IsActive,
                    Questions = g.Questions
                        .OrderBy(q => q.DisplayOrder)
                        .Select(q => MapQuestionToDto(q))
                        .ToList()
                })
                .ToList(),
            Questions = audit.AuditTemplate.Questions
                .Where(q => q.QuestionGroupId == null) // Only root-level questions not in groups
                .OrderBy(q => q.DisplayOrder)
                .Select(q => MapQuestionToDto(q))
                .ToList()
        };
    }

    private static QuestionDto MapQuestionToDto(Domain.Entities.Templates.Question question)
    {
        return new QuestionDto
        {
            Id = question.Id,
            AuditTemplateId = question.AuditTemplateId,
            QuestionGroupId = question.QuestionGroupId,
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType,
            DisplayOrder = question.DisplayOrder,
            IsRequired = question.IsRequired,
            Weight = question.Weight,
            HelpText = question.HelpText,
            ParentQuestionId = question.ParentQuestionId,
            TriggerAnswerValue = question.TriggerAnswerValue,
            SeverityLevel = question.SeverityLevel,
            EvidenceRequired = question.EvidenceRequired,
            AllowedEvidenceTypes = question.AllowedEvidenceTypes
                .Where(aet => aet.IsActive)
                .OrderBy(aet => aet.DisplayOrder)
                .Select(aet => new EvidenceTypeDto
                {
                    EvidenceType = aet.EvidenceType,
                    DisplayOrder = aet.DisplayOrder,
                    IsActive = aet.IsActive
                })
                .ToList(),
            IsActive = question.IsActive,
            IsConditional = question.IsConditional,
            Options = question.Options
                .OrderBy(o => o.DisplayOrder)
                .Select(o => new QuestionOptionDto
                {
                    Id = o.Id,
                    QuestionId = o.QuestionId,
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                })
                .ToList(),
            ChildQuestions = question.ChildQuestions
                .Where(cq => cq.IsActive)
                .OrderBy(cq => cq.DisplayOrder)
                .Select(cq => MapQuestionToDto(cq))
                .ToList()
        };
    }
}
