using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Organization;

/// <summary>
/// Represents a sub-area within an area (e.g., Welding Station 1).
/// Maps to the SubAreas table in the database.
/// </summary>
public class SubArea : AuditableEntity<int>
{
    /// <summary>
    /// Name of the sub-area
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string SubAreaName { get; set; } = string.Empty;

    /// <summary>
    /// Links to the parent area
    /// </summary>
    public int AreaId { get; set; }

    /// <summary>
    /// Navigation property for the parent area
    /// </summary>
    public virtual Area Area { get; set; } = null!;

    /// <summary>
    /// Optional description of the sub-area
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Indicates if the sub-area is currently active/relevant
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property for the user who created this sub-area
    /// </summary>
    public virtual User? CreatedByUser { get; set; }

    /// <summary>
    /// Navigation property for the user who last updated this sub-area
    /// </summary>
    public virtual User? UpdatedByUser { get; set; }
}
