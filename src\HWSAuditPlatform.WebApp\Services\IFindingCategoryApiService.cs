using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for finding category API operations
/// </summary>
public interface IFindingCategoryApiService
{
    /// <summary>
    /// Gets finding categories with optional filtering
    /// </summary>
    /// <param name="filter">Filter criteria</param>
    /// <returns>List of finding categories</returns>
    Task<List<FindingCategoryModel>> GetFindingCategoriesAsync(FindingCategoryFilterModel? filter = null);

    /// <summary>
    /// Gets a specific finding category by ID
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <returns>Finding category details</returns>
    Task<FindingCategoryModel?> GetFindingCategoryAsync(int id);

    /// <summary>
    /// Creates a new finding category
    /// </summary>
    /// <param name="model">Finding category creation data</param>
    /// <returns>Created finding category</returns>
    Task<FindingCategoryModel> CreateFindingCategoryAsync(CreateFindingCategoryModel model);

    /// <summary>
    /// Updates an existing finding category
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <param name="model">Updated finding category data</param>
    /// <returns>Updated finding category</returns>
    Task<FindingCategoryModel> UpdateFindingCategoryAsync(int id, CreateFindingCategoryModel model);

    /// <summary>
    /// Deletes a finding category
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <returns>Success status</returns>
    Task<bool> DeleteFindingCategoryAsync(int id);

    /// <summary>
    /// Gets finding categories for a specific audit template
    /// </summary>
    /// <param name="auditTemplateId">Audit template ID</param>
    /// <param name="includeUsageStatistics">Include usage statistics</param>
    /// <returns>List of finding categories</returns>
    Task<List<FindingCategoryModel>> GetFindingCategoriesForTemplateAsync(int auditTemplateId, bool includeUsageStatistics = false);

    /// <summary>
    /// Gets category statistics for an audit template
    /// </summary>
    /// <param name="auditTemplateId">Audit template ID</param>
    /// <returns>Category usage statistics</returns>
    Task<CategoryStatisticsModel?> GetCategoryStatisticsAsync(int auditTemplateId);

    /// <summary>
    /// Assigns a category to a finding
    /// </summary>
    /// <param name="findingId">Finding ID</param>
    /// <param name="categoryId">Category ID (null to remove category)</param>
    /// <returns>Success status</returns>
    Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId);

    /// <summary>
    /// Gets predefined color options for categories
    /// </summary>
    /// <returns>List of color options</returns>
    List<ColorOptionModel> GetColorOptions();

    /// <summary>
    /// Gets predefined icon options for categories
    /// </summary>
    /// <returns>List of icon options</returns>
    List<IconOptionModel> GetIconOptions();
}
