using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.StartAudit;

/// <summary>
/// Handler for StartAuditCommand
/// </summary>
public class StartAuditCommandHandler : BaseCommandHandler<StartAuditCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public StartAuditCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(StartAuditCommand request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        // Validate status transition
        if (audit.OverallStatus != AuditOverallStatus.Scheduled)
        {
            throw new InvalidOperationException($"Cannot start audit in {audit.OverallStatus} status");
        }

        // Update audit status and start time
        audit.OverallStatus = AuditOverallStatus.InProgress;
        audit.StartedAt = DateTime.UtcNow;
        audit.UpdatedAt = DateTime.UtcNow;
        audit.UpdatedByUserId = _currentUserService.UserId;

        // If group assignment, assign to current user
        if (audit.AssignmentType == AssignmentType.GroupAny)
        {
            audit.AssignedToUserId = _currentUserService.UserId;
            audit.AssignmentType = AssignmentType.Individual;
        }

        await _context.SaveChangesAsync(cancellationToken);
    }
}
