@using HWSAuditPlatform.Domain.Entities.Templates

<div class="card h-100 template-preview-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="card-title mb-0 text-truncate me-2">@Template.TemplateName</h6>
        <div class="template-status">
            @if (Template.IsActive)
            {
                <span class="badge bg-success">Active</span>
            }
            else
            {
                <span class="badge bg-secondary">Inactive</span>
            }
        </div>
    </div>
    
    <div class="card-body d-flex flex-column">
        <div class="template-info flex-grow-1">
            @if (!string.IsNullOrEmpty(Template.Description))
            {
                <p class="card-text text-muted small mb-3">@Template.Description</p>
            }
            else
            {
                <p class="card-text text-muted small mb-3 fst-italic">No description provided</p>
            }
            
            <div class="template-stats mb-3">
                <div class="row g-2">
                    <div class="col-6">
                        <div class="stat-item">
                            <i class="bi bi-question-circle text-primary me-1"></i>
                            <small class="text-muted">Questions:</small>
                            <strong class="ms-1">@(Template.Questions?.Count ?? 0)</strong>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <i class="bi bi-hash text-primary me-1"></i>
                            <small class="text-muted">Version:</small>
                            <strong class="ms-1">@Template.Version</strong>
                        </div>
                    </div>
                </div>
                
                @if (Template.IsPublished)
                {
                    <div class="mt-2">
                        <span class="badge bg-primary">
                            <i class="bi bi-check-circle me-1"></i>Published
                        </span>
                    </div>
                }
                else
                {
                    <div class="mt-2">
                        <span class="badge bg-warning">
                            <i class="bi bi-pencil me-1"></i>Draft
                        </span>
                    </div>
                }
            </div>
            
            <div class="template-dates">
                <small class="text-muted d-block">
                    <i class="bi bi-calendar-plus me-1"></i>
                    Created: @Template.CreatedAt.ToString("MMM dd, yyyy")
                </small>
                @if (Template.UpdatedAt != Template.CreatedAt)
                {
                    <small class="text-muted d-block">
                        <i class="bi bi-calendar-check me-1"></i>
                        Updated: @Template.UpdatedAt.ToString("MMM dd, yyyy")
                    </small>
                }
            </div>
        </div>
        
        <div class="template-actions mt-3">
            <div class="btn-group w-100" role="group">
                <button class="btn btn-outline-primary btn-sm" 
                        @onclick="() => OnView.InvokeAsync(Template.Id)"
                        title="View template details">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-outline-secondary btn-sm" 
                        @onclick="() => OnEdit.InvokeAsync(Template.Id)"
                        title="Edit template">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-outline-danger btn-sm" 
                        @onclick="() => OnDelete.InvokeAsync(Template.Id)"
                        title="Delete template">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .template-preview-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        background-color: white;
    }

    .template-preview-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .template-preview-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
        padding: 0.75rem 1rem;
    }

    .template-preview-card .card-body {
        padding: 1rem;
        min-height: 200px;
    }

    .template-status .badge {
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .template-actions .btn-group {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .template-actions .btn {
        border-color: #dee2e6;
        transition: all 0.2s ease;
    }

    .template-actions .btn:hover {
        transform: translateY(-1px);
    }

    .template-actions .btn-outline-primary:hover {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .template-actions .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    .template-actions .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .template-dates small {
        line-height: 1.4;
    }

    .badge.bg-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }

    .badge.bg-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d69e2e 100%) !important;
        color: white !important;
    }

    .badge.bg-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }

    .badge.bg-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    }
</style>

@code {
    [Parameter] public AuditTemplate Template { get; set; } = null!;
    [Parameter] public EventCallback<int> OnView { get; set; }
    [Parameter] public EventCallback<int> OnEdit { get; set; }
    [Parameter] public EventCallback<int> OnDelete { get; set; }
}
