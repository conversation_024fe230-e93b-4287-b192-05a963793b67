using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteAuditTemplate;

/// <summary>
/// Handler for DeleteAuditTemplateCommand
/// </summary>
public class DeleteAuditTemplateCommandHandler : BaseCommandHandler<DeleteAuditTemplateCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteAuditTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(DeleteAuditTemplateCommand request, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (template == null || !template.IsActive)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.AuditTemplate), request.Id);
        }

        // Check for optimistic concurrency
        if (template.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The template has been modified by another user. Please refresh and try again.");
        }

        // Check if template is being used in any active audits
        var isUsedInAudits = await _context.Audits
            .AnyAsync(a => a.AuditTemplateId == request.Id &&
                          a.OverallStatus != Domain.Enums.AuditOverallStatus.Cancelled &&
                          a.OverallStatus != Domain.Enums.AuditOverallStatus.Closed, cancellationToken);

        if (isUsedInAudits)
        {
            throw new InvalidOperationException("Cannot delete a template that is being used in active audits.");
        }

        // Soft delete the template
        template.IsActive = false;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedByUserId = _currentUserService.UserId;

        // Also soft delete all related questions and question groups
        var questions = await _context.Questions
            .Where(q => q.AuditTemplateId == request.Id)
            .ToListAsync(cancellationToken);

        foreach (var question in questions)
        {
            question.IsActive = false;
        }

        var questionGroups = await _context.QuestionGroups
            .Where(qg => qg.AuditTemplateId == request.Id)
            .ToListAsync(cancellationToken);

        foreach (var group in questionGroups)
        {
            group.IsActive = false;
        }

        await _context.SaveChangesAsync(cancellationToken);
    }
}
