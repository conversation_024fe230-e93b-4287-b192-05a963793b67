using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;

/// <summary>
/// Command to create a new area responsibility assignment
/// </summary>
public class CreateAreaResponsibilityCommand : BaseCommand<AreaResponsibilityDto>
{
    public int AreaId { get; set; }
    public ResponsibilityType ResponsibilityType { get; set; }
    public string ResponsibleUserId { get; set; } = string.Empty;
    public int? AuditTemplateId { get; set; }
    public bool IsActive { get; set; } = true;
    public string? Description { get; set; }
    public int Priority { get; set; } = 0;
}
