﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddViewerRoleAndAdGroupMappings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "hwsap_usr_ad_group_role_mappings",
                columns: new[] { "Id", "AdGroupName", "CreatedAt", "Description", "IsActive", "RoleId", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "HWSAudit-Administrators", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Maps AD Administrators group to Admin role", true, 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) },
                    { 2, "HWSAudit-Managers", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Maps AD Managers group to Manager role", true, 2, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) },
                    { 3, "HWSAudit-Auditors", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Maps AD Auditors group to Auditor role", true, 3, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) }
                });

            migrationBuilder.InsertData(
                table: "hwsap_usr_roles",
                columns: new[] { "Id", "CreatedAt", "Description", "RoleName", "UpdatedAt" },
                values: new object[] { 4, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Viewer with read-only access", "Viewer", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) });

            migrationBuilder.InsertData(
                table: "hwsap_usr_ad_group_role_mappings",
                columns: new[] { "Id", "AdGroupName", "CreatedAt", "Description", "IsActive", "RoleId", "UpdatedAt" },
                values: new object[] { 4, "HWSAudit-Viewers", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Maps AD Viewers group to Viewer role", true, 4, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "hwsap_usr_ad_group_role_mappings",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "hwsap_usr_roles",
                keyColumn: "Id",
                keyValue: 4);
        }
    }
}
