using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAudit;

/// <summary>
/// Handler for SubmitAuditCommand
/// </summary>
public class SubmitAuditCommandHandler : BaseCommandHandler<SubmitAuditCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public SubmitAuditCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(SubmitAuditCommand request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .Include(a => a.Answers)
            .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        // Validate status transition
        if (audit.OverallStatus != AuditOverallStatus.InProgress)
        {
            throw new InvalidOperationException($"Cannot submit audit in {audit.OverallStatus} status");
        }

        // Calculate overall score if applicable
        var overallScore = await CalculateOverallScore(audit, cancellationToken);

        // Update audit status and completion details
        audit.OverallStatus = AuditOverallStatus.PendingManagerReview;
        audit.CompletedAt = DateTime.UtcNow;
        audit.OverallScore = overallScore;
        audit.UpdatedAt = DateTime.UtcNow;
        audit.UpdatedByUserId = _currentUserService.UserId;

        // Add auditor comments if provided
        if (!string.IsNullOrEmpty(request.AuditorComments))
        {
            // Store auditor comments - this might need a separate field or entity
            // For now, we'll use a generic comments field
            audit.ManagerComments = request.AuditorComments; // This should be AuditorComments field
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    private async Task<decimal?> CalculateOverallScore(Domain.Entities.Audits.Audit audit, CancellationToken cancellationToken)
    {
        // Get all questions with weights for this audit template
        var questions = await _context.Questions
            .Where(q => q.AuditTemplateId == audit.AuditTemplateId)
            .ToListAsync(cancellationToken);

        if (!questions.Any(q => q.Weight.HasValue))
        {
            // No weighted scoring system
            return null;
        }

        var totalWeight = questions.Where(q => q.Weight.HasValue).Sum(q => q.Weight!.Value);
        var totalScore = 0m;

        foreach (var answer in audit.Answers)
        {
            var question = questions.FirstOrDefault(q => q.Id == answer.QuestionId);
            if (question?.Weight.HasValue == true)
            {
                var score = CalculateQuestionScore(answer, question);
                totalScore += score * question.Weight.Value;
            }
        }

        return totalWeight > 0 ? (totalScore / totalWeight) * 100 : null;
    }

    private static decimal CalculateQuestionScore(Domain.Entities.Audits.AuditAnswer answer, Domain.Entities.Templates.Question question)
    {
        if (answer.IsNotApplicable)
        {
            return 1m; // N/A questions get full score
        }

        // Simple scoring logic - this should be enhanced based on business rules
        return question.QuestionType switch
        {
            QuestionType.YesNo => answer.AnswerValue?.ToLower() == "yes" ? 1m : 0m,
            QuestionType.Numeric => 1m, // Assume numeric answers are always correct for now
            QuestionType.SingleSelect or QuestionType.MultiSelect => 1m, // Assume selections are correct
            _ => 1m
        };
    }
}
