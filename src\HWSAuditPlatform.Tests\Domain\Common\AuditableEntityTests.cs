using FluentAssertions;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Tests.Domain.Common;

public class AuditableEntityTests
{
    private class TestAuditableEntity : AuditableEntity<string>
    {
        public TestAuditableEntity(string id)
        {
            Id = id;
        }

        public void UpdateRecordVersion()
        {
            RecordVersion++;
        }
    }

    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var entity = new TestAuditableEntity("test-id");

        // Assert
        entity.Id.Should().Be("test-id");
        entity.RecordVersion.Should().Be(1);
        entity.CreatedByUserId.Should().BeNull();
        entity.UpdatedByUserId.Should().BeNull();
        entity.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        entity.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void RecordVersion_CanBeIncremented()
    {
        // Arrange
        var entity = new TestAuditableEntity("test-id");
        var initialVersion = entity.RecordVersion;

        // Act
        entity.UpdateRecordVersion();

        // Assert
        entity.RecordVersion.Should().Be(initialVersion + 1);
    }

    [Fact]
    public void CreatedByUserId_CanBeSet()
    {
        // Arrange
        var entity = new TestAuditableEntity("test-id");
        const string userId = "user-123";

        // Act
        entity.CreatedByUserId = userId;

        // Assert
        entity.CreatedByUserId.Should().Be(userId);
    }

    [Fact]
    public void UpdatedByUserId_CanBeSet()
    {
        // Arrange
        var entity = new TestAuditableEntity("test-id");
        const string userId = "user-456";

        // Act
        entity.UpdatedByUserId = userId;

        // Assert
        entity.UpdatedByUserId.Should().Be(userId);
    }

    [Fact]
    public void Equality_ShouldWorkWithCuidIds()
    {
        // Arrange
        const string cuid = "clh7ckb0x0000qh08w5t6h5zx";
        var entity1 = new TestAuditableEntity(cuid);
        var entity2 = new TestAuditableEntity(cuid);

        // Act & Assert
        entity1.Should().Be(entity2);
        entity1.GetHashCode().Should().Be(entity2.GetHashCode());
    }

    [Fact]
    public void Equality_ShouldFailWithDifferentCuidIds()
    {
        // Arrange
        var entity1 = new TestAuditableEntity("clh7ckb0x0000qh08w5t6h5zx");
        var entity2 = new TestAuditableEntity("clh7ckb0x0001qh08w5t6h5zy");

        // Act & Assert
        entity1.Should().NotBe(entity2);
        entity1.GetHashCode().Should().NotBe(entity2.GetHashCode());
    }
}
