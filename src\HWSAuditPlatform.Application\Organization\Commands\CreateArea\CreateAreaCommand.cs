using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateArea;

/// <summary>
/// Command to create a new area within a factory
/// </summary>
public class CreateAreaCommand : BaseCommand<int>
{
    public string AreaName { get; set; } = string.Empty;
    public int FactoryId { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
}
