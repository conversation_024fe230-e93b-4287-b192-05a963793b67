@page "/logout"
@using Microsoft.AspNetCore.Components.Authorization
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IAuthenticationService AuthService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ILogger<Logout> Logger

<PageTitle>Logging out... - HWS Audit Platform PWA</PageTitle>

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Logging out...</span>
        </div>
        <h4 class="text-muted">Logging out...</h4>
        <p class="text-muted">Please wait while we securely log you out.</p>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            Logger.LogInformation("Logout page initialized, performing logout");
            
            // Perform logout operations
            await AuthService.LogoutAsync();
            AuthStateProvider.MarkUserAsLoggedOut();
            
            Logger.LogInformation("Logout completed successfully");
            
            // Small delay to show the logout message
            await Task.Delay(1000);
            
            // Navigate to login page
            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during logout process");
            
            try
            {
                // Fallback: Force page reload to clear any cached state
                await JSRuntime.InvokeVoidAsync("location.reload");
            }
            catch (Exception jsEx)
            {
                Logger.LogError(jsEx, "Error during fallback page reload");
                // Last resort: navigate to login
                Navigation.NavigateTo("/login", forceLoad: true);
            }
        }
    }
}
