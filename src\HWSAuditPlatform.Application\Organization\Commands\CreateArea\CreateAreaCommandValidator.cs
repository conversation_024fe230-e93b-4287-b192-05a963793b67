using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateArea;

/// <summary>
/// Validator for CreateAreaCommand
/// </summary>
public class CreateAreaCommandValidator : AbstractValidator<CreateAreaCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateAreaCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AreaName)
            .NotEmpty().WithMessage("Area name is required")
            .MaximumLength(150).WithMessage("Area name must not exceed 150 characters");

        RuleFor(x => x.FactoryId)
            .GreaterThan(0).WithMessage("Factory ID must be greater than 0")
            .MustAsync(BeValidFactory).WithMessage("Factory does not exist or is not active");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description must not exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x)
            .MustAsync(BeUniqueAreaName).WithMessage("Area name must be unique within the factory");
    }

    private async Task<bool> BeValidFactory(int factoryId, CancellationToken cancellationToken)
    {
        return await _context.Factories.AnyAsync(f => f.Id == factoryId && f.IsActive, cancellationToken);
    }

    private async Task<bool> BeUniqueAreaName(CreateAreaCommand command, CancellationToken cancellationToken)
    {
        return !await _context.Areas.AnyAsync(a => 
            a.FactoryId == command.FactoryId && 
            a.AreaName == command.AreaName, 
            cancellationToken);
    }
}
