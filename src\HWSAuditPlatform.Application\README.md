# HWS Audit Platform - Application Layer

## Overview

The Application layer serves as the orchestration layer between the presentation layer (API/UI) and the domain layer. It implements the CQRS (Command Query Responsibility Segregation) pattern using MediatR and provides a clean separation between commands (write operations) and queries (read operations).

## Architecture

### Key Patterns & Technologies

- **CQRS with MediatR**: Commands and queries are handled separately for better separation of concerns
- **AutoMapper**: Object-to-object mapping between entities and DTOs
- **FluentValidation**: Input validation with rich validation rules
- **Pipeline Behaviors**: Cross-cutting concerns like validation, logging, and domain event handling

### Project Structure

```
src/HWSAuditPlatform.Application/
├── Common/                     # Base classes and shared utilities
│   ├── BaseCommand.cs         # Base command classes
│   ├── BaseQuery.cs           # Base query classes
│   ├── BaseHandler.cs         # Base handler classes
│   ├── PaginatedResult.cs     # Pagination support
│   └── Result.cs              # Result pattern implementation
├── Interfaces/                 # Application interfaces
│   ├── IRepository.cs         # Generic repository interface
│   ├── IUnitOfWork.cs         # Unit of work pattern
│   ├── IApplicationDbContext.cs # Database context interface
│   ├── ICurrentUserService.cs # Current user access
│   └── IDomainEventService.cs # Domain event publishing
├── DTOs/                      # Data Transfer Objects
│   └── BaseDto.cs             # Base DTO classes
├── Behaviors/                 # MediatR pipeline behaviors
│   ├── ValidationBehavior.cs  # Request validation
│   ├── LoggingBehavior.cs     # Request logging
│   └── DomainEventBehavior.cs # Domain event handling
├── Exceptions/                # Application-specific exceptions
│   ├── ApplicationException.cs # Base application exception
│   └── ValidationException.cs # Validation exception
├── Mappings/                  # AutoMapper profiles
│   └── UserMappingProfile.cs  # User entity mappings
├── Users/                     # User management functionality
│   ├── DTOs/                  # User-related DTOs
│   ├── Commands/              # User commands (Create, Update, Delete)
│   └── Queries/               # User queries (Get, List)
├── Organization/              # Organizational structure management
├── Audits/                    # Audit execution functionality
├── Templates/                 # Audit template management
├── Findings/                  # Finding and corrective action management
├── Scheduling/                # Recurring audit scheduling
├── Workflow/                  # Correction requests and workflow
├── Sync/                      # PWA synchronization logic
└── DependencyInjection.cs     # Service registration
```

## Functional Areas

### 1. User Management (`Users/`)
- User CRUD operations
- User group management
- Role assignment
- Active Directory synchronization

### 2. Organization Management (`Organization/`)
- Location, Factory, Area, SubArea management
- Hierarchical organizational structure
- Address management

### 3. Audit Execution (`Audits/`)
- Audit creation and assignment
- Audit execution workflow
- Answer and evidence capture
- Status management

### 4. Template Management (`Templates/`)
- Audit template creation and versioning
- Question and question group management
- Conditional logic support
- Evidence requirements

### 5. Findings Management (`Findings/`)
- Finding creation from audit answers
- Corrective action planning
- Status tracking and verification

### 6. Scheduling (`Scheduling/`)
- Recurring audit configuration
- Automated audit generation
- Recurrence rule management

### 7. Workflow (`Workflow/`)
- Audit correction requests
- Approval workflows
- Audit logging

### 8. Synchronization (`Sync/`)
- PWA offline data synchronization
- Conflict resolution
- Batch data operations

## Key Features

### CQRS Implementation

**Commands** (Write Operations):
- Modify application state
- Trigger business logic
- Raise domain events
- Return simple results (IDs, success/failure)

**Queries** (Read Operations):
- Read-only operations
- Return DTOs optimized for specific views
- Support filtering, sorting, and pagination
- No side effects

### Pipeline Behaviors

1. **ValidationBehavior**: Validates requests using FluentValidation
2. **LoggingBehavior**: Logs request execution time and details
3. **DomainEventBehavior**: Publishes domain events after successful operations

### Data Transfer Objects (DTOs)

- **BaseDto**: Common properties (CreatedAt, UpdatedAt)
- **AuditableDto**: Adds versioning and audit tracking
- **Summary DTOs**: Lightweight versions for lists and lookups
- **Detail DTOs**: Complete information for single entity views

### Validation

- **FluentValidation**: Rich validation rules with async support
- **Business rule validation**: Database lookups and cross-field validation
- **Localization ready**: Error messages can be localized

### Error Handling

- **Structured exceptions**: Specific exception types for different scenarios
- **Validation errors**: Detailed field-level validation messages
- **Not found handling**: Consistent entity not found responses
- **Concurrency conflicts**: Optimistic concurrency control

## Usage Examples

### Creating a User

```csharp
var command = new CreateUserCommand
{
    Username = "john.doe",
    FirstName = "John",
    LastName = "Doe",
    Email = "<EMAIL>",
    Role = UserRole.Auditor,
    FactoryId = 1
};

var userId = await mediator.Send(command);
```

### Querying Users

```csharp
var query = new GetUsersQuery
{
    PageNumber = 1,
    PageSize = 10,
    SearchTerm = "john",
    Role = UserRole.Auditor,
    IsActive = true
};

var result = await mediator.Send(query);
```

### Creating an Audit

```csharp
var command = new CreateAuditCommand
{
    AuditTemplateId = 1,
    AssignmentType = AssignmentType.Individual,
    AssignedToUserId = "user123",
    ScheduledDate = DateTime.UtcNow.AddDays(1),
    DueDate = DateTime.UtcNow.AddDays(7),
    FactoryId = 1,
    AreaId = 1
};

var auditId = await mediator.Send(command);
```

## Dependencies

### External Packages
- **MediatR**: CQRS implementation
- **AutoMapper**: Object mapping
- **FluentValidation**: Input validation
- **Microsoft.Extensions.Logging**: Logging abstractions
- **Microsoft.Extensions.DependencyInjection**: Dependency injection

### Internal Dependencies
- **HWSAuditPlatform.Domain**: Domain entities, enums, and business logic

## Configuration

Register the Application layer services in your DI container:

```csharp
services.AddApplication();
```

This registers:
- MediatR with all handlers
- AutoMapper with all profiles
- FluentValidation with all validators
- Pipeline behaviors for cross-cutting concerns

## Testing

The Application layer is designed to be easily testable:

- **Unit tests**: Test individual handlers with mocked dependencies
- **Integration tests**: Test complete request/response flows
- **Validation tests**: Test validation rules and error scenarios
- **Behavior tests**: Test pipeline behaviors in isolation

## Future Enhancements

- **Caching**: Add caching behavior for frequently accessed data
- **Authorization**: Add authorization behavior for role-based access control
- **Metrics**: Add performance metrics and monitoring
- **Event sourcing**: Consider event sourcing for audit trail requirements
- **Background processing**: Add support for background job processing
