using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for organizational data API operations
/// </summary>
public interface IOrganizationApiService
{
    /// <summary>
    /// Gets all factories
    /// </summary>
    Task<IEnumerable<FactorySummary>> GetFactoriesAsync();

    /// <summary>
    /// Gets areas for a specific factory
    /// </summary>
    Task<IEnumerable<AreaSummary>> GetAreasByFactoryAsync(int factoryId);

    /// <summary>
    /// Gets sub-areas for a specific area
    /// </summary>
    Task<IEnumerable<SubAreaSummary>> GetSubAreasByAreaAsync(int areaId);

    /// <summary>
    /// Gets all users
    /// </summary>
    Task<IEnumerable<UserSummary>> GetUsersAsync();

    /// <summary>
    /// Searches users by term
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="pageSize">Maximum number of results to return</param>
    Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int pageSize = 10);

    /// <summary>
    /// Gets all user groups
    /// </summary>
    Task<IEnumerable<UserGroupSummary>> GetUserGroupsAsync();
}
