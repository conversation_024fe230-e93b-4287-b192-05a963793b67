﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableNaming : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_QuestionOptions_SelectedOptionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_Questions_QuestionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswerSelectedOptions_QuestionOptions_QuestionOptionId",
                table: "AuditAnswerSelectedOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId",
                table: "AuditCorrectionRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId1",
                table: "AuditCorrectionRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditCorrectionRequests_Users_RequestedByUserId",
                table: "AuditCorrectionRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditCorrectionRequests_Users_ReviewedByUserId",
                table: "AuditCorrectionRequests");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_AuditTemplates_AuditTemplateId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_RecurringAuditSettings_RecurringAuditSettingId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_CorrectiveActions_Findings_FindingId",
                table: "CorrectiveActions");

            migrationBuilder.DropForeignKey(
                name: "FK_CorrectiveActions_Users_AssignedToUserId",
                table: "CorrectiveActions");

            migrationBuilder.DropForeignKey(
                name: "FK_Findings_AuditAnswers_AuditAnswerId",
                table: "Findings");

            migrationBuilder.DropForeignKey(
                name: "FK_Findings_Users_ReportedByUserId",
                table: "Findings");

            migrationBuilder.DropForeignKey(
                name: "FK_QuestionGroups_AuditTemplates_AuditTemplateId",
                table: "QuestionGroups");

            migrationBuilder.DropForeignKey(
                name: "FK_QuestionOptions_Questions_QuestionId",
                table: "QuestionOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Questions_AuditTemplates_AuditTemplateId",
                table: "Questions");

            migrationBuilder.DropForeignKey(
                name: "FK_Questions_QuestionGroups_QuestionGroupId",
                table: "Questions");

            migrationBuilder.DropForeignKey(
                name: "FK_Questions_Questions_ParentQuestionId",
                table: "Questions");

            migrationBuilder.DropForeignKey(
                name: "FK_RecurrenceRules_RecurringAuditSettings_RecurringAuditSettingId",
                table: "RecurrenceRules");

            migrationBuilder.DropForeignKey(
                name: "FK_RecurringAuditSettings_AuditTemplates_AuditTemplateId",
                table: "RecurringAuditSettings");

            migrationBuilder.DropForeignKey(
                name: "FK_RecurringAuditSettings_UserGroups_AssignToUserGroupId",
                table: "RecurringAuditSettings");

            migrationBuilder.DropForeignKey(
                name: "FK_RecurringAuditSettings_Users_AssignToUserId",
                table: "RecurringAuditSettings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_RecurringAuditSettings",
                table: "RecurringAuditSettings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_RecurrenceRules",
                table: "RecurrenceRules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Questions",
                table: "Questions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_QuestionOptions",
                table: "QuestionOptions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_QuestionGroups",
                table: "QuestionGroups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Findings",
                table: "Findings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CorrectiveActions",
                table: "CorrectiveActions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditTemplates",
                table: "AuditTemplates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditCorrectionRequests",
                table: "AuditCorrectionRequests");

            migrationBuilder.RenameTable(
                name: "RecurringAuditSettings",
                newName: "hwsap_recurring_audit_settings");

            migrationBuilder.RenameTable(
                name: "RecurrenceRules",
                newName: "hwsap_recurrence_rules");

            migrationBuilder.RenameTable(
                name: "Questions",
                newName: "hwsap_questions");

            migrationBuilder.RenameTable(
                name: "QuestionOptions",
                newName: "hwsap_question_options");

            migrationBuilder.RenameTable(
                name: "QuestionGroups",
                newName: "hwsap_question_groups");

            migrationBuilder.RenameTable(
                name: "Findings",
                newName: "hwsap_findings");

            migrationBuilder.RenameTable(
                name: "CorrectiveActions",
                newName: "hwsap_corrective_actions");

            migrationBuilder.RenameTable(
                name: "AuditTemplates",
                newName: "hwsap_audit_templates");

            migrationBuilder.RenameTable(
                name: "AuditCorrectionRequests",
                newName: "hwsap_audit_correction_requests");

            migrationBuilder.RenameIndex(
                name: "IX_AuditCorrectionRequests_AuditId1",
                table: "hwsap_audit_correction_requests",
                newName: "IX_hwsap_audit_correction_requests_AuditId1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_recurring_audit_settings",
                table: "hwsap_recurring_audit_settings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_recurrence_rules",
                table: "hwsap_recurrence_rules",
                column: "RecurringAuditSettingId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_questions",
                table: "hwsap_questions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_question_options",
                table: "hwsap_question_options",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_question_groups",
                table: "hwsap_question_groups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_findings",
                table: "hwsap_findings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_corrective_actions",
                table: "hwsap_corrective_actions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_audit_templates",
                table: "hwsap_audit_templates",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_audit_correction_requests",
                table: "hwsap_audit_correction_requests",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_hwsap_question_options_SelectedOptionId",
                table: "AuditAnswers",
                column: "SelectedOptionId",
                principalTable: "hwsap_question_options",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_hwsap_questions_QuestionId",
                table: "AuditAnswers",
                column: "QuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerSelectedOptions_hwsap_question_options_QuestionOptionId",
                table: "AuditAnswerSelectedOptions",
                column: "QuestionOptionId",
                principalTable: "hwsap_question_options",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_hwsap_audit_templates_AuditTemplateId",
                table: "Audits",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "Audits",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_recurring_audit_settings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId",
                table: "hwsap_audit_correction_requests",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId1",
                table: "hwsap_audit_correction_requests",
                column: "AuditId1",
                principalTable: "Audits",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_RequestedByUserId",
                table: "hwsap_audit_correction_requests",
                column: "RequestedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_ReviewedByUserId",
                table: "hwsap_audit_correction_requests",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_corrective_actions_Users_AssignedToUserId",
                table: "hwsap_corrective_actions",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_corrective_actions_hwsap_findings_FindingId",
                table: "hwsap_corrective_actions",
                column: "FindingId",
                principalTable: "hwsap_findings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_findings_AuditAnswers_AuditAnswerId",
                table: "hwsap_findings",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_findings_Users_ReportedByUserId",
                table: "hwsap_findings",
                column: "ReportedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_question_groups_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_question_groups",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_question_options_hwsap_questions_QuestionId",
                table: "hwsap_question_options",
                column: "QuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_questions",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_question_groups_QuestionGroupId",
                table: "hwsap_questions",
                column: "QuestionGroupId",
                principalTable: "hwsap_question_groups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_questions_ParentQuestionId",
                table: "hwsap_questions",
                column: "ParentQuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurrence_rules_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_recurrence_rules",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_recurring_audit_settings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_UserGroups_AssignToUserGroupId",
                table: "hwsap_recurring_audit_settings",
                column: "AssignToUserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_Users_AssignToUserId",
                table: "hwsap_recurring_audit_settings",
                column: "AssignToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_recurring_audit_settings",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_hwsap_question_options_SelectedOptionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_hwsap_questions_QuestionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswerSelectedOptions_hwsap_question_options_QuestionOptionId",
                table: "AuditAnswerSelectedOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_hwsap_audit_templates_AuditTemplateId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId1",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_RequestedByUserId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_ReviewedByUserId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_corrective_actions_Users_AssignedToUserId",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_corrective_actions_hwsap_findings_FindingId",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_findings_AuditAnswers_AuditAnswerId",
                table: "hwsap_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_findings_Users_ReportedByUserId",
                table: "hwsap_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_question_groups_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_question_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_question_options_hwsap_questions_QuestionId",
                table: "hwsap_question_options");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_question_groups_QuestionGroupId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_questions_ParentQuestionId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurrence_rules_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_recurrence_rules");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_UserGroups_AssignToUserGroupId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_Users_AssignToUserId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_recurring_audit_settings",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_recurrence_rules",
                table: "hwsap_recurrence_rules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_questions",
                table: "hwsap_questions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_question_options",
                table: "hwsap_question_options");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_question_groups",
                table: "hwsap_question_groups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_findings",
                table: "hwsap_findings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_corrective_actions",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_audit_templates",
                table: "hwsap_audit_templates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_audit_correction_requests",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.RenameTable(
                name: "hwsap_recurring_audit_settings",
                newName: "RecurringAuditSettings");

            migrationBuilder.RenameTable(
                name: "hwsap_recurrence_rules",
                newName: "RecurrenceRules");

            migrationBuilder.RenameTable(
                name: "hwsap_questions",
                newName: "Questions");

            migrationBuilder.RenameTable(
                name: "hwsap_question_options",
                newName: "QuestionOptions");

            migrationBuilder.RenameTable(
                name: "hwsap_question_groups",
                newName: "QuestionGroups");

            migrationBuilder.RenameTable(
                name: "hwsap_findings",
                newName: "Findings");

            migrationBuilder.RenameTable(
                name: "hwsap_corrective_actions",
                newName: "CorrectiveActions");

            migrationBuilder.RenameTable(
                name: "hwsap_audit_templates",
                newName: "AuditTemplates");

            migrationBuilder.RenameTable(
                name: "hwsap_audit_correction_requests",
                newName: "AuditCorrectionRequests");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_audit_correction_requests_AuditId1",
                table: "AuditCorrectionRequests",
                newName: "IX_AuditCorrectionRequests_AuditId1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_RecurringAuditSettings",
                table: "RecurringAuditSettings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_RecurrenceRules",
                table: "RecurrenceRules",
                column: "RecurringAuditSettingId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Questions",
                table: "Questions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_QuestionOptions",
                table: "QuestionOptions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_QuestionGroups",
                table: "QuestionGroups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Findings",
                table: "Findings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CorrectiveActions",
                table: "CorrectiveActions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditTemplates",
                table: "AuditTemplates",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditCorrectionRequests",
                table: "AuditCorrectionRequests",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_QuestionOptions_SelectedOptionId",
                table: "AuditAnswers",
                column: "SelectedOptionId",
                principalTable: "QuestionOptions",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_Questions_QuestionId",
                table: "AuditAnswers",
                column: "QuestionId",
                principalTable: "Questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerSelectedOptions_QuestionOptions_QuestionOptionId",
                table: "AuditAnswerSelectedOptions",
                column: "QuestionOptionId",
                principalTable: "QuestionOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId",
                table: "AuditCorrectionRequests",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Audits_AuditId1",
                table: "AuditCorrectionRequests",
                column: "AuditId1",
                principalTable: "Audits",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Users_RequestedByUserId",
                table: "AuditCorrectionRequests",
                column: "RequestedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditCorrectionRequests_Users_ReviewedByUserId",
                table: "AuditCorrectionRequests",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_AuditTemplates_AuditTemplateId",
                table: "Audits",
                column: "AuditTemplateId",
                principalTable: "AuditTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_RecurringAuditSettings_RecurringAuditSettingId",
                table: "Audits",
                column: "RecurringAuditSettingId",
                principalTable: "RecurringAuditSettings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CorrectiveActions_Findings_FindingId",
                table: "CorrectiveActions",
                column: "FindingId",
                principalTable: "Findings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CorrectiveActions_Users_AssignedToUserId",
                table: "CorrectiveActions",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Findings_AuditAnswers_AuditAnswerId",
                table: "Findings",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Findings_Users_ReportedByUserId",
                table: "Findings",
                column: "ReportedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_QuestionGroups_AuditTemplates_AuditTemplateId",
                table: "QuestionGroups",
                column: "AuditTemplateId",
                principalTable: "AuditTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_QuestionOptions_Questions_QuestionId",
                table: "QuestionOptions",
                column: "QuestionId",
                principalTable: "Questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_AuditTemplates_AuditTemplateId",
                table: "Questions",
                column: "AuditTemplateId",
                principalTable: "AuditTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_QuestionGroups_QuestionGroupId",
                table: "Questions",
                column: "QuestionGroupId",
                principalTable: "QuestionGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_Questions_ParentQuestionId",
                table: "Questions",
                column: "ParentQuestionId",
                principalTable: "Questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_RecurrenceRules_RecurringAuditSettings_RecurringAuditSettingId",
                table: "RecurrenceRules",
                column: "RecurringAuditSettingId",
                principalTable: "RecurringAuditSettings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RecurringAuditSettings_AuditTemplates_AuditTemplateId",
                table: "RecurringAuditSettings",
                column: "AuditTemplateId",
                principalTable: "AuditTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_RecurringAuditSettings_UserGroups_AssignToUserGroupId",
                table: "RecurringAuditSettings",
                column: "AssignToUserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RecurringAuditSettings_Users_AssignToUserId",
                table: "RecurringAuditSettings",
                column: "AssignToUserId",
                principalTable: "Users",
                principalColumn: "Id");
        }
    }
}
