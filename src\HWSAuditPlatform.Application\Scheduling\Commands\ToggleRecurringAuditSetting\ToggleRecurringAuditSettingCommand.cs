using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Scheduling.Commands.ToggleRecurringAuditSetting;

/// <summary>
/// Command to pause or resume a recurring audit setting
/// </summary>
public class ToggleRecurringAuditSettingCommand : BaseCommand
{
    /// <summary>
    /// The ID of the recurring audit setting to toggle
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Whether to enable (true) or disable (false) the recurring audit setting
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }

    public ToggleRecurringAuditSettingCommand(string id, bool isEnabled, int recordVersion)
    {
        Id = id;
        IsEnabled = isEnabled;
        RecordVersion = recordVersion;
    }
}
