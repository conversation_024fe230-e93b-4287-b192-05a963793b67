﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ChangingForeignKeyToAdGuID : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "hwsap_wfl_audit_logs",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_usr_users",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_usr_users",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AdObjectGuid",
                table: "hwsap_usr_users",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_usr_groups",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_usr_groups",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "AdObjectGuid",
                table: "hwsap_usr_groups",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_questions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_questions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_question_options",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_question_options",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_question_groups",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_question_groups",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_audit_templates",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_audit_templates",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_sub_areas",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_factories",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_factories",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_areas",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_areas",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReportedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AssignedToUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReviewedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "AssignedToUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_attachments",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_attachments",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_answers",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_answers",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_answer_failure_reasons",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_answer_failure_reasons",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_hwsap_usr_users_AdObjectGuid",
                table: "hwsap_usr_users",
                column: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings",
                column: "ReportedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AssignToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "RequestedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs",
                column: "UserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_hwsap_usr_users_AdObjectGuid",
                table: "hwsap_usr_users");

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "hwsap_wfl_audit_logs",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_usr_users",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_usr_users",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AdObjectGuid",
                table: "hwsap_usr_users",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_usr_groups",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_usr_groups",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "AdObjectGuid",
                table: "hwsap_usr_groups",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_questions",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_questions",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_question_options",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_question_options",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_question_groups",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_question_groups",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_tpl_audit_templates",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_tpl_audit_templates",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_sub_areas",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_factories",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_factories",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_org_areas",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_org_areas",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReportedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AssignedToUserId",
                table: "hwsap_fnd_corrective_actions",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReviewedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "AssignedToUserId",
                table: "hwsap_aud_audits",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_attachments",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_attachments",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_answers",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_answers",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "UpdatedByUserId",
                table: "hwsap_aud_answer_failure_reasons",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedByUserId",
                table: "hwsap_aud_answer_failure_reasons",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings",
                column: "ReportedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AssignToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "RequestedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs",
                column: "UserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");
        }
    }
}
