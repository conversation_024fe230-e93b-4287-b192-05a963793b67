﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class FixAddressHasValueComputedColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "Address_HasValue",
                table: "hwsap_org_factories",
                type: "bit",
                nullable: true,
                computedColumnSql: "CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END",
                stored: true,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true,
                oldComputedColumnSql: "CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN 1 ELSE 0 END",
                oldStored: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "Address_HasValue",
                table: "hwsap_org_factories",
                type: "bit",
                nullable: true,
                computedColumnSql: "CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN 1 ELSE 0 END",
                stored: true,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true,
                oldComputedColumnSql: "CASE WHEN [AddressLine1] IS NOT NULL OR [City] IS NOT NULL OR [PostalCode] IS NOT NULL THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END",
                oldStored: true);
        }
    }
}
