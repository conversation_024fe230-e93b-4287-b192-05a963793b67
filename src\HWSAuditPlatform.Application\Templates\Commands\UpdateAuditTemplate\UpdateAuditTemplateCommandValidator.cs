using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;

/// <summary>
/// Validator for UpdateAuditTemplateCommand
/// </summary>
public class UpdateAuditTemplateCommandValidator : AbstractValidator<UpdateAuditTemplateCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateAuditTemplateCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Template ID must be greater than 0");

        RuleFor(x => x.TemplateName)
            .NotEmpty()
            .WithMessage("Template name is required")
            .MaximumLength(255)
            .WithMessage("Template name cannot exceed 255 characters")
            .MustAsync(BeUniqueTemplateName)
            .WithMessage("A template with this name and version already exists");

        RuleFor(x => x.Description)
            .MaximumLength(2000)
            .WithMessage("Description cannot exceed 2000 characters");

        RuleFor(x => x.Version)
            .GreaterThan(0)
            .WithMessage("Version must be greater than 0");

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0)
            .WithMessage("Record version must be greater than 0");
    }

    private async Task<bool> BeUniqueTemplateName(UpdateAuditTemplateCommand command, string templateName, CancellationToken cancellationToken)
    {
        return !await _context.AuditTemplates.AnyAsync(
            t => t.TemplateName == templateName && 
                 t.Version == command.Version && 
                 t.Id != command.Id,
            cancellationToken);
    }
}
