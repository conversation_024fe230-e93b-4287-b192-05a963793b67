services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: hwsaudit-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=HWSAudit123!
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./docker/sql-init:/docker-entrypoint-initdb.d
    networks:
      - hwsaudit-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P HWSAudit123! -Q 'SELECT 1' -C"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Simple LDAP Server for development
  openldap:
    image: osixia/openldap:1.5.0
    container_name: hwsaudit-ad
    environment:
      - LDAP_ORGANISATION=HWS Audit Platform
      - LDAP_DOMAIN=hwsaudit.local
      - LDAP_ADMIN_PASSWORD=HWSAudit123!
      - LDAP_CONFIG_PASSWORD=HWSAudit123!
      - LDAP_READONLY_USER=false
      - LDAP_RFC2307BIS_SCHEMA=false
      - LDAP_BACKEND=mdb
      - LDAP_TLS=false
      - LDAP_REPLICATION=false
      - KEEP_EXISTING_CONFIG=false
      - LDAP_REMOVE_CONFIG_AFTER_SETUP=true
      - LDAP_LOG_LEVEL=256
    ports:
      - "389:389"
    volumes:
      - ldap_data:/var/lib/ldap
      - ldap_config:/etc/ldap/slapd.d
    networks:
      hwsaudit-network:
        ipv4_address: ***********
    restart: unless-stopped
    hostname: ldap
    domainname: hwsaudit.local
    healthcheck:
      test: ["CMD-SHELL", "ldapsearch -x -H ldap://localhost -b dc=hwsaudit,dc=local -D cn=admin,dc=hwsaudit,dc=local -w HWSAudit123! '(objectClass=*)' dn || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  sqlserver_data:
    driver: local
  ldap_data:
    driver: local
  ldap_config:
    driver: local

networks:
  hwsaudit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
