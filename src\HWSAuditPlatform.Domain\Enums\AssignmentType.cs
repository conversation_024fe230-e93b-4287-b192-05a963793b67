namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the assignment type for audits and recurring audit settings.
/// Maps to the assignment_type enum in the database.
/// </summary>
public enum AssignmentType
{
    /// <summary>
    /// Assigned to a specific user
    /// </summary>
    Individual,

    /// <summary>
    /// Assigned to a group; any one member can claim and complete the audit
    /// </summary>
    GroupAny,

    /// <summary>
    /// Scheduler creates individual audits for each member of the specified group
    /// </summary>
    GroupAllScheduled
}
