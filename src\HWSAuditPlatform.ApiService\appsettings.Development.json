{"Logging": {"LogLevel": {"Default": "Information", "System": "Information", "Microsoft": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Critical", "Microsoft.EntityFrameworkCore.Query": "Warning", "Microsoft.EntityFrameworkCore.Update": "Warning", "HWSAuditPlatform": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost,1433;Database=HWSAuditPlatformDb;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true", "SupportConnection": "Server=localhost,1433;Database=HWSAuditPlatformSupport;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "Jwt": {"Key": "DevelopmentKeyThatIsAtLeast32CharactersLongForTesting!", "Issuer": "HWSAuditPlatform-Dev", "Audience": "HWSAuditPlatformUsers-Dev"}, "FileStorage": {"Type": "Local"}, "LocalFileStorage": {"StoragePath": "D:\\uploads", "BaseUrl": "https://localhost:5001"}, "ActiveDirectory": {"Domain": "hwsaudit.local", "Username": "<EMAIL>", "Password": "************", "SearchBase": "OU=Users,OU=HWSAudit,DC=hwsaudit,DC=local", "UseSSL": false, "Port": 389, "TimeoutSeconds": 30, "Server": "localhost"}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "http://localhost:3000", "https://localhost:5173", "http://localhost:5173", "https://localhost:4200", "http://localhost:4200", "http://localhost:5375", "https://localhost:5375", "https://localhost:7134", "http://localhost:5219", "https://localhost:7144", "http://localhost:5112"]}, "ApiSettings": {"MaxPageSize": 50, "DefaultPageSize": 10, "MaxFileSize": 5242880, "AllowedFileTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain"]}, "DetailedErrors": true, "EnableSensitiveDataLogging": true}