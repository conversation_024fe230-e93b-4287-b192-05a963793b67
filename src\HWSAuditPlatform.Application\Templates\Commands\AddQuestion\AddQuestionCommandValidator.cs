using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

/// <summary>
/// Validator for AddQuestionCommand
/// </summary>
public class AddQuestionCommandValidator : AbstractValidator<AddQuestionCommand>
{
    private readonly IApplicationDbContext _context;

    public AddQuestionCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0).WithMessage("Audit template ID must be greater than 0")
            .MustAsync(BeValidAuditTemplate).WithMessage("Audit template does not exist or is published");

        RuleFor(x => x.QuestionGroupId)
            .MustAsync(BeValidQuestionGroup).WithMessage("Question group does not exist or does not belong to the template")
            .When(x => x.QuestionGroupId.HasValue);

        RuleFor(x => x.QuestionText)
            .NotEmpty().WithMessage("Question text is required")
            .MaximumLength(1000).WithMessage("Question text cannot exceed 1000 characters");

        RuleFor(x => x.QuestionType)
            .IsInEnum().WithMessage("Question type must be valid");

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Display order must be greater than or equal to 0");

        RuleFor(x => x.Weight)
            .GreaterThan(0).WithMessage("Weight must be greater than 0")
            .When(x => x.Weight.HasValue);

        RuleFor(x => x.HelpText)
            .MaximumLength(2000).WithMessage("Help text cannot exceed 2000 characters");

        RuleFor(x => x.ParentQuestionId)
            .MustAsync(BeValidParentQuestion).WithMessage("Parent question does not exist or does not belong to the template")
            .When(x => x.ParentQuestionId.HasValue);

        RuleFor(x => x.TriggerAnswerValue)
            .NotEmpty().WithMessage("Trigger answer value is required for conditional questions")
            .MaximumLength(255).WithMessage("Trigger answer value cannot exceed 255 characters")
            .When(x => x.ParentQuestionId.HasValue);

        RuleFor(x => x.AllowedEvidenceTypes)
            .Must(HaveUniqueEvidenceTypes).WithMessage("Evidence types must be unique for a question");

        RuleFor(x => x.Options)
            .Must(HaveOptionsForSelectTypes).WithMessage("Select-type questions must have at least one option")
            .When(x => x.QuestionType == QuestionType.SingleSelect || x.QuestionType == QuestionType.MultiSelect);

        RuleForEach(x => x.Options).SetValidator(new AddQuestionOptionValidator());
    }

    private async Task<bool> BeValidAuditTemplate(int auditTemplateId, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == auditTemplateId, cancellationToken);
        return template != null && !template.IsPublished; // Can only add questions to unpublished templates
    }

    private async Task<bool> BeValidQuestionGroup(AddQuestionCommand command, int? questionGroupId, CancellationToken cancellationToken)
    {
        if (!questionGroupId.HasValue) return true;

        return await _context.QuestionGroups.AnyAsync(
            g => g.Id == questionGroupId.Value && g.AuditTemplateId == command.AuditTemplateId,
            cancellationToken);
    }

    private async Task<bool> BeValidParentQuestion(AddQuestionCommand command, int? parentQuestionId, CancellationToken cancellationToken)
    {
        if (!parentQuestionId.HasValue) return true;

        return await _context.Questions.AnyAsync(
            q => q.Id == parentQuestionId.Value && q.AuditTemplateId == command.AuditTemplateId,
            cancellationToken);
    }

    private static bool HaveOptionsForSelectTypes(AddQuestionCommand command, List<AddQuestionOptionRequest> options)
    {
        if (command.QuestionType == QuestionType.SingleSelect || command.QuestionType == QuestionType.MultiSelect)
        {
            return options.Count > 0;
        }
        return true;
    }

    private static bool HaveUniqueEvidenceTypes(List<AddEvidenceTypeRequest> evidenceTypes)
    {
        if (evidenceTypes == null || evidenceTypes.Count == 0) return true;

        var uniqueTypes = evidenceTypes.Select(et => et.EvidenceType).Distinct().Count();
        return uniqueTypes == evidenceTypes.Count;
    }
}

/// <summary>
/// Validator for AddQuestionOptionRequest
/// </summary>
public class AddQuestionOptionValidator : AbstractValidator<AddQuestionOptionRequest>
{
    public AddQuestionOptionValidator()
    {
        RuleFor(x => x.OptionText)
            .NotEmpty().WithMessage("Option text is required")
            .MaximumLength(255).WithMessage("Option text cannot exceed 255 characters");

        RuleFor(x => x.OptionValue)
            .MaximumLength(255).WithMessage("Option value cannot exceed 255 characters");

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Display order must be greater than or equal to 0");
    }
}
