using System.ComponentModel.DataAnnotations;

namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Represents the type of responsibility assigned to users for specific areas
/// </summary>
public enum ResponsibilityType
{
    CorrectiveActionOwner,
    RetrospectiveAnalyst,
    FindingReviewer,
    EscalationContact
}

/// <summary>
/// Model for area responsibility data
/// </summary>
public class AreaResponsibilityModel
{
    public string Id { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public ResponsibilityType ResponsibilityType { get; set; }
    public string ResponsibilityTypeName { get; set; } = string.Empty;
    public string ResponsibleUserId { get; set; } = string.Empty;
    public string? ResponsibleUserName { get; set; }
    public string? ResponsibleUserFullName { get; set; }
    public int? AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public int Priority { get; set; }
    public bool IsTemplateSpecific { get; set; }
    public bool IsEffective { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int RecordVersion { get; set; }
    public string? CreatedByUserId { get; set; }
    public string? UpdatedByUserId { get; set; }
}

/// <summary>
/// Model for creating area responsibility assignments
/// </summary>
public class CreateAreaResponsibilityModel
{
    [Required(ErrorMessage = "Area is required")]
    public int AreaId { get; set; }

    [Required(ErrorMessage = "Responsibility type is required")]
    public ResponsibilityType ResponsibilityType { get; set; }

    [Required(ErrorMessage = "Responsible user is required")]
    public string ResponsibleUserId { get; set; } = string.Empty;

    public int? AuditTemplateId { get; set; }

    public bool IsActive { get; set; } = true;

    [MaxLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Priority must be 0 or greater")]
    public int Priority { get; set; } = 0;
}

/// <summary>
/// Model for responsibility type information
/// </summary>
public class ResponsibilityTypeModel
{
    public int Value { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Model for area responsibility filters
/// </summary>
public class AreaResponsibilityFilterModel
{
    public int? AreaId { get; set; }
    public int? AuditTemplateId { get; set; }
    public ResponsibilityType? ResponsibilityType { get; set; }
    public string? ResponsibleUserId { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool IncludeTemplateSpecific { get; set; } = true;
    public bool IncludeGeneral { get; set; } = true;
}

/// <summary>
/// Model for area responsibility summary (for dropdowns and lists)
/// </summary>
public class AreaResponsibilitySummaryModel
{
    public string Id { get; set; } = string.Empty;
    public string AreaName { get; set; } = string.Empty;
    public string ResponsibilityTypeName { get; set; } = string.Empty;
    public string ResponsibleUserName { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public bool IsActive { get; set; }
    public bool IsTemplateSpecific { get; set; }
}
