using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Repositories;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Infrastructure.Repositories;

public class AuditRepositoryTests : BaseDbTestClass
{
    private readonly AuditRepository _repository;

    public AuditRepositoryTests()
    {
        _repository = new AuditRepository(Context);
    }

    [Fact]
    public async Task GetAuditsWithDetailsAsync_ShouldReturnAuditsWithRelatedData()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();

        // Act
        var result = await _repository.GetAuditsWithDetailsAsync();

        // Assert
        result.Should().NotBeEmpty();
        var auditResult = result.First(a => a.Id == audit.Id);
        auditResult.AuditTemplate.Should().NotBeNull();
        auditResult.Factory.Should().NotBeNull();
        auditResult.Area.Should().NotBeNull();
    }

    [Fact]
    public async Task GetAuditWithFullDetailsAsync_ShouldReturnAuditWithAllRelatedData()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithAnswersAsync();

        // Act
        var result = await _repository.GetAuditWithFullDetailsAsync(audit.Id);

        // Assert
        result.Should().NotBeNull();
        result!.AuditTemplate.Should().NotBeNull();
        result.Factory.Should().NotBeNull();
        result.Area.Should().NotBeNull();
        result.Answers.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetAuditsByUserAsync_ShouldReturnUserAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        var userId = "test-user-id";
        var audit = await CreateTestAuditAsync(assignedToUserId: userId);

        // Act
        var result = await _repository.GetAuditsByUserAsync(userId);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(a => a.AssignedToUserId == userId);
    }

    [Fact]
    public async Task GetAuditsByUserAsync_WithStatus_ShouldFilterByStatus()
    {
        // Arrange
        await SeedTestDataAsync();
        var userId = "test-user-id";
        await CreateTestAuditAsync(assignedToUserId: userId, status: AuditOverallStatus.Scheduled);
        await CreateTestAuditAsync(assignedToUserId: userId, status: AuditOverallStatus.InProgress);

        // Act
        var result = await _repository.GetAuditsByUserAsync(userId, AuditOverallStatus.InProgress);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(a => a.OverallStatus == AuditOverallStatus.InProgress);
    }

    [Fact]
    public async Task GetOverdueAuditsAsync_ShouldReturnOverdueAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateOverdueAuditAsync();
        await CreateTestAuditAsync(); // Not overdue

        // Act
        var result = await _repository.GetOverdueAuditsAsync();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(a => a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow);
    }

    [Fact]
    public async Task GetAuditsByFactoryAsync_ShouldReturnFactoryAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        var factoryId = 1;
        await CreateTestAuditAsync(factoryId: factoryId);
        await CreateTestAuditAsync(factoryId: 2); // Different factory

        // Act
        var result = await _repository.GetAuditsByFactoryAsync(factoryId);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(a => a.FactoryId == factoryId);
    }

    [Fact]
    public async Task GetAuditsByFactoryAsync_WithDateRange_ShouldFilterByDateRange()
    {
        // Arrange
        await SeedTestDataAsync();
        var factoryId = 1;
        var fromDate = DateTime.UtcNow.AddDays(-1);
        var toDate = DateTime.UtcNow.AddDays(1);
        
        await CreateTestAuditAsync(factoryId: factoryId, scheduledDate: DateTime.UtcNow);
        await CreateTestAuditAsync(factoryId: factoryId, scheduledDate: DateTime.UtcNow.AddDays(-5)); // Outside range

        // Act
        var result = await _repository.GetAuditsByFactoryAsync(factoryId, fromDate, toDate);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().OnlyContain(a => a.ScheduledDate >= fromDate && a.ScheduledDate <= toDate);
    }

    [Fact]
    public async Task GetAuditCountByStatusAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditAsync(status: AuditOverallStatus.InProgress);
        await CreateTestAuditAsync(status: AuditOverallStatus.InProgress);
        await CreateTestAuditAsync(status: AuditOverallStatus.Scheduled);

        // Act
        var result = await _repository.GetAuditCountByStatusAsync(AuditOverallStatus.InProgress);

        // Assert
        result.Should().Be(2);
    }

    [Fact]
    public async Task GetPagedAuditsWithFiltersAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateMultipleTestAuditsAsync(15);

        // Act
        var result = await _repository.GetPagedAuditsWithFiltersAsync(
            pageNumber: 2,
            pageSize: 5);

        // Assert
        result.Items.Should().HaveCount(5);
        result.TotalCount.Should().Be(15);
    }

    [Fact]
    public async Task GetPagedAuditsWithFiltersAsync_WithSearchTerm_ShouldFilterResults()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditAsync(templateName: "Safety Audit");
        await CreateTestAuditAsync(templateName: "Quality Audit");

        // Act
        var result = await _repository.GetPagedAuditsWithFiltersAsync(
            pageNumber: 1,
            pageSize: 10,
            searchTerm: "Safety");

        // Assert
        result.Items.Should().NotBeEmpty();
        result.Items.Should().OnlyContain(a => a.AuditTemplate.TemplateName.Contains("Safety"));
    }

    private async Task<Audit> CreateTestAuditAsync(
        string assignedToUserId = "test-user-id",
        AuditOverallStatus status = AuditOverallStatus.Scheduled,
        int factoryId = 1,
        DateTime? scheduledDate = null,
        string templateName = "Test Template")
    {
        var template = new AuditTemplate
        {
            TemplateName = templateName,
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = assignedToUserId,
            ScheduledDate = scheduledDate ?? DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = status,
            FactoryId = factoryId,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }

    private async Task<Audit> CreateTestAuditWithAnswersAsync()
    {
        var audit = await CreateTestAuditAsync();

        var question = new Question
        {
            AuditTemplateId = audit.AuditTemplateId,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();

        var answer = new AuditAnswer
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerBoolean = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditAnswers.Add(answer);
        await Context.SaveChangesAsync();

        return audit;
    }

    private async Task<Audit> CreateOverdueAuditAsync()
    {
        return await CreateTestAuditAsync(
            status: AuditOverallStatus.InProgress,
            scheduledDate: DateTime.UtcNow.AddDays(-5));
    }

    private async Task CreateMultipleTestAuditsAsync(int count)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Bulk Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audits = new List<Audit>();
        for (int i = 0; i < count; i++)
        {
            audits.Add(new Audit
            {
                Id = $"c{Guid.NewGuid():N}"[..25],
                AuditTemplateId = template.Id,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "test-user-id",
                ScheduledDate = DateTime.UtcNow.AddDays(i),
                DueDate = DateTime.UtcNow.AddDays(i + 7),
                OverallStatus = AuditOverallStatus.Scheduled,
                FactoryId = 1,
                AreaId = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        Context.Audits.AddRange(audits);
        await Context.SaveChangesAsync();
    }
}
