using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Organization;

/// <summary>
/// Represents a responsibility assignment for a specific area.
/// Maps to the AreaResponsibilities table in the database.
/// </summary>
public class AreaResponsibility : AuditableEntity<string>
{
    /// <summary>
    /// Links to the organizational area (FK)
    /// </summary>
    public int AreaId { get; set; }

    /// <summary>
    /// Navigation property for the area
    /// </summary>
    public virtual Area Area { get; set; } = null!;

    /// <summary>
    /// Type of responsibility assigned
    /// </summary>
    public ResponsibilityType ResponsibilityType { get; set; }

    /// <summary>
    /// User responsible for this area and responsibility type (AD ObjectGUID FK)
    /// </summary>
    [Required]
    [MaxLength(36)]
    public string ResponsibleUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the responsible user
    /// </summary>
    public virtual User ResponsibleUser { get; set; } = null!;

    /// <summary>
    /// Optional: Links to specific audit template (null means applies to all templates)
    /// </summary>
    public int? AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template (optional)
    /// </summary>
    public virtual AuditTemplate? AuditTemplate { get; set; }

    /// <summary>
    /// Indicates if this responsibility assignment is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Optional description or notes about this responsibility assignment
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Priority level for this responsibility (lower number = higher priority)
    /// Used when multiple users have the same responsibility type for an area
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Indicates if this responsibility is template-specific
    /// </summary>
    public bool IsTemplateSpecific => AuditTemplateId.HasValue;

    /// <summary>
    /// Indicates if this responsibility is currently effective
    /// </summary>
    public bool IsEffective => IsActive;
}
