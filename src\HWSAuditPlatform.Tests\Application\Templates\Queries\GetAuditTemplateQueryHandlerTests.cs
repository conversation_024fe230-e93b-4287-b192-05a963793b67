using FluentAssertions;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Queries;

public class GetAuditTemplateQueryHandlerTests : BaseDbTestClass
{
    private readonly GetAuditTemplateQueryHandler _handler;

    public GetAuditTemplateQueryHandlerTests()
    {
        _handler = new GetAuditTemplateQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(template.Id);
        result.TemplateName.Should().Be(template.TemplateName);
        result.Description.Should().Be(template.Description);
        result.Version.Should().Be(template.Version);
        result.IsPublished.Should().Be(template.IsPublished);
        result.IsActive.Should().Be(template.IsActive);
        result.FullName.Should().Be(template.FullName);
        result.CanBeUsed.Should().Be(template.CanBeUsed);
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetAuditTemplateQuery(999);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithTemplateWithQuestions_ShouldReturnQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Questions.Should().NotBeEmpty();
        result.Questions.Should().HaveCount(2);
        result.Questions.Should().BeInAscendingOrder(q => q.DisplayOrder);
    }

    [Fact]
    public async Task Handle_WithTemplateWithQuestionGroups_ShouldReturnGroups()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithGroupsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.QuestionGroups.Should().NotBeEmpty();
        result.QuestionGroups.Should().HaveCount(1);
        result.QuestionGroups.First().Questions.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Handle_WithQuestionOptions_ShouldReturnOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithOptionsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        var questionWithOptions = result.Questions.First(q => q.QuestionType == QuestionType.SingleSelect);
        questionWithOptions.Options.Should().NotBeEmpty();
        questionWithOptions.Options.Should().HaveCount(3);
        questionWithOptions.Options.Should().BeInAscendingOrder(o => o.DisplayOrder);
    }

    [Fact]
    public async Task Handle_WithConditionalQuestions_ShouldReturnChildQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithConditionalQuestionsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        var parentQuestion = result.Questions.First(q => q.ParentQuestionId == null);
        parentQuestion.ChildQuestions.Should().NotBeEmpty();
        parentQuestion.ChildQuestions.Should().HaveCount(1);
        
        var childQuestion = parentQuestion.ChildQuestions.First();
        childQuestion.ParentQuestionId.Should().Be(parentQuestion.Id);
        childQuestion.TriggerAnswerValue.Should().Be("Yes");
        childQuestion.IsConditional.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldReturnAuditTrailFields()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CreatedAt.Should().Be(template.CreatedAt);
        result.UpdatedAt.Should().Be(template.UpdatedAt);
        result.CreatedByUserId.Should().Be(template.CreatedByUserId);
        result.UpdatedByUserId.Should().Be(template.UpdatedByUserId);
        result.RecordVersion.Should().Be(template.RecordVersion);
    }

    [Fact]
    public async Task Handle_WithInactiveQuestions_ShouldExcludeInactiveQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithInactiveQuestionsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Questions.Should().OnlyContain(q => q.IsActive);
        result.Questions.Should().HaveCount(1); // Only active question
    }

    [Fact]
    public async Task Handle_WithQuestionDetails_ShouldReturnAllQuestionProperties()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithDetailedQuestionsAsync();
        
        var query = new GetAuditTemplateQuery(template.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        var detailedQuestion = result.Questions.First();
        
        detailedQuestion.QuestionText.Should().Be("Detailed safety question");
        detailedQuestion.QuestionType.Should().Be(QuestionType.YesNo);
        detailedQuestion.IsRequired.Should().BeTrue();
        detailedQuestion.Weight.Should().Be(10.5m);
        detailedQuestion.HelpText.Should().Be("Check all safety protocols");
        detailedQuestion.SeverityLevel.Should().Be(SeverityLevel.Major);
        detailedQuestion.EvidenceRequired.Should().BeTrue();
        detailedQuestion.AllowedEvidenceTypes.Should().HaveCount(2);
        detailedQuestion.AllowedEvidenceTypes.Should().Contain(et => et.EvidenceType == EvidenceType.Image);
        detailedQuestion.AllowedEvidenceTypes.Should().Contain(et => et.EvidenceType == EvidenceType.Document);
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithQuestionsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var questions = new[]
        {
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "First Question",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Second Question",
                QuestionType = QuestionType.ShortText,
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.Questions.AddRange(questions);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithGroupsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var group = new QuestionGroup
        {
            AuditTemplateId = template.Id,
            GroupName = "Safety Group",
            Description = "Safety related questions",
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.QuestionGroups.Add(group);
        await Context.SaveChangesAsync();

        var question = new Question
        {
            AuditTemplateId = template.Id,
            QuestionGroupId = group.Id,
            QuestionText = "Group Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithOptionsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var question = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Select Question",
            QuestionType = QuestionType.SingleSelect,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();

        var options = new[]
        {
            new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = "Option 1",
                OptionValue = "opt1",
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = "Option 2",
                OptionValue = "opt2",
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = "Option 3",
                OptionValue = "opt3",
                DisplayOrder = 3,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }
        };

        Context.QuestionOptions.AddRange(options);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithConditionalQuestionsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var parentQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Is equipment present?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(parentQuestion);
        await Context.SaveChangesAsync();

        var childQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "What is the equipment condition?",
            QuestionType = QuestionType.ShortText,
            DisplayOrder = 2,
            ParentQuestionId = parentQuestion.Id,
            TriggerAnswerValue = "Yes",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(childQuestion);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithInactiveQuestionsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var questions = new[]
        {
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Active Question",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Inactive Question",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 2,
                IsActive = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.Questions.AddRange(questions);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithDetailedQuestionsAsync()
    {
        var template = await CreateTestTemplateAsync();

        var question = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Detailed safety question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            Weight = 10.5m,
            HelpText = "Check all safety protocols",
            SeverityLevel = SeverityLevel.Major,
            EvidenceRequired = true,
            AllowedEvidenceTypes = new List<QuestionAllowedEvidenceType>
            {
                new() { EvidenceType = EvidenceType.Image, DisplayOrder = 0, IsActive = true },
                new() { EvidenceType = EvidenceType.Document, DisplayOrder = 1, IsActive = true }
            },
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return template;
    }
}
