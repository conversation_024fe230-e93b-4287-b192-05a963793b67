using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for template API operations
/// </summary>
public interface ITemplateApiService
{
    /// <summary>
    /// Gets templates with optional search and filtering
    /// </summary>
    Task<IEnumerable<AuditTemplate>> GetTemplatesAsync(string? searchTerm = null, bool? isActive = null, int pageSize = 100);

    /// <summary>
    /// Gets a template by ID
    /// </summary>
    Task<AuditTemplate?> GetTemplateByIdAsync(int id);

    /// <summary>
    /// Creates a new template
    /// </summary>
    Task<AuditTemplate> CreateTemplateAsync(AuditTemplate template);

    /// <summary>
    /// Creates a new template using request model
    /// </summary>
    Task<int?> CreateTemplateAsync(CreateAuditTemplateRequest request);

    /// <summary>
    /// Updates an existing template
    /// </summary>
    Task<AuditTemplate> UpdateTemplateAsync(AuditTemplate template);

    /// <summary>
    /// Updates an existing template using request model
    /// </summary>
    Task<bool> UpdateTemplateAsync(int id, UpdateAuditTemplateRequest request);

    /// <summary>
    /// Creates a new version of an existing template
    /// </summary>
    Task<int?> CreateTemplateVersionAsync(int originalTemplateId, string? newTemplateName = null, string? newDescription = null);

    /// <summary>
    /// Publishes a template to make it available for use
    /// </summary>
    Task<bool> PublishTemplateAsync(int id);

    /// <summary>
    /// Publishes a template with detailed error information
    /// </summary>
    Task<PublishTemplateResult> PublishTemplateWithDetailsAsync(int id);

    /// <summary>
    /// Deletes a template
    /// </summary>
    Task<bool> DeleteTemplateAsync(int id);

    /// <summary>
    /// Adds a question to a template
    /// </summary>
    Task<int?> AddQuestionToTemplateAsync(int templateId, CreateQuestionRequest request);

    /// <summary>
    /// Updates a question
    /// </summary>
    Task<bool> UpdateQuestionAsync(int questionId, UpdateQuestionRequest request);

    /// <summary>
    /// Deletes a question
    /// </summary>
    Task<bool> DeleteQuestionAsync(int questionId);

    /// <summary>
    /// Updates template feature flags (area-based responsibility and finding categorization)
    /// </summary>
    Task<bool> UpdateTemplateFeatureFlagsAsync(int templateId, bool enableAreaBasedResponsibility, bool enableFindingCategorization);

    /// <summary>
    /// Gets templates that have area-based responsibility enabled
    /// </summary>
    Task<IEnumerable<AuditTemplate>> GetTemplatesWithAreaResponsibilityAsync();

    /// <summary>
    /// Gets templates that have finding categorization enabled
    /// </summary>
    Task<IEnumerable<AuditTemplate>> GetTemplatesWithCategorizationAsync();
}

/// <summary>
/// Result of template publishing operation
/// </summary>
public class PublishTemplateResult
{
    public bool IsSuccess { get; private set; }
    public List<string> ErrorMessages { get; private set; } = new();
    public bool IsValidationError { get; private set; }

    private PublishTemplateResult() { }

    public static PublishTemplateResult Success()
    {
        return new PublishTemplateResult { IsSuccess = true };
    }

    public static PublishTemplateResult ValidationFailed(List<string> errorMessages)
    {
        return new PublishTemplateResult
        {
            IsSuccess = false,
            ErrorMessages = errorMessages,
            IsValidationError = true
        };
    }

    public static PublishTemplateResult Failed(string errorMessage)
    {
        return new PublishTemplateResult
        {
            IsSuccess = false,
            ErrorMessages = new List<string> { errorMessage },
            IsValidationError = false
        };
    }

    public string GetErrorMessage()
    {
        return ErrorMessages.Any() ? string.Join("; ", ErrorMessages) : "Unknown error occurred";
    }

    public string GetFormattedErrorMessage()
    {
        if (!ErrorMessages.Any()) return "Unknown error occurred";

        if (ErrorMessages.Count == 1)
        {
            return ErrorMessages[0];
        }

        return $"Multiple errors occurred:\n• {string.Join("\n• ", ErrorMessages)}";
    }
}
