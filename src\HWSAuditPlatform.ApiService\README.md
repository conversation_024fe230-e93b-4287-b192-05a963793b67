# HWS Audit Platform - API Service

## Overview

The HWS Audit Platform API Service provides a comprehensive RESTful API for managing manufacturing quality audits. Built with ASP.NET Core, it implements clean architecture principles with CQRS pattern, JWT authentication, and enterprise-grade security features.

## Features

### 🔐 **Authentication & Authorization**

- JWT token-based authentication
- Active Directory integration
- Role-based access control (Admin, Manager, Auditor)
- Claims-based authorization
- Token refresh functionality

### 📊 **Core Functionality**

- **User Management**: CRUD operations with AD sync
- **Audit Management**: Complete audit lifecycle
- **Organization Structure**: Location/Factory/Area/SubArea hierarchy
- **File Management**: Secure file upload/download with validation
- **Health Monitoring**: Comprehensive health checks

### 🛡️ **Security Features**

- Global exception handling
- Input validation with FluentValidation
- Security headers middleware
- CORS configuration
- Request size limits
- File type validation

### 📈 **Enterprise Features**

- Structured logging with Serilog
- API versioning
- Swagger/OpenAPI documentation
- Health checks for dependencies
- Performance monitoring ready

## Quick Start

### Prerequisites

- .NET 9.0 SDK
- SQL Server (LocalDB for development)
- Visual Studio 2024 or VS Code

### Running the API

```bash
# Clone and navigate to the API project
cd src/HWSAuditPlatform.ApiService

# Restore packages
dotnet restore

# Update database
dotnet ef database update --project ../HWSAuditPlatform.Infrastructure

# Run the API
dotnet run
```

The API will be available at:
- **HTTPS**: https://localhost:5001
- **HTTP**: http://localhost:5000
- **Swagger UI**: https://localhost:5001/swagger

## API Endpoints

### Authentication

```
POST   /api/v1/auth/login          # Authenticate user
POST   /api/v1/auth/refresh        # Refresh JWT token
POST   /api/v1/auth/logout         # Logout user
GET    /api/v1/auth/me             # Get current user info
```

### Users

```
GET    /api/v1/users               # Get paginated users list
GET    /api/v1/users/{id}          # Get user by ID
GET    /api/v1/users/me            # Get current user profile
GET    /api/v1/users/search        # Search users
POST   /api/v1/users               # Create new user
PUT    /api/v1/users/{id}          # Update user
```

### Audits

```
GET    /api/v1/audits              # Get paginated audits list
GET    /api/v1/audits/{id}         # Get audit by ID
GET    /api/v1/audits/my-audits    # Get current user's audits
POST   /api/v1/audits              # Create new audit
POST   /api/v1/audits/{id}/start   # Start audit execution
POST   /api/v1/audits/{id}/submit  # Submit audit for review
POST   /api/v1/audits/{id}/review  # Review and approve audit
```

### Organization

```
GET    /api/v1/organization/locations                    # Get all locations
GET    /api/v1/organization/locations/{id}/factories     # Get factories by location
GET    /api/v1/organization/factories                    # Get all factories
GET    /api/v1/organization/factories/{id}/areas         # Get areas by factory
GET    /api/v1/organization/areas/{id}/subareas          # Get sub-areas by area
GET    /api/v1/organization/hierarchy                    # Get complete hierarchy
POST   /api/v1/organization/factories                    # Create factory
POST   /api/v1/organization/factories/{id}/areas         # Create area
POST   /api/v1/organization/areas/{id}/subareas          # Create sub-area
```

### Files

```
POST   /api/v1/files/upload                # Upload file
GET    /api/v1/files/download/{*filePath}  # Download file
GET    /api/v1/files/download-url/{*filePath} # Get temporary download URL
DELETE /api/v1/files/{*filePath}           # Delete file
```

### Health

```
GET    /health                     # Health check endpoint
```

## Authentication

### Login

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "john.doe",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "username": "john.doe",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "role": "Auditor",
    "factoryId": "1",
    "expiresAt": "2024-01-15T16:00:00Z"
  },
  "message": "Login successful"
}
```

### Using JWT Token

Include the JWT token in the Authorization header:
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Configuration

### Development Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true"
  },
  "Jwt": {
    "Key": "YourSecretKeyHere",
    "Issuer": "HWSAuditPlatform",
    "Audience": "HWSAuditPlatformUsers"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

### Production Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=HWSAuditPlatformDb;User Id=app-user;Password=***;TrustServerCertificate=true"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "/app/uploads",
    "BaseUrl": "https://your-domain.com"
  },
  "ActiveDirectory": {
    "Domain": "company.local",
    "Username": "<EMAIL>",
    "Password": "***",
    "SearchBase": "OU=Users,DC=company,DC=local"
  }
}
```

## Error Handling

The API uses a consistent error response format:

```json
{
  "statusCode": 400,
  "title": "Validation Error",
  "detail": "One or more validation errors occurred",
  "errors": {
    "Username": ["Username is required"],
    "Email": ["Email must be a valid email address"]
  },
  "traceId": "0HMVB9LBQK7QH:00000001",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes

- **200 OK**: Successful GET requests
- **201 Created**: Successful POST requests
- **204 No Content**: Successful PUT/DELETE requests
- **400 Bad Request**: Validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Concurrency conflicts
- **500 Internal Server Error**: Server errors

## File Upload

### Upload File

```http
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

file: [binary data]
```

**Supported File Types:**
- Images: JPEG, PNG, GIF, BMP
- Documents: PDF, DOC, DOCX, XLS, XLSX
- Text: TXT, CSV

**File Size Limit:** 10MB (configurable)

### Download File

```http
GET /api/v1/files/download/2024/01/15/document_abc123.pdf
Authorization: Bearer {token}
```

## Security

### Security Headers

The API automatically adds security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`
- `Content-Security-Policy: default-src 'self'`

### CORS Configuration

CORS is configured to allow requests from:
- `https://localhost:3000` (React dev server)
- `https://localhost:5173` (Vite dev server)
- `https://localhost:4200` (Angular dev server)

### Rate Limiting

Consider implementing rate limiting for production:
```csharp
services.AddRateLimiter(options => {
    options.AddFixedWindowLimiter("api", limiterOptions => {
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.PermitLimit = 100;
    });
});
```

## Monitoring

### Health Checks

The API includes health checks for:
- Database connectivity
- Active Directory connectivity
- File storage accessibility

Access health checks at: `GET /health`

### Logging

Structured logging with Serilog:
- Console output for development
- File output: `logs/hwsaudit-{Date}.txt`
- Configurable log levels
- Request/response logging

### Performance Monitoring

Ready for Application Insights integration:
```csharp
services.AddApplicationInsightsTelemetry();
```

## Development

### Adding New Endpoints

1. Create command/query in Application layer
2. Add controller action with proper authorization
3. Update Swagger documentation
4. Add integration tests

### Testing

```bash
# Run unit tests
dotnet test

# Run integration tests
dotnet test --filter Category=Integration

# Generate test coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Database Migrations

```bash
# Add migration
dotnet ef migrations add MigrationName --project ../HWSAuditPlatform.Infrastructure

# Update database
dotnet ef database update --project ../HWSAuditPlatform.Infrastructure

# Generate SQL script
dotnet ef migrations script --project ../HWSAuditPlatform.Infrastructure
```

## Deployment

### Production Deployment

1. Configure connection strings securely
2. Set up monitoring and logging
3. Configure custom domains and SSL
4. Set up deployment pipelines

## Support

### Documentation

- **Swagger UI**: Available at `/swagger` endpoint
- **API Documentation**: See `docs/` folder
- **Architecture Guide**: See `docs/Architecture-Overview.md`

### Troubleshooting

- Check logs in `logs/` directory
- Verify database connectivity
- Confirm JWT configuration
- Test Active Directory connectivity

This API service provides a robust foundation for the HWS Audit Platform with enterprise-grade security, monitoring, and scalability features.
