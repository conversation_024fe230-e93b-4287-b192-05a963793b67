@page "/organization/area-responsibilities"
@using HWSAuditPlatform.WebApp.Components.Organization
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject IAreaResponsibilityApiService AreaResponsibilityApiService
@inject IOrganizationApiService OrganizationApiService
@inject ITemplateApiService TemplateApiService
@inject ILogger<AreaResponsibilities> Logger

<PageTitle>Area Responsibilities - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Area Responsibilities</h2>
                    <p class="text-muted">Manage area-based responsibility assignments for corrective actions and analysis.</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary me-2" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Filters</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Factory/Area</label>
                        <select class="form-select" @bind="selectedAreaId" @bind:after="OnFilterChanged">
                            <option value="">All Areas</option>
                            @foreach (var area in areas)
                            {
                                <option value="@area.Id">@area.FactoryName - @area.AreaName</option>
                            }
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Audit Template</label>
                        <select class="form-select" @bind="selectedTemplateId" @bind:after="OnFilterChanged">
                            <option value="">All Templates</option>
                            @foreach (var template in templates)
                            {
                                <option value="@template.Id">@template.TemplateName <EMAIL></option>
                            }
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Responsibility Type</label>
                        <select class="form-select" @bind="selectedResponsibilityType" @bind:after="OnFilterChanged">
                            <option value="">All Types</option>
                            @foreach (var type in responsibilityTypes)
                            {
                                <option value="@type.Value">@type.Name</option>
                            }
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="showInactiveOnly" @bind:after="OnFilterChanged" id="showInactive">
                            <label class="form-check-label" for="showInactive">
                                Show inactive only
                            </label>
                        </div>
                    </div>

                    @if (hasActiveFilters)
                    {
                        <button class="btn btn-outline-secondary btn-sm w-100" @onclick="ClearFilters">
                            <i class="fas fa-times"></i> Clear Filters
                        </button>
                    }
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value">@totalResponsibilities</div>
                                <div class="stat-label">Total</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value">@activeResponsibilities</div>
                                <div class="stat-label">Active</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <AreaResponsibilityList 
                FilterAreaId="GetFilterAreaId()"
                FilterTemplateId="GetFilterTemplateId()"
                OnResponsibilityChanged="OnResponsibilityChanged" />
        </div>
    </div>
</div>

<style>
    .stat-item {
        padding: 0.5rem 0;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
    }
</style>

@code {
    private List<AreaSummary> areas = new();
    private List<AuditTemplate> templates = new();
    private List<ResponsibilityTypeModel> responsibilityTypes = new();
    
    private string selectedAreaId = string.Empty;
    private string selectedTemplateId = string.Empty;
    private string selectedResponsibilityType = string.Empty;
    private bool showInactiveOnly = false;
    
    private int totalResponsibilities = 0;
    private int activeResponsibilities = 0;
    private bool hasActiveFilters = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await LoadStatistics();
    }

    private async Task LoadData()
    {
        try
        {
            // Load areas - get all factories first, then get areas for each factory
            var factories = await OrganizationApiService.GetFactoriesAsync();
            var allAreas = new List<AreaSummary>();
            foreach (var factory in factories)
            {
                var factoryAreas = await OrganizationApiService.GetAreasByFactoryAsync(factory.Id);
                allAreas.AddRange(factoryAreas);
            }
            areas = allAreas;

            // Load templates
            templates = (await TemplateApiService.GetTemplatesAsync()).ToList();

            // Load responsibility types
            responsibilityTypes = await AreaResponsibilityApiService.GetResponsibilityTypesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading data for area responsibilities page");
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            var allResponsibilities = await AreaResponsibilityApiService.GetAreaResponsibilitiesAsync();
            totalResponsibilities = allResponsibilities.Count;
            activeResponsibilities = allResponsibilities.Count(r => r.IsActive);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading statistics");
        }
    }

    private void OnFilterChanged()
    {
        hasActiveFilters = !string.IsNullOrEmpty(selectedAreaId) ||
                          !string.IsNullOrEmpty(selectedTemplateId) ||
                          !string.IsNullOrEmpty(selectedResponsibilityType) ||
                          showInactiveOnly;

        StateHasChanged();
    }

    private void ClearFilters()
    {
        selectedAreaId = string.Empty;
        selectedTemplateId = string.Empty;
        selectedResponsibilityType = string.Empty;
        showInactiveOnly = false;
        hasActiveFilters = false;
        StateHasChanged();
    }

    private async Task RefreshData()
    {
        await LoadData();
        await LoadStatistics();
        StateHasChanged();
    }

    private async Task OnResponsibilityChanged()
    {
        await LoadStatistics();
        StateHasChanged();
    }

    private int? GetFilterAreaId()
    {
        return int.TryParse(selectedAreaId, out var areaId) ? areaId : null;
    }

    private int? GetFilterTemplateId()
    {
        return int.TryParse(selectedTemplateId, out var templateId) ? templateId : null;
    }
}
