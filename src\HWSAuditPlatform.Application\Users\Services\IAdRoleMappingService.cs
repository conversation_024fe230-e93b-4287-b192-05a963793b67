using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Service for mapping Active Directory groups to application roles
/// </summary>
public interface IAdRoleMappingService
{
    /// <summary>
    /// Maps AD group memberships to the highest privilege application role
    /// </summary>
    /// <param name="adGroupMemberships">List of AD group names/DNs the user belongs to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The mapped application role</returns>
    Task<UserRole> MapAdGroupsToRoleAsync(IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user role by first checking database for existing user, then falling back to AD group mapping
    /// </summary>
    /// <param name="adObjectGuid">AD Object GUID of the user</param>
    /// <param name="adGroupMemberships">AD group memberships for fallback mapping</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User role</returns>
    Task<UserRole> GetUserRoleAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user information including role by first checking database, then falling back to AD group mapping
    /// </summary>
    /// <param name="adObjectGuid">AD Object GUID of the user</param>
    /// <param name="adGroupMemberships">AD group memberships for fallback mapping</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tuple containing user role and whether user exists in database</returns>
    Task<(UserRole role, bool userExistsInDb)> GetUserRoleWithExistenceAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active AD group role mappings from the database
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of AD group names to roles</returns>
    Task<Dictionary<string, UserRole>> GetAdGroupRoleMappingsAsync(CancellationToken cancellationToken = default);
}
