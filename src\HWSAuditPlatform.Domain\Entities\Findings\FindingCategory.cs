using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Findings;

/// <summary>
/// Represents a category for classifying findings within an audit template.
/// Maps to the FindingCategories table in the database.
/// </summary>
public class FindingCategory : AuditableEntity<int>
{
    /// <summary>
    /// Name of the finding category
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the category
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Links to the audit template this category belongs to
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// Display order for UI presentation
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Indicates if this category is currently active/available for use
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Optional color code for UI display (hex format, e.g., #FF5733)
    /// </summary>
    [MaxLength(7)]
    public string? ColorCode { get; set; }

    /// <summary>
    /// Optional icon identifier for UI display
    /// </summary>
    [MaxLength(50)]
    public string? IconName { get; set; }

    /// <summary>
    /// Indicates if this category requires additional documentation
    /// </summary>
    public bool RequiresDocumentation { get; set; } = false;

    /// <summary>
    /// Navigation property for findings in this category
    /// </summary>
    public virtual ICollection<Finding> Findings { get; set; } = new List<Finding>();

    /// <summary>
    /// Navigation property for the user who created this category
    /// </summary>
    public virtual User? CreatedByUser { get; set; }

    /// <summary>
    /// Navigation property for the user who last updated this category
    /// </summary>
    public virtual User? UpdatedByUser { get; set; }

    /// <summary>
    /// Gets the number of findings in this category
    /// </summary>
    public int FindingCount => Findings.Count;

    /// <summary>
    /// Gets the number of open findings in this category
    /// </summary>
    public int OpenFindingCount => Findings.Count(f => f.IsOpen);

    /// <summary>
    /// Indicates if this category is effectively usable
    /// </summary>
    public bool IsUsable => IsActive;
}
