namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Shared constants for area-based responsibility and finding categorization features
/// </summary>
public static class SharedConstants
{
    /// <summary>
    /// Constants for finding categories
    /// </summary>
    public static class FindingCategories
    {
        public const int MaxCategoryNameLength = 100;
        public const int MaxDescriptionLength = 500;
        public const int DefaultDisplayOrder = 100;
        public const string DefaultColorCode = "#6c757d";
        public const string DefaultIconName = "tag";

        /// <summary>
        /// Predefined color options for finding categories
        /// </summary>
        public static readonly List<(string ColorCode, string ColorName)> ColorOptions = new()
        {
            ("#dc3545", "Red - Critical"),
            ("#fd7e14", "Orange - Major"),
            ("#ffc107", "Yellow - Minor"),
            ("#28a745", "Green - Good Practice"),
            ("#007bff", "Blue - Information"),
            ("#6f42c1", "Purple - Process"),
            ("#20c997", "Teal - Safety"),
            ("#6c757d", "Gray - General"),
            ("#e83e8c", "Pink - Quality"),
            ("#fd7e14", "Amber - Warning")
        };

        /// <summary>
        /// Predefined icon options for finding categories
        /// </summary>
        public static readonly List<(string IconName, string DisplayName)> IconOptions = new()
        {
            ("warning-triangle", "Warning Triangle"),
            ("exclamation-circle", "Exclamation Circle"),
            ("info-circle", "Information Circle"),
            ("shield-alt", "Safety Shield"),
            ("tools", "Tools"),
            ("cog", "Settings/Process"),
            ("bug", "Bug/Defect"),
            ("fire", "Fire/Emergency"),
            ("bolt", "Electrical"),
            ("eye", "Observation"),
            ("wrench", "Maintenance"),
            ("clipboard-check", "Checklist"),
            ("hard-hat", "Safety Equipment"),
            ("industry", "Industrial"),
            ("thermometer", "Temperature")
        };
    }

    /// <summary>
    /// Constants for area responsibilities
    /// </summary>
    public static class AreaResponsibilities
    {
        public const int MaxDescriptionLength = 500;
        public const int DefaultPriority = 100;
        public const int MaxPriority = 999;
        public const int MinPriority = 0;

        /// <summary>
        /// Default due date offsets for different responsibility types (in days)
        /// </summary>
        public static readonly Dictionary<string, int> DefaultDueDateOffsets = new()
        {
            ["CorrectiveActionOwner"] = 30,
            ["RetrospectiveAnalyst"] = 14,
            ["FindingReviewer"] = 7,
            ["EscalationContact"] = 1
        };

        /// <summary>
        /// Escalation thresholds for overdue findings (in days)
        /// </summary>
        public static readonly Dictionary<string, int> EscalationThresholds = new()
        {
            ["FirstReminder"] = 3,
            ["SecondReminder"] = 7,
            ["ManagerEscalation"] = 14,
            ["ExecutiveEscalation"] = 30
        };
    }

    /// <summary>
    /// Constants for audit execution
    /// </summary>
    public static class AuditExecution
    {
        public const int MaxFindingDescriptionLength = 2000;
        public const int MaxCommentsLength = 1000;
        public const int MaxAttachmentSizeMB = 10;
        public const int MaxAttachmentsPerQuestion = 5;
        public static readonly string[] AllowedImageTypes = { "image/jpeg", "image/png", "image/webp" };
        public static readonly string[] AllowedDocumentTypes = { "application/pdf", "text/plain" };

        /// <summary>
        /// Default validation rules
        /// </summary>
        public static class ValidationRules
        {
            public const bool RequireFindingsForNoAnswers = true;
            public const bool RequireCategoryForFindings = false;
            public const bool RequireResponsibleUserForFindings = false;
            public const bool RequireCommentsForCriticalFindings = true;
            public const bool RequireEvidenceForFindings = false;
        }
    }

    /// <summary>
    /// Constants for notifications
    /// </summary>
    public static class Notifications
    {
        public const int MaxNotificationTitleLength = 100;
        public const int MaxNotificationMessageLength = 500;
        public const int DefaultNotificationRetentionDays = 30;
        public const int MaxNotificationsPerUser = 100;

        /// <summary>
        /// Notification templates
        /// </summary>
        public static class Templates
        {
            public const string FindingAssigned = "You have been assigned responsibility for a finding in audit '{AuditTitle}'.";
            public const string FindingDueSoon = "Finding '{FindingDescription}' is due in {DaysRemaining} days.";
            public const string FindingOverdue = "Finding '{FindingDescription}' is overdue by {DaysOverdue} days.";
            public const string AuditCompleted = "Audit '{AuditTitle}' has been completed with {FindingCount} findings.";
            public const string ResponsibilityAssigned = "You have been assigned as {ResponsibilityType} for area '{AreaName}'.";
        }
    }

    /// <summary>
    /// Constants for API endpoints
    /// </summary>
    public static class ApiEndpoints
    {
        public const string BaseApiPath = "api/v1";
        
        public static class AreaResponsibilities
        {
            public const string Base = $"{BaseApiPath}/area-responsibilities";
            public const string GetAll = Base;
            public const string GetById = $"{Base}/{{id}}";
            public const string Create = Base;
            public const string Update = $"{Base}/{{id}}";
            public const string Delete = $"{Base}/{{id}}";
            public const string GetByArea = $"{Base}/area/{{areaId}}";
            public const string GetByTemplate = $"{Base}/template/{{templateId}}";
            public const string GetResponsibilityTypes = $"{Base}/types";
        }

        public static class FindingCategories
        {
            public const string Base = $"{BaseApiPath}/finding-categories";
            public const string GetAll = Base;
            public const string GetById = $"{Base}/{{id}}";
            public const string Create = Base;
            public const string Update = $"{Base}/{{id}}";
            public const string Delete = $"{Base}/{{id}}";
            public const string GetByTemplate = $"{Base}/template/{{templateId}}";
            public const string GetColorOptions = $"{Base}/colors";
            public const string GetIconOptions = $"{Base}/icons";
            public const string GetStatistics = $"{Base}/statistics/{{templateId}}";
            public const string AssignToFinding = $"{BaseApiPath}/findings/{{findingId}}/category";
        }
    }

    /// <summary>
    /// Constants for caching
    /// </summary>
    public static class Caching
    {
        public const int DefaultCacheExpirationMinutes = 30;
        public const int ShortCacheExpirationMinutes = 5;
        public const int LongCacheExpirationMinutes = 120;

        public static class Keys
        {
            public const string AreaResponsibilities = "area_responsibilities";
            public const string FindingCategories = "finding_categories";
            public const string ResponsibilityTypes = "responsibility_types";
            public const string ColorOptions = "color_options";
            public const string IconOptions = "icon_options";
            public const string UserList = "user_list";
            public const string AreaList = "area_list";
            public const string TemplateList = "template_list";
        }
    }

    /// <summary>
    /// Constants for feature flags
    /// </summary>
    public static class FeatureFlags
    {
        public const string AreaBasedResponsibility = "EnableAreaBasedResponsibility";
        public const string FindingCategorization = "EnableFindingCategorization";
        public const string AutomaticAssignment = "EnableAutomaticAssignment";
        public const string DueDateTracking = "EnableDueDateTracking";
        public const string EscalationWorkflow = "EnableEscalationWorkflow";
        public const string AdvancedReporting = "EnableAdvancedReporting";
        public const string BulkOperations = "EnableBulkOperations";
        public const string NotificationSystem = "EnableNotificationSystem";
    }

    /// <summary>
    /// Constants for permissions
    /// </summary>
    public static class Permissions
    {
        public const string ManageAreaResponsibilities = "manage_area_responsibilities";
        public const string ManageFindingCategories = "manage_finding_categories";
        public const string AssignResponsibilities = "assign_responsibilities";
        public const string ViewAllFindings = "view_all_findings";
        public const string ManageAllFindings = "manage_all_findings";
        public const string ViewReports = "view_reports";
        public const string ExportData = "export_data";
        public const string ManageSystemSettings = "manage_system_settings";
    }

    /// <summary>
    /// Constants for audit statuses
    /// </summary>
    public static class AuditStatuses
    {
        public const string Draft = "Draft";
        public const string InProgress = "InProgress";
        public const string Completed = "Completed";
        public const string UnderReview = "UnderReview";
        public const string Approved = "Approved";
        public const string Rejected = "Rejected";
        public const string Archived = "Archived";
    }

    /// <summary>
    /// Constants for finding statuses
    /// </summary>
    public static class FindingStatuses
    {
        public const string Open = "Open";
        public const string InProgress = "InProgress";
        public const string Resolved = "Resolved";
        public const string Verified = "Verified";
        public const string Closed = "Closed";
        public const string Cancelled = "Cancelled";
    }
}
