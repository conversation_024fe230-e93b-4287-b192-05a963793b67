@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if (!string.IsNullOrEmpty(Icon))
                        {
                            <i class="@Icon me-2"></i>
                        }
                        @Title
                    </h5>
                    <button type="button" class="btn-close" @onclick="Cancel"></button>
                </div>
                <div class="modal-body">
                    @if (!string.IsNullOrEmpty(Message))
                    {
                        <p class="mb-3">@Message</p>
                    }
                    @if (ChildContent != null)
                    {
                        @ChildContent
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" @onclick="Cancel" disabled="@IsProcessing">
                        <i class="bi bi-x-lg me-2"></i>
                        @CancelText
                    </button>
                    <button type="button" class="btn @ConfirmButtonClass" @onclick="Confirm" disabled="@IsProcessing">
                        @if (IsProcessing)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else if (!string.IsNullOrEmpty(ConfirmIcon))
                        {
                            <i class="@ConfirmIcon me-2"></i>
                        }
                        @ConfirmText
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public string Title { get; set; } = "Confirm Action";
    [Parameter] public string Message { get; set; } = string.Empty;
    [Parameter] public string Icon { get; set; } = string.Empty;
    [Parameter] public string ConfirmText { get; set; } = "Confirm";
    [Parameter] public string ConfirmIcon { get; set; } = string.Empty;
    [Parameter] public string ConfirmButtonClass { get; set; } = "btn-primary";
    [Parameter] public string CancelText { get; set; } = "Cancel";
    [Parameter] public bool IsProcessing { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public EventCallback OnConfirm { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private async Task Confirm()
    {
        if (OnConfirm.HasDelegate)
        {
            await OnConfirm.InvokeAsync();
        }
    }

    private async Task Cancel()
    {
        if (OnCancel.HasDelegate)
        {
            await OnCancel.InvokeAsync();
        }
    }
}
