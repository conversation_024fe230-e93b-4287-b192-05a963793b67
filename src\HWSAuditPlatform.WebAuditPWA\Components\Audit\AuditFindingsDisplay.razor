@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums

<div class="audit-findings-display">
    @if (Findings?.Any() == true)
    {
        <div class="findings-header">
            <h5 class="findings-title">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Findings (@(Findings?.Count() ?? 0))
            </h5>
            @if (ShowCategoryFilter && categories.Any())
            {
                <div class="category-filter">
                    <select class="form-select form-select-sm" @bind="selectedCategoryFilter" @bind:after="OnCategoryFilterChanged">
                        <option value="">All Categories</option>
                        @foreach (var category in categories)
                        {
                            <option value="@category.Id">@category.CategoryName</option>
                        }
                    </select>
                </div>
            }
        </div>

        <div class="findings-list">
            @foreach (var finding in filteredFindings)
            {
                <div class="finding-item @GetSeverityClass(finding.SeverityLevel)">
                    <div class="finding-header">
                        <div class="finding-title-section">
                            @if (finding.CategoryId.HasValue && !string.IsNullOrEmpty(finding.CategoryColorCode))
                            {
                                <div class="category-indicator" style="background-color: @finding.CategoryColorCode;"></div>
                            }
                            @if (!string.IsNullOrEmpty(finding.CategoryIconName))
                            {
                                <i class="@GetIconClass(finding.CategoryIconName) me-2"></i>
                            }
                            <span class="finding-number">Finding @(Findings.ToList().IndexOf(finding) + 1)</span>
                        </div>
                        <div class="finding-severity">
                            <span class="severity-badge @GetSeverityBadgeClass(finding.SeverityLevel)">
                                @finding.SeverityLevel
                            </span>
                        </div>
                    </div>

                    <div class="finding-content">
                        <p class="finding-description">@finding.Description</p>
                        
                        @if (!string.IsNullOrEmpty(finding.CategoryName))
                        {
                            <div class="finding-metadata">
                                <span class="metadata-label">Category:</span>
                                <span class="category-badge">@finding.CategoryName</span>
                            </div>
                        }

                        @if (ShowResponsibilityInfo)
                        {
                            @if (!string.IsNullOrEmpty(finding.ResponsibleUserName))
                            {
                                <div class="finding-metadata">
                                    <span class="metadata-label">Responsible:</span>
                                    <span class="responsible-user">
                                        <i class="fas fa-user me-1"></i>
                                        @finding.ResponsibleUserName
                                    </span>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(finding.RetrospectiveAnalystUserName))
                            {
                                <div class="finding-metadata">
                                    <span class="metadata-label">Analyst:</span>
                                    <span class="analyst-user">
                                        <i class="fas fa-chart-line me-1"></i>
                                        @finding.RetrospectiveAnalystUserName
                                    </span>
                                </div>
                            }
                        }

                        @if (finding.DueDate.HasValue)
                        {
                            <div class="finding-metadata">
                                <span class="metadata-label">Due Date:</span>
                                <span class="due-date @(finding.IsOverdue ? "overdue" : "")">
                                    @finding.DueDate.Value.ToString("MMM dd, yyyy")
                                    @if (finding.IsOverdue)
                                    {
                                        <i class="fas fa-exclamation-triangle ms-1"></i>
                                    }
                                </span>
                            </div>
                        }
                    </div>

                    @if (ShowQuestionContext)
                    {
                        <div class="finding-context">
                            <small class="text-muted">
                                <i class="fas fa-question-circle me-1"></i>
                                Question: @finding.QuestionText
                            </small>
                        </div>
                    }
                </div>
            }
        </div>

        @if (ShowSummary)
        {
            <div class="findings-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-value">@(Findings?.Count() ?? 0)</span>
                        <span class="stat-label">Total</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@(Findings?.Count(f => f.SeverityLevel == SeverityLevel.Critical) ?? 0)</span>
                        <span class="stat-label">Critical</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@(Findings?.Count(f => f.SeverityLevel == SeverityLevel.Major) ?? 0)</span>
                        <span class="stat-label">Major</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@Findings.Count(f => f.CategoryId.HasValue)</span>
                        <span class="stat-label">Categorized</span>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="no-findings">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <h6>No Findings</h6>
            <p class="text-muted">This audit completed without any findings.</p>
        </div>
    }
</div>

<style>
    .audit-findings-display {
        background: var(--industrial-light-gray);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .findings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .findings-title {
        color: var(--industrial-red);
        font-weight: 700;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .category-filter .form-select {
        min-width: 200px;
        background-color: var(--industrial-dark);
        border-color: var(--industrial-border);
        color: var(--industrial-text);
    }

    .findings-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .finding-item {
        background: var(--industrial-dark);
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        padding: 1rem;
        transition: all 0.2s ease;
    }

    .finding-item.severity-critical {
        border-left: 4px solid #dc3545;
    }

    .finding-item.severity-major {
        border-left: 4px solid #fd7e14;
    }

    .finding-item.severity-minor {
        border-left: 4px solid #ffc107;
    }

    .finding-item.severity-observation {
        border-left: 4px solid #6c757d;
    }

    .finding-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .finding-title-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid var(--industrial-border);
    }

    .finding-number {
        font-weight: 600;
        color: var(--industrial-text);
    }

    .severity-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .severity-badge.critical {
        background-color: #dc3545;
        color: white;
    }

    .severity-badge.major {
        background-color: #fd7e14;
        color: white;
    }

    .severity-badge.minor {
        background-color: #ffc107;
        color: black;
    }

    .severity-badge.observation {
        background-color: #6c757d;
        color: white;
    }

    .finding-description {
        color: var(--industrial-text);
        margin-bottom: 0.75rem;
        line-height: 1.5;
    }

    .finding-metadata {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .metadata-label {
        font-weight: 500;
        color: var(--industrial-text-muted);
        min-width: 80px;
        font-size: 0.875rem;
    }

    .category-badge {
        background: var(--industrial-teal);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .responsible-user,
    .analyst-user {
        color: var(--industrial-text);
        font-size: 0.875rem;
    }

    .due-date.overdue {
        color: #dc3545;
        font-weight: 500;
    }

    .finding-context {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid var(--industrial-border);
    }

    .findings-summary {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 2px solid var(--industrial-border);
    }

    .summary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 1rem;
        text-align: center;
    }

    .stat-item {
        background: var(--industrial-dark);
        border-radius: 8px;
        padding: 1rem;
    }

    .stat-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--industrial-teal);
    }

    .stat-label {
        display: block;
        font-size: 0.75rem;
        color: var(--industrial-text-muted);
        text-transform: uppercase;
        margin-top: 0.25rem;
    }

    .no-findings {
        text-align: center;
        color: var(--industrial-text-muted);
        padding: 3rem 1rem;
    }

    .no-findings i {
        color: var(--industrial-teal);
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .findings-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .finding-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .finding-metadata {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .metadata-label {
            min-width: auto;
        }

        .summary-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>

@code {
    [Parameter] public IEnumerable<AuditFindingModel>? Findings { get; set; }
    [Parameter] public bool ShowCategoryFilter { get; set; } = true;
    [Parameter] public bool ShowResponsibilityInfo { get; set; } = true;
    [Parameter] public bool ShowQuestionContext { get; set; } = false;
    [Parameter] public bool ShowSummary { get; set; } = true;

    private List<FindingCategorySummaryModel> categories = new();
    private string selectedCategoryFilter = string.Empty;
    private IEnumerable<AuditFindingModel> filteredFindings = Enumerable.Empty<AuditFindingModel>();

    protected override void OnParametersSet()
    {
        if (Findings != null)
        {
            // Extract unique categories from findings
            categories = Findings
                .Where(f => f.CategoryId.HasValue && !string.IsNullOrEmpty(f.CategoryName))
                .GroupBy(f => f.CategoryId)
                .Select(g => new FindingCategorySummaryModel
                {
                    Id = g.Key!.Value,
                    CategoryName = g.First().CategoryName!,
                    ColorCode = g.First().CategoryColorCode,
                    IconName = g.First().CategoryIconName,
                    FindingCount = g.Count()
                })
                .OrderBy(c => c.CategoryName)
                .ToList();

            ApplyFilter();
        }
    }

    private void OnCategoryFilterChanged()
    {
        ApplyFilter();
    }

    private void ApplyFilter()
    {
        if (Findings == null)
        {
            filteredFindings = Enumerable.Empty<AuditFindingModel>();
            return;
        }

        filteredFindings = Findings;

        if (!string.IsNullOrEmpty(selectedCategoryFilter) && int.TryParse(selectedCategoryFilter, out var categoryId))
        {
            filteredFindings = filteredFindings.Where(f => f.CategoryId == categoryId);
        }
    }

    private static string GetSeverityClass(SeverityLevel severity)
    {
        return $"severity-{severity.ToString().ToLower()}";
    }

    private static string GetSeverityBadgeClass(SeverityLevel severity)
    {
        return severity.ToString().ToLower();
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }
}
