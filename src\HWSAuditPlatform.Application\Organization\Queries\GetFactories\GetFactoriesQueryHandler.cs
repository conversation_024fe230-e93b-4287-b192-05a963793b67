using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetFactories;

/// <summary>
/// Handler for GetFactoriesQuery
/// </summary>
public class GetFactoriesQueryHandler : BaseQueryHandler<GetFactoriesQuery, List<FactorySummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetFactoriesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<FactorySummaryDto>> Handle(GetFactoriesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Factories
            .Include(f => f.Location)
            .AsQueryable();

        // Apply filters
        if (request.LocationId.HasValue)
        {
            query = query.Where(f => f.LocationId == request.LocationId.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(f => f.IsActive == request.IsActive.Value);
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(f =>
                EF.Functions.Like(f.FactoryName, searchPattern) ||
                (f.Location != null && EF.Functions.Like(f.Location.LocationName, searchPattern)));
        }

        // Restrict to specific factory for non-admin users
        if (request.RestrictToFactoryId.HasValue)
        {
            query = query.Where(f => f.Id == request.RestrictToFactoryId.Value);
        }

        // Get factories with area counts using subquery to avoid N+1 problem
        var factories = await query
            .Select(f => new FactorySummaryDto
            {
                Id = f.Id,
                FactoryName = f.FactoryName,
                LocationName = f.Location != null ? f.Location.LocationName : null,
                LocationCountry = f.Location != null ? f.Location.LocationCountry : null,
                IsActive = f.IsActive,
                AreaCount = _context.Areas.Count(a => a.FactoryId == f.Id && a.IsActive)
            })
            .OrderBy(f => f.LocationName)
            .ThenBy(f => f.FactoryName)
            .ToListAsync(cancellationToken);

        return factories;
    }
}
