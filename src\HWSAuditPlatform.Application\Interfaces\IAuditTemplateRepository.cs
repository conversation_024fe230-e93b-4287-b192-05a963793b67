using System.Linq.Expressions;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Repository interface for AuditTemplate entities with template-specific operations
/// </summary>
public interface IAuditTemplateRepository : IRepository<AuditTemplate, int>
{
    /// <summary>
    /// Gets templates with basic related data loaded
    /// </summary>
    Task<IReadOnlyList<AuditTemplate>> GetTemplatesWithDetailsAsync(
        Expression<Func<AuditTemplate, bool>>? predicate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a single template with all related data loaded
    /// </summary>
    Task<AuditTemplate?> GetTemplateWithFullDetailsAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all published and active templates
    /// </summary>
    Task<IReadOnlyList<AuditTemplate>> GetPublishedTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all versions of a template by name
    /// </summary>
    Task<IReadOnlyList<AuditTemplate>> GetTemplateVersionsAsync(
        string templateName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the latest version of a template by name
    /// </summary>
    Task<AuditTemplate?> GetLatestVersionAsync(
        string templateName,
        bool publishedOnly = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a template name and version combination already exists
    /// </summary>
    Task<bool> TemplateNameVersionExistsAsync(
        string templateName,
        int version,
        int? excludeId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the count of active questions for a template
    /// </summary>
    Task<int> GetQuestionCountAsync(int templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets templates that have been used in audits
    /// </summary>
    Task<IReadOnlyList<AuditTemplate>> GetTemplatesUsedInAuditsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated templates with comprehensive filtering and sorting
    /// </summary>
    Task<(IReadOnlyList<AuditTemplate> Items, int TotalCount)> GetPagedTemplatesWithFiltersAsync(
        int pageNumber,
        int pageSize,
        string? searchTerm = null,
        bool? isPublished = null,
        bool? isActive = null,
        bool? canBeUsed = null,
        string sortBy = "TemplateName",
        bool ascending = true,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Question entities with question-specific operations
/// </summary>
public interface IQuestionRepository : IRepository<Question, int>
{
    /// <summary>
    /// Gets all questions for a template
    /// </summary>
    Task<IReadOnlyList<Question>> GetQuestionsByTemplateAsync(
        int templateId,
        bool includeInactive = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all questions in a specific group
    /// </summary>
    Task<IReadOnlyList<Question>> GetQuestionsByGroupAsync(
        int groupId,
        bool includeInactive = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets root-level questions (no parent) for a template
    /// </summary>
    Task<IReadOnlyList<Question>> GetRootQuestionsAsync(
        int templateId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets conditional questions for a parent question
    /// </summary>
    Task<IReadOnlyList<Question>> GetConditionalQuestionsAsync(
        int parentQuestionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a question has dependent conditional questions
    /// </summary>
    Task<bool> HasDependentQuestionsAsync(int questionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a question has been used in any audits
    /// </summary>
    Task<bool> IsQuestionUsedInAuditsAsync(int questionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the maximum display order for questions in a template or group
    /// </summary>
    Task<int> GetMaxDisplayOrderAsync(
        int templateId,
        int? groupId = null,
        CancellationToken cancellationToken = default);
}
