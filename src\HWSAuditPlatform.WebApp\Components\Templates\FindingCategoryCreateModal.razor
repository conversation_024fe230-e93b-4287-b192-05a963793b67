@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@using System.ComponentModel.DataAnnotations
@inject IFindingCategoryApiService FindingCategoryApiService
@inject ILogger<FindingCategoryCreateModal> Logger

@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Finding Category</h5>
                    <button type="button" class="btn-close" @onclick="Close"></button>
                </div>
                <EditForm Model="model" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger">
                                @errorMessage
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Category Name *</label>
                                    <input type="text" class="form-control" @bind="model.CategoryName" maxlength="100" />
                                    <ValidationMessage For="@(() => model.CategoryName)" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Display Order</label>
                                    <input type="number" class="form-control" @bind="model.DisplayOrder" min="0" />
                                    <ValidationMessage For="@(() => model.DisplayOrder)" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description (Optional)</label>
                            <textarea class="form-control" @bind="model.Description" rows="3" maxlength="500"></textarea>
                            <ValidationMessage For="@(() => model.Description)" />
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Color</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color me-2" @bind="model.ColorCode" style="width: 60px;" />
                                        <input type="text" class="form-control" @bind="model.ColorCode" placeholder="#FF5733" />
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">Predefined colors:</small>
                                        <div class="d-flex flex-wrap gap-1 mt-1">
                                            @foreach (var color in colorOptions)
                                            {
                                                <button type="button" class="btn btn-sm p-1 border" 
                                                        style="background-color: @color.ColorCode; width: 30px; height: 30px;"
                                                        title="@color.ColorName"
                                                        @onclick="() => model.ColorCode = color.ColorCode">
                                                </button>
                                            }
                                        </div>
                                    </div>
                                    <ValidationMessage For="@(() => model.ColorCode)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Icon</label>
                                    <select class="form-select" @bind="model.IconName">
                                        <option value="">No icon</option>
                                        @foreach (var icon in iconOptions)
                                        {
                                            <option value="@icon.IconName">@icon.IconDisplayName</option>
                                        }
                                    </select>
                                    @if (!string.IsNullOrEmpty(model.IconName))
                                    {
                                        <div class="mt-2">
                                            <small class="text-muted">Preview: </small>
                                            <i class="@GetIconClass(model.IconName)"></i>
                                        </div>
                                    }
                                    <ValidationMessage For="@(() => model.IconName)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" @bind="model.IsActive" id="isActive">
                                        <label class="form-check-label" for="isActive">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" @bind="model.RequiresDocumentation" id="requiresDocumentation">
                                        <label class="form-check-label" for="requiresDocumentation">
                                            Requires Documentation
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(model.CategoryName) || !string.IsNullOrEmpty(model.ColorCode) || !string.IsNullOrEmpty(model.IconName))
                        {
                            <div class="mb-3">
                                <label class="form-label">Preview</label>
                                <div class="border rounded p-3">
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(model.ColorCode))
                                        {
                                            <div class="me-2" style="width: 16px; height: 16px; background-color: @model.ColorCode; border-radius: 50%; border: 1px solid #dee2e6;"></div>
                                        }
                                        @if (!string.IsNullOrEmpty(model.IconName))
                                        {
                                            <i class="@GetIconClass(model.IconName) me-2"></i>
                                        }
                                        <span class="fw-bold">@(string.IsNullOrEmpty(model.CategoryName) ? "Category Name" : model.CategoryName)</span>
                                    </div>
                                    @if (!string.IsNullOrEmpty(model.Description))
                                    {
                                        <small class="text-muted d-block mt-1">@model.Description</small>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Create Category
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public int AuditTemplateId { get; set; }
    [Parameter] public EventCallback OnCategoryCreated { get; set; }

    private CreateFindingCategoryModel model = new();
    private List<ColorOptionModel> colorOptions = new();
    private List<IconOptionModel> iconOptions = new();
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;

    protected override void OnInitialized()
    {
        colorOptions = FindingCategoryApiService.GetColorOptions();
        iconOptions = FindingCategoryApiService.GetIconOptions();
        model.AuditTemplateId = AuditTemplateId;
    }

    protected override void OnParametersSet()
    {
        model.AuditTemplateId = AuditTemplateId;
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            await FindingCategoryApiService.CreateFindingCategoryAsync(model);
            await OnCategoryCreated.InvokeAsync();
            await Close();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating finding category");
            errorMessage = "Failed to create finding category. Please check your input and try again.";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task Close()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        
        // Reset form
        model = new CreateFindingCategoryModel { AuditTemplateId = AuditTemplateId };
        errorMessage = string.Empty;
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }
}
