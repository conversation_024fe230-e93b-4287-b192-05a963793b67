# Architecture Overview - Visual Diagrams

## 🏗️ **System Architecture Overview**

### **High-Level System Architecture**
```mermaid
graph TB
    subgraph "Client Applications"
        PWA[PWA - Audit Execution<br/>Blazor WASM + Offline]
        WebApp[WebApp - Management<br/>Blazor WASM]
        Web[Web - Legacy<br/>Blazor Server]
    end
    
    subgraph "API Layer"
        API[API Service<br/>ASP.NET Core]
        Auth[Authentication<br/>JWT + AD]
    end
    
    subgraph "Application Layer"
        App[Application<br/>CQRS + MediatR]
        Commands[Commands<br/>Write Operations]
        Queries[Queries<br/>Read Operations]
    end
    
    subgraph "Domain Layer"
        Domain[Domain Entities<br/>Business Logic]
        Events[Domain Events]
    end
    
    subgraph "Infrastructure Layer"
        EF[Entity Framework<br/>Data Access]
        FileStore[File Storage<br/>Local D:\uploads]
        AD[Active Directory<br/>User Management]
    end
    
    subgraph "Data Layer"
        DB[(SQL Server<br/>AuditFlowDB)]
        Files[File System<br/>Attachments]
    end
    
    PWA --> API
    WebApp --> API
    Web --> API
    
    API --> Auth
    API --> App
    
    App --> Commands
    App --> Queries
    App --> Domain
    
    Domain --> Events
    
    App --> EF
    App --> FileStore
    App --> AD
    
    EF --> DB
    FileStore --> Files
    AD --> DB
```

## 🔄 **Clean Architecture Layers**

### **Dependency Flow Diagram**
```mermaid
graph TD
    subgraph "Presentation Layer"
        UI1[PWA<br/>Blazor WASM]
        UI2[WebApp<br/>Blazor WASM]
        UI3[Web<br/>Blazor Server]
        API[API Controllers<br/>ASP.NET Core]
    end
    
    subgraph "Application Layer"
        CQRS[CQRS Handlers<br/>MediatR]
        DTOs[DTOs & Mapping<br/>AutoMapper]
        Validation[Validation<br/>FluentValidation]
    end
    
    subgraph "Domain Layer"
        Entities[Domain Entities<br/>Business Rules]
        ValueObjects[Value Objects<br/>Immutable Types]
        DomainEvents[Domain Events<br/>Business Events]
        Interfaces[Repository Interfaces<br/>Abstractions]
    end
    
    subgraph "Infrastructure Layer"
        Repositories[Repository Implementations<br/>Entity Framework]
        ExternalServices[External Services<br/>AD, File Storage]
        Database[Database Context<br/>EF Core]
    end
    
    UI1 --> API
    UI2 --> API
    UI3 --> API
    
    API --> CQRS
    CQRS --> DTOs
    CQRS --> Validation
    CQRS --> Entities
    
    Entities --> ValueObjects
    Entities --> DomainEvents
    Entities --> Interfaces
    
    CQRS --> Repositories
    Repositories --> Database
    Repositories --> ExternalServices
    
    style Entities fill:#e1f5fe
    style CQRS fill:#f3e5f5
    style API fill:#e8f5e8
    style Repositories fill:#fff3e0
```

## 🔐 **Authentication Architecture**

### **Current Authentication Approaches**
```mermaid
graph TB
    subgraph "Authentication Challenges"
        subgraph "Web Project (Legacy)"
            WebAuth[Cookie-based<br/>Session Authentication]
            WebSession[Server-side Sessions]
        end
        
        subgraph "WebApp Project (New)"
            WebAppAuth[JWT Authentication<br/>Client-side Storage]
            WebAppToken[localStorage/sessionStorage]
        end
        
        subgraph "PWA Project"
            PWAAuth[JWT + Offline Support<br/>Local Storage]
            PWAOffline[Offline Token Cache]
            PWASync[Sync on Reconnect]
        end
    end
    
    subgraph "API Service"
        JWTEndpoint[/api/auth/login<br/>JWT Generation]
        ValidateEndpoint[Token Validation<br/>Middleware]
    end
    
    subgraph "Active Directory"
        AD[AD Authentication<br/>User Validation]
        Groups[AD Groups<br/>Role Mapping]
    end
    
    WebAuth --> JWTEndpoint
    WebAppAuth --> JWTEndpoint
    PWAAuth --> JWTEndpoint
    
    JWTEndpoint --> AD
    ValidateEndpoint --> Groups
    
    WebSession -.-> WebAuth
    WebAppToken -.-> WebAppAuth
    PWAOffline -.-> PWAAuth
    PWASync -.-> PWAAuth
    
    style WebAuth fill:#ffcdd2
    style WebAppAuth fill:#fff3e0
    style PWAAuth fill:#e8f5e8
```

## 💾 **Database Architecture**

### **Database Schema Overview**
```mermaid
erDiagram
    Users ||--o{ UserGroupMembers : "belongs to"
    UserGroups ||--o{ UserGroupMembers : "contains"
    UserGroups ||--o{ AdGroupRoleMapping : "maps to"
    Roles ||--o{ AdGroupRoleMapping : "assigned"
    
    Factories ||--o{ Areas : "contains"
    Areas ||--o{ SubAreas : "contains"
    
    AuditTemplates ||--o{ Questions : "contains"
    Questions ||--o{ QuestionOptions : "has options"
    Questions ||--o{ QuestionGroups : "grouped by"
    
    AuditTemplates ||--o{ Audits : "instantiated as"
    Audits ||--o{ AuditAnswers : "contains"
    AuditAnswers ||--o{ AuditAnswerSelectedOptions : "selected"
    AuditAnswers ||--o{ AuditAnswerFailureReasons : "failure reasons"
    Audits ||--o{ AuditAttachments : "has evidence"
    
    Audits ||--o{ Findings : "generates"
    Findings ||--o{ CorrectiveActions : "requires"
    
    RecurringAuditSettings ||--o{ Audits : "schedules"
    RecurrenceRules ||--o{ RecurringAuditSettings : "defines"
    
    Users {
        varchar(36) AdGuid PK
        varchar(100) Username
        varchar(200) FullName
        varchar(100) Email
        int RoleId FK
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    Audits {
        varchar(25) AuditId PK "CUID"
        int AuditTemplateId FK
        varchar(36) AssignedToUserId FK
        varchar(100) Status
        datetime ScheduledDate
        datetime CompletedDate
        varchar(36) CreatedByUserId FK
    }
    
    AuditAnswers {
        varchar(25) AuditAnswerId PK "CUID"
        varchar(25) AuditId FK
        int QuestionId FK
        varchar(50) AnswerType
        text AnswerValue
        datetime AnsweredAt
    }
```

## 🔄 **Data Flow Architecture**

### **Offline Sync Data Flow**
```mermaid
sequenceDiagram
    participant PWA as PWA Client
    participant LocalDB as Local Storage
    participant API as API Service
    participant DB as SQL Server
    
    Note over PWA,DB: Offline Audit Creation
    PWA->>LocalDB: Create Audit (CUID)
    PWA->>LocalDB: Save Answers (CUIDs)
    PWA->>LocalDB: Store Attachments
    
    Note over PWA,DB: Online Sync Process
    PWA->>API: POST /api/sync/audits
    API->>DB: Check existing CUIDs
    API->>DB: Insert new records
    API->>PWA: Sync confirmation
    
    Note over PWA,DB: Conflict Resolution
    PWA->>API: POST audit with CUID
    API->>DB: CUID already exists
    API->>API: Compare RecordVersion
    API->>PWA: Conflict response
    PWA->>PWA: User resolves conflict
    PWA->>API: POST resolved audit
```

## 🏭 **Deployment Architecture**

### **Current vs Target Deployment**
```mermaid
graph TB
    subgraph "Current Development"
        DevAPI[API Service<br/>localhost:7490]
        DevWeb[Web App<br/>localhost:5000]
        DevPWA[PWA<br/>localhost:3000]
        DevDB[(LocalDB<br/>Development)]
        DevFiles[Local Files<br/>D:\uploads]
    end
    
    subgraph "Target Production"
        ProdLB[Load Balancer<br/>nginx/IIS]
        ProdAPI1[API Instance 1<br/>Docker Container]
        ProdAPI2[API Instance 2<br/>Docker Container]
        ProdWeb[Static Web Assets<br/>CDN/Web Server]
        ProdDB[(SQL Server<br/>Production)]
        ProdFiles[File Storage<br/>Network Share/S3]
        ProdAD[Active Directory<br/>Corporate]
    end
    
    DevAPI --> DevDB
    DevAPI --> DevFiles
    DevWeb --> DevAPI
    DevPWA --> DevAPI
    
    ProdLB --> ProdAPI1
    ProdLB --> ProdAPI2
    ProdAPI1 --> ProdDB
    ProdAPI2 --> ProdDB
    ProdAPI1 --> ProdFiles
    ProdAPI2 --> ProdFiles
    ProdAPI1 --> ProdAD
    ProdAPI2 --> ProdAD
    ProdWeb --> ProdLB
    
    style DevAPI fill:#ffcdd2
    style DevWeb fill:#ffcdd2
    style DevPWA fill:#ffcdd2
    style ProdAPI1 fill:#e8f5e8
    style ProdAPI2 fill:#e8f5e8
    style ProdWeb fill:#e8f5e8
```
