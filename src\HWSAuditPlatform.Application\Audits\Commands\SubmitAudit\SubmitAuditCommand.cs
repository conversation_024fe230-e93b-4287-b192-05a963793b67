using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAudit;

/// <summary>
/// Command to submit a completed audit for review
/// </summary>
public class SubmitAuditCommand : BaseCommand
{
    /// <summary>
    /// The ID of the audit to submit
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Optional comments from the auditor
    /// </summary>
    public string? AuditorComments { get; set; }
}
