using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;
using HWSAuditPlatform.Application.Findings.Commands.CreateFindingCategory;
using HWSAuditPlatform.Application.Organization.Queries.GetAreaResponsibilities;
using HWSAuditPlatform.Application.Findings.Queries.GetFindingCategories;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc.Testing;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Integration;

/// <summary>
/// Integration tests for area-based responsibility and finding categorization features
/// </summary>
public class AreaResponsibilityAndCategorizationIntegrationTests : BaseDbTestClass
{
    private readonly CreateAreaResponsibilityCommandHandler _areaResponsibilityHandler;
    private readonly CreateFindingCategoryCommandHandler _findingCategoryHandler;
    private readonly GetAreaResponsibilitiesQueryHandler _getAreaResponsibilitiesHandler;
    private readonly GetFindingCategoriesQueryHandler _getFindingCategoriesHandler;

    public AreaResponsibilityAndCategorizationIntegrationTests()
    {
        _areaResponsibilityHandler = new CreateAreaResponsibilityCommandHandler(Context, MockCurrentUserService.Object, MockServices.CreateAreaResponsibilityService().Object);
        _findingCategoryHandler = new CreateFindingCategoryCommandHandler(Context, MockCurrentUserService.Object, MockServices.CreateFindingCategorizationService().Object);
        _getAreaResponsibilitiesHandler = new GetAreaResponsibilitiesQueryHandler(Context, MockCurrentUserService.Object);
        _getFindingCategoriesHandler = new GetFindingCategoriesQueryHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task CompleteWorkflow_CreateTemplateWithFeaturesAndAssignments_ShouldWorkEndToEnd()
    {
        // Arrange
        var templateId = await CreateTestTemplate();
        var areaId = await CreateTestArea();
        var userId = "test-user-id";

        // Act & Assert - Enable features on template
        await EnableTemplateFeatures(templateId);

        // Act & Assert - Create finding categories
        var categoryId = await CreateFindingCategory(templateId);

        // Act & Assert - Create area responsibility
        var responsibilityId = await CreateAreaResponsibility(areaId, userId, templateId);

        // Act & Assert - Verify everything is properly linked
        await VerifyIntegration(templateId, areaId, categoryId, responsibilityId);
    }

    [Fact]
    public async Task AreaResponsibility_CreateMultipleTypesForSameArea_ShouldAllowDifferentResponsibilityTypes()
    {
        // Arrange
        var areaId = await CreateTestArea();
        var userId1 = "user-1";
        var userId2 = "user-2";

        // Act - Create different responsibility types for the same area
        var correctiveActionId = await CreateAreaResponsibility(areaId, userId1, null, ResponsibilityType.CorrectiveActionOwner);
        var analystId = await CreateAreaResponsibility(areaId, userId2, null, ResponsibilityType.RetrospectiveAnalyst);

        // Assert
        Assert.NotNull(correctiveActionId);
        Assert.NotNull(analystId);
        Assert.NotEqual(correctiveActionId, analystId);

        var responsibilities = await Context.AreaResponsibilities
            .Where(ar => ar.AreaId == areaId)
            .ToListAsync();

        Assert.Equal(2, responsibilities.Count);
        Assert.Contains(responsibilities, r => r.ResponsibilityType == ResponsibilityType.CorrectiveActionOwner);
        Assert.Contains(responsibilities, r => r.ResponsibilityType == ResponsibilityType.RetrospectiveAnalyst);
    }

    [Fact]
    public async Task FindingCategory_CreateMultipleForTemplate_ShouldMaintainDisplayOrder()
    {
        // Arrange
        var templateId = await CreateTestTemplate();
        await EnableTemplateFeatures(templateId);

        // Act - Create categories with different display orders
        var category1Id = await CreateFindingCategory(templateId, "Critical Issues", 1);
        var category2Id = await CreateFindingCategory(templateId, "Minor Issues", 2);
        var category3Id = await CreateFindingCategory(templateId, "Observations", 3);

        // Assert
        var categories = await Context.FindingCategories
            .Where(fc => fc.AuditTemplateId == templateId)
            .OrderBy(fc => fc.DisplayOrder)
            .ToListAsync();

        Assert.Equal(3, categories.Count);
        Assert.Equal("Critical Issues", categories[0].CategoryName);
        Assert.Equal("Minor Issues", categories[1].CategoryName);
        Assert.Equal("Observations", categories[2].CategoryName);
        Assert.Equal(1, categories[0].DisplayOrder);
        Assert.Equal(2, categories[1].DisplayOrder);
        Assert.Equal(3, categories[2].DisplayOrder);
    }

    [Fact]
    public async Task TemplateFeatures_EnableAndDisable_ShouldUpdateCorrectly()
    {
        // Arrange
        var templateId = await CreateTestTemplate();

        // Act - Enable features
        await EnableTemplateFeatures(templateId);

        // Assert - Features are enabled
        var template = await Context.AuditTemplates.FindAsync(templateId);
        Assert.NotNull(template);
        Assert.True(template.EnableAreaBasedResponsibility);
        Assert.True(template.EnableFindingCategorization);

        // Act - Disable features
        template.EnableAreaBasedResponsibility = false;
        template.EnableFindingCategorization = false;
        await Context.SaveChangesAsync();

        // Assert - Features are disabled
        await Context.Entry(template).ReloadAsync();
        Assert.False(template.EnableAreaBasedResponsibility);
        Assert.False(template.EnableFindingCategorization);
    }

    private async Task<int> CreateTestTemplate()
    {
        var template = new AuditTemplate
        {
            TemplateName = $"Test Template {Guid.NewGuid()}",
            Description = "Test template for integration tests",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            EnableAreaBasedResponsibility = false,
            EnableFindingCategorization = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "test-user",
            UpdatedByUserId = "test-user"
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template.Id;
    }

    private async Task<int> CreateTestArea()
    {
        var factory = new Factory
        {
            FactoryName = $"Test Factory {Guid.NewGuid()}",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "test-user",
            UpdatedByUserId = "test-user"
        };

        Context.Factories.Add(factory);
        await Context.SaveChangesAsync();

        var area = new Area
        {
            AreaName = $"Test Area {Guid.NewGuid()}",
            FactoryId = factory.Id,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "test-user",
            UpdatedByUserId = "test-user"
        };

        Context.Areas.Add(area);
        await Context.SaveChangesAsync();
        return area.Id;
    }

    private async Task EnableTemplateFeatures(int templateId)
    {
        var template = await Context.AuditTemplates.FindAsync(templateId);
        Assert.NotNull(template);

        template.EnableAreaBasedResponsibility = true;
        template.EnableFindingCategorization = true;
        template.UpdatedAt = DateTime.UtcNow;

        await Context.SaveChangesAsync();
    }

    private async Task<int> CreateFindingCategory(int templateId, string categoryName = "Test Category", int displayOrder = 1)
    {
        var command = new CreateFindingCategoryCommand
        {
            CategoryName = categoryName,
            Description = "Test category description",
            AuditTemplateId = templateId,
            DisplayOrder = displayOrder,
            IsActive = true,
            ColorCode = "#FF5733",
            IconName = "warning-triangle",
            RequiresDocumentation = false
        };

        var result = await _findingCategoryHandler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        return result.Id;
    }

    private async Task<string> CreateAreaResponsibility(int areaId, string userId, int? templateId = null, ResponsibilityType type = ResponsibilityType.CorrectiveActionOwner)
    {
        var command = new CreateAreaResponsibilityCommand
        {
            AreaId = areaId,
            ResponsibilityType = type,
            ResponsibleUserId = userId,
            AuditTemplateId = templateId,
            Priority = 1,
            IsActive = true,
            Description = "Test responsibility"
        };

        var result = await _areaResponsibilityHandler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.NotNull(result.Id);
        return result.Id;
    }

    private async Task VerifyIntegration(int templateId, int areaId, int categoryId, string responsibilityId)
    {
        // Verify template features are enabled
        var template = await Context.AuditTemplates.FindAsync(templateId);
        Assert.NotNull(template);
        Assert.True(template.EnableAreaBasedResponsibility);
        Assert.True(template.EnableFindingCategorization);

        // Verify category exists and is linked to template
        var category = await Context.FindingCategories.FindAsync(categoryId);
        Assert.NotNull(category);
        Assert.Equal(templateId, category.AuditTemplateId);
        Assert.True(category.IsActive);

        // Verify responsibility exists and is linked to area
        var responsibility = await Context.AreaResponsibilities.FindAsync(responsibilityId);
        Assert.NotNull(responsibility);
        Assert.Equal(areaId, responsibility.AreaId);
        Assert.True(responsibility.IsActive);

        // Verify queries work correctly
        var categoryQuery = new GetFindingCategoriesQuery { AuditTemplateId = templateId };
        var categoryResult = await _getFindingCategoriesHandler.Handle(categoryQuery, CancellationToken.None);
        Assert.NotNull(categoryResult);
        Assert.Contains(categoryResult, c => c.Id == categoryId);

        var responsibilityQuery = new GetAreaResponsibilitiesQuery { AreaId = areaId };
        var responsibilityResult = await _getAreaResponsibilitiesHandler.Handle(responsibilityQuery, CancellationToken.None);
        Assert.NotNull(responsibilityResult);
        Assert.Contains(responsibilityResult, r => r.Id == responsibilityId);
    }
}
