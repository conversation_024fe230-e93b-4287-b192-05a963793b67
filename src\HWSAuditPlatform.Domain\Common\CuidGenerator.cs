using System.Security.Cryptography;
using System.Text;

namespace HWSAuditPlatform.Domain.Common;

/// <summary>
/// Generates CUIDs (Collision-resistant Unique Identifiers) following the proper CUID specification.
/// CUIDs are designed to be:
/// - Collision-resistant
/// - Horizontally scalable
/// - Sequentially ordered (roughly)
/// - URL safe
/// - Offline-capable
/// </summary>
public static class CuidGenerator
{
    private static readonly object _lock = new();
    private static int _counter = 0;
    private static readonly string _fingerprint;
    private static readonly Random _random = new();

    static CuidGenerator()
    {
        _fingerprint = GenerateFingerprint();
    }

    /// <summary>
    /// Generates a new CUID
    /// </summary>
    /// <returns>A 25-character CUID string</returns>
    public static string Generate()
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var counter = GetNextCounter();
        
        var cuid = new StringBuilder(25);
        cuid.Append('c'); // prefix (1 char)
        cuid.Append(ToBase36(timestamp).PadLeft(8, '0')); // timestamp (8 chars)
        cuid.Append(ToBase36(counter).PadLeft(4, '0')); // counter (4 chars)
        cuid.Append(_fingerprint); // fingerprint (4 chars)
        cuid.Append(GenerateRandomBlock()); // random (8 chars)
        
        return cuid.ToString();
    }

    /// <summary>
    /// Gets the next counter value in a thread-safe manner
    /// </summary>
    private static int GetNextCounter()
    {
        lock (_lock)
        {
            _counter = (_counter + 1) % 1679616; // 36^4 = 1679616
            return _counter;
        }
    }

    /// <summary>
    /// Generates a machine/process fingerprint
    /// </summary>
    private static string GenerateFingerprint()
    {
        var hostname = Environment.MachineName;
        var processId = Environment.ProcessId;
        
        // Create a hash from hostname and process ID
        using var sha1 = SHA1.Create();
        var input = Encoding.UTF8.GetBytes($"{hostname}{processId}");
        var hash = sha1.ComputeHash(input);
        
        // Convert to base36 and take first 4 characters
        var hashInt = BitConverter.ToUInt32(hash, 0);
        return ToBase36(hashInt).PadLeft(4, '0')[..4];
    }

    /// <summary>
    /// Generates a random block of characters
    /// </summary>
    private static string GenerateRandomBlock()
    {
        var random1 = _random.Next(0, 1679616); // 36^4
        var random2 = _random.Next(0, 1679616); // 36^4
        
        return ToBase36(random1).PadLeft(4, '0') + ToBase36(random2).PadLeft(4, '0');
    }

    /// <summary>
    /// Converts a number to base36 representation
    /// </summary>
    private static string ToBase36(long value)
    {
        const string chars = "0123456789abcdefghijklmnopqrstuvwxyz";
        
        if (value == 0) return "0";
        
        var result = new StringBuilder();
        var absValue = Math.Abs(value);
        
        while (absValue > 0)
        {
            result.Insert(0, chars[(int)(absValue % 36)]);
            absValue /= 36;
        }
        
        return result.ToString();
    }

    /// <summary>
    /// Validates if a string is a valid CUID format
    /// </summary>
    /// <param name="cuid">The string to validate</param>
    /// <returns>True if the string is a valid CUID format</returns>
    public static bool IsValidCuid(string? cuid)
    {
        if (string.IsNullOrEmpty(cuid))
            return false;
            
        if (cuid.Length != 25)
            return false;
            
        if (cuid[0] != 'c')
            return false;
            
        // Check if all characters after 'c' are valid base36 characters
        for (int i = 1; i < cuid.Length; i++)
        {
            var c = cuid[i];
            if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'z')))
                return false;
        }
        
        return true;
    }
}
