@using Microsoft.AspNetCore.Components.Authorization
@using HWSAuditPlatform.WebAuditPWA.Services
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject IAuthenticationService AuthService
@inject IJSRuntime JSRuntime
@inject ILogger<NavMenu> Logger

<!-- Modern Industrial Navigation Bar -->
<nav class="industrial-navbar">
    <div class="navbar-container">
        <!-- Brand Section -->
        <div class="navbar-brand-section">
            <a class="navbar-brand" href="/">
                <div class="brand-icon">
                    <i class="fas fa-industry"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-title">HWS AUDIT</span>
                    <span class="brand-subtitle">PLATFORM</span>
                </div>
            </a>
        </div>

        <!-- Main Navigation -->
        <div class="navbar-nav-section">
            <AuthorizeView>
                <Authorized>
                    <!-- Primary Navigation -->
                    <div class="nav-primary">
                        <NavLink class="nav-item" href="/" Match="NavLinkMatch.All">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </NavLink>
                        <NavLink class="nav-item" href="/audits">
                            <i class="fas fa-clipboard-list"></i>
                            <span>Audits</span>
                        </NavLink>
                        <NavLink class="nav-item" href="/audits/pending">
                            <i class="fas fa-clock"></i>
                            <span>Pending</span>
                        </NavLink>
                        <div class="nav-item dropdown" @onclick="ToggleMoreMenu">
                            <div class="nav-link @(showMoreMenu ? "active" : "")">
                                <i class="fas fa-ellipsis-h"></i>
                                <span>More</span>
                            </div>
                            <div class="dropdown-menu @(showMoreMenu ? "show" : "")">
                                <NavLink class="dropdown-item" href="/audit-history">
                                    <i class="fas fa-history"></i>
                                    <span>History</span>
                                </NavLink>
                                <NavLink class="dropdown-item" href="/correction-requests">
                                    <i class="fas fa-edit"></i>
                                    <span>Corrections</span>
                                </NavLink>
                                <NavLink class="dropdown-item" href="/offline">
                                    <i class="fas fa-wifi-slash"></i>
                                    <span>Offline Mode</span>
                                </NavLink>
                                <NavLink class="dropdown-item" href="/debug">
                                    <i class="fas fa-bug"></i>
                                    <span>Debug</span>
                                </NavLink>
                            </div>
                        </div>
                    </div>

                    <!-- Status & User Section -->
                    <div class="nav-secondary">
                        <!-- Status Indicators -->
                        <div class="status-indicators">
                            <div class="status-item" id="network-status">
                                <i class="fas fa-wifi status-icon online"></i>
                                <span class="status-text d-none d-lg-inline">Online</span>
                            </div>
                            <div class="status-item" id="sync-status">
                                <i class="fas fa-sync-alt status-icon synced"></i>
                                <span class="status-text d-none d-lg-inline">Synced</span>
                                <div class="loading-spinner d-none" id="sync-spinner-nav"></div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="user-menu dropdown" @onclick="ToggleUserMenu">
                            <div class="user-avatar">
                                <i class="fas fa-user-circle"></i>
                                <span class="user-name d-none d-md-inline">@GetUserDisplayName(context)</span>
                                <i class="fas fa-chevron-down dropdown-arrow @(showUserMenu ? "rotated" : "")"></i>
                            </div>
                            <div class="dropdown-menu user-dropdown @(showUserMenu ? "show" : "")">
                                <div class="user-info">
                                    <div class="user-full-name">@context.User.FindFirst("FullName")?.Value</div>
                                    <div class="user-role">@context.User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value</div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="/profile">
                                    <i class="fas fa-user"></i>
                                    <span>Profile</span>
                                </a>
                                <a class="dropdown-item" href="/settings">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                                <button class="dropdown-item" @onclick="SyncDataAsync">
                                    <i class="fas fa-sync"></i>
                                    <span>Sync Data</span>
                                </button>
                                <div class="dropdown-divider"></div>
                                <button class="dropdown-item logout-item" @onclick="LogoutAsync">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Logout</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="nav-secondary">
                        <a class="login-btn" href="/login">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                        </a>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle d-lg-none" @onclick="ToggleMobileMenu">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay @(showMobileMenu ? "show" : "")" @onclick="CloseMobileMenu">
        <div class="mobile-nav-content" @onclick:stopPropagation="true">
            <div class="mobile-nav-header">
                <div class="mobile-brand">
                    <i class="fas fa-industry"></i>
                    <span>HWS AUDIT PLATFORM</span>
                </div>
                <button class="mobile-close-btn" @onclick="CloseMobileMenu">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mobile-nav-body">
                <AuthorizeView>
                    <Authorized>
                        <NavLink class="mobile-nav-item" href="/" Match="NavLinkMatch.All" @onclick="CloseMobileMenu">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/audits" @onclick="CloseMobileMenu">
                            <i class="fas fa-clipboard-list"></i>
                            <span>My Audits</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/audits/pending" @onclick="CloseMobileMenu">
                            <i class="fas fa-clock"></i>
                            <span>Pending</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/audit-history" @onclick="CloseMobileMenu">
                            <i class="fas fa-history"></i>
                            <span>History</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/correction-requests" @onclick="CloseMobileMenu">
                            <i class="fas fa-edit"></i>
                            <span>Corrections</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/offline" @onclick="CloseMobileMenu">
                            <i class="fas fa-wifi-slash"></i>
                            <span>Offline Mode</span>
                        </NavLink>
                        <NavLink class="mobile-nav-item" href="/debug" @onclick="CloseMobileMenu">
                            <i class="fas fa-bug"></i>
                            <span>Debug</span>
                        </NavLink>
                        <div class="mobile-nav-divider"></div>
                        <div class="mobile-user-section">
                            <div class="mobile-user-info">
                                <i class="fas fa-user-circle"></i>
                                <div>
                                    <div class="mobile-user-name">@context.User.FindFirst("FullName")?.Value</div>
                                    <div class="mobile-user-role">@context.User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value</div>
                                </div>
                            </div>
                            <a class="mobile-nav-item" href="/profile" @onclick="CloseMobileMenu">
                                <i class="fas fa-user"></i>
                                <span>Profile</span>
                            </a>
                            <a class="mobile-nav-item" href="/settings" @onclick="CloseMobileMenu">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                            <button class="mobile-nav-item" @onclick="async () => { await SyncDataAsync(); CloseMobileMenu(); }">
                                <i class="fas fa-sync"></i>
                                <span>Sync Data</span>
                            </button>
                            <button class="mobile-nav-item logout-item" @onclick="async () => { await LogoutAsync(); CloseMobileMenu(); }">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </button>
                        </div>
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>
    </div>
</nav>

@code {
    private bool showMobileMenu = false;
    private bool showUserMenu = false;
    private bool showMoreMenu = false;

    private string GetUserDisplayName(AuthenticationState context)
    {
        var fullName = context.User.FindFirst("FullName")?.Value;
        if (!string.IsNullOrEmpty(fullName))
        {
            var parts = fullName.Split(' ');
            return parts.Length > 1 ? $"{parts[0]} {parts[^1][0]}." : parts[0];
        }
        return context.User.Identity?.Name ?? "User";
    }

    private void ToggleMobileMenu()
    {
        showMobileMenu = !showMobileMenu;
        if (showMobileMenu)
        {
            showUserMenu = false;
            showMoreMenu = false;
        }
    }

    private void CloseMobileMenu()
    {
        showMobileMenu = false;
    }

    private void ToggleUserMenu()
    {
        showUserMenu = !showUserMenu;
        if (showUserMenu)
        {
            showMoreMenu = false;
        }
    }

    private void ToggleMoreMenu()
    {
        showMoreMenu = !showMoreMenu;
        if (showMoreMenu)
        {
            showUserMenu = false;
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("addEventListener", "click", DotNetObjectReference.Create(this), "HandleDocumentClick");
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Failed to add document click listener");
            }
        }
    }

    [JSInvokable]
    public void HandleDocumentClick()
    {
        if (showUserMenu || showMoreMenu)
        {
            showUserMenu = false;
            showMoreMenu = false;
            StateHasChanged();
        }
    }

    private async Task LogoutAsync()
    {
        try
        {
            Logger.LogInformation("User logout initiated");
            await AuthService.LogoutAsync();
            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during logout");
            Navigation.NavigateTo("/login", forceLoad: true);
        }
    }

    private async Task SyncDataAsync()
    {
        try
        {
            Logger.LogInformation("Manual sync initiated");
            // Show sync spinner
            try
            {
                await JSRuntime.InvokeVoidAsync("showSyncSpinner");
            }
            catch { /* Ignore JS errors */ }

            // TODO: Implement actual sync functionality
            await Task.Delay(2000); // Simulate sync

            try
            {
                await JSRuntime.InvokeVoidAsync("hideSyncSpinner");
            }
            catch { /* Ignore JS errors */ }

            Logger.LogInformation("Manual sync completed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during manual sync");
            try
            {
                await JSRuntime.InvokeVoidAsync("hideSyncSpinner");
            }
            catch { /* Ignore JS errors */ }
        }
    }

    public void Dispose()
    {
        // Clean up event listeners if needed
    }
}



<style>
    /* Modern Industrial Navigation Bar */
    .industrial-navbar {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border-bottom: 3px solid var(--industrial-teal);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        position: sticky;
        top: 0;
        z-index: 1000;
        height: 70px;
    }

    .navbar-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 0 1.5rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* Brand Section */
    .navbar-brand-section {
        flex-shrink: 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--industrial-text);
        transition: all 0.3s ease;
    }

    .navbar-brand:hover {
        color: var(--industrial-teal-light);
        text-decoration: none;
    }

    .brand-icon {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
    }

    .brand-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .brand-text {
        display: flex;
        flex-direction: column;
        line-height: 1.1;
    }

    .brand-title {
        font-size: 1.2rem;
        font-weight: 700;
        font-family: 'Roboto Condensed', sans-serif;
        text-transform: uppercase;
        letter-spacing: 1.5px;
        color: var(--industrial-text);
    }

    .brand-subtitle {
        font-size: 0.75rem;
        font-weight: 400;
        color: var(--industrial-teal);
        text-transform: uppercase;
        letter-spacing: 2px;
        margin-top: -2px;
    }

    /* Navigation Section */
    .navbar-nav-section {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: space-between;
        margin-left: 2rem;
    }

    .nav-primary {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .nav-secondary {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Navigation Items */
    .nav-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--industrial-text-muted);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        cursor: pointer;
        min-height: 44px;
        touch-action: manipulation;
    }

    .nav-item i {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 16px;
        text-align: center;
    }

    .nav-item:hover {
        background: rgba(20, 184, 166, 0.1);
        color: var(--industrial-teal-light);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .nav-item.active {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
    }

    .nav-item.active:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(20, 184, 166, 0.4);
    }

    /* Dropdown Menus */
    .dropdown {
        position: relative;
    }

    .dropdown-menu {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        background: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1001;
        overflow: hidden;
    }

    .dropdown-menu.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--industrial-text);
        text-decoration: none;
        transition: all 0.2s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        font-size: 0.9rem;
        cursor: pointer;
        min-height: 44px;
        touch-action: manipulation;
    }

    .dropdown-item i {
        margin-right: 0.75rem;
        width: 16px;
        text-align: center;
        color: var(--industrial-teal);
    }

    .dropdown-item:hover {
        background: var(--industrial-light-gray);
        color: var(--industrial-teal-light);
        text-decoration: none;
    }

    .dropdown-item.logout-item:hover {
        background: rgba(229, 62, 62, 0.1);
        color: var(--industrial-red);
    }

    .dropdown-item.logout-item:hover i {
        color: var(--industrial-red);
    }

    .dropdown-divider {
        height: 1px;
        background: var(--industrial-border);
        margin: 0.5rem 0;
        border: none;
    }

    /* Status Indicators */
    .status-indicators {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .status-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        background: rgba(45, 45, 45, 0.5);
        border: 1px solid var(--industrial-border);
        min-height: 36px;
    }

    .status-icon {
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .status-icon.online {
        color: var(--industrial-green);
        animation: pulse 2s infinite;
    }

    .status-icon.offline {
        color: var(--industrial-red);
    }

    .status-icon.synced {
        color: var(--industrial-teal);
    }

    .status-icon.syncing {
        color: var(--industrial-teal);
        animation: spin 1s linear infinite;
    }

    .status-text {
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    @@keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* User Menu */
    .user-menu {
        position: relative;
    }

    .user-avatar {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(45, 45, 45, 0.5);
        border: 1px solid var(--industrial-border);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 44px;
        touch-action: manipulation;
    }

    .user-avatar:hover {
        background: rgba(20, 184, 166, 0.1);
        border-color: var(--industrial-teal);
    }

    .user-avatar i:first-child {
        font-size: 1.5rem;
        color: var(--industrial-teal);
    }

    .user-name {
        color: var(--industrial-text);
        font-weight: 500;
        font-size: 0.9rem;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .dropdown-arrow {
        font-size: 0.7rem;
        color: var(--industrial-text-muted);
        transition: transform 0.3s ease;
    }

    .dropdown-arrow.rotated {
        transform: rotate(180deg);
    }

    .user-dropdown {
        right: 0;
        left: auto;
        min-width: 220px;
    }

    .user-info {
        padding: 1rem;
        border-bottom: 1px solid var(--industrial-border);
        background: rgba(20, 184, 166, 0.05);
    }

    .user-full-name {
        font-weight: 600;
        color: var(--industrial-text);
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .user-role {
        font-size: 0.8rem;
        color: var(--industrial-teal);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    /* Login Button */
    .login-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
        min-height: 44px;
        touch-action: manipulation;
    }

    .login-btn:hover {
        background: linear-gradient(135deg, var(--industrial-teal-light) 0%, var(--industrial-teal) 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(20, 184, 166, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Mobile Menu Toggle */
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 44px;
        height: 44px;
        background: none;
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        gap: 4px;
        touch-action: manipulation;
    }

    .mobile-menu-toggle:hover {
        border-color: var(--industrial-teal);
        background: rgba(20, 184, 166, 0.1);
    }

    .hamburger-line {
        width: 20px;
        height: 2px;
        background: var(--industrial-text);
        transition: all 0.3s ease;
        border-radius: 1px;
    }

    .mobile-menu-toggle:hover .hamburger-line {
        background: var(--industrial-teal);
    }

    /* Mobile Navigation Overlay */
    .mobile-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        backdrop-filter: blur(4px);
    }

    .mobile-nav-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .mobile-nav-content {
        position: absolute;
        top: 0;
        right: 0;
        width: 320px;
        max-width: 85vw;
        height: 100%;
        background: var(--industrial-gray);
        border-left: 3px solid var(--industrial-teal);
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.4);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        overflow-y: auto;
    }

    .mobile-nav-overlay.show .mobile-nav-content {
        transform: translateX(0);
    }

    .mobile-nav-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 2px solid var(--industrial-teal);
    }

    .mobile-brand {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--industrial-text);
        font-weight: 700;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .mobile-brand i {
        font-size: 1.5rem;
        color: var(--industrial-teal);
    }

    .mobile-close-btn {
        width: 40px;
        height: 40px;
        background: none;
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        color: var(--industrial-text);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
    }

    .mobile-close-btn:hover {
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
        background: rgba(20, 184, 166, 0.1);
    }

    .mobile-nav-body {
        padding: 1rem 0;
    }

    .mobile-nav-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1.5rem;
        color: var(--industrial-text);
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        min-height: 56px;
        touch-action: manipulation;
    }

    .mobile-nav-item i {
        width: 20px;
        text-align: center;
        color: var(--industrial-teal);
        font-size: 1.1rem;
    }

    .mobile-nav-item:hover {
        background: rgba(20, 184, 166, 0.1);
        color: var(--industrial-teal-light);
        text-decoration: none;
    }

    .mobile-nav-item.active {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        color: white;
    }

    .mobile-nav-item.active i {
        color: white;
    }

    .mobile-nav-item.logout-item:hover {
        background: rgba(229, 62, 62, 0.1);
        color: var(--industrial-red);
    }

    .mobile-nav-item.logout-item:hover i {
        color: var(--industrial-red);
    }

    .mobile-nav-divider {
        height: 1px;
        background: var(--industrial-border);
        margin: 1rem 0;
    }

    .mobile-user-section {
        padding-top: 1rem;
    }

    .mobile-user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1.5rem;
        background: rgba(20, 184, 166, 0.05);
        border-top: 1px solid var(--industrial-border);
        border-bottom: 1px solid var(--industrial-border);
        margin-bottom: 1rem;
    }

    .mobile-user-info i {
        font-size: 2rem;
        color: var(--industrial-teal);
    }

    .mobile-user-name {
        font-weight: 600;
        color: var(--industrial-text);
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .mobile-user-role {
        font-size: 0.8rem;
        color: var(--industrial-teal);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    /* Responsive Design */
    @@media (max-width: 1199px) {
        .nav-primary .nav-item span {
            display: none;
        }

        .nav-primary .nav-item {
            padding: 0.75rem;
            min-width: 44px;
            justify-content: center;
        }

        .nav-primary .nav-item i {
            margin-right: 0;
        }
    }

    @@media (min-width: 1200px) {
        .mobile-menu-toggle {
            display: none !important;
        }
    }

    @@media (max-width: 991px) {
        .navbar-nav-section {
            display: none;
        }

        .navbar-container {
            padding: 0 1rem;
        }

        .brand-text {
            display: none;
        }

        .brand-icon {
            margin-right: 0;
        }
    }

    @@media (min-width: 768px) and (max-width: 1024px) {
        .industrial-navbar {
            height: 80px;
        }

        .brand-icon {
            width: 50px;
            height: 50px;
        }

        .brand-icon i {
            font-size: 1.75rem;
        }

        .brand-title {
            font-size: 1.4rem;
        }

        .nav-item {
            padding: 1rem 1.25rem;
            font-size: 1rem;
            min-height: 48px;
        }

        .status-item {
            min-height: 40px;
        }

        .user-avatar {
            min-height: 48px;
            padding: 0.75rem 1.25rem;
        }
    }

    /* Touch device optimizations */
    @@media (hover: none) and (pointer: coarse) {
        .nav-item,
        .dropdown-item,
        .mobile-nav-item,
        .user-avatar,
        .status-item,
        .login-btn,
        .mobile-menu-toggle,
        .mobile-close-btn {
            min-height: 48px;
        }

        .nav-item:hover,
        .dropdown-item:hover,
        .mobile-nav-item:hover,
        .user-avatar:hover {
            transform: none;
        }
    }
</style>
