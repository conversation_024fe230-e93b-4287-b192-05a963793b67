using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.Queries.GetUsers;

/// <summary>
/// Query to get a paginated list of users
/// </summary>
public class GetUsersQuery : BaseQuery<PaginatedResult<UserSummaryDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
    public int? FactoryId { get; set; }
    public string? SortBy { get; set; } = "Username";
    public bool SortDescending { get; set; } = false;
}
