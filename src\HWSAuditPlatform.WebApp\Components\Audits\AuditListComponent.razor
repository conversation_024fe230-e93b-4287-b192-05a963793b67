@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@using Microsoft.AspNetCore.Components
@inject IAuditApiService AuditApiService
@inject ILogger<AuditListComponent> Logger
@inject NavigationManager Navigation

<div class="audit-management-container">
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Audit Management</h1>
                <p class="text-muted">View and manage audit activities</p>
            </div>
            <button class="btn btn-primary" @onclick="ShowCreateAuditModal">
                <i class="bi bi-plus-circle me-2"></i>Create Audit
            </button>
        </div>
    </div>

    <!-- User Feedback Messages -->
    <HWSAuditPlatform.WebApp.Components.Shared.ErrorComponent 
        HasError="@(!string.IsNullOrEmpty(errorMessage))"
        Title="Error"
        Message="@errorMessage"
        AlertType="danger"
        OnDismiss="DismissMessages" />

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@successMessage
            <button type="button" class="btn-close" @onclick="DismissMessages"></button>
        </div>
    }

    <!-- Audits Table -->
    <div class="card">
        <div class="card-body">
            <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
                IsLoading="@isLoading" 
                Message="Loading audits..." />

            @if (!isLoading && audits?.Any() == true)
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Audit Template</th>
                                <th>Location</th>
                                <th>Scheduled Date</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var audit in audits)
                            {
                                <tr class="@(IsOverdue(audit) ? "table-warning" : "")">
                                    <td>
                                        <div class="fw-semibold">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</div>
                                    </td>
                                    <td>
                                        <div>@(audit.Factory?.FactoryName ?? "Unknown Factory")</div>
                                        @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                                        {
                                            <small class="text-muted">@audit.Area.AreaName</small>
                                        }
                                    </td>
                                    <td>@audit.ScheduledDate.ToString("MMM dd, yyyy")</td>
                                    <td>
                                        @if (audit.DueDate.HasValue)
                                        {
                                            <span class="@(IsOverdue(audit) ? "text-danger fw-bold" : "")">
                                                @audit.DueDate.Value.ToString("MMM dd, yyyy")
                                            </span>
                                            @if (IsOverdue(audit))
                                            {
                                                <i class="bi bi-exclamation-triangle text-danger ms-1"></i>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">No due date</span>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                            @audit.OverallStatus
                                        </span>
                                    </td>
                                    <td>
                                        @if (audit.OverallScore.HasValue)
                                        {
                                            <span class="badge @GetScoreBadgeClass(audit.OverallScore.Value)">
                                                @audit.OverallScore.Value.ToString("F1")%
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewAudit(audit.Id)"
                                                    title="View audit details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            @if (audit.OverallStatus == AuditOverallStatus.Scheduled)
                                            {
                                                <button class="btn btn-outline-success"
                                                        @onclick="() => StartAudit(audit.Id)"
                                                        disabled="@(isProcessingAction && processingAuditId == audit.Id)"
                                                        title="Start audit">
                                                    @if (isProcessingAction && processingAuditId == audit.Id)
                                                    {
                                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                                    }
                                                    else
                                                    {
                                                        <i class="bi bi-play"></i>
                                                    }
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else if (!isLoading)
            {
                <div class="text-center p-4">
                    <i class="bi bi-clipboard-x" style="font-size: 3rem; color: #ccc;"></i>
                    <h5 class="mt-3">No audits found</h5>
                    <p class="text-muted">Create a new audit to get started.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create Audit Modal -->
@if (showCreateModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>Create New Audit
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseCreateModal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Audit creation functionality will be implemented when the comprehensive API client is available.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseCreateModal">Close</button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .audit-management-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>

@code {
    private IEnumerable<Audit>? audits;
    private bool isLoading = true;
    private bool showCreateModal = false;
    private bool isProcessingAction = false;
    private string? processingAuditId = null;
    private string? successMessage = null;
    private string? errorMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadAudits();
    }

    private async Task LoadAudits()
    {
        try
        {
            isLoading = true;
            audits = await AuditApiService.GetAuditsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audits");
            errorMessage = "Failed to load audits. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private bool IsOverdue(Audit audit)
    {
        return audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now && 
               audit.OverallStatus != AuditOverallStatus.Closed;
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private string GetScoreBadgeClass(decimal score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-primary",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private void ShowCreateAuditModal()
    {
        showCreateModal = true;
    }

    private void CloseCreateModal()
    {
        showCreateModal = false;
    }

    private void ViewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}");
    }

    private void StartAudit(string auditId)
    {
        if (isProcessingAction) return;

        try
        {
            isProcessingAction = true;
            processingAuditId = auditId;
            StateHasChanged();

            // For now, just show a message since we need the comprehensive API
            ShowErrorMessage("Start Audit functionality requires the comprehensive API client to be implemented.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting audit {AuditId}", auditId);
            ShowErrorMessage("An error occurred while starting the audit. Please try again.");
        }
        finally
        {
            isProcessingAction = false;
            processingAuditId = null;
            StateHasChanged();
        }
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
        StateHasChanged();

        _ = Task.Delay(5000).ContinueWith(_ =>
        {
            successMessage = null;
            InvokeAsync(StateHasChanged);
        });
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();

        _ = Task.Delay(8000).ContinueWith(_ =>
        {
            errorMessage = null;
            InvokeAsync(StateHasChanged);
        });
    }

    private void DismissMessages()
    {
        successMessage = null;
        errorMessage = null;
        StateHasChanged();
    }
}
