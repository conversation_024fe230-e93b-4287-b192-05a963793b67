<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 909.296875 1067" style="max-width: 909.297px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .client&gt;*{fill:#D6EAF8!important;stroke:#5DADE2!important;stroke-width:2px!important;}#my-svg .client span{fill:#D6EAF8!important;stroke:#5DADE2!important;stroke-width:2px!important;}#my-svg .api&gt;*{fill:#D1F2EB!important;stroke:#48C9B0!important;stroke-width:2px!important;}#my-svg .api span{fill:#D1F2EB!important;stroke:#48C9B0!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="API_Endpoints" class="cluster"><rect height="1051" width="310" y="8" x="452.734375" style=""/><g transform="translate(579.0859375, 8)" class="cluster-label"><foreignObject height="24" width="57.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web API</p></span></div></foreignObject></g></g><g data-look="classic" id="Clients" class="cluster"><rect height="946" width="252.09375" y="56" x="8" style=""/><g transform="translate(109.4765625, 56)" class="cluster-label"><foreignObject height="24" width="49.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Clients</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PWA_AuthN_AuthZ_0" d="M204.039,286.324L213.382,288.77C222.724,291.216,241.409,296.108,266.805,298.554C292.201,301,324.307,301,356.414,301C388.521,301,420.628,301,455.834,321.018C491.041,341.037,529.348,381.073,548.501,401.092L567.654,421.11"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PWA_AuditExec_0" d="M180.667,241L193.905,233.333C207.143,225.667,233.618,210.333,262.909,202.667C292.201,195,324.307,195,356.414,195C388.521,195,420.628,195,459.741,254.212C498.854,313.424,544.974,431.848,568.034,491.061L591.094,550.273"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PWA_SyncData_0" d="M163.135,241L179.294,226C195.454,211,227.774,181,259.987,166C292.201,151,324.307,151,356.414,151C388.521,151,420.628,151,451.942,144.108C483.257,137.215,513.78,123.431,529.042,116.539L544.303,109.646"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PWA_FindingsCAPA_0" d="M155.185,241L172.67,218.667C190.155,196.333,225.124,151.667,258.662,129.333C292.201,107,324.307,107,356.414,107C388.521,107,420.628,107,451.67,116.477C482.713,125.954,512.691,144.908,527.681,154.385L542.67,163.862"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_AuthN_AuthZ_0" d="M169.498,598L184.597,609.5C199.696,621,229.895,644,261.048,655.5C292.201,667,324.307,667,356.414,667C388.521,667,420.628,667,457.172,640.031C493.717,613.062,534.7,559.123,555.191,532.154L575.682,505.185"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_UserMgmt_0" d="M199.494,598L209.594,602.167C219.694,606.333,239.894,614.667,266.047,618.833C292.201,623,324.307,623,356.414,623C388.521,623,420.628,623,458.266,658.929C495.904,694.857,539.074,766.714,560.659,802.643L582.244,838.571"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_OrgStruct_0" d="M234.143,544L238.468,542.833C242.793,541.667,251.443,539.333,271.822,538.167C292.201,537,324.307,537,356.414,537C388.521,537,420.628,537,457.461,565.958C494.295,594.917,535.856,652.833,556.636,681.792L577.416,710.75"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_TemplateMgmt_0" d="M168.774,544L183.994,532.167C199.214,520.333,229.654,496.667,260.927,484.833C292.201,473,324.307,473,356.414,473C388.521,473,420.628,473,454.823,456.614C489.019,440.227,525.303,407.454,543.445,391.068L561.587,374.681"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_AuditExec_0" d="M158.014,544L175.027,524.833C192.04,505.667,226.067,467.333,259.134,448.167C292.201,429,324.307,429,356.414,429C388.521,429,420.628,429,455.913,449.349C491.199,469.698,529.663,510.395,548.895,530.744L568.127,551.093"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Portal_FindingsCAPA_0" d="M149.106,544L167.604,510.833C186.102,477.667,223.098,411.333,257.649,378.167C292.201,345,324.307,345,356.414,345C388.521,345,420.628,345,454.823,328.614C489.019,312.227,525.303,279.454,543.445,263.068L561.587,246.681"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_AuditExec_0" d="M211.394,944L219.51,946.833C227.627,949.667,243.86,955.333,268.03,958.167C292.201,961,324.307,961,356.414,961C388.521,961,420.628,961,459.518,906.781C498.408,852.562,544.082,744.124,566.918,689.905L589.755,635.686"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_UserMgmt_0" d="M224.609,917L230.523,917C236.438,917,248.266,917,270.233,917C292.201,917,324.307,917,356.414,917C388.521,917,420.628,917,440.198,916.183C459.769,915.366,466.804,913.732,470.321,912.915L473.838,912.098"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_Logging_0" d="M211.394,890L219.51,887.167C227.627,884.333,243.86,878.667,268.03,875.833C292.201,873,324.307,873,356.414,873C388.521,873,420.628,873,456.369,888.75C492.11,904.5,531.485,936.001,551.173,951.751L570.861,967.501"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserMgmt_HWSAP_DB_0" d="M741.734,881L745.234,881C748.734,881,755.734,881,763.401,881C771.068,881,779.401,881,793.616,837.462C807.83,793.925,827.926,706.849,837.974,663.311L848.022,619.774"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OrgStruct_HWSAP_DB_0" d="M741.734,753L745.234,753C748.734,753,755.734,753,763.401,753C771.068,753,779.401,753,792.513,730.704C805.625,708.407,823.517,663.815,832.462,641.519L841.408,619.222"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TemplateMgmt_HWSAP_DB_0" d="M741.734,333L745.234,333C748.734,333,755.734,333,763.401,333C771.068,333,779.401,333,793.303,367.892C807.206,402.784,826.677,472.569,836.413,507.461L846.149,542.353"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuditExec_HWSAP_DB_0" d="M741.734,593L745.234,593C748.734,593,755.734,593,763.401,593C771.068,593,779.401,593,787.077,592.392C794.754,591.784,801.773,590.568,805.283,589.96L808.793,589.352"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SyncData_HWSAP_DB_0" d="M731.352,81L736.582,81C741.813,81,752.273,81,761.671,81C771.068,81,779.401,81,794.214,157.842C809.026,234.683,830.318,388.366,840.964,465.208L851.61,542.049"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FindingsCAPA_HWSAP_DB_0" d="M741.734,205L745.234,205C748.734,205,755.734,205,763.401,205C771.068,205,779.401,205,793.917,261.188C808.434,317.375,829.133,429.751,839.483,485.938L849.833,542.126"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Logging_HWSAP_DB_0" d="M706.117,997L715.553,997C724.99,997,743.862,997,757.465,997C771.068,997,779.401,997,794.032,934.151C808.663,871.302,829.592,745.604,840.057,682.755L850.521,619.906"/></g><g class="edgeLabels"><g transform="translate(356.4140625, 301)" class="edgeLabel"><g transform="translate(-22.8359375, -12)" class="label"><foreignObject height="24" width="45.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 195)" class="edgeLabel"><g transform="translate(-22.8359375, -12)" class="label"><foreignObject height="24" width="45.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 151)" class="edgeLabel"><g transform="translate(-22.8359375, -12)" class="label"><foreignObject height="24" width="45.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 107)" class="edgeLabel"><g transform="translate(-22.8359375, -12)" class="label"><foreignObject height="24" width="45.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 667)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 623)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 537)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 473)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 429)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 345)" class="edgeLabel"><g transform="translate(-52.765625, -12)" class="label"><foreignObject height="24" width="105.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/SignalR</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 961)" class="edgeLabel"><g transform="translate(-71.3203125, -12)" class="label"><foreignObject height="24" width="142.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Internal Call/HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 917)" class="edgeLabel"><g transform="translate(-71.3203125, -12)" class="label"><foreignObject height="24" width="142.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Internal Call/HTTPS</p></span></div></foreignObject></g></g><g transform="translate(356.4140625, 873)" class="edgeLabel"><g transform="translate(-71.3203125, -12)" class="label"><foreignObject height="24" width="142.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Internal Call/HTTPS</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(134.046875, 268)" id="flowchart-PWA-0" class="node default client"><rect height="54" width="139.984375" y="-27" x="-69.9921875" style="fill:#D6EAF8 !important;stroke:#5DADE2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-39.9921875, -12)" style="" class="label"><rect/><foreignObject height="24" width="79.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Blazor PWA</p></span></div></foreignObject></g></g><g transform="translate(134.046875, 571)" id="flowchart-Portal-1" class="node default client"><rect height="54" width="202.09375" y="-27" x="-101.046875" style="fill:#D6EAF8 !important;stroke:#5DADE2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-71.046875, -12)" style="" class="label"><rect/><foreignObject height="24" width="142.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Blazor Server Portal</p></span></div></foreignObject></g></g><g transform="translate(134.046875, 917)" id="flowchart-Scheduler-2" class="node default client"><rect height="54" width="181.125" y="-27" x="-90.5625" style="fill:#D6EAF8 !important;stroke:#5DADE2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.5625, -12)" style="" class="label"><rect/><foreignObject height="24" width="121.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SchedulerWorker</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 463)" id="flowchart-AuthN_AuthZ-3" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authentication &amp; Authorization Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 881)" id="flowchart-UserMgmt-4" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User &amp; Group Management Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 753)" id="flowchart-OrgStruct-5" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Organizational Structure Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 333)" id="flowchart-TemplateMgmt-6" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Audit Template &amp; Question Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 593)" id="flowchart-AuditExec-7" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Audit Execution &amp; Scheduling Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 81)" id="flowchart-SyncData-8" class="node default api"><rect height="54" width="239.234375" y="-27" x="-119.6171875" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-89.6171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="179.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PWA Data Sync Endpoints</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 205)" id="flowchart-FindingsCAPA-9" class="node default api"><rect height="78" width="260" y="-39" x="-130" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Findings &amp; CAPA Endpoints Placeholder</p></span></div></foreignObject></g></g><g transform="translate(607.734375, 997)" id="flowchart-Logging-10" class="node default api"><rect height="54" width="188.765625" y="-27" x="-94.3828125" style="fill:#D1F2EB !important;stroke:#48C9B0 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.3828125, -12)" style="" class="label"><rect/><foreignObject height="24" width="128.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logging Endpoints</p></span></div></foreignObject></g></g><g transform="translate(857.015625, 581)" id="flowchart-HWSAP_DB-38" class="node default"><path transform="translate(-44.28125, -35.05092186128183)" style="" class="basic label-container" d="M0,10.36728124085455 a44.28125,10.36728124085455 0,0,0 88.5625,0 a44.28125,10.36728124085455 0,0,0 -88.5625,0 l0,49.36728124085455 a44.28125,10.36728124085455 0,0,0 88.5625,0 l0,-49.36728124085455"/><g transform="translate(-36.78125, -2)" style="" class="label"><rect/><foreignObject height="24" width="73.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HWSAP DB</p></span></div></foreignObject></g></g></g></g></g></svg>