@if (IsVisible)
{
    <div class="loading-container @ContainerClass">
        @if (ShowOverlay)
        {
            <div class="loading-overlay"></div>
        }
        
        <div class="loading-content d-flex flex-column align-items-center justify-content-center">
            <div class="loading-spinner-large mb-3"></div>
            
            @if (!string.IsNullOrEmpty(Message))
            {
                <div class="loading-message text-center">
                    <h5 class="mb-2">@Message</h5>
                    @if (!string.IsNullOrEmpty(SubMessage))
                    {
                        <p class="text-muted mb-0">@SubMessage</p>
                    }
                </div>
            }
            
            @if (ShowProgress && Progress.HasValue)
            {
                <div class="loading-progress mt-3">
                    <div class="progress" style="width: 200px; height: 8px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: @Progress.Value%"
                             aria-valuenow="@Progress.Value" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                    <small class="text-muted mt-1">@Progress.Value.ToString("F0")%</small>
                </div>
            }
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public string Message { get; set; } = "Loading...";
    [Parameter] public string SubMessage { get; set; } = string.Empty;
    [Parameter] public bool ShowOverlay { get; set; } = false;
    [Parameter] public bool ShowProgress { get; set; } = false;
    [Parameter] public double? Progress { get; set; }
    [Parameter] public string ContainerClass { get; set; } = string.Empty;
    [Parameter] public LoadingSize Size { get; set; } = LoadingSize.Medium;
}

@code {
    public enum LoadingSize
    {
        Small,
        Medium,
        Large
    }
}

<style>
    .loading-container {
        position: relative;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .loading-container.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background-color: rgba(26, 26, 26, 0.9);
        min-height: 100vh;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(26, 26, 26, 0.8);
        z-index: 1;
    }

    .loading-content {
        position: relative;
        z-index: 2;
        color: var(--industrial-text);
    }

    .loading-spinner-large {
        width: 3rem;
        height: 3rem;
        border: 4px solid var(--industrial-border);
        border-radius: 50%;
        border-top-color: var(--industrial-teal);
        animation: spin 1s linear infinite;
    }

    .loading-message h5 {
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .loading-progress {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .loading-progress .progress {
        background-color: var(--industrial-light-gray);
        border-radius: 4px;
    }

    .loading-progress .progress-bar {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        transition: width 0.3s ease;
    }

    @@keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Size variations */
    .loading-container.size-small .loading-spinner-large {
        width: 1.5rem;
        height: 1.5rem;
        border-width: 2px;
    }

    .loading-container.size-small {
        min-height: 100px;
    }

    .loading-container.size-large .loading-spinner-large {
        width: 4rem;
        height: 4rem;
        border-width: 6px;
    }

    .loading-container.size-large {
        min-height: 300px;
    }

    /* Mobile optimizations */
    @@media (max-width: 767px) {
        .loading-container {
            min-height: 150px;
        }
        
        .loading-spinner-large {
            width: 2.5rem;
            height: 2.5rem;
        }
        
        .loading-message h5 {
            font-size: 1.1rem;
        }
        
        .loading-progress {
            width: 150px;
        }
    }

    /* Tablet optimizations */
    @@media (min-width: 768px) and (max-width: 1024px) {
        .loading-container {
            min-height: 250px;
        }
        
        .loading-spinner-large {
            width: 3.5rem;
            height: 3.5rem;
        }
        
        .loading-message h5 {
            font-size: 1.3rem;
        }
        
        .loading-progress {
            width: 250px;
        }
    }
</style>

<!-- Inline loading spinner component for smaller use cases -->
@if (Size == LoadingSize.Small)
{
    <div class="d-inline-flex align-items-center">
        <div class="loading-spinner me-2"></div>
        @if (!string.IsNullOrEmpty(Message))
        {
            <span class="text-muted">@Message</span>
        }
    </div>
}
