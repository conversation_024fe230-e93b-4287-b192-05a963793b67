using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.Tests.WebAuditPWA.Models;

public class AuditAnswerExtensionsTests
{
    [Fact]
    public void GetFailureReasons_WithNoFailureReasons_ShouldReturnEmptyList()
    {
        // Arrange
        var answer = new AuditAnswer();

        // Act
        var result = answer.GetFailureReasons();

        // Assert
        result.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void GetFailureReasons_WithFailureReasons_ShouldReturnReasonTexts()
    {
        // Arrange
        var answer = new AuditAnswer();
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "First reason" });
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "Second reason" });

        // Act
        var result = answer.GetFailureReasons();

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain("First reason");
        result.Should().Contain("Second reason");
    }

    [Fact]
    public void SetFailureReasons_WithValidTexts_ShouldCreateFailureReasonEntities()
    {
        // Arrange
        var answer = new AuditAnswer { Id = "test-id" };
        var reasonTexts = new List<string> { "Reason 1", "Reason 2", "Reason 3" };

        // Act
        answer.SetFailureReasons(reasonTexts);

        // Assert
        answer.FailureReasons.Should().HaveCount(3);
        answer.FailureReasons.Select(fr => fr.ReasonText).Should().BeEquivalentTo(reasonTexts);
        answer.FailureReasons.Should().AllSatisfy(fr => fr.AuditAnswerId.Should().Be("test-id"));
        
        // Check display order
        var orderedReasons = answer.FailureReasons.OrderBy(fr => fr.DisplayOrder).ToList();
        orderedReasons[0].DisplayOrder.Should().Be(0);
        orderedReasons[1].DisplayOrder.Should().Be(1);
        orderedReasons[2].DisplayOrder.Should().Be(2);
    }

    [Fact]
    public void SetFailureReasons_WithEmptyAndWhitespaceTexts_ShouldIgnoreInvalidTexts()
    {
        // Arrange
        var answer = new AuditAnswer { Id = "test-id" };
        var reasonTexts = new List<string> { "Valid reason", "", "  ", "Another valid reason", null! };

        // Act
        answer.SetFailureReasons(reasonTexts);

        // Assert
        answer.FailureReasons.Should().HaveCount(2);
        answer.FailureReasons.Select(fr => fr.ReasonText).Should().BeEquivalentTo(new[] { "Valid reason", "Another valid reason" });
    }

    [Fact]
    public void SetFailureReasons_ShouldClearExistingFailureReasons()
    {
        // Arrange
        var answer = new AuditAnswer { Id = "test-id" };
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "Old reason" });
        var newReasonTexts = new List<string> { "New reason" };

        // Act
        answer.SetFailureReasons(newReasonTexts);

        // Assert
        answer.FailureReasons.Should().HaveCount(1);
        answer.FailureReasons.First().ReasonText.Should().Be("New reason");
    }

    [Fact]
    public void HasFailureReasons_WithValidFailureReasons_ShouldReturnTrue()
    {
        // Arrange
        var answer = new AuditAnswer();
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "Valid reason" });

        // Act
        var result = answer.HasFailureReasons();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasFailureReasons_WithEmptyFailureReasons_ShouldReturnFalse()
    {
        // Arrange
        var answer = new AuditAnswer();

        // Act
        var result = answer.HasFailureReasons();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HasFailureReasons_WithOnlyEmptyReasonTexts_ShouldReturnFalse()
    {
        // Arrange
        var answer = new AuditAnswer();
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "" });
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "   " });

        // Act
        var result = answer.HasFailureReasons();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void SetFailureReasons_WithNullList_ShouldClearFailureReasons()
    {
        // Arrange
        var answer = new AuditAnswer { Id = "test-id" };
        answer.FailureReasons.Add(new AuditAnswerFailureReason { ReasonText = "Existing reason" });

        // Act
        answer.SetFailureReasons(null);

        // Assert
        answer.FailureReasons.Should().BeEmpty();
    }

    [Fact]
    public void SetFailureReasons_ShouldTrimReasonTexts()
    {
        // Arrange
        var answer = new AuditAnswer { Id = "test-id" };
        var reasonTexts = new List<string> { "  Reason with spaces  ", "\tTabbed reason\t" };

        // Act
        answer.SetFailureReasons(reasonTexts);

        // Assert
        answer.FailureReasons.Should().HaveCount(2);
        answer.FailureReasons.Select(fr => fr.ReasonText).Should().BeEquivalentTo(new[] { "Reason with spaces", "Tabbed reason" });
    }
}
