using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for user API operations
/// </summary>
public interface IUserApiService
{
    /// <summary>
    /// Gets all users
    /// </summary>
    Task<IEnumerable<User>> GetUsersAsync();

    /// <summary>
    /// Gets a user by ID
    /// </summary>
    Task<User?> GetUserByIdAsync(string id);

    /// <summary>
    /// Creates a new user
    /// </summary>
    Task<User> CreateUserAsync(User user);

    /// <summary>
    /// Updates an existing user
    /// </summary>
    Task<User> UpdateUserAsync(User user);

    /// <summary>
    /// Deletes a user
    /// </summary>
    Task<bool> DeleteUserAsync(string id);
}
