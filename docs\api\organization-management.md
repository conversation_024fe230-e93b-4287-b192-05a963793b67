# HWS Audit Platform - Organization Management API

## Overview

This document provides comprehensive documentation for the Organization Management endpoints of the HWS Audit Platform API. These endpoints handle the organizational hierarchy including locations, factories, areas, and sub-areas.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **Authentication**: JW<PERSON> <PERSON>er <PERSON> required
- **Content-Type**: `application/json`
- **API Version**: `1.0`

## Organization Hierarchy

The organizational structure follows this hierarchy:
- **Location** (e.g., "North America", "Europe")
  - **Factory** (e.g., "New York Plant", "Berlin Factory")
    - **Area** (e.g., "Production Floor A", "Quality Control")
      - **SubArea** (e.g., "Assembly Line 1", "Packaging Station")

## Location Endpoints

### GET /organization/locations

Get all locations in the system.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `includeInactive` (bool, optional): Include inactive locations (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "North America",
      "code": "NA",
      "description": "North American operations",
      "isActive": true,
      "factoryCount": 5,
      "createdAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Europe",
      "code": "EU",
      "description": "European operations",
      "isActive": true,
      "factoryCount": 3,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/locations" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Factory Endpoints

### GET /organization/factories

Get all factories with pagination and filtering.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `locationId` (int, optional): Filter by location ID
- `searchTerm` (string, optional): Search in factory name or code
- `includeInactive` (bool, optional): Include inactive factories (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Main Factory",
        "code": "MF001",
        "description": "Primary manufacturing facility",
        "locationId": 1,
        "locationName": "North America",
        "address": "123 Industrial Blvd, New York, NY 10001",
        "isActive": true,
        "areaCount": 8,
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 8,
    "totalPages": 1,
    "hasPreviousPage": false,
    "hasNextPage": false
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/factories?locationId=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /organization/factories/{id}

Get detailed information about a specific factory.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (int): Factory ID

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Main Factory",
    "code": "MF001",
    "description": "Primary manufacturing facility",
    "location": {
      "id": 1,
      "name": "North America",
      "code": "NA"
    },
    "address": "123 Industrial Blvd, New York, NY 10001",
    "contactEmail": "<EMAIL>",
    "contactPhone": "******-0123",
    "isActive": true,
    "areas": [
      {
        "id": 5,
        "name": "Production Floor A",
        "code": "PFA",
        "isActive": true,
        "subAreaCount": 4
      }
    ],
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "recordVersion": 3
  }
}
```

**Error Responses**:
- `404 Not Found`: Factory not found

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/factories/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /organization/locations/{id}/factories

Get all factories within a specific location.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (int): Location ID

**Query Parameters**:
- `includeInactive` (bool, optional): Include inactive factories (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Main Factory",
      "code": "MF001",
      "description": "Primary manufacturing facility",
      "address": "123 Industrial Blvd, New York, NY 10001",
      "isActive": true,
      "areaCount": 8
    }
  ]
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/locations/1/factories" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /organization/factories

Create a new factory.

**Authorization**: Manager role or above required

**Request Body**:
```json
{
  "name": "West Coast Factory",
  "code": "WCF001",
  "description": "West coast manufacturing facility",
  "locationId": 1,
  "address": "456 Manufacturing Ave, Los Angeles, CA 90001",
  "contactEmail": "<EMAIL>",
  "contactPhone": "******-0456",
  "isActive": true
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": 9,
  "message": "Factory created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `409 Conflict`: Factory code already exists

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/organization/factories" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "West Coast Factory",
    "code": "WCF001",
    "description": "West coast manufacturing facility",
    "locationId": 1,
    "address": "456 Manufacturing Ave, Los Angeles, CA 90001",
    "contactEmail": "<EMAIL>",
    "contactPhone": "******-0456",
    "isActive": true
  }'
```

### PUT /organization/factories/{id}

Update an existing factory.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (int): Factory ID

**Request Body**:
```json
{
  "id": 1,
  "name": "Main Factory - Updated",
  "code": "MF001",
  "description": "Primary manufacturing facility - updated description",
  "locationId": 1,
  "address": "123 Industrial Blvd, New York, NY 10001",
  "contactEmail": "<EMAIL>",
  "contactPhone": "******-0123",
  "isActive": true,
  "recordVersion": 3
}
```

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Validation errors or ID mismatch
- `404 Not Found`: Factory not found
- `409 Conflict`: Concurrency conflict (record version mismatch) or code already exists

**Example**:
```bash
curl -X PUT "https://localhost:7490/api/v1/organization/factories/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "Main Factory - Updated",
    "code": "MF001",
    "description": "Primary manufacturing facility - updated description",
    "locationId": 1,
    "address": "123 Industrial Blvd, New York, NY 10001",
    "contactEmail": "<EMAIL>",
    "contactPhone": "******-0123",
    "isActive": true,
    "recordVersion": 3
  }'
```

## Area Endpoints

### GET /organization/factories/{id}/areas

Get all areas within a specific factory.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (int): Factory ID

**Query Parameters**:
- `includeInactive` (bool, optional): Include inactive areas (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": [
    {
      "id": 5,
      "name": "Production Floor A",
      "code": "PFA",
      "description": "Main production area for assembly",
      "factoryId": 1,
      "factoryName": "Main Factory",
      "isActive": true,
      "subAreaCount": 4,
      "createdAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": 6,
      "name": "Quality Control",
      "code": "QC",
      "description": "Quality assurance and testing area",
      "factoryId": 1,
      "factoryName": "Main Factory",
      "isActive": true,
      "subAreaCount": 2,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/factories/1/areas" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /organization/factories/{id}/areas

Create a new area within a factory.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (int): Factory ID

**Request Body**:
```json
{
  "name": "Packaging Area",
  "code": "PKG",
  "description": "Product packaging and shipping preparation",
  "isActive": true
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": 12,
  "message": "Area created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `404 Not Found`: Factory not found
- `409 Conflict`: Area code already exists within factory

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/organization/factories/1/areas" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Packaging Area",
    "code": "PKG",
    "description": "Product packaging and shipping preparation",
    "isActive": true
  }'
```

## SubArea Endpoints

### GET /organization/areas/{id}/subareas

Get all sub-areas within a specific area.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (int): Area ID

**Query Parameters**:
- `includeInactive` (bool, optional): Include inactive sub-areas (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": [
    {
      "id": 15,
      "name": "Assembly Line 1",
      "code": "AL1",
      "description": "Primary assembly line for product A",
      "areaId": 5,
      "areaName": "Production Floor A",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": 16,
      "name": "Assembly Line 2",
      "code": "AL2",
      "description": "Secondary assembly line for product B",
      "areaId": 5,
      "areaName": "Production Floor A",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/areas/5/subareas" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /organization/areas/{id}/subareas

Create a new sub-area within an area.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (int): Area ID

**Request Body**:
```json
{
  "name": "Inspection Station",
  "code": "IS1",
  "description": "Final inspection before packaging",
  "isActive": true
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": 25,
  "message": "SubArea created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `404 Not Found`: Area not found
- `409 Conflict`: SubArea code already exists within area

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/organization/areas/5/subareas" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Inspection Station",
    "code": "IS1",
    "description": "Final inspection before packaging",
    "isActive": true
  }'
```

## Hierarchy Endpoint

### GET /organization/hierarchy

Get the complete organizational hierarchy with optional filtering.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `locationId` (int, optional): Filter by specific location
- `includeInactive` (bool, optional): Include inactive entities (default: false)

**Response** (200 OK):
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "North America",
      "code": "NA",
      "isActive": true,
      "factories": [
        {
          "id": 1,
          "name": "Main Factory",
          "code": "MF001",
          "isActive": true,
          "areas": [
            {
              "id": 5,
              "name": "Production Floor A",
              "code": "PFA",
              "isActive": true,
              "subAreas": [
                {
                  "id": 15,
                  "name": "Assembly Line 1",
                  "code": "AL1",
                  "isActive": true
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/organization/hierarchy?locationId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Error Handling

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "name",
      "message": "Name is required"
    }
  ]
}
```

## Business Rules

- Location codes must be unique globally
- Factory codes must be unique within a location
- Area codes must be unique within a factory
- SubArea codes must be unique within an area
- Inactive entities are hidden by default but can be included with `includeInactive=true`
- Only Managers and Admins can create/update organizational entities
- Deletion is soft delete (sets `isActive = false`)
