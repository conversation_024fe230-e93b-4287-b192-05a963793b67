using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Users.Queries.GetUser;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Tests.Application.Users.Queries;

public class GetUserQueryHandlerTests : BaseDbTestClass
{
    private readonly GetUserQueryHandler _handler;

    public GetUserQueryHandlerTests()
    {
        _handler = new GetUserQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnUser()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .FirstAsync(u => u.Username == "admin");
        
        var query = new GetUserQuery(existingUser.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(existingUser.Id);
        result.Username.Should().Be(existingUser.Username);
        result.FirstName.Should().Be(existingUser.FirstName);
        result.LastName.Should().Be(existingUser.LastName);
        result.Email.Should().Be(existingUser.Email);
        result.Role.Should().Be(existingUser.Role.RoleName);
        result.FactoryId.Should().Be(existingUser.FactoryId);
        result.FactoryName.Should().Be(existingUser.Factory?.FactoryName);
        result.IsActive.Should().Be(existingUser.IsActive);
        result.AdObjectGuid.Should().Be(existingUser.AdObjectGuid);
        result.AdDistinguishedName.Should().Be(existingUser.AdDistinguishedName);
        result.CreatedAt.Should().Be(existingUser.CreatedAt);
        result.UpdatedAt.Should().Be(existingUser.UpdatedAt);
        result.RecordVersion.Should().Be(existingUser.RecordVersion);
        result.CreatedByUserId.Should().Be(existingUser.CreatedByUserId);
        result.UpdatedByUserId.Should().Be(existingUser.UpdatedByUserId);
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUserQuery("non-existent-id");

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithUserWithoutFactory_ShouldReturnUserWithNullFactory()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Create a user without factory
        var userWithoutFactory = User.Create(
            username: "nofactory",
            firstName: "No",
            lastName: "Factory",
            email: "<EMAIL>",
            roleId: 1, // Admin role
            factoryId: null,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: "CN=No Factory,OU=Users,DC=test,DC=com",
            createdByUserId: "system"
        );
        
        Context.Users.Add(userWithoutFactory);
        await Context.SaveChangesAsync();
        
        var query = new GetUserQuery (userWithoutFactory.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.FactoryId.Should().BeNull();
        result.FactoryName.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldIncludeRelatedData()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "manager");
        var query = new GetUserQuery (existingUser.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Role.Should().NotBe(default);
        result.FactoryName.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_WithEmptyFirstName_ShouldReturnEmptyString()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Update user to have null first name
        var user = await Context.Users.FirstAsync(u => u.Username == "auditor");
        user.GetType().GetProperty("FirstName")?.SetValue(user, null);
        await Context.SaveChangesAsync();
        
        var query = new GetUserQuery (user.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.FirstName.Should().Be(string.Empty);
    }

    [Fact]
    public async Task Handle_WithEmptyLastName_ShouldReturnEmptyString()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Update user to have null last name
        var user = await Context.Users.FirstAsync(u => u.Username == "auditor");
        user.GetType().GetProperty("LastName")?.SetValue(user, null);
        await Context.SaveChangesAsync();
        
        var query = new GetUserQuery (user.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.LastName.Should().Be(string.Empty);
    }

    [Fact]
    public async Task Handle_ShouldReturnCorrectAuditInformation()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "admin");
        var query = new GetUserQuery (existingUser.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CreatedAt.Should().NotBe(default);
        result.UpdatedAt.Should().NotBe(default);
        result.RecordVersion.Should().BeGreaterThan(0);
        result.CreatedByUserId.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_WithNullAdFields_ShouldReturnNullValues()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Create user without AD fields
        var userWithoutAd = User.Create(
            username: "localuser",
            firstName: "Local",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 3, // Auditor role
            factoryId: 1,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "system"
        );
        
        Context.Users.Add(userWithoutAd);
        await Context.SaveChangesAsync();
        
        var query = new GetUserQuery (userWithoutAd.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.AdObjectGuid.Should().Be("");
        result.AdDistinguishedName.Should().BeNull();
        result.AdSyncLastDate.Should().BeNull();
    }
}
