using FluentAssertions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Templates.Commands.DeleteAuditTemplate;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class DeleteAuditTemplateCommandHandlerTests : BaseDbTestClass
{
    private readonly DeleteAuditTemplateCommandHandler _handler;

    public DeleteAuditTemplateCommandHandlerTests()
    {
        _handler = new DeleteAuditTemplateCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldSoftDeleteTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new DeleteAuditTemplateCommand(template.Id, template.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var deletedTemplate = await Context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == template.Id);

        deletedTemplate.Should().NotBeNull();
        deletedTemplate!.IsActive.Should().BeFalse();
        deletedTemplate.UpdatedByUserId.Should().Be(MockCurrentUserService.Object.UserId);
        deletedTemplate.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldSoftDeleteRelatedQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync();
        
        var command = new DeleteAuditTemplateCommand(template.Id, template.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var questions = await Context.Questions
            .Where(q => q.AuditTemplateId == template.Id)
            .ToListAsync();

        questions.Should().NotBeEmpty();
        questions.Should().OnlyContain(q => !q.IsActive);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldSoftDeleteRelatedQuestionGroups()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionGroupsAsync();
        
        var command = new DeleteAuditTemplateCommand(template.Id, template.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var questionGroups = await Context.QuestionGroups
            .Where(qg => qg.AuditTemplateId == template.Id)
            .ToListAsync();

        questionGroups.Should().NotBeEmpty();
        questionGroups.Should().OnlyContain(qg => !qg.IsActive);
    }

    [Fact]
    public async Task Handle_WithInvalidId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new DeleteAuditTemplateCommand(999, 1);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*AuditTemplate*999*");
    }

    [Fact]
    public async Task Handle_WithInactiveTemplate_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsActive = false;
        await Context.SaveChangesAsync();
        
        var command = new DeleteAuditTemplateCommand(template.Id, template.RecordVersion);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>();
    }

    [Fact]
    public async Task Handle_WithStaleRecordVersion_ShouldThrowConcurrencyException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new DeleteAuditTemplateCommand(template.Id, template.RecordVersion - 1);

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<ConcurrencyException>()
            .WithMessage("*modified by another user*");
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = false,
            IsActive = true,
            CreatedByUserId = MockCurrentUserService.Object.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await Context.AuditTemplates.AddAsync(template);
        await Context.SaveChangesAsync();
        
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithQuestionsAsync()
    {
        var template = await CreateTestTemplateAsync();
        
        var question = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        await Context.Questions.AddAsync(question);
        await Context.SaveChangesAsync();
        
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithQuestionGroupsAsync()
    {
        var template = await CreateTestTemplateAsync();
        
        var questionGroup = new QuestionGroup
        {
            AuditTemplateId = template.Id,
            GroupName = "Test Group",
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        await Context.QuestionGroups.AddAsync(questionGroup);
        await Context.SaveChangesAsync();
        
        return template;
    }
}
