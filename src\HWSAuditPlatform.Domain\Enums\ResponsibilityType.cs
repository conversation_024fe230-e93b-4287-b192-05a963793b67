namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the type of responsibility assigned to users for specific areas.
/// Maps to the responsibility_type enum in the database.
/// </summary>
public enum ResponsibilityType
{
    /// <summary>
    /// Responsible for managing and implementing corrective actions
    /// </summary>
    CorrectiveActionOwner,

    /// <summary>
    /// Responsible for performing retrospective efficiency analysis
    /// </summary>
    RetrospectiveAnalyst,

    /// <summary>
    /// Responsible for reviewing and validating findings
    /// </summary>
    FindingReviewer,

    /// <summary>
    /// Contact person for escalations and critical issues
    /// </summary>
    EscalationContact
}
