using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetMyAudits;

/// <summary>
/// Query to get audits assigned to the current user
/// </summary>
public class GetMyAuditsQuery : BaseQuery<PaginatedResult<AuditSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter by audit status
    /// </summary>
    public AuditOverallStatus? Status { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Show only overdue audits
    /// </summary>
    public bool? IsOverdue { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
