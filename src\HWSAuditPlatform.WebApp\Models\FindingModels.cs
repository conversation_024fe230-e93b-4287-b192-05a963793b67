using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Model for finding display in UI
/// </summary>
public class FindingDto
{
    public string Id { get; set; } = string.Empty;
    public string FindingCode { get; set; } = string.Empty;
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsOpen { get; set; }
    public bool IsOverdue { get; set; }
    
    // Category information
    public int? FindingCategoryId { get; set; }
    public string? FindingCategoryName { get; set; }
    public string? FindingCategoryColorCode { get; set; }
    public string? FindingCategoryIconName { get; set; }
    
    // Responsibility information
    public bool HasResponsibleUser { get; set; }
    public string? ResponsibleUserFullName { get; set; }
    public string? ResponsibleUserName { get; set; }
    public bool HasRetrospectiveAnalyst { get; set; }
    public string? RetrospectiveAnalystUserFullName { get; set; }
    public string? RetrospectiveAnalystUserName { get; set; }
    
    // Audit information
    public string AuditId { get; set; } = string.Empty;
    public string AuditTitle { get; set; } = string.Empty;
    public string AreaName { get; set; } = string.Empty;
    public string FactoryName { get; set; } = string.Empty;
    
    // Corrective actions
    public int CorrectiveActionCount { get; set; }
    public int OpenCorrectiveActionCount { get; set; }
    
    // Dates
    public DateTime? DueDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Model for finding summary (for lists and cards)
/// </summary>
public class FindingSummaryModel
{
    public string Id { get; set; } = string.Empty;
    public string FindingCode { get; set; } = string.Empty;
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsOpen { get; set; }
    public int? FindingCategoryId { get; set; }
    public string? FindingCategoryName { get; set; }
    public string? FindingCategoryColorCode { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Model for finding filters
/// </summary>
public class FindingFilterModel
{
    public string? SearchTerm { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
    public int? CategoryId { get; set; }
    public bool? IsOpen { get; set; }
    public bool? IsOverdue { get; set; }
    public string? ResponsibleUserId { get; set; }
    public int? AreaId { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// Model for finding statistics
/// </summary>
public class FindingStatisticsModel
{
    public int TotalFindings { get; set; }
    public int OpenFindings { get; set; }
    public int ClosedFindings { get; set; }
    public int OverdueFindings { get; set; }
    public int CategorizedFindings { get; set; }
    public int UncategorizedFindings { get; set; }
    
    // By severity
    public int CriticalFindings { get; set; }
    public int MajorFindings { get; set; }
    public int MinorFindings { get; set; }
    public int ObservationFindings { get; set; }
    
    // By responsibility
    public int FindingsWithResponsibleUser { get; set; }
    public int FindingsWithAnalyst { get; set; }
    
    // Trends
    public double AverageResolutionDays { get; set; }
    public int FindingsCreatedThisMonth { get; set; }
    public int FindingsClosedThisMonth { get; set; }
}

/// <summary>
/// Model for finding category assignment
/// </summary>
public class AssignFindingCategoryRequest
{
    public string FindingId { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public string? Reason { get; set; }
}

/// <summary>
/// Model for bulk finding operations
/// </summary>
public class BulkFindingOperationModel
{
    public List<string> FindingIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "assign_category", "assign_responsible", "close", etc.
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Model for finding export
/// </summary>
public class FindingExportModel
{
    public FindingFilterModel? Filter { get; set; }
    public string Format { get; set; } = "excel"; // "excel", "csv", "pdf"
    public List<string> IncludeFields { get; set; } = new();
    public bool IncludeCorrectiveActions { get; set; } = false;
    public bool IncludeAttachments { get; set; } = false;
}
