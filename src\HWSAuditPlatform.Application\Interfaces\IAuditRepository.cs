using System.Linq.Expressions;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Repository interface for Audit entities with audit-specific operations
/// </summary>
public interface IAuditRepository : IRepository<Audit, string>
{
    /// <summary>
    /// Gets audits with basic related data loaded
    /// </summary>
    Task<IReadOnlyList<Audit>> GetAuditsWithDetailsAsync(
        Expression<Func<Audit, bool>>? predicate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a single audit with all related data loaded
    /// </summary>
    Task<Audit?> GetAuditWithFullDetailsAsync(string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets audits assigned to a specific user
    /// </summary>
    Task<IReadOnlyList<Audit>> GetAuditsByUserAsync(
        string userId,
        AuditOverallStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets audits assigned to a specific user group
    /// </summary>
    Task<IReadOnlyList<Audit>> GetAuditsByUserGroupAsync(
        string userGroupId,
        AuditOverallStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all overdue audits
    /// </summary>
    Task<IReadOnlyList<Audit>> GetOverdueAuditsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets audits for a specific factory within a date range
    /// </summary>
    Task<IReadOnlyList<Audit>> GetAuditsByFactoryAsync(
        int factoryId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets audits using a specific template
    /// </summary>
    Task<IReadOnlyList<Audit>> GetAuditsByTemplateAsync(
        int templateId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets count of audits by status
    /// </summary>
    Task<int> GetAuditCountByStatusAsync(
        AuditOverallStatus status,
        int? factoryId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated audits with comprehensive filtering and sorting
    /// </summary>
    Task<(IReadOnlyList<Audit> Items, int TotalCount)> GetPagedAuditsWithFiltersAsync(
        int pageNumber,
        int pageSize,
        string? searchTerm = null,
        AuditOverallStatus? status = null,
        string? assignedToUserId = null,
        int? factoryId = null,
        int? areaId = null,
        int? templateId = null,
        DateTime? scheduledDateFrom = null,
        DateTime? scheduledDateTo = null,
        DateTime? dueDateFrom = null,
        DateTime? dueDateTo = null,
        bool? isOverdue = null,
        string sortBy = "ScheduledDate",
        bool ascending = false,
        CancellationToken cancellationToken = default);
}
