using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Audits.Commands.StartAudit;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Commands;

public class StartAuditCommandHandlerTests : BaseDbTestClass
{
    private readonly StartAuditCommandHandler _handler;

    public StartAuditCommandHandlerTests()
    {
        _handler = new StartAuditCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldStartAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.OverallStatus.Should().Be(AuditOverallStatus.InProgress);
        updatedAudit.StartedAt.Should().NotBeNull();
        updatedAudit.StartedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithNonExistentAudit_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new StartAuditCommand
        {
            AuditId = "non-existent-id"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithInProgressAudit_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(AuditOverallStatus.InProgress);
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithGroupAssignment_ShouldAssignToCurrentUser()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-current-user";
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.Scheduled,
            assignmentType: AssignmentType.GroupAny,
            assignedToUserGroupId: "test-group-id");
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.AssignmentType.Should().Be(AssignmentType.Individual);
        updatedAudit.AssignedToUserId.Should().Be(currentUserId);
    }

    [Fact]
    public async Task Handle_ShouldSetUpdatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-current-user";
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var audit = await CreateTestAuditAsync();
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.UpdatedByUserId.Should().Be(currentUserId);
        updatedAudit.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithCompletedAudit_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(AuditOverallStatus.Closed);
        
        var command = new StartAuditCommand
        {
            AuditId = audit.Id
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    private async Task<Audit> CreateTestAuditAsync(
        AuditOverallStatus status = AuditOverallStatus.Scheduled,
        AssignmentType assignmentType = AssignmentType.Individual,
        string? assignedToUserId = "test-user-id",
        string? assignedToUserGroupId = null)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = CuidGenerator.Generate(),
            AuditTemplateId = template.Id,
            AssignmentType = assignmentType,
            AssignedToUserId = assignedToUserId,
            AssignedToUserGroupId = assignedToUserGroupId,
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = status,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }
}
