# HWS Audit Platform

A comprehensive manufacturing quality audit management system built with .NET 9, implementing Clean Architecture principles with Domain-Driven Design (DDD). The platform supports offline-capable Progressive Web Apps (PWA), Active Directory integration, and enterprise-scale audit workflows.

## 🚀 Quick Start

### Prerequisites
- **.NET 9 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/9.0)
- **SQL Server** - LocalDB, Express, or full version
- **Visual Studio 2024** or **VS Code** with C# extension
- **Docker Desktop** (optional, for containerized development)

### Setup Options

#### Option 1: Local Development
```bash
# Clone and setup
git clone [repository-url]
cd hws-audit-platform
dotnet restore

# Setup database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Run application
dotnet run --project src/HWSAuditPlatform.ApiService
```

#### Option 2: Docker Environment (Recommended)
```powershell
# Start containerized environment
./docker-start.ps1

# Run application with Docker environment
$env:ASPNETCORE_ENVIRONMENT="Docker"
dotnet run --project src/HWSAuditPlatform.ApiService
```

### Access Points
- **API Documentation**: https://localhost:7490/scalar/v1
- **Health Check**: https://localhost:7490/health
- **Swagger UI**: https://localhost:7490/swagger

## 📋 System Overview

### Key Features
- **Digital Audit Management**: Replace paper-based audits with digital workflows
- **Offline Capabilities**: Progressive Web App (PWA) for offline audit execution
- **Enterprise Integration**: Active Directory user management and authentication
- **Template Management**: Create and manage reusable audit templates
- **Evidence Capture**: Attach photos, documents, and notes to audit responses
- **Workflow Management**: Structured audit review and approval processes
- **Real-time Reporting**: Instant visibility into audit progress and results

### Target Users
- **Auditors**: Conduct audits using mobile devices and tablets
- **Audit Managers**: Oversee audit programs and review results
- **System Administrators**: Manage users, templates, and system configuration
- **Quality Managers**: Analyze audit data and generate reports

## 🏗️ Architecture

### Clean Architecture Layers
```
┌─────────────────────────────────────┐
│        Presentation (API)           │  ← Controllers, HTTP handling
├─────────────────────────────────────┤
│         Application                 │  ← Use cases, CQRS, validation
├─────────────────────────────────────┤
│           Domain                    │  ← Business logic, entities
├─────────────────────────────────────┤
│       Infrastructure               │  ← Database, external services
└─────────────────────────────────────┘
```

### Technology Stack
- **.NET 9**: Latest framework with performance improvements
- **ASP.NET Core**: Web API framework
- **Entity Framework Core**: Object-relational mapping
- **SQL Server**: Primary database
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Input validation
- **Local File Storage**: File storage implementation
- **System.DirectoryServices**: Active Directory integration

### Project Structure
```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns
│   ├── HWSAuditPlatform.ApiService/      # Web API controllers
│   ├── HWSAuditPlatform.Web/            # Blazor web application
│   ├── HWSAuditPlatform.AppHost/        # .NET Aspire orchestration
│   ├── HWSAuditPlatform.ServiceDefaults/ # Shared service configuration
│   ├── HWSAuditPlatform.SchedulerWorker/ # Background services
│   └── HWSAuditPlatform.Tests/          # Unit and integration tests
├── docs/                                 # Documentation
└── scripts/                             # Deployment and utility scripts
```

## 📚 Documentation

### For Developers
- **[Getting Started](docs/development/getting-started.md)** - Complete developer onboarding guide
- **[Architecture Overview](docs/architecture/README.md)** - System design and architecture
- **[Testing Guide](docs/development/testing-guide.md)** - Testing procedures and guidelines
- **[Coding Standards](docs/development/coding-standards.md)** - Coding conventions and best practices

### For Deployment
- **[Docker Setup](docs/deployment/docker-setup.md)** - Containerized development environment
- **[Local Development](docs/deployment/local-development.md)** - Local development setup
- **[External Storage](docs/deployment/external-storage.md)** - External file storage configuration
- **[Production Deployment](docs/deployment/production-deployment.md)** - Production deployment guide

### For API Integration
- **[API Documentation](docs/api/README.md)** - Complete API reference
- **[Authentication](docs/api/authentication.md)** - Authentication and user management
- **[Audit Management](docs/api/audit-management.md)** - Audit lifecycle APIs
- **[Integration Guide](docs/api/integration-guide.md)** - Complete integration guide

### For System Understanding
- **[System Overview](docs/user-guides/system-overview.md)** - System capabilities and features
- **[User Hierarchy](docs/architecture/user-hierarchy.md)** - User roles and permissions
- **[Database Design](docs/architecture/database-design.md)** - Database schema and design

## 🧪 Testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run with coverage
.\run-tests.ps1 -Coverage

# Run specific category
.\run-tests.ps1 -Filter "Domain"

# Run in watch mode
.\run-tests.ps1 -Watch
```

### Test Coverage Goals
- **Domain Layer**: 95% coverage (critical business logic)
- **Application Layer**: 85% coverage (use cases)
- **Infrastructure Layer**: 70% coverage (external integrations)
- **Overall Project**: 80% minimum coverage

## 🔧 Development Workflow

### Git Workflow
1. Create feature branch from `develop`
2. Implement feature following coding standards
3. Write comprehensive tests (minimum 80% coverage)
4. Create pull request for code review
5. Merge to `develop` after approval
6. Deploy to staging for testing
7. Merge to `main` for production release

### Key Development Principles
- Follow Clean Architecture principles
- Use Domain-Driven Design (DDD) patterns
- Implement CQRS with MediatR
- Write comprehensive tests
- Maintain high code quality standards

## 🚀 Key Business Benefits

### Efficiency Improvements
- **50% reduction** in audit completion time
- **Elimination** of transcription errors
- **Real-time** audit progress tracking
- **Automated** report generation
- **Streamlined** review and approval processes

### Quality Enhancements
- **Consistent** audit procedures and standards
- **Comprehensive** evidence capture and storage
- **Improved** audit trail and documentation
- **Enhanced** data accuracy and reliability
- **Better** compliance and regulatory adherence

## 📞 Support

### Getting Help
- **Documentation**: Comprehensive documentation in the `/docs` folder
- **Code Examples**: Look at existing entities and handlers for patterns
- **Testing**: Run tests frequently to ensure your changes work correctly
- **Health Checks**: Monitor system health at `/health` endpoint

### Contributing
- Follow the [Contributing Guidelines](docs/development/contributing.md)
- Review [Coding Standards](docs/development/coding-standards.md)
- Ensure tests pass and coverage requirements are met
- Submit pull requests for code review

---

**Ready to get started?** Check out the [Developer Getting Started Guide](docs/development/getting-started.md) for detailed setup instructions.
