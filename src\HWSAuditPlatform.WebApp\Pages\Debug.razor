@page "/debug"
@using HWSAuditPlatform.WebApp.Services
@inject IAuthenticationService AuthService
@inject ILogger<Debug> Logger
@inject NavigationManager Navigation

<PageTitle>Debug - HWS Audit Platform</PageTitle>

<div class="debug-container">
    <div class="page-header mb-4">
        <h1>
            <i class="bi bi-bug me-2"></i>Debug Information
        </h1>
        <p class="text-muted">Development and debugging tools</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-check me-2"></i>Authentication Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="auth-status">
                        <div class="status-item">
                            <span class="status-label">Authenticated:</span>
                            <span class="badge @(isAuthenticated ? "bg-success" : "bg-danger")">
                                @(isAuthenticated ? "Yes" : "No")
                            </span>
                        </div>
                        @if (isAuthenticated && currentUser != null)
                        {
                            <div class="status-item">
                                <span class="status-label">User ID:</span>
                                <span class="status-value">@currentUser.Id</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">Username:</span>
                                <span class="status-value">@currentUser.Username</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">Full Name:</span>
                                <span class="status-value">@currentUser.FullName</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">Email:</span>
                                <span class="status-value">@currentUser.Email</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">Role:</span>
                                <span class="status-value">@(userInfo?.Role ?? "Unknown")</span>
                            </div>
                        }
                    </div>

                    <div class="mt-3">
                        <h6>Quick Actions</h6>
                        <div class="d-grid gap-2">
                            @if (!isAuthenticated)
                            {
                                <button class="btn btn-primary" @onclick="TestLogin">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Test Login
                                </button>
                            }
                            else
                            {
                                <button class="btn btn-outline-danger" @onclick="Logout">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </button>
                            }
                            <button class="btn btn-outline-info" @onclick="RefreshStatus">
                                <i class="bi bi-arrow-clockwise me-2"></i>Refresh Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>Application Info
                    </h5>
                </div>
                <div class="card-body">
                    <div class="app-info">
                        <div class="status-item">
                            <span class="status-label">Environment:</span>
                            <span class="badge bg-info">Development</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Current URL:</span>
                            <span class="status-value small">@Navigation.Uri</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Base URL:</span>
                            <span class="status-value small">@Navigation.BaseUri</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Timestamp:</span>
                            <span class="status-value">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</span>
                        </div>
                    </div>

                    <div class="mt-3">
                        <h6>Navigation</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" @onclick="NavigateToAudits">
                                <i class="bi bi-clipboard-data me-2"></i>Go to Audits
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="NavigateToTemplates">
                                <i class="bi bi-file-earmark-ruled me-2"></i>Go to Templates
                            </button>
                            <button class="btn btn-outline-success" @onclick="NavigateToUsers">
                                <i class="bi bi-people me-2"></i>Go to Users
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-terminal me-2"></i>Debug Console
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Debug Mode Active</strong>
                        <p class="mb-0 mt-2">
                            This page is only available in development mode. It provides debugging information
                            and quick access to development tools.
                        </p>
                    </div>

                    <div class="debug-actions">
                        <h6>Debug Actions</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-outline-warning w-100" @onclick="ClearLocalStorage">
                                    <i class="bi bi-trash me-2"></i>Clear Local Storage
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info w-100" @onclick="TestApiConnection">
                                    <i class="bi bi-wifi me-2"></i>Test API Connection
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-secondary w-100" @onclick="ViewLogs">
                                    <i class="bi bi-journal-text me-2"></i>View Logs
                                </button>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(debugMessage))
                    {
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <strong>Debug Output:</strong>
                                <pre class="mb-0 mt-2">@debugMessage</pre>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .debug-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
        border-radius: 12px 12px 0 0 !important;
    }

    .auth-status, .app-info {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .status-item:last-child {
        border-bottom: none;
    }

    .status-label {
        font-weight: 600;
        color: #495057;
    }

    .status-value {
        color: #6c757d;
        word-break: break-all;
    }

    .debug-actions .btn {
        margin-bottom: 0.5rem;
    }

    pre {
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
</style>

@code {
    private bool isAuthenticated = false;
    private Domain.Entities.Users.User? currentUser = null;
    private Models.UserInfo? userInfo = null;
    private string debugMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await RefreshStatus();
    }

    private async Task RefreshStatus()
    {
        try
        {
            isAuthenticated = await AuthService.IsAuthenticatedAsync();
            if (isAuthenticated)
            {
                userInfo = await AuthService.GetCurrentUserAsync();
                if (userInfo != null)
                {
                    // Create a simple display object since User entity is complex
                    currentUser = Domain.Entities.Users.User.Create(
                        username: userInfo.Username,
                        firstName: userInfo.FullName.Split(' ').FirstOrDefault(),
                        lastName: string.Join(" ", userInfo.FullName.Split(' ').Skip(1)),
                        email: userInfo.Email,
                        roleId: 1, // Default role for display
                        factoryId: null,
                        isActive: userInfo.IsActive,
                        adObjectGuid: userInfo.UserId,
                        adDistinguishedName: null,
                        createdByUserId: null
                    );
                }
            }
            else
            {
                currentUser = null;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error refreshing authentication status");
            debugMessage = $"Error refreshing status: {ex.Message}";
        }

        StateHasChanged();
    }

    private async Task TestLogin()
    {
        try
        {
            debugMessage = "Attempting test login...";
            StateHasChanged();

            // Use test credentials
            var result = await AuthService.LoginAsync("admin.user", "HWSAudit123!");
            if (result.IsSuccess)
            {
                debugMessage = "Test login successful!";
                await RefreshStatus();
            }
            else
            {
                debugMessage = $"Test login failed: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during test login");
            debugMessage = $"Test login error: {ex.Message}";
        }
        
        StateHasChanged();
    }

    private async Task Logout()
    {
        try
        {
            await AuthService.LogoutAsync();
            debugMessage = "Logged out successfully";
            await RefreshStatus();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during logout");
            debugMessage = $"Logout error: {ex.Message}";
        }
        
        StateHasChanged();
    }

    private void ClearLocalStorage()
    {
        debugMessage = "Local storage clearing functionality would be implemented here";
        StateHasChanged();
    }

    private void TestApiConnection()
    {
        debugMessage = "API connection test functionality would be implemented here";
        StateHasChanged();
    }

    private void ViewLogs()
    {
        debugMessage = "Log viewing functionality would be implemented here";
        StateHasChanged();
    }

    private void NavigateToAudits()
    {
        Navigation.NavigateTo("/audits");
    }

    private void NavigateToTemplates()
    {
        Navigation.NavigateTo("/templates");
    }

    private void NavigateToUsers()
    {
        Navigation.NavigateTo("/users");
    }
}
