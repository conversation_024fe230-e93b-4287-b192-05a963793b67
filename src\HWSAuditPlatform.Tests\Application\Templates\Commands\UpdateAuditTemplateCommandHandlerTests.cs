using FluentAssertions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Tests.Common;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class UpdateAuditTemplateCommandHandlerTests : BaseDbTestClass
{
    private readonly UpdateAuditTemplateCommandHandler _handler;

    public UpdateAuditTemplateCommandHandlerTests()
    {
        _handler = new UpdateAuditTemplateCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldUpdateTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = template.Id,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = template.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == template.Id);

        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.TemplateName.Should().Be("Updated Template Name");
        updatedTemplate.Description.Should().Be("Updated description");
        updatedTemplate.Version.Should().Be(2);
        updatedTemplate.UpdatedByUserId.Should().Be(MockCurrentUserService.Object.UserId);
        updatedTemplate.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithInvalidId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = 999,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = 1
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*AuditTemplate*999*");
    }

    [Fact]
    public async Task Handle_WithInactiveTemplate_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsActive = false;
        await Context.SaveChangesAsync();
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = template.Id,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = template.RecordVersion
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>();
    }

    [Fact]
    public async Task Handle_WithStaleRecordVersion_ShouldThrowConcurrencyException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = template.Id,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = template.RecordVersion - 1 // Stale version
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<ConcurrencyException>()
            .WithMessage("*modified by another user*");
    }

    [Fact]
    public async Task Handle_WithPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsPublished = true;
        await Context.SaveChangesAsync();
        
        var command = new UpdateAuditTemplateCommand
        {
            Id = template.Id,
            TemplateName = "Updated Template Name",
            Description = "Updated description",
            Version = 2,
            RecordVersion = template.RecordVersion
        };

        // Act & Assert
        await _handler.Invoking(h => h.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*published template*");
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = false,
            IsActive = true,
            CreatedByUserId = MockCurrentUserService.Object.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await Context.AuditTemplates.AddAsync(template);
        await Context.SaveChangesAsync();
        
        return template;
    }
}
