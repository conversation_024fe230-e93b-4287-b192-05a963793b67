using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.Commands.CreateUser;
using HWSAuditPlatform.Application.Users.Commands.UpdateUser;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Queries.GetUser;
using HWSAuditPlatform.Application.Users.Queries.GetUsers;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for user management operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class UsersController : BaseController
{
    public UsersController(IMediator mediator, ILogger<UsersController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get a paginated list of users
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of users</returns>
    [HttpGet]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<UserSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<UserSummaryDto>>> GetUsers(
        [FromQuery] GetUsersQuery query,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting users with query: {@Query}", query);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(UserDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserDto>> GetUser(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting user with ID: {UserId}", id);
        
        // Users can only view their own profile unless they're SystemManager or DevAdmin
        var currentUserId = GetCurrentUserId();
        if (currentUserId != id && !HasAnyRole("SystemManager", "DevAdmin"))
        {
            Logger.LogWarning("User {CurrentUserId} attempted to access user {UserId} without permission", currentUserId, id);
            return Forbid();
        }

        var query = new GetUserQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    /// <param name="command">User creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created user ID</returns>
    [HttpPost]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(string), 201)]
    public async Task<ActionResult<string>> CreateUser(
        CreateUserCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating user with username: {Username}", command.Username);
        var userId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetUser), new { id = userId }, userId, "User created successfully");
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="command">User update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)]
    public async Task<ActionResult> UpdateUser(
        string id,
        UpdateUserCommand command,
        CancellationToken cancellationToken)
    {
        if (id != command.Id)
        {
            Logger.LogWarning("User ID mismatch: URL ID {UrlId}, Command ID {CommandId}", id, command.Id);
            return BadRequest("User ID mismatch");
        }

        Logger.LogInformation("Updating user with ID: {UserId}", id);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("User updated successfully");
    }

    /// <summary>
    /// Get the current user's profile
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current user's profile</returns>
    [HttpGet("me")]
    [ProducesResponseType(typeof(UserDto), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<UserDto>> GetCurrentUser(CancellationToken cancellationToken)
    {
        var currentUserId = GetCurrentUserId();
        if (string.IsNullOrEmpty(currentUserId))
        {
            Logger.LogWarning("Current user ID not found in claims");
            return Unauthorized();
        }

        Logger.LogInformation("Getting current user profile for ID: {UserId}", currentUserId);
        var query = new GetUserQuery(currentUserId);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Search users by term
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated search results</returns>
    [HttpGet("search")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<UserSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<UserSummaryDto>>> SearchUsers(
        [FromQuery] string searchTerm,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Searching users with term: {SearchTerm}", searchTerm);
        
        var query = new GetUsersQuery
        {
            SearchTerm = searchTerm,
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50), // Limit page size
            IsActive = true // Only return active users in search
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }
}
