using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.Application.Templates.Queries.GetTemplateQuestions;

/// <summary>
/// Query to get all questions for a specific audit template
/// </summary>
public class GetTemplateQuestionsQuery : BaseQuery<List<QuestionDto>>
{
    /// <summary>
    /// The ID of the audit template to get questions for
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// Optional filter by question group ID
    /// </summary>
    public int? QuestionGroupId { get; set; }

    /// <summary>
    /// Whether to include inactive questions (default: false)
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    public GetTemplateQuestionsQuery(int templateId)
    {
        TemplateId = templateId;
    }
}
