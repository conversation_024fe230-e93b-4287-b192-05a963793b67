using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAuditForReview;

/// <summary>
/// Query to get a single audit with detailed review information
/// </summary>
public class GetAuditForReviewQuery : BaseQuery<AuditReviewDto>
{
    /// <summary>
    /// The ID of the audit to retrieve
    /// </summary>
    public string Id { get; set; } = string.Empty;

    public GetAuditForReviewQuery(string id)
    {
        Id = id;
    }

    public GetAuditForReviewQuery() { }
}
