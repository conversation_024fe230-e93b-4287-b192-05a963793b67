using System.Text.Json;
using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Service for finding category API operations
/// </summary>
public class FindingCategoryApiService : IFindingCategoryApiService
{
    private readonly AuthenticatedHttpClientService _httpClientService;
    private readonly ILogger<FindingCategoryApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public FindingCategoryApiService(
        AuthenticatedHttpClientService httpClientService,
        ILogger<FindingCategoryApiService> logger)
    {
        _httpClientService = httpClientService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<List<FindingCategoryModel>> GetFindingCategoriesAsync(FindingCategoryFilterModel? filter = null)
    {
        try
        {
            var queryParams = new List<string>();

            if (filter != null)
            {
                if (filter.AuditTemplateId.HasValue)
                    queryParams.Add($"auditTemplateId={filter.AuditTemplateId}");
                if (filter.IsActive.HasValue)
                    queryParams.Add($"isActive={filter.IsActive}");
                queryParams.Add($"includeUsageStatistics={filter.IncludeUsageStatistics}");
            }

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var response = await _httpClientService.GetAsync($"api/v1/findings/categories{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<FindingCategoryModel>>>(content, _jsonOptions);
                return apiResponse?.Data ?? new List<FindingCategoryModel>();
            }

            _logger.LogWarning("Failed to get finding categories. Status: {StatusCode}", response.StatusCode);
            return new List<FindingCategoryModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting finding categories");
            return new List<FindingCategoryModel>();
        }
    }

    public async Task<FindingCategoryModel?> GetFindingCategoryAsync(int id)
    {
        try
        {
            var response = await _httpClientService.GetAsync($"api/v1/findings/categories/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingCategoryModel>>(content, _jsonOptions);
                return apiResponse?.Data;
            }

            _logger.LogWarning("Failed to get finding category {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting finding category {Id}", id);
            return null;
        }
    }

    public async Task<FindingCategoryModel> CreateFindingCategoryAsync(CreateFindingCategoryModel model)
    {
        try
        {
            var response = await _httpClientService.PostAsJsonAsync("api/v1/findings/categories", model);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingCategoryModel>>(content, _jsonOptions);
                return apiResponse?.Data ?? throw new InvalidOperationException("Failed to create finding category");
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to create finding category. Status: {StatusCode}, Error: {Error}", 
                response.StatusCode, errorContent);
            throw new InvalidOperationException($"Failed to create finding category: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating finding category");
            throw;
        }
    }

    public async Task<FindingCategoryModel> UpdateFindingCategoryAsync(int id, CreateFindingCategoryModel model)
    {
        try
        {
            var response = await _httpClientService.PutAsJsonAsync($"api/v1/findings/categories/{id}", model);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingCategoryModel>>(content, _jsonOptions);
                return apiResponse?.Data ?? throw new InvalidOperationException("Failed to update finding category");
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to update finding category {Id}. Status: {StatusCode}, Error: {Error}", 
                id, response.StatusCode, errorContent);
            throw new InvalidOperationException($"Failed to update finding category: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating finding category {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteFindingCategoryAsync(int id)
    {
        try
        {
            var response = await _httpClientService.DeleteAsync($"api/v1/findings/categories/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting finding category {Id}", id);
            return false;
        }
    }

    public async Task<List<FindingCategoryModel>> GetFindingCategoriesForTemplateAsync(int auditTemplateId, bool includeUsageStatistics = false)
    {
        var filter = new FindingCategoryFilterModel
        {
            AuditTemplateId = auditTemplateId,
            IsActive = true,
            IncludeUsageStatistics = includeUsageStatistics
        };

        return await GetFindingCategoriesAsync(filter);
    }

    public async Task<CategoryStatisticsModel?> GetCategoryStatisticsAsync(int auditTemplateId)
    {
        try
        {
            var response = await _httpClientService.GetAsync($"api/v1/findings/categories/templates/{auditTemplateId}/statistics");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<CategoryStatisticsModel>>(content, _jsonOptions);
                return apiResponse?.Data;
            }

            _logger.LogWarning("Failed to get category statistics for template {TemplateId}. Status: {StatusCode}", 
                auditTemplateId, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category statistics for template {TemplateId}", auditTemplateId);
            return null;
        }
    }

    public async Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId)
    {
        try
        {
            var request = new AssignFindingCategoryModel { FindingCategoryId = categoryId };
            var response = await _httpClientService.PutAsJsonAsync($"api/v1/findings/{findingId}/category", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);
            return false;
        }
    }

    public List<ColorOptionModel> GetColorOptions()
    {
        return SharedConstants.FindingCategories.ColorOptions
            .Select(option => new ColorOptionModel
            {
                ColorCode = option.ColorCode,
                ColorName = option.ColorName,
                IsDefault = option.ColorCode == SharedConstants.FindingCategories.DefaultColorCode
            })
            .ToList();
    }

    public List<IconOptionModel> GetIconOptions()
    {
        return SharedConstants.FindingCategories.IconOptions
            .Select(option => new IconOptionModel
            {
                IconName = option.IconName,
                IconDisplayName = option.DisplayName,
                IconClass = $"fas fa-{option.IconName}",
                IsDefault = option.IconName == SharedConstants.FindingCategories.DefaultIconName
            })
            .ToList();
    }
}
