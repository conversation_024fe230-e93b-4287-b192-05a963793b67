using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents a group of questions within an audit template for organization.
/// Maps to the QuestionGroups table in the database.
/// </summary>
public class QuestionGroup : AuditableEntity<int>
{
    /// <summary>
    /// Links to the audit template this group belongs to
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// Name of the question group (e.g., "Safety Procedures", "Documentation")
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// Order in which this group appears within the template
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Optional description for the question group
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Navigation property for questions in this group
    /// </summary>
    public virtual ICollection<Question> Questions { get; set; } = new List<Question>();

    /// <summary>
    /// Indicates if this question group is active (for soft deletion)
    /// </summary>
    public bool IsActive { get; set; } = true;
}
