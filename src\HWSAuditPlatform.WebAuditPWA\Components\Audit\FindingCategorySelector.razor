@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IFindingCategoryService FindingCategoryService
@inject ILogger<FindingCategorySelector> Logger

<div class="finding-category-selector">
    @if (ShowCategorySelection && categories.Any())
    {
        <div class="category-selection-container">
            <div class="category-header">
                <h6 class="category-title">
                    <i class="fas fa-tags me-2"></i>
                    Categorize Finding
                </h6>
                @if (!string.IsNullOrEmpty(selectedCategoryName))
                {
                    <button class="btn btn-sm btn-outline-secondary" @onclick="ClearCategory">
                        <i class="fas fa-times"></i>
                    </button>
                }
            </div>

            @if (string.IsNullOrEmpty(selectedCategoryName))
            {
                <div class="category-grid">
                    @foreach (var category in categories.OrderBy(c => c.CategoryName))
                    {
                        <button class="category-option @(SelectedCategoryId == category.Id ? "selected" : "")"
                                @onclick="() => SelectCategory(category)">
                            <div class="category-content">
                                @if (!string.IsNullOrEmpty(category.ColorCode))
                                {
                                    <div class="category-indicator" style="background-color: @category.ColorCode;"></div>
                                }
                                @if (!string.IsNullOrEmpty(category.IconName))
                                {
                                    <i class="@GetIconClass(category.IconName) category-icon"></i>
                                }
                                <span class="category-name">@category.CategoryName</span>
                            </div>
                            @if (!string.IsNullOrEmpty(category.Description))
                            {
                                <small class="category-description">@category.Description</small>
                            }
                        </button>
                    }
                </div>
            }
            else
            {
                <div class="selected-category-display">
                    <div class="selected-category">
                        @if (!string.IsNullOrEmpty(selectedCategoryColor))
                        {
                            <div class="category-indicator" style="background-color: @selectedCategoryColor;"></div>
                        }
                        @if (!string.IsNullOrEmpty(selectedCategoryIcon))
                        {
                            <i class="@GetIconClass(selectedCategoryIcon) category-icon"></i>
                        }
                        <span class="category-name">@selectedCategoryName</span>
                        <span class="badge bg-success ms-2">Selected</span>
                    </div>
                </div>
            }
        </div>
    }
    else if (ShowCategorySelection && isLoading)
    {
        <div class="category-loading">
            <div class="spinner-border spinner-border-sm me-2"></div>
            <span>Loading categories...</span>
        </div>
    }
    else if (ShowCategorySelection && !categories.Any())
    {
        <div class="no-categories">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                No categories available for this audit template.
            </small>
        </div>
    }
</div>

<style>
    .finding-category-selector {
        margin-top: 1rem;
    }

    .category-selection-container {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border: 1px solid #dee2e6;
    }

    .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .category-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
    }

    .category-option {
        background: white;
        border: 2px solid #dee2e6;
        border-radius: 6px;
        padding: 0.75rem;
        text-align: left;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .category-option:hover {
        border-color: #007bff;
        box-shadow: 0 2px 4px rgba(0,123,255,0.1);
    }

    .category-option.selected {
        border-color: #007bff;
        background-color: #e7f3ff;
    }

    .category-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        flex-shrink: 0;
    }

    .category-icon {
        color: #6c757d;
        flex-shrink: 0;
    }

    .category-name {
        font-weight: 500;
        color: #495057;
        flex: 1;
    }

    .category-description {
        display: block;
        color: #6c757d;
        margin-top: 0.25rem;
        font-size: 0.8rem;
    }

    .selected-category-display {
        background: white;
        border-radius: 6px;
        padding: 0.75rem;
        border: 2px solid #28a745;
    }

    .selected-category {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-loading,
    .no-categories {
        text-align: center;
        padding: 1rem;
        color: #6c757d;
    }

    @@media (max-width: 576px) {
        .category-grid {
            grid-template-columns: 1fr;
        }
        
        .category-selection-container {
            padding: 0.75rem;
        }
    }
</style>

@code {
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public int? SelectedCategoryId { get; set; }
    [Parameter] public EventCallback<int?> SelectedCategoryIdChanged { get; set; }
    [Parameter] public bool ShowCategorySelection { get; set; } = false;

    private List<FindingCategorySummaryModel> categories = new();
    private bool isLoading = false;
    private string selectedCategoryName = string.Empty;
    private string selectedCategoryColor = string.Empty;
    private string selectedCategoryIcon = string.Empty;

    protected override async Task OnParametersSetAsync()
    {
        if (ShowCategorySelection && AuditTemplateId.HasValue && !categories.Any())
        {
            await LoadCategories();
        }

        UpdateSelectedCategoryDisplay();
    }

    private async Task LoadCategories()
    {
        if (!AuditTemplateId.HasValue) return;

        try
        {
            isLoading = true;
            var result = await FindingCategoryService.GetCategoriesForTemplateAsync(AuditTemplateId.Value);
            
            if (result.IsSuccess && result.Data != null)
            {
                categories = result.Data;
            }
            else
            {
                Logger.LogWarning("Failed to load categories: {Error}", result.ErrorMessage);
                categories = new List<FindingCategorySummaryModel>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading categories for template {TemplateId}", AuditTemplateId);
            categories = new List<FindingCategorySummaryModel>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SelectCategory(FindingCategorySummaryModel category)
    {
        SelectedCategoryId = category.Id;
        await SelectedCategoryIdChanged.InvokeAsync(SelectedCategoryId);
        UpdateSelectedCategoryDisplay();
    }

    private async Task ClearCategory()
    {
        SelectedCategoryId = null;
        await SelectedCategoryIdChanged.InvokeAsync(SelectedCategoryId);
        UpdateSelectedCategoryDisplay();
    }

    private void UpdateSelectedCategoryDisplay()
    {
        if (SelectedCategoryId.HasValue)
        {
            var selectedCategory = categories.FirstOrDefault(c => c.Id == SelectedCategoryId.Value);
            if (selectedCategory != null)
            {
                selectedCategoryName = selectedCategory.CategoryName;
                selectedCategoryColor = selectedCategory.ColorCode ?? string.Empty;
                selectedCategoryIcon = selectedCategory.IconName ?? string.Empty;
            }
        }
        else
        {
            selectedCategoryName = string.Empty;
            selectedCategoryColor = string.Empty;
            selectedCategoryIcon = string.Empty;
        }
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }
}
