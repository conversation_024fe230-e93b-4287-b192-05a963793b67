using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Maps Active Directory groups to application roles.
/// Maps to the AdGroupRoleMapping table in the database.
/// </summary>
public class AdGroupRoleMapping : BaseEntity<int>
{
    /// <summary>
    /// Name of the Active Directory group
    /// </summary>
    [Required]
    [MaxLength(256)]
    public string AdGroupName { get; set; } = string.Empty;

    /// <summary>
    /// Maps to the application's RoleId
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Navigation property for the role
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Description of this mapping
    /// </summary>
    [MaxLength(255)]
    public string? Description { get; set; }

    /// <summary>
    /// Toggle for enabling/disabling mapping
    /// </summary>
    public bool IsActive { get; set; } = true;
}
