# HWS Audit Platform - Docker Setup Guide

This comprehensive guide explains how to set up and run the HWS Audit Platform using Docker containers for SQL Server and Active Directory.

## Overview

The Docker setup provides a complete development environment including:

- **SQL Server 2022**: Main database server with pre-configured databases
- **Samba AD DC**: Active Directory Domain Controller with test users and groups
- **Network Configuration**: Isolated Docker network for secure communication
- **Test Data**: Pre-configured users, groups, and database schemas

## Prerequisites

### Required Software
1. **Docker Desktop** installed and running
2. **PowerShell 7+** (for running setup scripts)
3. **.NET 9 SDK** (for running the application)
4. **Git** (for cloning the repository)

### System Requirements
- **RAM**: Minimum 8GB (16GB recommended)
- **Disk Space**: At least 10GB free space
- **Ports**: Ensure the following ports are available:
  - 1433 (SQL Server)
  - 389, 636 (LDAP/LDAPS)
  - 53 (DNS)
  - 88, 464 (Kerberos)
  - 135, 139, 445 (SMB/NetBIOS)

## Quick Start

### 1. Start Docker Environment

```powershell
# Start all services (first time setup)
./docker-start.ps1

# Clean start (removes existing data)
./docker-start.ps1 -Clean

# Start with verbose output
./docker-start.ps1 -Verbose
```

### 2. Run the Application

```powershell
# Option 1: Run API Service with Docker environment
$env:ASPNETCORE_ENVIRONMENT="Docker"
dotnet run --project src/HWSAuditPlatform.ApiService

# Option 2: Use AppHost (recommended)
dotnet run --project src/HWSAuditPlatform.AppHost
```

### 3. Access the Application

- **API Documentation**: https://localhost:7490/scalar/v1
- **Swagger UI**: https://localhost:7490/swagger
- **Health Check**: https://localhost:7490/health

## Docker Services Configuration

### SQL Server Container

- **Container Name**: `hwsaudit-sqlserver`
- **Image**: `mcr.microsoft.com/mssql/server:2022-latest`
- **Port**: `1433`
- **SA Password**: `************`
- **Application User**: `hwsaudit_user` / `************`

**Databases Created**:
- `HWSAuditPlatformDb` - Main application database
- `HWSAuditPlatformSupport` - Support/runtime database

### Active Directory Container

- **Container Name**: `hwsaudit-ad`
- **Image**: `nowsci/samba-domain`
- **Domain**: `hwsaudit.local`
- **LDAP Port**: `389` (non-SSL for development)
- **LDAPS Port**: `636` (SSL)
- **Admin Password**: `************`

**Service Account**:
- **Username**: `<EMAIL>`
- **Password**: `************`
- **Purpose**: Used by the application to query AD

## Test Users

The following test users are automatically created:

| Username | Email | Password | Role | Group |
|----------|-------|----------|------|-------|
| admin.user | <EMAIL> | ************ | Administrator | HWSAudit-Administrators |
| auditor.user | <EMAIL> | ************ | Auditor | HWSAudit-Auditors |
| manager.user | <EMAIL> | ************ | Manager | HWSAudit-Managers |
| viewer.user | <EMAIL> | ************ | Viewer | HWSAudit-Viewers |

## Application Configuration

### Docker Environment Settings

The application uses `appsettings.Docker.json` when running in Docker environment:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=hwsaudit-sqlserver,1433;Database=HWSAuditPlatformDb;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true",
    "SupportConnection": "Server=hwsaudit-sqlserver,1433;Database=HWSAuditPlatformSupport;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true"
  },
  "ActiveDirectory": {
    "Domain": "hwsaudit.local",
    "Username": "<EMAIL>",
    "Password": "************",
    "SearchBase": "OU=HWSUsers,OU=HWSAudit,DC=hwsaudit,DC=local",
    "UseSSL": false,
    "Port": 389,
    "Server": "hwsaudit-ad"
  }
}
```

### Environment Variables

Environment variables are defined in `.env.docker`:

```bash
MSSQL_SA_PASSWORD=************
AD_DOMAIN=HWSAUDIT
AD_DOMAIN_PASS=************
ASPNETCORE_ENVIRONMENT=Docker
```

## Management Commands

### Starting and Stopping Services

```powershell
# Start all services
./docker-start.ps1

# Start with clean slate (removes all data)
./docker-start.ps1 -Clean

# Stop containers (keeps data)
./docker-stop.ps1

# Stop and remove volumes (removes all data)
./docker-stop.ps1 -RemoveVolumes

# Complete cleanup
./docker-stop.ps1 -Clean
```

### Manual Docker Commands

```powershell
# View running containers
docker-compose ps

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f sqlserver
docker-compose logs -f samba-ad

# Restart a service
docker-compose restart sqlserver

# Execute commands in containers
docker exec -it hwsaudit-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P ************
docker exec -it hwsaudit-ad samba-tool user list
```

## Database Management

### Running Migrations

```powershell
# Set environment
$env:ASPNETCORE_ENVIRONMENT="Docker"

# Run migrations
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

### Direct Database Access

```powershell
# Connect to SQL Server
docker exec -it hwsaudit-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U hwsaudit_user -P ************ -d HWSAuditPlatformDb
```

## Active Directory Management

### Adding Users

```powershell
# Execute commands in AD container
docker exec -it hwsaudit-ad samba-tool user create newuser "Password123!" --userou="OU=Users,OU=HWSAudit,DC=hwsaudit,DC=local"
```

### Managing Groups

```powershell
# List groups
docker exec -it hwsaudit-ad samba-tool group list

# Add user to group
docker exec -it hwsaudit-ad samba-tool group addmembers "HWSAudit-Auditors" newuser
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Ensure ports 1433, 389, 636, 53, 88, 135, 139, 445, 464 are not in use
   - Stop IIS or other services using these ports

2. **Container Health Checks Failing**
   - Wait longer for services to start (AD can take 2-3 minutes)
   - Check container logs: `docker-compose logs -f`

3. **Database Connection Issues**
   - Verify SQL Server container is healthy: `docker inspect hwsaudit-sqlserver`
   - Test connection: `docker exec hwsaudit-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P ************ -Q "SELECT 1"`

4. **Active Directory Authentication Issues**
   - Verify AD container is running: `docker exec hwsaudit-ad samba-tool domain info hwsaudit.local`
   - Test user authentication: `docker exec hwsaudit-ad samba-tool user show admin.user`

### Logs and Debugging

```powershell
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f sqlserver
docker-compose logs -f samba-ad

# Check container health
docker inspect --format='{{.State.Health.Status}}' hwsaudit-sqlserver
docker inspect --format='{{.State.Health.Status}}' hwsaudit-ad
```

### Reset Everything

```powershell
# Complete reset (removes all data)
./docker-stop.ps1 -Clean
./docker-start.ps1 -Clean
```

## Security Notes

⚠️ **Important**: This setup is for development only!

- Default passwords are used (`************`)
- SSL is disabled for LDAP connections
- SA account is enabled in SQL Server
- Containers run with elevated privileges

For production deployment, ensure:
- Strong, unique passwords
- SSL/TLS encryption enabled
- Proper network security
- Regular security updates
- Principle of least privilege

## Next Steps

After successful setup:

1. **Run Database Migrations**: Ensure all tables are created
2. **Test Authentication**: Try logging in with test users
3. **Configure Application**: Update any application-specific settings
4. **Run Tests**: Execute the test suite to verify functionality
5. **Development**: Start developing with the containerized environment

For more information, see:
- [Getting Started Guide](../development/getting-started.md)
- [API Documentation](../api/README.md)
- [Architecture Overview](../architecture/README.md)
