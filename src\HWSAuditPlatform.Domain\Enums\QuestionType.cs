namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the different types of questions that can be asked in an audit.
/// Maps to the question_type enum in the database.
/// </summary>
public enum QuestionType
{
    /// <summary>
    /// Yes/No question type
    /// </summary>
    YesNo,

    /// <summary>
    /// Numeric input question type
    /// </summary>
    Numeric,

    /// <summary>
    /// Single selection from predefined options
    /// </summary>
    SingleSelect,

    /// <summary>
    /// Multiple selections from predefined options
    /// </summary>
    MultiSelect,

    /// <summary>
    /// Short text input
    /// </summary>
    ShortText,

    /// <summary>
    /// Long text input
    /// </summary>
    LongText,

    /// <summary>
    /// Date input
    /// </summary>
    Date
}
