using System.ComponentModel.DataAnnotations;

namespace HWSAuditPlatform.Domain.Common;

/// <summary>
/// Base class for entities that support optimistic concurrency control and audit tracking
/// </summary>
public abstract class AuditableEntity<TKey> : BaseEntity<TKey> where TKey : IEquatable<TKey>
{
    /// <summary>
    /// Version number for optimistic concurrency control during PWA sync
    /// </summary>
    public int RecordVersion { get; set; } = 1;

    /// <summary>
    /// User AD ObjectGUID who created this entity (FK to User.AdObjectGuid)
    /// </summary>
    [MaxLength(36)]
    public string? CreatedByUserId { get; set; }

    /// <summary>
    /// User AD ObjectGUID who last updated this entity (FK to User.AdObjectGuid)
    /// </summary>
    [MaxLength(36)]
    public string? UpdatedByUserId { get; set; }
}
