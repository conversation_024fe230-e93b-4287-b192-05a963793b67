using FluentValidation;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;

/// <summary>
/// Validator for CreateAreaResponsibilityCommand
/// </summary>
public class CreateAreaResponsibilityCommandValidator : AbstractValidator<CreateAreaResponsibilityCommand>
{
    public CreateAreaResponsibilityCommandValidator()
    {
        RuleFor(x => x.AreaId)
            .GreaterThan(0)
            .WithMessage("Area ID must be greater than 0.");

        RuleFor(x => x.ResponsibilityType)
            .IsInEnum()
            .WithMessage("Invalid responsibility type.");

        RuleFor(x => x.ResponsibleUserId)
            .NotEmpty()
            .WithMessage("Responsible user ID is required.")
            .Length(36)
            .WithMessage("Responsible user ID must be 36 characters (AD ObjectGUID).");

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0)
            .When(x => x.AuditTemplateId.HasValue)
            .WithMessage("Audit template ID must be greater than 0 when specified.");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters.");

        RuleFor(x => x.Priority)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Priority must be 0 or greater.");
    }
}
