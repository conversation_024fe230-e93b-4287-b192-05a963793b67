# Test External File Storage Configuration
# This script tests if the external storage is properly configured and accessible

param(
    [string]$StoragePath = "D:\uploads",
    [string]$ApiBaseUrl = "https://localhost:5001"
)

Write-Host "Testing External File Storage Configuration..." -ForegroundColor Green
Write-Host "Storage Path: $StoragePath" -ForegroundColor Yellow
Write-Host "API Base URL: $ApiBaseUrl" -ForegroundColor Yellow
Write-Host ""

# Test 1: Check if storage path exists
Write-Host "Test 1: Checking if storage path exists..." -ForegroundColor Cyan
if (Test-Path $StoragePath) {
    Write-Host "✓ Storage path exists: $StoragePath" -ForegroundColor Green
} else {
    Write-Host "✗ Storage path does not exist: $StoragePath" -ForegroundColor Red
    Write-Host "Run setup-external-storage.ps1 first" -ForegroundColor Yellow
    exit 1
}

# Test 2: Check write permissions
Write-Host "Test 2: Checking write permissions..." -ForegroundColor Cyan
$testFile = Join-Path $StoragePath "test-permissions-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
try {
    "Test file created at $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
    if (Test-Path $testFile) {
        Write-Host "✓ Write permissions OK" -ForegroundColor Green
        Remove-Item $testFile -Force
    } else {
        Write-Host "✗ Failed to create test file" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Write permission test failed: $_" -ForegroundColor Red
}

# Test 3: Check subdirectories
Write-Host "Test 3: Checking subdirectories..." -ForegroundColor Cyan
$expectedSubDirs = @("temp", "audit-attachments", "reports")
foreach ($subDir in $expectedSubDirs) {
    $subDirPath = Join-Path $StoragePath $subDir
    if (Test-Path $subDirPath) {
        Write-Host "✓ Subdirectory exists: $subDir" -ForegroundColor Green
    } else {
        Write-Host "⚠ Subdirectory missing: $subDir (will be created automatically)" -ForegroundColor Yellow
    }
}

# Test 4: Check disk space
Write-Host "Test 4: Checking available disk space..." -ForegroundColor Cyan
try {
    $drive = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq (Split-Path $StoragePath -Qualifier) }
    if ($drive) {
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $usedSpaceGB = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
        $freeSpacePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 1)
        
        Write-Host "✓ Disk space information:" -ForegroundColor Green
        Write-Host "  Total: $totalSpaceGB GB" -ForegroundColor White
        Write-Host "  Used: $usedSpaceGB GB" -ForegroundColor White
        Write-Host "  Free: $freeSpaceGB GB ($freeSpacePercent%)" -ForegroundColor White
        
        if ($freeSpaceGB -lt 1) {
            Write-Host "⚠ Warning: Low disk space (less than 1 GB free)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠ Could not retrieve disk space information" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Error checking disk space: $_" -ForegroundColor Yellow
}

# Test 5: Check configuration files
Write-Host "Test 5: Checking configuration files..." -ForegroundColor Cyan
$configFiles = @(
    "src\HWSAuditPlatform.ApiService\appsettings.json",
    "src\HWSAuditPlatform.ApiService\appsettings.Development.json"
)

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        $content = Get-Content $configFile -Raw
        if ($content -match '"StoragePath":\s*"([^"]+)"') {
            $configStoragePath = $matches[1] -replace '\\\\', '\'
            if ($configStoragePath -eq $StoragePath) {
                Write-Host "✓ Configuration correct in: $configFile" -ForegroundColor Green
            } else {
                Write-Host "✗ Configuration mismatch in: $configFile" -ForegroundColor Red
                Write-Host "  Expected: $StoragePath" -ForegroundColor White
                Write-Host "  Found: $configStoragePath" -ForegroundColor White
            }
        } else {
            Write-Host "⚠ StoragePath not found in: $configFile" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠ Configuration file not found: $configFile" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "External Storage Test Summary:" -ForegroundColor Green
Write-Host "Storage Location: $StoragePath" -ForegroundColor White

# Create a simple test file structure
Write-Host ""
Write-Host "Creating test file structure..." -ForegroundColor Cyan
$testStructure = @{
    "test-upload.txt" = "This is a test upload file created at $(Get-Date)"
    "audit-attachments\test-attachment.txt" = "Test audit attachment"
    "reports\test-report.txt" = "Test report file"
}

foreach ($file in $testStructure.GetEnumerator()) {
    $filePath = Join-Path $StoragePath $file.Key
    $directory = Split-Path $filePath -Parent
    
    if (-not (Test-Path $directory)) {
        New-Item -Path $directory -ItemType Directory -Force | Out-Null
    }
    
    try {
        $file.Value | Out-File -FilePath $filePath -Encoding UTF8
        Write-Host "✓ Created test file: $($file.Key)" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to create test file: $($file.Key) - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Test completed! External storage should be ready for use." -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Start the HWS Audit Platform API service" -ForegroundColor White
Write-Host "2. Test file upload via the API endpoint: POST $ApiBaseUrl/api/v1/files/upload" -ForegroundColor White
Write-Host "3. Verify files are saved to: $StoragePath" -ForegroundColor White
Write-Host ""
Write-Host "To clean up test files, run:" -ForegroundColor Yellow
Write-Host "Remove-Item '$StoragePath\test-*' -Recurse -Force" -ForegroundColor White
