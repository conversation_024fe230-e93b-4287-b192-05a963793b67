@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums

<div class="question-form">
    <div class="row g-3">
        <!-- Question Text -->
        <div class="col-12">
            <label class="form-label">Question Text *</label>
            <input type="text" class="form-control @(IsQuestionTextValid ? "" : "is-invalid")" 
                   @bind="Question.QuestionText" 
                   placeholder="Enter your question..." />
            @if (!IsQuestionTextValid)
            {
                <div class="invalid-feedback">Question text is required.</div>
            }
        </div>
        
        <!-- Question Type and Weight -->
        <div class="col-md-6">
            <label class="form-label">Question Type *</label>
            <select class="form-select @(IsQuestionTypeValid ? "" : "is-invalid")"
                    value="@Question.QuestionType"
                    @onchange="OnQuestionTypeChanged">
                <option value="">Select type...</option>
                <option value="YesNo">Yes/No</option>
                <option value="Numeric">Numeric</option>
                <option value="SingleSelect">Single Select</option>
                <option value="MultiSelect">Multiple Select</option>
                <option value="ShortText">Short Text</option>
                <option value="LongText">Long Text</option>
                <option value="Date">Date</option>
            </select>
            @if (!IsQuestionTypeValid)
            {
                <div class="invalid-feedback">Question type is required.</div>
            }
        </div>
        
        <div class="col-md-6">
            <label class="form-label">Weight</label>
            <input type="number" class="form-control" 
                   @bind="Question.Weight" 
                   min="0" max="100" step="0.1"
                   placeholder="0.0" />
            <div class="form-text">Weight for scoring (0-100)</div>
        </div>
        
        <!-- Severity Level and Display Order -->
        <div class="col-md-6">
            <label class="form-label">Severity Level</label>
            <select class="form-select" @bind="Question.SeverityLevel">
                <option value="">None</option>
                <option value="Minor">Minor</option>
                <option value="Major">Major</option>
                <option value="Critical">Critical</option>
            </select>
        </div>
        
        <div class="col-md-6">
            <label class="form-label">Display Order</label>
            <input type="number" class="form-control" 
                   @bind="Question.DisplayOrder" 
                   min="1" />
        </div>
        
        <!-- Help Text -->
        <div class="col-12">
            <label class="form-label">Help Text</label>
            <textarea class="form-control" rows="2"
                      @bind="Question.HelpText" 
                      placeholder="Optional help text to guide the auditor..."></textarea>
        </div>
        
        <!-- Checkboxes -->
        <div class="col-md-4">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" 
                       @bind="Question.IsRequired" />
                <label class="form-check-label">
                    Required Question
                </label>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" 
                       @bind="Question.EvidenceRequired" />
                <label class="form-check-label">
                    Evidence Required
                </label>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" 
                       @bind="Question.IsActive" />
                <label class="form-check-label">
                    Active Question
                </label>
            </div>
        </div>
        
        <!-- Evidence Types (shown when Evidence Required is checked) -->
        @if (Question.EvidenceRequired)
        {
            <div class="col-12">
                <label class="form-label">Allowed Evidence Types</label>
                <div class="evidence-types-container">
                    @if (availableEvidenceTypes != null && availableEvidenceTypes.Any())
                    {
                        @foreach (var evidenceType in availableEvidenceTypes)
                        {
                            <div class="form-check form-check-inline">
                                <input class="form-check-input"
                                       type="checkbox"
                                       id="<EMAIL>"
                                       checked="@Question.AllowedEvidenceTypes.Contains(evidenceType.EvidenceType.ToString())"
                                       @onchange="@(e => OnEvidenceTypeChanged(evidenceType.EvidenceType, (bool)e.Value!))" />
                                <label class="form-check-label" for="<EMAIL>">
                                    @evidenceType.Name
                                </label>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-muted">Loading evidence types...</div>
                    }
                </div>
                <div class="form-text">Select the types of evidence that can be attached to this question</div>
            </div>
        }
        
        <!-- Question Options (shown for SingleSelect and MultiSelect) -->
        @if (ShowOptionsSection)
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-list-ul me-2"></i>Answer Options
                        </h6>
                        <button type="button" class="btn btn-outline-primary btn-sm" @onclick="AddOption">
                            <i class="bi bi-plus me-1"></i>Add Option
                        </button>
                    </div>
                    <div class="card-body">
                        @if (Question.Options?.Any() == true)
                        {
                            <div class="options-list">
                                @for (int i = 0; i < Question.Options.Count; i++)
                                {
                                    var index = i; // Capture for closure
                                    var option = Question.Options[index];
                                    
                                    <div class="option-item mb-3 p-3 border rounded">
                                        <div class="row g-2 align-items-center">
                                            <div class="col-md-5">
                                                <label class="form-label small">Option Text *</label>
                                                <input type="text" class="form-control form-control-sm" 
                                                       @bind="option.OptionText" 
                                                       placeholder="Enter option text..." />
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label small">Value</label>
                                                <input type="text" class="form-control form-control-sm" 
                                                       @bind="option.OptionValue" 
                                                       placeholder="Internal value..." />
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label small">Order</label>
                                                <input type="number" class="form-control form-control-sm" 
                                                       @bind="option.DisplayOrder" 
                                                       min="1" />
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label small">&nbsp;</label>
                                                <div class="d-flex gap-1">
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            @onclick="() => RemoveOption(index)"
                                                            title="Remove option">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                    @if (index > 0)
                                                    {
                                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                                @onclick="() => MoveOptionUp(index)"
                                                                title="Move up">
                                                            <i class="bi bi-arrow-up"></i>
                                                        </button>
                                                    }
                                                    @if (index < Question.Options.Count - 1)
                                                    {
                                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                                @onclick="() => MoveOptionDown(index)"
                                                                title="Move down">
                                                            <i class="bi bi-arrow-down"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="bi bi-list-ul display-4 text-muted mb-2"></i>
                                <p class="text-muted mb-3">No options added yet</p>
                                <button type="button" class="btn btn-primary btn-sm" @onclick="AddOption">
                                    <i class="bi bi-plus me-1"></i>Add First Option
                                </button>
                            </div>
                        }
                        
                        @if (Question.Options?.Any() == true && !AreOptionsValid)
                        {
                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                All options must have text and at least 2 options are required for select questions.
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
    
    <!-- Action Buttons -->
    <div class="d-flex justify-content-end gap-2 mt-4">
        <button type="button" class="btn btn-outline-secondary" @onclick="OnCancel">
            <i class="bi bi-x-circle me-2"></i>Cancel
        </button>
        <button type="button" class="btn btn-primary" @onclick="() => OnSave.InvokeAsync(Question)" disabled="@(!IsFormValid || IsSaving)">
            @if (IsSaving)
            {
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            }
            else
            {
                <i class="bi bi-check-circle me-2"></i>
            }
            @(IsEditMode ? "Update Question" : "Add Question")
        </button>
    </div>
</div>

@inject IEvidenceTypeService EvidenceTypeService

@code {
    [Parameter] public CreateQuestionRequest Question { get; set; } = new();
    [Parameter] public bool IsEditMode { get; set; } = false;
    [Parameter] public bool IsSaving { get; set; } = false;
    [Parameter] public EventCallback OnCancel { get; set; }
    [Parameter] public EventCallback<CreateQuestionRequest> OnSave { get; set; }

    private List<EvidenceTypeInfo>? availableEvidenceTypes;

    private bool ShowOptionsSection => Question.QuestionType == "SingleSelect" || Question.QuestionType == "MultiSelect";
    
    private bool IsQuestionTextValid => !string.IsNullOrWhiteSpace(Question.QuestionText);
    private bool IsQuestionTypeValid => !string.IsNullOrWhiteSpace(Question.QuestionType);
    
    private bool AreOptionsValid
    {
        get
        {
            if (!ShowOptionsSection) return true;
            
            return Question.Options?.Count >= 2 && 
                   Question.Options.All(o => !string.IsNullOrWhiteSpace(o.OptionText));
        }
    }
    
    private bool IsFormValid => IsQuestionTextValid && IsQuestionTypeValid && AreOptionsValid;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            availableEvidenceTypes = await EvidenceTypeService.GetEvidenceTypesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading evidence types: {ex.Message}");
            // Use fallback evidence types
            availableEvidenceTypes = new List<EvidenceTypeInfo>
            {
                new() { EvidenceType = EvidenceType.Image, Name = "Image", Description = "Image files" },
                new() { EvidenceType = EvidenceType.Document, Name = "Document", Description = "Document files" },
                new() { EvidenceType = EvidenceType.Video, Name = "Video", Description = "Video files" },
                new() { EvidenceType = EvidenceType.Audio, Name = "Audio", Description = "Audio files" },
                new() { EvidenceType = EvidenceType.Spreadsheet, Name = "Spreadsheet", Description = "Spreadsheet files" },
                new() { EvidenceType = EvidenceType.Any, Name = "Any", Description = "Any file type" }
            };
        }
    }

    private void OnEvidenceTypeChanged(EvidenceType evidenceType, bool isSelected)
    {
        var evidenceTypeString = evidenceType.ToString();
        if (isSelected)
        {
            if (!Question.AllowedEvidenceTypes.Contains(evidenceTypeString))
            {
                Question.AllowedEvidenceTypes.Add(evidenceTypeString);
            }
        }
        else
        {
            Question.AllowedEvidenceTypes.Remove(evidenceTypeString);
        }
    }

    private void OnQuestionTypeChanged(ChangeEventArgs e)
    {
        Question.QuestionType = e.Value?.ToString() ?? "";
        
        // Clear options if not a select type
        if (!ShowOptionsSection)
        {
            Question.Options.Clear();
        }
        else if (!Question.Options.Any())
        {
            // Add default options for select types
            AddOption();
            AddOption();
        }
        
        StateHasChanged();
    }

    private void AddOption()
    {
        var nextOrder = Question.Options.Any() ? Question.Options.Max(o => o.DisplayOrder) + 1 : 1;
        
        Question.Options.Add(new CreateQuestionOptionRequest
        {
            OptionText = "",
            OptionValue = "",
            DisplayOrder = nextOrder,
            IsActive = true
        });
    }

    private void RemoveOption(int index)
    {
        if (index >= 0 && index < Question.Options.Count)
        {
            Question.Options.RemoveAt(index);
            
            // Reorder remaining options
            for (int i = 0; i < Question.Options.Count; i++)
            {
                Question.Options[i].DisplayOrder = i + 1;
            }
        }
    }

    private void MoveOptionUp(int index)
    {
        if (index > 0 && index < Question.Options.Count)
        {
            var option = Question.Options[index];
            Question.Options.RemoveAt(index);
            Question.Options.Insert(index - 1, option);
            
            // Update display orders
            for (int i = 0; i < Question.Options.Count; i++)
            {
                Question.Options[i].DisplayOrder = i + 1;
            }
        }
    }

    private void MoveOptionDown(int index)
    {
        if (index >= 0 && index < Question.Options.Count - 1)
        {
            var option = Question.Options[index];
            Question.Options.RemoveAt(index);
            Question.Options.Insert(index + 1, option);
            
            // Update display orders
            for (int i = 0; i < Question.Options.Count; i++)
            {
                Question.Options[i].DisplayOrder = i + 1;
            }
        }
    }
}

<style>
    .question-form .option-item {
        background-color: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border);
        transition: all 0.3s ease;
    }

    .question-form .option-item:hover {
        border-color: var(--industrial-orange);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    .question-form .options-list {
        max-height: 400px;
        overflow-y: auto;
        padding-right: 10px;
    }

    .question-form .options-list::-webkit-scrollbar {
        width: 6px;
    }

    .question-form .options-list::-webkit-scrollbar-track {
        background: var(--industrial-darker);
        border-radius: 3px;
    }

    .question-form .options-list::-webkit-scrollbar-thumb {
        background: var(--industrial-steel);
        border-radius: 3px;
    }

    .question-form .options-list::-webkit-scrollbar-thumb:hover {
        background: var(--industrial-orange);
    }

    .question-form .form-label {
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .question-form .form-label.small {
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
        text-transform: none;
        letter-spacing: normal;
    }

    .question-form .form-check-label {
        color: var(--industrial-text);
        font-weight: 500;
    }

    .question-form .card {
        border: 1px solid var(--industrial-border);
        background-color: var(--industrial-light-gray);
    }

    .question-form .card-header {
        background-color: var(--industrial-darker);
        border-bottom: 1px solid var(--industrial-border);
    }

    .question-form .alert-warning {
        background-color: rgba(255, 193, 7, 0.1);
        border-color: var(--industrial-orange);
        color: #ffc107;
    }

    /* Form Controls Styling */
    .question-form .form-control,
    .question-form .form-select {
        background-color: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 6px;
    }

    .question-form .form-control:focus,
    .question-form .form-select:focus {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-orange);
        color: var(--industrial-text);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }

    .question-form .form-control::placeholder {
        color: var(--industrial-text-muted);
    }

    .question-form .form-select option {
        background-color: var(--industrial-light-gray);
        color: var(--industrial-text);
    }

    .question-form .form-text {
        color: var(--industrial-text-muted);
        font-size: 0.875rem;
    }

    .question-form .invalid-feedback {
        color: var(--industrial-red);
        font-size: 0.875rem;
    }

    /* Button Styling */
    .question-form .btn-primary {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
        font-weight: 600;
    }

    .question-form .btn-primary:hover {
        background-color: #fbb040;
        border-color: #fbb040;
        color: white;
    }

    .question-form .btn-outline-secondary {
        border-color: var(--industrial-steel);
        color: var(--industrial-steel);
    }

    .question-form .btn-outline-secondary:hover {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-steel);
        color: white;
    }

    .question-form .btn-outline-primary {
        border-color: var(--industrial-orange);
        color: var(--industrial-orange);
    }

    .question-form .btn-outline-primary:hover {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
    }

    .question-form .btn-outline-danger {
        border-color: var(--industrial-red);
        color: var(--industrial-red);
    }

    .question-form .btn-outline-danger:hover {
        background-color: var(--industrial-red);
        border-color: var(--industrial-red);
        color: white;
    }

    /* Icon styling */
    .question-form .btn i {
        font-size: 0.875rem;
    }

    /* Evidence types styling */
    .evidence-types-container {
        border: 1px solid var(--industrial-steel-light);
        border-radius: 6px;
        padding: 12px;
        background-color: #f8f9fa;
    }

    .evidence-types-container .form-check-inline {
        margin-right: 20px;
        margin-bottom: 8px;
    }

    .evidence-types-container .form-check-label {
        font-weight: 500;
        color: var(--industrial-dark);
        cursor: pointer;
    }

    .evidence-types-container .form-check-input:checked {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
    }
</style>
