{"openapi": "3.0.1", "info": {"title": "HWS Audit Platform API", "description": "API for the HWS Audit Platform - Manufacturing Quality Audit Management System", "contact": {"name": "HWS Audit Platform Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/v1/Audits": {"get": {"tags": ["Audits"], "summary": "Get a paginated list of audits", "parameters": [{"name": "pageNumber", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Page size", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "description": "Search term", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "Audit status filter", "schema": {"type": "string"}}, {"name": "factoryId", "in": "query", "description": "Factory ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "assignedToUserId", "in": "query", "description": "Assigned user ID filter", "schema": {"type": "string"}}, {"name": "areaId", "in": "query", "description": "Area ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "templateId", "in": "query", "description": "Template ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "scheduledDateFrom", "in": "query", "description": "Scheduled date from filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "scheduledDateTo", "in": "query", "description": "Scheduled date to filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "dueDateFrom", "in": "query", "description": "Due date from filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "dueDateTo", "in": "query", "description": "Due date to filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "isOverdue", "in": "query", "description": "Overdue filter", "schema": {"type": "boolean"}}, {"name": "sortBy", "in": "query", "description": "Sort field", "schema": {"type": "string", "default": "ScheduledDate"}}, {"name": "sortDirection", "in": "query", "description": "Sort direction", "schema": {"type": "string", "default": "desc"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditSummaryDtoPaginatedResult"}}}}}}, "post": {"tags": ["Audits"], "summary": "Create a new audit", "requestBody": {"description": "Audit creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAuditCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAuditCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAuditCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/v1/Audits/{id}": {"get": {"tags": ["Audits"], "summary": "Get a specific audit by ID", "parameters": [{"name": "id", "in": "path", "description": "Audit ID", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Audits/my-audits": {"get": {"tags": ["Audits"], "summary": "Get audits assigned to the current user", "parameters": [{"name": "pageNumber", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Page size", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "description": "Status filter", "schema": {"type": "string"}}, {"name": "factoryId", "in": "query", "description": "Factory ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "areaId", "in": "query", "description": "Area ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "isOverdue", "in": "query", "description": "Overdue filter", "schema": {"type": "boolean"}}, {"name": "sortBy", "in": "query", "description": "Sort field", "schema": {"type": "string", "default": "ScheduledDate"}}, {"name": "sortDirection", "in": "query", "description": "Sort direction", "schema": {"type": "string", "default": "desc"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditSummaryDtoPaginatedResult"}}}}}}}, "/api/v1/Audits/{id}/start": {"post": {"tags": ["Audits"], "summary": "Start an audit", "parameters": [{"name": "id", "in": "path", "description": "Audit ID", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Audits/{id}/submit": {"post": {"tags": ["Audits"], "summary": "Submit an audit for review", "parameters": [{"name": "id", "in": "path", "description": "Audit ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Submit audit request with optional comments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitAuditRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitAuditRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitAuditRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Audits/{id}/review": {"post": {"tags": ["Audits"], "summary": "Review and approve/reject an audit", "parameters": [{"name": "id", "in": "path", "description": "Audit ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Review request containing approval status and comments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewAuditRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReviewAuditRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReviewAuditRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Authenticate user and return JWT token", "requestBody": {"description": "Login request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResult"}}}}}}}, "/api/v1/Auth/test-login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Test login endpoint that bypasses Active Directory (for development/testing only)", "requestBody": {"description": "Login request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResult"}}}}}}}, "/api/v1/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refresh JWT token", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResult"}}}}}}}, "/api/v1/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Logout user (client-side token invalidation)", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success"}}}}, "/api/v1/Auth/test-ad": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Test Active Directory connectivity", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get current user information", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentUserInfo"}}}}}}}, "/api/v1/Dashboard/stats": {"get": {"tags": ["Dashboard"], "summary": "Get dashboard statistics", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardStatsDto"}}}}}}}, "/api/v1/Files/upload": {"post": {"tags": ["Files"], "summary": "Upload a file", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResult"}}}}}}}, "/api/v1/Files/download/{filePath}": {"get": {"tags": ["Files"], "summary": "Download a file", "parameters": [{"name": "filePath", "in": "path", "description": "Path to the file", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Files/download-url/{filePath}": {"get": {"tags": ["Files"], "summary": "Get a temporary download URL for a file", "parameters": [{"name": "filePath", "in": "path", "description": "Path to the file", "required": true, "schema": {"type": "string"}}, {"name": "expiryHours", "in": "query", "description": "URL expiry time in hours (default: 1)", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Files/{filePath}": {"delete": {"tags": ["Files"], "summary": "Delete a file", "parameters": [{"name": "filePath", "in": "path", "description": "Path to the file", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Health": {"get": {"tags": ["Health"], "summary": "Basic health check endpoint", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/Health/info": {"get": {"tags": ["Health"], "summary": "API information endpoint", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/Health/ping": {"get": {"tags": ["Health"], "summary": "Test endpoint for connectivity", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/Health/auth-test": {"get": {"tags": ["Health"], "summary": "Test authenticated endpoint", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/Organization/locations": {"get": {"tags": ["Organization"], "summary": "Get all locations", "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationSummaryDto"}}}}}}}}, "/api/v1/Organization/locations/{locationId}/factories": {"get": {"tags": ["Organization"], "summary": "Get factories for a specific location", "parameters": [{"name": "locationId", "in": "path", "description": "Location ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactorySummaryDto"}}}}}}}}, "/api/v1/Organization/factories": {"get": {"tags": ["Organization"], "summary": "Get all factories (with optional filtering)", "parameters": [{"name": "locationId", "in": "query", "description": "Optional location ID filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "Optional active status filter", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactorySummaryDto"}}}}}}}, "post": {"tags": ["Organization"], "summary": "Create a new factory", "requestBody": {"description": "Factory creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFactoryCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFactoryCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFactoryCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/Organization/factories/{factoryId}/areas": {"get": {"tags": ["Organization"], "summary": "Get areas for a specific factory", "parameters": [{"name": "factoryId", "in": "path", "description": "Factory ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "Optional active status filter", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AreaSummaryDto"}}}}}}}, "post": {"tags": ["Organization"], "summary": "Create a new area", "parameters": [{"name": "factoryId", "in": "path", "description": "Factory ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/Organization/areas/{areaId}/subareas": {"get": {"tags": ["Organization"], "summary": "Get sub-areas for a specific area", "parameters": [{"name": "areaId", "in": "path", "description": "Area ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "Optional active status filter", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubAreaSummaryDto"}}}}}}}, "post": {"tags": ["Organization"], "summary": "Create a new sub-area", "parameters": [{"name": "areaId", "in": "path", "description": "Area ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/Organization/hierarchy": {"get": {"tags": ["Organization"], "summary": "Get the complete organizational hierarchy", "parameters": [{"name": "locationId", "in": "query", "description": "Optional location ID filter", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocationDto"}}}}}}}}, "/api/v1/Templates": {"get": {"tags": ["Templates"], "summary": "Get a paginated list of audit templates", "parameters": [{"name": "pageNumber", "in": "query", "description": "Page number (1-based)", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "description": "Search term to filter templates", "schema": {"type": "string"}}, {"name": "isPublished", "in": "query", "description": "Filter by published status", "schema": {"type": "boolean"}}, {"name": "isActive", "in": "query", "description": "Filter by active status", "schema": {"type": "boolean"}}, {"name": "canBeUsed", "in": "query", "description": "Show only templates that can be used for new audits", "schema": {"type": "boolean"}}, {"name": "sortBy", "in": "query", "description": "Sort field", "schema": {"type": "string", "default": "TemplateName"}}, {"name": "sortDirection", "in": "query", "description": "Sort direction (asc/desc)", "schema": {"type": "string", "default": "asc"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditTemplateSummaryDtoPaginatedResult"}}}}}}, "post": {"tags": ["Templates"], "summary": "Create a new audit template", "requestBody": {"description": "Template creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAuditTemplateCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAuditTemplateCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAuditTemplateCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/Templates/{id}": {"get": {"tags": ["Templates"], "summary": "Get a specific audit template by ID with full details", "parameters": [{"name": "id", "in": "path", "description": "Template ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditTemplateDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "put": {"tags": ["Templates"], "summary": "Update an existing audit template", "parameters": [{"name": "id", "in": "path", "description": "Template ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Template update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAuditTemplateCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAuditTemplateCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAuditTemplateCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["Templates"], "summary": "Soft delete an audit template", "parameters": [{"name": "id", "in": "path", "description": "Template ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "recordVersion", "in": "query", "description": "Record version for concurrency control", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Templates/{id}/publish": {"post": {"tags": ["Templates"], "summary": "Publish an audit template to make it available for use", "parameters": [{"name": "id", "in": "path", "description": "Template ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Templates/{id}/questions": {"post": {"tags": ["Templates"], "summary": "Add a question to an audit template", "parameters": [{"name": "id", "in": "path", "description": "Template ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Question data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddQuestionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddQuestionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddQuestionCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Users": {"get": {"tags": ["Users"], "summary": "Get a paginated list of users", "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"$ref": "#/components/schemas/UserRole"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "FactoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSummaryDtoPaginatedResult"}}}}}}, "post": {"tags": ["Users"], "summary": "Create a new user", "requestBody": {"description": "User creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/v1/Users/<USER>": {"get": {"tags": ["Users"], "summary": "Search users by term", "parameters": [{"name": "searchTerm", "in": "query", "description": "Search term", "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Page size", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiErrorResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSummaryDtoPaginatedResult"}}}}}}}}, "components": {"schemas": {"AddQuestionCommand": {"type": "object", "properties": {"auditTemplateId": {"type": "integer", "format": "int32"}, "questionGroupId": {"type": "integer", "format": "int32", "nullable": true}, "questionText": {"type": "string", "nullable": true}, "questionType": {"$ref": "#/components/schemas/QuestionType"}, "displayOrder": {"type": "integer", "format": "int32"}, "isRequired": {"type": "boolean"}, "weight": {"type": "number", "format": "double", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "parentQuestionId": {"type": "integer", "format": "int32", "nullable": true}, "triggerAnswerValue": {"type": "string", "nullable": true}, "severityLevel": {"$ref": "#/components/schemas/SeverityLevel"}, "evidenceRequired": {"type": "boolean"}, "allowedEvidenceTypes": {"type": "string", "nullable": true}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/AddQuestionOptionDto"}, "nullable": true}}, "additionalProperties": false}, "AddQuestionOptionDto": {"type": "object", "properties": {"optionText": {"type": "string", "nullable": true}, "optionValue": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ApiErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "integer", "description": "HTTP status code", "format": "int32"}, "title": {"type": "string", "description": "Error title", "nullable": true}, "detail": {"type": "string", "description": "Detailed error message", "nullable": true}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "Validation errors (if applicable)", "nullable": true}, "traceId": {"type": "string", "description": "Trace identifier for debugging", "nullable": true}, "timestamp": {"type": "string", "description": "Timestamp when the error occurred", "format": "date-time"}}, "additionalProperties": false, "description": "Standard API error response model"}, "AreaSummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "areaName": {"type": "string", "nullable": true}, "factoryId": {"type": "integer", "format": "int32"}, "factoryName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "subAreaCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AssignmentType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "AuditDto": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "id": {"type": "string", "nullable": true}, "recordVersion": {"type": "integer", "format": "int32"}, "createdByUserId": {"type": "string", "nullable": true}, "updatedByUserId": {"type": "string", "nullable": true}, "auditTemplateId": {"type": "integer", "format": "int32"}, "auditTemplateName": {"type": "string", "nullable": true}, "assignmentType": {"$ref": "#/components/schemas/AssignmentType"}, "assignedToUserGroupId": {"type": "string", "nullable": true}, "assignedToUserGroupName": {"type": "string", "nullable": true}, "assignedToUserId": {"type": "string", "nullable": true}, "assignedToUserName": {"type": "string", "nullable": true}, "scheduledDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "startedAt": {"type": "string", "format": "date-time", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "overallStatus": {"$ref": "#/components/schemas/AuditOverallStatus"}, "factoryId": {"type": "integer", "format": "int32"}, "factoryName": {"type": "string", "nullable": true}, "areaId": {"type": "integer", "format": "int32"}, "areaName": {"type": "string", "nullable": true}, "subAreaId": {"type": "integer", "format": "int32", "nullable": true}, "subAreaName": {"type": "string", "nullable": true}, "overallScore": {"type": "number", "format": "double", "nullable": true}, "managerComments": {"type": "string", "nullable": true}, "reviewedByUserId": {"type": "string", "nullable": true}, "reviewedByUserName": {"type": "string", "nullable": true}, "reviewedAt": {"type": "string", "format": "date-time", "nullable": true}, "recurringAuditSettingId": {"type": "string", "nullable": true}, "isOverdue": {"type": "boolean", "readOnly": true}, "isInProgress": {"type": "boolean", "readOnly": true}, "canBeStarted": {"type": "boolean", "readOnly": true}, "canBeSubmitted": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "AuditOverallStatus": {"enum": [0, 1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "AuditSummaryDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "auditTemplateName": {"type": "string", "nullable": true}, "assignedToUserName": {"type": "string", "nullable": true}, "scheduledDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "overallStatus": {"$ref": "#/components/schemas/AuditOverallStatus"}, "factoryName": {"type": "string", "nullable": true}, "areaName": {"type": "string", "nullable": true}, "overallScore": {"type": "number", "format": "double", "nullable": true}, "isOverdue": {"type": "boolean"}}, "additionalProperties": false}, "AuditSummaryDtoPaginatedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AuditSummaryDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "AuditTemplateDto": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int32"}, "recordVersion": {"type": "integer", "format": "int32"}, "createdByUserId": {"type": "string", "nullable": true}, "updatedByUserId": {"type": "string", "nullable": true}, "templateName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "integer", "format": "int32"}, "isPublished": {"type": "boolean"}, "isActive": {"type": "boolean"}, "fullName": {"type": "string", "nullable": true}, "canBeUsed": {"type": "boolean"}, "questionGroups": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionGroupDto"}, "nullable": true}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionDto"}, "nullable": true}}, "additionalProperties": false}, "AuditTemplateSummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "templateName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "integer", "format": "int32"}, "isPublished": {"type": "boolean"}, "isActive": {"type": "boolean"}, "fullName": {"type": "string", "nullable": true}, "canBeUsed": {"type": "boolean"}, "questionCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AuditTemplateSummaryDtoPaginatedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AuditTemplateSummaryDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "AuthenticationResult": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token", "nullable": true}, "username": {"type": "string", "description": "Username", "nullable": true}, "email": {"type": "string", "description": "Email address", "nullable": true}, "fullName": {"type": "string", "description": "Full name", "nullable": true}, "role": {"type": "string", "description": "User role", "nullable": true}, "factoryId": {"type": "string", "description": "Factory ID", "nullable": true}, "expiresAt": {"type": "string", "description": "Token expiration time", "format": "date-time"}}, "additionalProperties": false, "description": "Authentication result model"}, "CreateAuditCommand": {"type": "object", "properties": {"auditTemplateId": {"type": "integer", "format": "int32"}, "assignmentType": {"$ref": "#/components/schemas/AssignmentType"}, "assignedToUserGroupId": {"type": "string", "nullable": true}, "assignedToUserId": {"type": "string", "nullable": true}, "scheduledDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "factoryId": {"type": "integer", "format": "int32"}, "areaId": {"type": "integer", "format": "int32"}, "subAreaId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateAuditTemplateCommand": {"type": "object", "properties": {"templateName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "integer", "format": "int32"}, "isPublished": {"type": "boolean"}}, "additionalProperties": false}, "CreateFactoryCommand": {"type": "object", "properties": {"factoryName": {"type": "string", "nullable": true}, "factoryProcess": {"type": "string", "nullable": true}, "locationId": {"type": "integer", "format": "int32"}, "addressLine1": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateUserCommand": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "factoryId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "adObjectGuid": {"type": "string", "nullable": true}, "adDistinguishedName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CurrentUserInfo": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID", "nullable": true}, "username": {"type": "string", "description": "Username", "nullable": true}, "email": {"type": "string", "description": "Email address", "nullable": true}, "fullName": {"type": "string", "description": "Full name", "nullable": true}, "role": {"type": "string", "description": "User role", "nullable": true}, "factoryId": {"type": "string", "description": "Factory ID", "nullable": true}}, "additionalProperties": false, "description": "Current user information model"}, "DashboardStatsDto": {"type": "object", "properties": {"totalAudits": {"type": "integer", "format": "int32"}, "pendingAudits": {"type": "integer", "format": "int32"}, "overdueAuditsCount": {"type": "integer", "format": "int32"}, "completedAuditsThisMonth": {"type": "integer", "format": "int32"}, "averageScore": {"type": "number", "format": "double"}, "recentAudits": {"type": "array", "items": {"$ref": "#/components/schemas/AuditSummaryDto"}, "nullable": true}, "overdueAudits": {"type": "array", "items": {"$ref": "#/components/schemas/AuditSummaryDto"}, "nullable": true}}, "additionalProperties": false, "description": "Dashboard statistics DTO"}, "FactorySummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "factoryName": {"type": "string", "nullable": true}, "locationName": {"type": "string", "nullable": true}, "locationCountry": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "areaCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "FileUploadResult": {"type": "object", "properties": {"filePath": {"type": "string", "description": "Path to the uploaded file", "nullable": true}, "fileName": {"type": "string", "description": "Original filename", "nullable": true}, "contentType": {"type": "string", "description": "Content type of the file", "nullable": true}, "size": {"type": "integer", "description": "File size in bytes", "format": "int64"}, "uploadedAt": {"type": "string", "description": "Upload timestamp", "format": "date-time"}}, "additionalProperties": false, "description": "File upload result model"}, "LocationDto": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int32"}, "locationName": {"type": "string", "nullable": true}, "locationCountry": {"type": "string", "nullable": true}, "locationCountryCode": {"type": "string", "nullable": true}, "ownerGroupId": {"type": "string", "nullable": true}, "factories": {"type": "array", "items": {"$ref": "#/components/schemas/FactorySummaryDto"}, "nullable": true}, "factoryCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "LocationSummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "locationName": {"type": "string", "nullable": true}, "locationCountry": {"type": "string", "nullable": true}, "locationCountryCode": {"type": "string", "nullable": true}, "factoryCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "Username", "nullable": true}, "password": {"type": "string", "description": "Password", "nullable": true}}, "additionalProperties": false, "description": "Login request model"}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "QuestionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "auditTemplateId": {"type": "integer", "format": "int32"}, "questionGroupId": {"type": "integer", "format": "int32", "nullable": true}, "questionGroupName": {"type": "string", "nullable": true}, "questionText": {"type": "string", "nullable": true}, "questionType": {"$ref": "#/components/schemas/QuestionType"}, "displayOrder": {"type": "integer", "format": "int32"}, "isRequired": {"type": "boolean"}, "weight": {"type": "number", "format": "double", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "parentQuestionId": {"type": "integer", "format": "int32", "nullable": true}, "triggerAnswerValue": {"type": "string", "nullable": true}, "severityLevel": {"$ref": "#/components/schemas/SeverityLevel"}, "evidenceRequired": {"type": "boolean"}, "allowedEvidenceTypes": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isConditional": {"type": "boolean"}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionOptionDto"}, "nullable": true}, "childQuestions": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionDto"}, "nullable": true}}, "additionalProperties": false}, "QuestionGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "auditTemplateId": {"type": "integer", "format": "int32"}, "groupName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionDto"}, "nullable": true}}, "additionalProperties": false}, "QuestionOptionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "questionId": {"type": "integer", "format": "int32"}, "optionText": {"type": "string", "nullable": true}, "optionValue": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "QuestionType": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "ReviewAuditRequest": {"type": "object", "properties": {"approved": {"type": "boolean", "description": "Whether the audit is approved or rejected"}, "comments": {"type": "string", "description": "Optional comments from the reviewer", "nullable": true}}, "additionalProperties": false, "description": "Request model for audit review operations"}, "SeverityLevel": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "SubAreaSummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "subAreaName": {"type": "string", "nullable": true}, "areaId": {"type": "integer", "format": "int32"}, "areaName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "SubmitAuditRequest": {"type": "object", "properties": {"auditorComments": {"type": "string", "description": "Optional comments from the auditor", "nullable": true}}, "additionalProperties": false, "description": "Request model for audit submission operations"}, "UpdateAuditTemplateCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "templateName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "integer", "format": "int32"}, "recordVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateUserCommand": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "factoryId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}, "recordVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "id": {"type": "string", "nullable": true}, "recordVersion": {"type": "integer", "format": "int32"}, "createdByUserId": {"type": "string", "nullable": true}, "updatedByUserId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "factoryId": {"type": "integer", "format": "int32", "nullable": true}, "factoryName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginDate": {"type": "string", "format": "date-time", "nullable": true}, "adObjectGuid": {"type": "string", "nullable": true}, "adDistinguishedName": {"type": "string", "nullable": true}, "adSyncLastDate": {"type": "string", "format": "date-time", "nullable": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "isAdSynced": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "UserRole": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "UserSummaryDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isActive": {"type": "boolean"}, "factoryName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserSummaryDtoPaginatedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummaryDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header. You can enter either 'Bearer <token>' or just '<token>' directly.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}