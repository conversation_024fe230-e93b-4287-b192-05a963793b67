using FluentValidation;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateQuestion;

/// <summary>
/// Validator for UpdateQuestionCommand
/// </summary>
public class UpdateQuestionCommandValidator : AbstractValidator<UpdateQuestionCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateQuestionCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.QuestionId)
            .GreaterThan(0).WithMessage("Question ID must be greater than 0");

        RuleFor(x => x.TemplateId)
            .GreaterThan(0).WithMessage("Template ID must be greater than 0")
            .MustAsync(BeValidTemplate).WithMessage("Template does not exist or is published");

        RuleFor(x => x.QuestionGroupId)
            .MustAsync(BeValidQuestionGroup).WithMessage("Question group does not exist or does not belong to the template")
            .When(x => x.QuestionGroupId.HasValue);

        RuleFor(x => x.QuestionText)
            .NotEmpty().WithMessage("Question text is required")
            .MaximumLength(1000).WithMessage("Question text cannot exceed 1000 characters");

        RuleFor(x => x.QuestionType)
            .IsInEnum().WithMessage("Question type must be valid");

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Display order must be greater than or equal to 0");

        RuleFor(x => x.Weight)
            .GreaterThan(0).WithMessage("Weight must be greater than 0")
            .When(x => x.Weight.HasValue);

        RuleFor(x => x.HelpText)
            .MaximumLength(2000).WithMessage("Help text cannot exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.HelpText));

        RuleFor(x => x.ParentQuestionId)
            .MustAsync(BeValidParentQuestion).WithMessage("Parent question does not exist or does not belong to the template")
            .When(x => x.ParentQuestionId.HasValue);

        RuleFor(x => x.TriggerAnswerValue)
            .NotEmpty().WithMessage("Trigger answer value is required when parent question is specified")
            .MaximumLength(255).WithMessage("Trigger answer value cannot exceed 255 characters")
            .When(x => x.ParentQuestionId.HasValue);

        RuleFor(x => x.SeverityLevel)
            .IsInEnum().WithMessage("Severity level must be valid")
            .When(x => x.SeverityLevel.HasValue);

        RuleFor(x => x.AllowedEvidenceTypes)
            .Must(HaveUniqueEvidenceTypes).WithMessage("Evidence types must be unique for a question");

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0).WithMessage("Record version must be greater than 0");

        // Conditional validation for question types that require options
        RuleFor(x => x.QuestionType)
            .Must(BeValidQuestionTypeForOptions).WithMessage("SingleSelect and MultiSelect questions require options to be managed separately")
            .When(x => x.QuestionType == QuestionType.SingleSelect || x.QuestionType == QuestionType.MultiSelect);
    }

    private async Task<bool> BeValidTemplate(int templateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates
            .AnyAsync(t => t.Id == templateId && t.IsActive && !t.IsPublished, cancellationToken);
    }

    private async Task<bool> BeValidQuestionGroup(UpdateQuestionCommand command, int? questionGroupId, CancellationToken cancellationToken)
    {
        if (!questionGroupId.HasValue)
            return true;

        return await _context.QuestionGroups
            .AnyAsync(g => g.Id == questionGroupId.Value && 
                          g.AuditTemplateId == command.TemplateId && 
                          g.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidParentQuestion(UpdateQuestionCommand command, int? parentQuestionId, CancellationToken cancellationToken)
    {
        if (!parentQuestionId.HasValue)
            return true;

        // Check if parent question exists and belongs to the same template
        var parentExists = await _context.Questions
            .AnyAsync(q => q.Id == parentQuestionId.Value && 
                          q.AuditTemplateId == command.TemplateId && 
                          q.IsActive, cancellationToken);

        if (!parentExists)
            return false;

        // Prevent circular references
        if (parentQuestionId.Value == command.QuestionId)
            return false;

        return true;
    }

    private static bool BeValidQuestionTypeForOptions(QuestionType questionType)
    {
        // This is just a validation note - options are managed through separate endpoints
        return true;
    }

    private static bool HaveUniqueEvidenceTypes(List<AddEvidenceTypeRequest> evidenceTypes)
    {
        if (evidenceTypes == null || evidenceTypes.Count == 0) return true;

        var uniqueTypes = evidenceTypes.Select(et => et.EvidenceType).Distinct().Count();
        return uniqueTypes == evidenceTypes.Count;
    }
}
