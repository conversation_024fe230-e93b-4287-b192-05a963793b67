using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateSubArea;

/// <summary>
/// Validator for CreateSubAreaCommand
/// </summary>
public class CreateSubAreaCommandValidator : AbstractValidator<CreateSubAreaCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateSubAreaCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.SubAreaName)
            .NotEmpty().WithMessage("Sub-area name is required")
            .MaximumLength(150).WithMessage("Sub-area name must not exceed 150 characters");

        RuleFor(x => x.AreaId)
            .GreaterThan(0).WithMessage("Area ID must be greater than 0")
            .MustAsync(BeValidArea).WithMessage("Area does not exist or is not active");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description must not exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x)
            .MustAsync(BeUniqueSubAreaName).WithMessage("Sub-area name must be unique within the area");
    }

    private async Task<bool> BeValidArea(int areaId, CancellationToken cancellationToken)
    {
        return await _context.Areas.AnyAsync(a => a.Id == areaId && a.IsActive, cancellationToken);
    }

    private async Task<bool> BeUniqueSubAreaName(CreateSubAreaCommand command, CancellationToken cancellationToken)
    {
        return !await _context.SubAreas.AnyAsync(sa => 
            sa.AreaId == command.AreaId && 
            sa.SubAreaName == command.SubAreaName, 
            cancellationToken);
    }
}
