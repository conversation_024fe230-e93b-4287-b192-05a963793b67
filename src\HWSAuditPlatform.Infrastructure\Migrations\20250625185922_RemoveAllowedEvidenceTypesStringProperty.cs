﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveAllowedEvidenceTypesStringProperty : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowedEvidenceTypes",
                table: "hwsap_tpl_questions");

            migrationBuilder.AddColumn<bool>(
                name: "EnableAreaBasedResponsibility",
                table: "hwsap_tpl_audit_templates",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "EnableFindingCategorization",
                table: "hwsap_tpl_audit_templates",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "FindingCategoryId",
                table: "hwsap_fnd_findings",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResponsibleUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "hwsap_fnd_finding_categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CategoryName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    ColorCode = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: true),
                    IconName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RequiresDocumentation = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hwsap_fnd_finding_categories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_hwsap_fnd_finding_categories_hwsap_tpl_audit_templates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "hwsap_tpl_audit_templates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_hwsap_fnd_finding_categories_hwsap_usr_users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid");
                    table.ForeignKey(
                        name: "FK_hwsap_fnd_finding_categories_hwsap_usr_users_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid");
                });

            migrationBuilder.CreateTable(
                name: "hwsap_org_area_responsibilities",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    AreaId = table.Column<int>(type: "int", nullable: false),
                    ResponsibilityType = table.Column<int>(type: "int", nullable: false),
                    ResponsibleUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: false),
                    AuditTemplateId = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Priority = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    RecordVersion = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hwsap_org_area_responsibilities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_hwsap_org_area_responsibilities_hwsap_org_areas_AreaId",
                        column: x => x.AreaId,
                        principalTable: "hwsap_org_areas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_hwsap_org_area_responsibilities_hwsap_tpl_audit_templates_AuditTemplateId",
                        column: x => x.AuditTemplateId,
                        principalTable: "hwsap_tpl_audit_templates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_hwsap_org_area_responsibilities_hwsap_usr_users_ResponsibleUserId",
                        column: x => x.ResponsibleUserId,
                        principalTable: "hwsap_usr_users",
                        principalColumn: "AdObjectGuid");
                });

            migrationBuilder.CreateTable(
                name: "hwsap_tpl_question_allowed_evidence_types",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    QuestionId = table.Column<int>(type: "int", nullable: false),
                    EvidenceType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hwsap_tpl_question_allowed_evidence_types", x => x.Id);
                    table.ForeignKey(
                        name: "FK_hwsap_tpl_question_allowed_evidence_types_hwsap_tpl_questions_QuestionId",
                        column: x => x.QuestionId,
                        principalTable: "hwsap_tpl_questions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Findings_FindingCategoryId",
                table: "hwsap_fnd_findings",
                column: "FindingCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_ResponsibleUserId",
                table: "hwsap_fnd_findings",
                column: "ResponsibleUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Findings_RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings",
                column: "RetrospectiveAnalystUserId");

            migrationBuilder.CreateIndex(
                name: "IX_FindingCategories_AuditTemplateId",
                table: "hwsap_fnd_finding_categories",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_FindingCategories_IsActive",
                table: "hwsap_fnd_finding_categories",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_FindingCategories_Template_DisplayOrder",
                table: "hwsap_fnd_finding_categories",
                columns: new[] { "AuditTemplateId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_FindingCategories_Template_Name",
                table: "hwsap_fnd_finding_categories",
                columns: new[] { "AuditTemplateId", "CategoryName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_fnd_finding_categories_CreatedByUserId",
                table: "hwsap_fnd_finding_categories",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_hwsap_fnd_finding_categories_UpdatedByUserId",
                table: "hwsap_fnd_finding_categories",
                column: "UpdatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AreaResponsibilities_Area_Type_Template",
                table: "hwsap_org_area_responsibilities",
                columns: new[] { "AreaId", "ResponsibilityType", "AuditTemplateId" });

            migrationBuilder.CreateIndex(
                name: "IX_AreaResponsibilities_AreaId",
                table: "hwsap_org_area_responsibilities",
                column: "AreaId");

            migrationBuilder.CreateIndex(
                name: "IX_AreaResponsibilities_AuditTemplateId",
                table: "hwsap_org_area_responsibilities",
                column: "AuditTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_AreaResponsibilities_IsActive",
                table: "hwsap_org_area_responsibilities",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_AreaResponsibilities_ResponsibleUserId",
                table: "hwsap_org_area_responsibilities",
                column: "ResponsibleUserId");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionAllowedEvidenceTypes_QuestionId",
                table: "hwsap_tpl_question_allowed_evidence_types",
                column: "QuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType",
                table: "hwsap_tpl_question_allowed_evidence_types",
                columns: new[] { "QuestionId", "EvidenceType" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_fnd_finding_categories_FindingCategoryId",
                table: "hwsap_fnd_findings",
                column: "FindingCategoryId",
                principalTable: "hwsap_fnd_finding_categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ResponsibleUserId",
                table: "hwsap_fnd_findings",
                column: "ResponsibleUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings",
                column: "RetrospectiveAnalystUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "AdObjectGuid");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_fnd_finding_categories_FindingCategoryId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ResponsibleUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropTable(
                name: "hwsap_fnd_finding_categories");

            migrationBuilder.DropTable(
                name: "hwsap_org_area_responsibilities");

            migrationBuilder.DropTable(
                name: "hwsap_tpl_question_allowed_evidence_types");

            migrationBuilder.DropIndex(
                name: "IX_Findings_FindingCategoryId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropIndex(
                name: "IX_Findings_ResponsibleUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropIndex(
                name: "IX_Findings_RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropColumn(
                name: "EnableAreaBasedResponsibility",
                table: "hwsap_tpl_audit_templates");

            migrationBuilder.DropColumn(
                name: "EnableFindingCategorization",
                table: "hwsap_tpl_audit_templates");

            migrationBuilder.DropColumn(
                name: "FindingCategoryId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropColumn(
                name: "ResponsibleUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropColumn(
                name: "RetrospectiveAnalystUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.AddColumn<string>(
                name: "AllowedEvidenceTypes",
                table: "hwsap_tpl_questions",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);
        }
    }
}
