-- Migration: Update Evidence Types to Predefined List
-- Description: Converts AllowedEvidenceTypes from comma-separated string to normalized structure
-- Date: 2025-06-25

-- Step 1: Create the evidence_type enum
IF NOT EXISTS (SELECT 1 FROM sys.types WHERE name = 'evidence_type' AND is_user_defined = 1)
BEGIN
    CREATE TYPE evidence_type FROM varchar(50) NOT NULL;
END
GO

-- Step 2: Create the QuestionAllowedEvidenceTypes table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'hwsap_tpl_question_allowed_evidence_types')
BEGIN
    CREATE TABLE hwsap_tpl_question_allowed_evidence_types (
        Id int IDENTITY(1,1) PRIMARY KEY,
        QuestionId int NOT NULL,
        EvidenceType varchar(50) NOT NULL,
        DisplayOrder int NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Foreign key constraint
        CONSTRAINT FK_QuestionAllowedEvidenceTypes_Questions 
            FOREIGN KEY (QuestionId) REFERENCES hwsap_tpl_questions(Id) ON DELETE CASCADE,
        
        -- Unique constraint to prevent duplicate evidence types per question
        CONSTRAINT UQ_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType 
            UNIQUE (QuestionId, EvidenceType)
    );
    
    -- Create indexes
    CREATE INDEX IX_QuestionAllowedEvidenceTypes_QuestionId 
        ON hwsap_tpl_question_allowed_evidence_types(QuestionId);
END
GO

-- Step 3: Migrate existing data from AllowedEvidenceTypes string to new structure
-- This function maps MIME types to our new evidence type enum
DECLARE @QuestionId int, @AllowedEvidenceTypes nvarchar(max);
DECLARE question_cursor CURSOR FOR 
    SELECT Id, AllowedEvidenceTypes 
    FROM hwsap_tpl_questions 
    WHERE AllowedEvidenceTypes IS NOT NULL AND AllowedEvidenceTypes != '';

OPEN question_cursor;
FETCH NEXT FROM question_cursor INTO @QuestionId, @AllowedEvidenceTypes;

WHILE @@FETCH_STATUS = 0
BEGIN
    DECLARE @DisplayOrder int = 0;
    
    -- Parse comma-separated MIME types and map to evidence types
    DECLARE @MimeType nvarchar(100);
    DECLARE @StartPos int = 1;
    DECLARE @CommaPos int;
    
    -- Add trailing comma for easier parsing
    SET @AllowedEvidenceTypes = @AllowedEvidenceTypes + ',';
    
    WHILE @StartPos <= LEN(@AllowedEvidenceTypes)
    BEGIN
        SET @CommaPos = CHARINDEX(',', @AllowedEvidenceTypes, @StartPos);
        IF @CommaPos = 0 BREAK;
        
        SET @MimeType = LTRIM(RTRIM(SUBSTRING(@AllowedEvidenceTypes, @StartPos, @CommaPos - @StartPos)));
        
        -- Map MIME types to evidence types
        DECLARE @EvidenceType varchar(50) = NULL;
        
        IF @MimeType LIKE 'image/%' SET @EvidenceType = 'Image';
        ELSE IF @MimeType LIKE 'video/%' SET @EvidenceType = 'Video';
        ELSE IF @MimeType LIKE 'audio/%' SET @EvidenceType = 'Audio';
        ELSE IF @MimeType IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain') 
            SET @EvidenceType = 'Document';
        ELSE IF @MimeType IN ('application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv') 
            SET @EvidenceType = 'Spreadsheet';
        ELSE SET @EvidenceType = 'Any'; -- Default for unknown types
        
        -- Insert if not already exists (avoid duplicates)
        IF @EvidenceType IS NOT NULL AND NOT EXISTS (
            SELECT 1 FROM hwsap_tpl_question_allowed_evidence_types 
            WHERE QuestionId = @QuestionId AND EvidenceType = @EvidenceType
        )
        BEGIN
            INSERT INTO hwsap_tpl_question_allowed_evidence_types 
                (QuestionId, EvidenceType, DisplayOrder, IsActive, CreatedAt, UpdatedAt)
            VALUES 
                (@QuestionId, @EvidenceType, @DisplayOrder, 1, GETUTCDATE(), GETUTCDATE());
            
            SET @DisplayOrder = @DisplayOrder + 1;
        END
        
        SET @StartPos = @CommaPos + 1;
    END
    
    FETCH NEXT FROM question_cursor INTO @QuestionId, @AllowedEvidenceTypes;
END

CLOSE question_cursor;
DEALLOCATE question_cursor;
GO

-- Step 4: Remove the old AllowedEvidenceTypes column (commented out for safety)
-- Uncomment this after verifying the migration worked correctly
-- ALTER TABLE hwsap_tpl_questions DROP COLUMN AllowedEvidenceTypes;
-- GO

PRINT 'Evidence types migration completed successfully.';
PRINT 'Please verify the data in hwsap_tpl_question_allowed_evidence_types table.';
PRINT 'Once verified, uncomment the DROP COLUMN statement to remove the old AllowedEvidenceTypes column.';
