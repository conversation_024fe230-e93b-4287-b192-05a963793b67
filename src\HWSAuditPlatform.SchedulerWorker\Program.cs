using HWSAuditPlatform.SchedulerWorker;
using HWSAuditPlatform.Application;
using HWSAuditPlatform.Infrastructure;

var builder = Host.CreateApplicationBuilder(args);

builder.AddServiceDefaults();

// Add Application and Infrastructure layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Configure AD sync options
builder.Services.Configure<AdSyncOptions>(
    builder.Configuration.GetSection(AdSyncOptions.SectionName));

// Configure recurring audit options
builder.Services.Configure<RecurringAuditOptions>(
    builder.Configuration.GetSection(RecurringAuditOptions.SectionName));

// Add the background worker
builder.Services.AddHostedService<Worker>();

var host = builder.Build();
host.Run();
