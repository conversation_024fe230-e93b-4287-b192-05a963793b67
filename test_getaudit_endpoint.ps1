# Test script to verify GetAudit endpoint returns questions and options
$ApiBaseUrl = "https://localhost:7490"

Write-Host "Testing GetAudit endpoint with questions and options..." -ForegroundColor Yellow

# Skip certificate validation for localhost testing
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

# First, let's try to get a list of audits to find an existing audit ID
try {
    Write-Host "`n1. Getting list of audits..." -ForegroundColor Cyan
    $auditsResponse = Invoke-RestMethod -Uri "$ApiBaseUrl/api/v1/audits" -Method GET
    
    if ($auditsResponse.success -and $auditsResponse.data.items.Count -gt 0) {
        $auditId = $auditsResponse.data.items[0].id
        Write-Host "   Found audit with ID: $auditId" -ForegroundColor Green

        # Now test the GetAudit endpoint
        Write-Host "`n2. Getting audit details..." -ForegroundColor Cyan
        $auditResponse = Invoke-RestMethod -Uri "$ApiBaseUrl/api/v1/audits/$auditId" -Method GET

        if ($auditResponse.success) {
            Write-Host "   Successfully retrieved audit details" -ForegroundColor Green

            $audit = $auditResponse.data
            Write-Host "   Audit Template: $($audit.auditTemplateName)" -ForegroundColor White
            Write-Host "   Scheduled Date: $($audit.scheduledDate)" -ForegroundColor White
            Write-Host "   Factory: $($audit.factoryName)" -ForegroundColor White
            Write-Host "   Area: $($audit.areaName)" -ForegroundColor White
            
            # Check if questions are included
            if ($audit.questions -and $audit.questions.Count -gt 0) {
                Write-Host "   Questions are included! Count: $($audit.questions.Count)" -ForegroundColor Green

                foreach ($question in $audit.questions) {
                    Write-Host "   Question: $($question.questionText)" -ForegroundColor Cyan
                    Write-Host "      Type: $($question.questionType)" -ForegroundColor Gray
                    Write-Host "      Required: $($question.isRequired)" -ForegroundColor Gray
                    
                    if ($question.options -and $question.options.Count -gt 0) {
                        Write-Host "      Options included! Count: $($question.options.Count)" -ForegroundColor Green
                        foreach ($option in $question.options) {
                            Write-Host "         - $($option.optionText) (Value: $($option.optionValue))" -ForegroundColor Yellow
                        }
                    } else {
                        Write-Host "      No options (normal for Yes/No questions)" -ForegroundColor Gray
                    }
                    Write-Host ""
                }
            } else {
                Write-Host "   No questions found in the response!" -ForegroundColor Red
            }

            # Check if question groups are included
            if ($audit.questionGroups -and $audit.questionGroups.Count -gt 0) {
                Write-Host "   Question Groups are included! Count: $($audit.questionGroups.Count)" -ForegroundColor Green

                foreach ($group in $audit.questionGroups) {
                    Write-Host "   Group: $($group.groupName)" -ForegroundColor Magenta
                    if ($group.questions -and $group.questions.Count -gt 0) {
                        Write-Host "      Questions in group: $($group.questions.Count)" -ForegroundColor Gray
                    }
                }
            } else {
                Write-Host "   No question groups found" -ForegroundColor Gray
            }
            
        } else {
            Write-Host "   Failed to get audit details: $($auditResponse.message)" -ForegroundColor Red
        }

    } else {
        Write-Host "   No audits found or failed to get audits list" -ForegroundColor Red
        Write-Host "   You may need to create an audit first" -ForegroundColor Yellow
    }

} catch {
    Write-Host "   Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure the API is running and accessible" -ForegroundColor Yellow
}

Write-Host "`nTest completed!" -ForegroundColor Green
