using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

/// <summary>
/// Handler for AddQuestionCommand
/// </summary>
public class AddQuestionCommandHandler : BaseCommandHandler<AddQuestionCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public AddQuestionCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(AddQuestionCommand request, CancellationToken cancellationToken)
    {
        // Verify the audit template exists and is not published
        var auditTemplate = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId, cancellationToken);

        if (auditTemplate == null)
        {
            throw new NotFoundException(nameof(AuditTemplate), request.AuditTemplateId);
        }

        if (auditTemplate.IsPublished)
        {
            throw new InvalidOperationException("Cannot add questions to a published template");
        }

        // Create the question
        var question = new Question
        {
            AuditTemplateId = request.AuditTemplateId,
            QuestionGroupId = request.QuestionGroupId,
            QuestionText = request.QuestionText,
            QuestionType = request.QuestionType,
            DisplayOrder = request.DisplayOrder,
            IsRequired = request.IsRequired,
            Weight = request.Weight,
            HelpText = request.HelpText,
            ParentQuestionId = request.ParentQuestionId,
            TriggerAnswerValue = request.TriggerAnswerValue,
            SeverityLevel = request.SeverityLevel,
            EvidenceRequired = request.EvidenceRequired,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.Questions.AddAsync(question, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Add allowed evidence types if any
        foreach (var evidenceTypeRequest in request.AllowedEvidenceTypes)
        {
            var allowedEvidenceType = new QuestionAllowedEvidenceType
            {
                QuestionId = question.Id,
                EvidenceType = evidenceTypeRequest.EvidenceType,
                DisplayOrder = evidenceTypeRequest.DisplayOrder,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.QuestionAllowedEvidenceTypes.AddAsync(allowedEvidenceType, cancellationToken);
        }

        // Add question options if any
        foreach (var optionRequest in request.Options)
        {
            var option = new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = optionRequest.OptionText,
                OptionValue = optionRequest.OptionValue,
                DisplayOrder = optionRequest.DisplayOrder,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            await _context.QuestionOptions.AddAsync(option, cancellationToken);
        }

        // Update the template's modified timestamp
        auditTemplate.UpdatedAt = DateTime.UtcNow;
        auditTemplate.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);

        return question.Id;
    }
}
