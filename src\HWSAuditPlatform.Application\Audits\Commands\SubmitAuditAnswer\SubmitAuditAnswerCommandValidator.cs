using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums; // Assuming QuestionType enum is here
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAuditAnswer;

/// <summary>
/// Validator for SubmitAuditAnswerCommand
/// </summary>
public class SubmitAuditAnswerCommandValidator
    : AbstractValidator<SubmitAuditAnswerCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public SubmitAuditAnswerCommandValidator(
        IApplicationDbContext context,
        ICurrentUserService currentUserService
    )
    {
        _context = context;
        _currentUserService = currentUserService;

        RuleFor(x => x.AuditId)
            .NotEmpty()
            .WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit)
            .WithMessage("Audit does not exist")
            .MustAsync(BeInProgressStatus)
            .WithMessage("Audit must be in progress to submit answers")
            .MustAsync(BeAssignedToCurrentUser)
            .WithMessage("Audit is not assigned to current user");

        RuleFor(x => x.QuestionId)
            .GreaterThan(0)
            .WithMessage("Question ID must be greater than 0")
            .MustAsync(BeValidQuestion)
            .WithMessage("Question does not exist");

        RuleFor(x => x)
            .MustAsync(BeValidQuestionForAudit)
            .WithMessage("Question does not belong to the audit template");

        RuleFor(x => x.AnswerValue)
            .NotEmpty()
            .WithMessage("Answer value is required")
            .When(x => !x.IsNotApplicable);

        // This custom rule replaces the previous simple check on SelectedOptionIds
        RuleFor(x => x)
            .CustomAsync(ValidateOptionsBasedOnQuestionType);

        // This rule ensures that if any option IDs are provided, they are valid.
        RuleFor(x => x.SelectedOptionIds)
            .MustAsync(BeValidOptions)
            .WithMessage("One or more selected options are invalid")
            .When(
                x =>
                    x.SelectedOptionIds != null &&
                    x.SelectedOptionIds.Count > 0
            );


    }

    private async Task ValidateOptionsBasedOnQuestionType(
        SubmitAuditAnswerCommand command,
        ValidationContext<SubmitAuditAnswerCommand> context,
        CancellationToken cancellationToken
    )
    {
        // This validation is not needed if the question is marked as not applicable
        if (command.IsNotApplicable)
        {
            return;
        }

        var question = await _context.Questions.FirstOrDefaultAsync(
            q => q.Id == command.QuestionId,
            cancellationToken
        );

        // If question is not found, another validator will catch it.
        if (question == null)
        {
            return;
        }

        // Assuming a QuestionType enum exists with these values
        bool isSelectType =
            question.QuestionType == QuestionType.MultiSelect ||
            question.QuestionType == QuestionType.SingleSelect;

        bool hasSelectedOptions =
            command.SelectedOptionIds != null &&
            command.SelectedOptionIds.Count > 0;

        if (isSelectType && !hasSelectedOptions)
        {
            context.AddFailure(
                nameof(command.SelectedOptionIds),
                "At least one option must be selected for this question type."
            );
        }
        else if (!isSelectType && hasSelectedOptions)
        {
            context.AddFailure(
                nameof(command.SelectedOptionIds),
                "Options cannot be selected for this question type."
            );
        }
    }

    private async Task<bool> BeValidAudit(
        string auditId,
        CancellationToken cancellationToken
    )
    {
        return await _context.Audits.AnyAsync(
            a => a.Id == auditId,
            cancellationToken
        );
    }

    private async Task<bool> BeInProgressStatus(
        string auditId,
        CancellationToken cancellationToken
    )
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(
            a => a.Id == auditId,
            cancellationToken
        );
        return audit?.OverallStatus == AuditOverallStatus.InProgress;
    }

    private async Task<bool> BeAssignedToCurrentUser(
        string auditId,
        CancellationToken cancellationToken
    )
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
            return false;

        var audit = await _context.Audits.FirstOrDefaultAsync(
            a => a.Id == auditId,
            cancellationToken
        );
        return audit?.AssignedToUserId == currentUserId;
    }

    private async Task<bool> BeValidQuestion(
        int questionId,
        CancellationToken cancellationToken
    )
    {
        return await _context.Questions.AnyAsync(
            q => q.Id == questionId,
            cancellationToken
        );
    }

    private async Task<bool> BeValidQuestionForAudit(
        SubmitAuditAnswerCommand command,
        CancellationToken cancellationToken
    )
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(
            a => a.Id == command.AuditId,
            cancellationToken
        );
        if (audit == null)
            return false;

        return await _context.Questions.AnyAsync(
            q =>
                q.Id == command.QuestionId &&
                q.AuditTemplateId == audit.AuditTemplateId,
            cancellationToken
        );
    }

    private async Task<bool> BeValidOptions(
        List<int> optionIds,
        CancellationToken cancellationToken
    )
    {
        if (optionIds == null || optionIds.Count == 0)
            return true;

        var validCount = await _context.QuestionOptions.CountAsync(
            o => optionIds.Contains(o.Id),
            cancellationToken
        );

        return validCount == optionIds.Count;
    }


}