using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Debug audit API service that returns mock data for development/testing
/// </summary>
public class DebugAuditApiService : IAuditApiService
{
    private readonly ILogger<DebugAuditApiService> _logger;
    private readonly IConfiguration _configuration;
    private readonly List<Audit> _mockAudits;

    public DebugAuditApiService(ILogger<DebugAuditApiService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _mockAudits = CreateMockAudits();
        
        _logger.LogWarning("🚨 DEBUG MODE: Debug audit API service initialized with {Count} mock audits", _mockAudits.Count);
    }

    private List<Audit> CreateMockAudits()
    {
        return new List<Audit>
        {
            new()
            {
                Id = "audit-001",
                AuditTemplateId = 1,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                ScheduledDate = DateTime.UtcNow.AddDays(-2),
                DueDate = DateTime.UtcNow.AddDays(5),
                OverallStatus = AuditOverallStatus.Scheduled,
                FactoryId = 1,
                AreaId = 1,
                CreatedAt = DateTime.UtcNow.AddDays(-3),
                CreatedByUserId = "manager-001"
            },
            new()
            {
                Id = "audit-002",
                AuditTemplateId = 2,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                ScheduledDate = DateTime.UtcNow.AddDays(-1),
                DueDate = DateTime.UtcNow.AddDays(3),
                StartedAt = DateTime.UtcNow.AddHours(-2),
                OverallStatus = AuditOverallStatus.InProgress,
                FactoryId = 1,
                AreaId = 2,
                CreatedAt = DateTime.UtcNow.AddDays(-2),
                CreatedByUserId = "manager-001"
            },
            new()
            {
                Id = "audit-003",
                AuditTemplateId = 3,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                ScheduledDate = DateTime.UtcNow.AddDays(-10),
                DueDate = DateTime.UtcNow.AddDays(-5),
                StartedAt = DateTime.UtcNow.AddDays(-8),
                CompletedAt = DateTime.UtcNow.AddDays(-6),
                OverallStatus = AuditOverallStatus.Closed,
                FactoryId = 1,
                AreaId = 3,
                CreatedAt = DateTime.UtcNow.AddDays(-12),
                CreatedByUserId = "manager-001"
            }
        };
    }

    public async Task<Result<List<Audit>>> GetAssignedAuditsAsync()
    {
        await Task.Delay(200); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock assigned audits", _mockAudits.Count);
        
        return Result<List<Audit>>.Success(_mockAudits);
    }

    public async Task<Result<Audit?>> GetAuditAsync(string auditId)
    {
        await Task.Delay(150); // Simulate network delay
        
        var audit = _mockAudits.FirstOrDefault(a => a.Id == auditId);
        
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock audit for ID: {AuditId}, Found: {Found}", auditId, audit != null);
        
        return audit != null
            ? Result<Audit?>.Success(audit)
            : Result<Audit?>.Failure("Audit not found");
    }

    public async Task<Result<Models.AuditSummaryDto?>> GetAuditForResultsAsync(string auditId)
    {
        await Task.Delay(150); // Simulate network delay

        var audit = _mockAudits.FirstOrDefault(a => a.Id == auditId);

        if (audit != null)
        {
            // Create a mock audit summary with realistic data
            var auditSummary = new Models.AuditSummaryDto
            {
                Id = audit.Id,
                AuditTemplateName = audit.AuditTemplate?.TemplateName ?? "Mock Safety Inspection",
                AssignedToUserName = audit.AssignedToUser?.FullName ?? "Debug User",
                ScheduledDate = audit.ScheduledDate,
                DueDate = audit.DueDate,
                OverallStatus = audit.OverallStatus,
                FactoryName = audit.Factory?.FactoryName ?? "Debug Manufacturing Plant",
                AreaName = audit.Area?.AreaName ?? "Production Line A",
                SubAreaName = audit.SubArea?.SubAreaName,
                OverallScore = audit.OverallScore.HasValue ? (double)audit.OverallScore.Value : null,
                IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now &&
                           audit.OverallStatus != AuditOverallStatus.Closed,
                StartedAt = audit.StartedAt,
                CompletedAt = audit.CompletedAt,
                // Mock realistic statistics
                TotalQuestions = 25,
                AnsweredQuestions = audit.OverallStatus == AuditOverallStatus.Closed ? 25 :
                                  audit.OverallStatus == AuditOverallStatus.InProgress ? 15 : 0,
                EvidenceCount = audit.OverallStatus == AuditOverallStatus.Closed ? 8 :
                               audit.OverallStatus == AuditOverallStatus.InProgress ? 3 : 0,
                NonConformanceCount = audit.OverallStatus == AuditOverallStatus.Closed ? 2 :
                                     audit.OverallStatus == AuditOverallStatus.InProgress ? 1 : 0,
                PassedQuestions = audit.OverallStatus == AuditOverallStatus.Closed ? 23 :
                                 audit.OverallStatus == AuditOverallStatus.InProgress ? 14 : 0,
                FailedQuestions = audit.OverallStatus == AuditOverallStatus.Closed ? 2 :
                                 audit.OverallStatus == AuditOverallStatus.InProgress ? 1 : 0,
                NotApplicableQuestions = audit.OverallStatus == AuditOverallStatus.Closed ? 0 : 0,
                ManagerComments = audit.ManagerComments,
                ReviewedByUserName = audit.OverallStatus == AuditOverallStatus.Closed ? "Debug Manager" : null,
                ReviewedAt = audit.OverallStatus == AuditOverallStatus.Closed ? audit.CompletedAt?.AddHours(1) : null
            };

            _logger.LogWarning("🚨 DEBUG MODE: Returning mock audit results for ID: {AuditId}", auditId);
            return Result<Models.AuditSummaryDto?>.Success(auditSummary);
        }

        _logger.LogWarning("🚨 DEBUG MODE: Mock audit not found for results: {AuditId}", auditId);
        return Result<Models.AuditSummaryDto?>.Success(null);
    }

    public async Task<Result> StartAuditAsync(string auditId)
    {
        await Task.Delay(100); // Simulate network delay

        var audit = _mockAudits.FirstOrDefault(a => a.Id == auditId);
        if (audit != null)
        {
            audit.OverallStatus = AuditOverallStatus.InProgress;
            audit.StartedAt = DateTime.UtcNow;
        }

        _logger.LogWarning("🚨 DEBUG MODE: Mock audit started for ID: {AuditId}", auditId);

        return Result.Success();
    }

    public async Task<Result> SubmitAuditAnswersAsync(string auditId, List<AuditAnswer> answers)
    {
        await Task.Delay(300); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Mock audit answers submitted for ID: {AuditId}, Answers: {Count}", auditId, answers.Count);
        
        return Result.Success();
    }

    public async Task<Result> CompleteAuditAsync(string auditId)
    {
        await Task.Delay(200); // Simulate network delay

        var audit = _mockAudits.FirstOrDefault(a => a.Id == auditId);
        if (audit != null)
        {
            audit.OverallStatus = AuditOverallStatus.Closed;
            audit.CompletedAt = DateTime.UtcNow;
        }

        _logger.LogWarning("🚨 DEBUG MODE: Mock audit completed for ID: {AuditId}", auditId);

        return Result.Success();
    }

    public async Task<Result<string>> UploadAttachmentAsync(string auditId, string questionId, byte[] fileData, string fileName, string contentType)
    {
        await Task.Delay(500); // Simulate file upload delay

        var attachmentId = $"attachment-{Guid.NewGuid():N}";

        _logger.LogWarning("🚨 DEBUG MODE: Mock attachment uploaded for audit: {AuditId}, question: {QuestionId}, file: {FileName}, size: {Size} bytes",
            auditId, questionId, fileName, fileData.Length);

        return Result<string>.Success(attachmentId);
    }

    public async Task<Result<PaginatedResult<Models.AuditSummaryDto>>> GetMyAuditHistoryAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null, string? status = null, DateTime? completedDateFrom = null, DateTime? completedDateTo = null)
    {
        await Task.Delay(300); // Simulate network delay

        _logger.LogWarning("🚨 DEBUG MODE: Mock audit history request - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // Create mock audit history data
        var mockAuditHistory = new List<Models.AuditSummaryDto>
        {
            new()
            {
                Id = "audit-history-001",
                AuditTemplateName = "Safety Inspection Protocol",
                AssignedToUserName = "Debug User",
                ScheduledDate = DateTime.UtcNow.AddDays(-30),
                OverallStatus = AuditOverallStatus.Closed,
                FactoryName = "Debug Manufacturing Plant",
                AreaName = "Production Line A",
                OverallScore = 92.5,
                IsOverdue = false
            },
            new()
            {
                Id = "audit-history-002",
                AuditTemplateName = "Quality Control Checklist",
                AssignedToUserName = "Debug User",
                ScheduledDate = DateTime.UtcNow.AddDays(-20),
                OverallStatus = AuditOverallStatus.Closed,
                FactoryName = "Debug Manufacturing Plant",
                AreaName = "Quality Control",
                OverallScore = 88.0,
                IsOverdue = false
            },
            new()
            {
                Id = "audit-history-003",
                AuditTemplateName = "Environmental Compliance Template",
                AssignedToUserName = "Debug User",
                ScheduledDate = DateTime.UtcNow.AddDays(-10),
                OverallStatus = AuditOverallStatus.Closed,
                FactoryName = "Debug Manufacturing Plant",
                AreaName = "Environmental",
                OverallScore = 95.0,
                IsOverdue = false
            }
        };

        // Apply search filter if provided
        if (!string.IsNullOrEmpty(searchTerm))
        {
            mockAuditHistory = mockAuditHistory
                .Where(a => a.AuditTemplateName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                           a.FactoryName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                           a.AreaName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        // Apply status filter if provided
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var statusEnum))
        {
            mockAuditHistory = mockAuditHistory
                .Where(a => a.OverallStatus == statusEnum)
                .ToList();
        }

        // Apply date filters if provided
        if (completedDateFrom.HasValue)
        {
            mockAuditHistory = mockAuditHistory
                .Where(a => a.ScheduledDate >= completedDateFrom.Value)
                .ToList();
        }

        if (completedDateTo.HasValue)
        {
            mockAuditHistory = mockAuditHistory
                .Where(a => a.ScheduledDate <= completedDateTo.Value)
                .ToList();
        }

        // Apply pagination
        var totalCount = mockAuditHistory.Count;
        var pagedAudits = mockAuditHistory
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        var result = new PaginatedResult<Models.AuditSummaryDto>
        {
            Items = pagedAudits,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        };

        return Result<PaginatedResult<Models.AuditSummaryDto>>.Success(result);
    }
}

/// <summary>
/// Debug template API service that returns mock data for development/testing
/// </summary>
public class DebugTemplateApiService : ITemplateApiService
{
    private readonly ILogger<DebugTemplateApiService> _logger;
    private readonly List<AuditTemplate> _mockTemplates;

    public DebugTemplateApiService(ILogger<DebugTemplateApiService> logger)
    {
        _logger = logger;
        _mockTemplates = CreateMockTemplates();
        
        _logger.LogWarning("🚨 DEBUG MODE: Debug template API service initialized with {Count} mock templates", _mockTemplates.Count);
    }

    private List<AuditTemplate> CreateMockTemplates()
    {
        return new List<AuditTemplate>
        {
            new()
            {
                Id = 1,
                TemplateName = "Safety Inspection Template",
                Description = "Standard safety inspection checklist",
                Version = 1,
                IsActive = true,
                IsPublished = true,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                CreatedByUserId = "admin-001"
            },
            new()
            {
                Id = 2,
                TemplateName = "Quality Control Template",
                Description = "Quality control checklist for production",
                Version = 2,
                IsActive = true,
                IsPublished = true,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                CreatedByUserId = "admin-001"
            },
            new()
            {
                Id = 3,
                TemplateName = "Environmental Compliance Template",
                Description = "Environmental compliance audit template",
                Version = 1,
                IsActive = true,
                IsPublished = true,
                CreatedAt = DateTime.UtcNow.AddDays(-45),
                CreatedByUserId = "admin-001"
            }
        };
    }

    public async Task<Result<List<AuditTemplate>>> GetAvailableTemplatesAsync()
    {
        await Task.Delay(150); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock templates", _mockTemplates.Count);
        
        return Result<List<AuditTemplate>>.Success(_mockTemplates);
    }

    public async Task<Result<AuditTemplate?>> GetTemplateAsync(int templateId)
    {
        await Task.Delay(100); // Simulate network delay
        
        var template = _mockTemplates.FirstOrDefault(t => t.Id == templateId);
        
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock template for ID: {TemplateId}, Found: {Found}", templateId, template != null);
        
        return template != null 
            ? Result<AuditTemplate?>.Success(template)
            : Result<AuditTemplate?>.Failure("Template not found");
    }
}

/// <summary>
/// Debug user API service that returns mock data for development/testing
/// </summary>
public class DebugUserApiService : IUserApiService
{
    private readonly ILogger<DebugUserApiService> _logger;
    private readonly IConfiguration _configuration;
    private readonly User _mockUser;

    public DebugUserApiService(ILogger<DebugUserApiService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _mockUser = CreateMockUser();
        
        _logger.LogWarning("🚨 DEBUG MODE: Debug user API service initialized for user: {Username}", _mockUser.Username);
    }

    private User CreateMockUser()
    {
        var mockUserSection = _configuration.GetSection("Debug:MockUser");

        return new User
        {
            Id = mockUserSection["UserId"] ?? "debug-user-001",
            Username = mockUserSection["Username"] ?? "debug.user",
            FirstName = "Debug",
            LastName = "User",
            Email = mockUserSection["Email"] ?? "<EMAIL>",
            RoleId = 1, // Admin role
            FactoryId = 1,
            IsActive = true,
            AdObjectGuid = Guid.NewGuid().ToString(),
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            LastLoginDate = DateTime.UtcNow.AddHours(-1)
        };
    }

    public async Task<Result<User?>> GetCurrentUserProfileAsync()
    {
        await Task.Delay(100); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock user profile for: {Username}", _mockUser.Username);
        
        return Result<User?>.Success(_mockUser);
    }

    public async Task<Result> UpdateUserProfileAsync(User user)
    {
        await Task.Delay(200); // Simulate network delay

        _logger.LogWarning("🚨 DEBUG MODE: Mock user profile update for: {Username}", user.Username);

        return Result.Success();
    }


}
