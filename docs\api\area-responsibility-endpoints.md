# Area Responsibility Management API

This document describes the API endpoints for managing area-based responsibility assignments in the HWS Audit Platform.

## Overview

Area-based responsibility assignment allows organizations to define who is responsible for different types of activities (corrective actions, retrospective analysis, etc.) based on the audit's location (area). This enables automatic assignment of responsible users when findings are created.

## Endpoints

### Get Area Responsibilities

**GET** `/api/organization/area-responsibilities`

Retrieves area responsibility assignments with optional filtering.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `areaId` | integer | No | Filter by specific area ID |
| `auditTemplateId` | integer | No | Filter by specific audit template ID |
| `responsibilityType` | string | No | Filter by responsibility type (CorrectiveActionOwner, RetrospectiveAnalyst, FindingReviewer, EscalationContact) |
| `responsibleUserId` | string | No | Filter by responsible user ID (AD ObjectGUID) |
| `isActive` | boolean | No | Filter by active status (default: true) |
| `includeTemplateSpecific` | boolean | No | Include template-specific responsibilities (default: true) |
| `includeGeneral` | boolean | No | Include general (non-template-specific) responsibilities (default: true) |

#### Response

```json
[
  {
    "id": "c1234567890123456789012345",
    "areaId": 1,
    "areaName": "Production Line A",
    "responsibilityType": "CorrectiveActionOwner",
    "responsibilityTypeName": "CorrectiveActionOwner",
    "responsibleUserId": "12345678-1234-1234-1234-123456789012",
    "responsibleUserName": "john.doe",
    "responsibleUserFullName": "John Doe",
    "auditTemplateId": 5,
    "auditTemplateName": "Safety Audit v2",
    "isActive": true,
    "description": "Responsible for safety-related corrective actions",
    "priority": 0,
    "isTemplateSpecific": true,
    "isEffective": true,
    "createdAt": "2025-01-25T10:00:00Z",
    "updatedAt": "2025-01-25T10:00:00Z",
    "recordVersion": 1,
    "createdByUserId": "12345678-1234-1234-1234-123456789012",
    "updatedByUserId": null
  }
]
```

### Create Area Responsibility

**POST** `/api/organization/area-responsibilities`

Creates a new area responsibility assignment.

#### Request Body

```json
{
  "areaId": 1,
  "responsibilityType": "CorrectiveActionOwner",
  "responsibleUserId": "12345678-1234-1234-1234-123456789012",
  "auditTemplateId": 5,
  "isActive": true,
  "description": "Responsible for safety-related corrective actions",
  "priority": 0
}
```

#### Request Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `areaId` | integer | Yes | ID of the area |
| `responsibilityType` | string | Yes | Type of responsibility (CorrectiveActionOwner, RetrospectiveAnalyst, FindingReviewer, EscalationContact) |
| `responsibleUserId` | string | Yes | AD ObjectGUID of the responsible user |
| `auditTemplateId` | integer | No | ID of specific audit template (null for general responsibility) |
| `isActive` | boolean | No | Whether the responsibility is active (default: true) |
| `description` | string | No | Optional description (max 500 characters) |
| `priority` | integer | No | Priority level (default: 0, lower number = higher priority) |

#### Response

Returns the created area responsibility object (same structure as GET response).

### Update Area Responsibility

**PUT** `/api/organization/area-responsibilities/{id}`

Updates an existing area responsibility assignment.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | CUID of the area responsibility |

#### Request Body

Same structure as Create Area Responsibility.

### Delete Area Responsibility

**DELETE** `/api/organization/area-responsibilities/{id}`

Deletes an area responsibility assignment.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | CUID of the area responsibility |

#### Response

```json
{
  "success": true,
  "message": "Area responsibility deleted successfully"
}
```

## Responsibility Types

| Type | Description |
|------|-------------|
| `CorrectiveActionOwner` | Responsible for managing and implementing corrective actions |
| `RetrospectiveAnalyst` | Responsible for performing retrospective efficiency analysis |
| `FindingReviewer` | Responsible for reviewing and validating findings |
| `EscalationContact` | Contact person for escalations and critical issues |

## Business Rules

1. **Template-Specific vs General**: Responsibilities can be either template-specific (applies only to specific audit templates) or general (applies to all templates).

2. **Priority Handling**: When multiple users have the same responsibility type for an area, the one with the lowest priority number is selected.

3. **Template Enablement**: Area-based responsibility must be enabled at the audit template level (`EnableAreaBasedResponsibility = true`).

4. **Automatic Assignment**: When findings are created, responsible users are automatically assigned based on the audit's area and template configuration.

5. **Fallback Logic**: If no area-specific responsibility exists, the system falls back to the audit assignee.

## Error Responses

### 400 Bad Request
```json
{
  "type": "ValidationError",
  "title": "Validation failed",
  "status": 400,
  "errors": {
    "ResponsibleUserId": ["Responsible user ID is required."]
  }
}
```

### 404 Not Found
```json
{
  "type": "NotFound",
  "title": "Resource not found",
  "status": 404,
  "detail": "Area with ID 999 was not found."
}
```

### 409 Conflict
```json
{
  "type": "ValidationError",
  "title": "Business rule violation",
  "status": 409,
  "detail": "Area-based responsibility is not enabled for this audit template."
}
```
