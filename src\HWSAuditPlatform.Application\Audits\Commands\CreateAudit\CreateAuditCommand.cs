using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Commands.CreateAudit;

/// <summary>
/// Command to create a new audit
/// </summary>
public class CreateAuditCommand : BaseCommand<string>
{
    public int AuditTemplateId { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignedToUserGroupId { get; set; }
    public string? AssignedToUserId { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public int FactoryId { get; set; }
    public int AreaId { get; set; }
    public int? SubAreaId { get; set; }
}
