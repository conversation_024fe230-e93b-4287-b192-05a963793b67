using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for client-side template validation
/// </summary>
public interface ITemplateValidationService
{
    /// <summary>
    /// Validates if a template can be published
    /// </summary>
    Task<TemplateValidationResult> ValidateForPublishingAsync(AuditTemplate template);

    /// <summary>
    /// Validates template basic information
    /// </summary>
    TemplateValidationResult ValidateTemplateInfo(AuditTemplate template);

    /// <summary>
    /// Validates a single question
    /// </summary>
    QuestionValidationResult ValidateQuestion(Question question);
}

/// <summary>
/// Result of template validation
/// </summary>
public class TemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
}

/// <summary>
/// Result of question validation
/// </summary>
public class QuestionValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
}
