using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Organization.Queries.GetAreaResponsibilities;

/// <summary>
/// Query to get area responsibilities with optional filtering
/// </summary>
public class GetAreaResponsibilitiesQuery : BaseQuery<List<AreaResponsibilityDto>>
{
    public int? AreaId { get; set; }
    public int? AuditTemplateId { get; set; }
    public ResponsibilityType? ResponsibilityType { get; set; }
    public string? ResponsibleUserId { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool IncludeTemplateSpecific { get; set; } = true;
    public bool IncludeGeneral { get; set; } = true;
}
