# HWSAuditPlatform.Domain - Project Summary

## Quick Overview

The **HWSAuditPlatform.Domain** project is the core business logic layer of the HWS Audit Platform, implementing a comprehensive audit management system using Domain-Driven Design principles.

## Key Statistics

- **Entities**: 20+ domain entities across 7 functional areas
- **Enumerations**: 11 strongly-typed enums mapping to database types
- **Events**: 10+ domain events for event-driven architecture
- **Value Objects**: Immutable objects for complex domain concepts
- **Business Rules**: Comprehensive validation and workflow enforcement

## Functional Areas

| Area | Entities | Purpose |
|------|----------|---------|
| **Users** | 5 entities | User management, roles, groups, AD integration |
| **Organization** | 4 entities | Hierarchical structure (Location → Factory → Area → SubArea) |
| **Templates** | 4 entities | Audit template definition and question management |
| **Audits** | 5 entities | Audit execution, answers, evidence, attachments |
| **Findings** | 2 entities | Non-conformity management and corrective actions |
| **Scheduling** | 2 entities | Recurring audit automation and scheduling rules |
| **Workflow** | 2 entities | Correction requests and comprehensive audit logging |

## Key Features

### 🔐 User Management
- Role-based access control (Admin, Manager, Auditor)
- All users are Active Directory synchronized (no local users)
- User groups for flexible audit assignment (can be AD-synced or local)
- Comprehensive user profile management

### 🏭 Organizational Structure
- Multi-level hierarchy: Location → Factory → Area → SubArea
- Flexible organizational modeling
- Active status management for operational control

### 📋 Audit Templates
- Versioned template system for audit consistency
- Rich question types: YesNo, Numeric, Text, Date, Select options
- Conditional question logic for dynamic audits
- Evidence requirements and timing guidance
- Question grouping for better organization

### ✅ Audit Execution
- Flexible assignment: Individual, Group (any member), Group (all members)
- Comprehensive status workflow management
- Multi-type answer support with validation
- Evidence attachment with file type restrictions
- Failure reason tracking for non-conformities

### 🔍 Findings & CAPA
- Automatic finding generation from failed audit answers
- Severity-based classification (Critical, Major, Minor, Observation)
- Corrective action assignment and tracking
- Status-based workflow management
- Due date management and overdue tracking

### ⏰ Scheduling & Recurrence
- Flexible recurring audit patterns (Daily, Weekly, Monthly, Yearly)
- Complex recurrence rules with detailed configuration
- Automatic audit generation based on schedules
- Assignment inheritance from recurring settings

### 🔄 Workflow Management
- Audit correction request system
- Manager approval workflows
- Comprehensive audit logging for compliance
- Event-driven architecture for loose coupling

## Technical Highlights

### Domain-Driven Design
- **Aggregate Roots**: Clear boundaries for consistency
- **Domain Events**: Event-driven architecture for reactive behaviors
- **Value Objects**: Immutable objects for complex concepts
- **Rich Domain Models**: Business logic encapsulated in entities

### Offline-First Design
- **CUID Primary Keys**: Client-side unique ID generation
- **Optimistic Concurrency**: RecordVersion fields for conflict resolution
- **Audit Tracking**: Comprehensive change tracking for synchronization

### Data Integrity
- **Strong Typing**: Enums prevent invalid state transitions
- **Business Rules**: Domain-level validation and constraints
- **Referential Integrity**: Proper foreign key relationships
- **Soft Deletion**: IsActive flags for data preservation

### Event Architecture
- **Domain Events**: 10+ events for significant business occurrences
- **Loose Coupling**: Events enable reactive behaviors without tight coupling
- **Integration Ready**: Events can be converted to integration events

## Database Mapping

The domain entities map directly to the AuditFlowDB schema with:
- **25+ Tables**: Complete coverage of audit management domain
- **Enum Types**: Database enums for type safety
- **Indexes**: Optimized for common query patterns
- **Constraints**: Referential integrity and business rule enforcement

## Business Rules Enforcement

### Audit Workflow
- Status transitions follow defined business processes
- Assignment validation based on user roles and group membership
- Evidence requirements enforced for marked questions
- Correction requests require manager approval

### Template Management
- Only published templates can be used for new audits
- Question conditional logic prevents circular references
- Evidence types restricted to allowed MIME types

### Finding Management
- Findings must be linked to specific audit answers
- Corrective actions require valid assignees and due dates
- Status transitions follow CAPA workflow requirements

## Getting Started

1. **Read the [Main README](README.md)** for comprehensive overview
2. **Study [Entity Relationships](docs/ENTITY_RELATIONSHIPS.md)** for data model understanding
3. **Review [Business Rules](docs/BUSINESS_RULES.md)** for constraint details
4. **Explore [Domain Events](docs/DOMAIN_EVENTS.md)** for event architecture
5. **Check [Database Mapping](docs/DATABASE_MAPPING.md)** for persistence details

## Dependencies

- **.NET 9.0**: Latest LTS framework
- **System.ComponentModel.Annotations**: For validation attributes
- **No External Dependencies**: Pure domain layer following DDD principles

## Testing Strategy

The domain layer is designed for comprehensive unit testing:
- **Pure Business Logic**: No infrastructure dependencies
- **Rich Domain Models**: Testable business rules and behaviors
- **Event Testing**: Domain events can be verified in tests
- **Value Object Testing**: Immutable objects with equality semantics

## Future Considerations

### Extensibility Points
- **Custom Question Types**: Framework supports new question types
- **Additional Evidence Types**: Configurable evidence type support
- **Enhanced Recurrence**: Support for complex scheduling patterns
- **Integration Events**: Ready for external system integration

### Performance Optimizations
- **Aggregate Optimization**: Consider aggregate size and boundaries
- **Event Batching**: Batch domain events for performance
- **Caching Strategy**: Cache frequently accessed reference data
- **Query Optimization**: Optimize for common access patterns

## Compliance and Auditing

The domain supports comprehensive auditing requirements:
- **Audit Logs**: Complete system activity tracking
- **Change Tracking**: Entity-level change history
- **Event Sourcing Ready**: Domain events support event sourcing patterns
- **Compliance Reporting**: Rich data model supports regulatory reporting

This domain layer provides a solid foundation for the HWS Audit Platform, ensuring business rule enforcement, data integrity, and extensibility for future requirements.
