using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Scheduling.DTOs;

/// <summary>
/// Data Transfer Object for RecurringAuditSetting entity
/// </summary>
public class RecurringAuditSettingDto : AuditableDto<string>
{
    public string SettingName { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public int DeadlineDays { get; set; }
    public bool IsEnabled { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignToUserId { get; set; }
    public string? AssignToUserName { get; set; }
    public string? AssignToUserGroupId { get; set; }
    public string? AssignToUserGroupName { get; set; }
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public int? AreaId { get; set; }
    public string? AreaName { get; set; }
    public int? SubAreaId { get; set; }
    public string? SubAreaName { get; set; }
    public DateOnly? NextGenerationDate { get; set; }
    public DateTime? LastGeneratedAt { get; set; }
    public bool IsReadyToGenerate { get; set; }
    public bool HasValidAssignment { get; set; }
    
    // Recurrence rule details
    public RecurrenceRuleDto? RecurrenceRule { get; set; }
}

/// <summary>
/// Summary DTO for listing recurring audit settings
/// </summary>
public class RecurringAuditSettingSummaryDto : AuditableDto<string>
{
    public string SettingName { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public bool IsEnabled { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignToUserName { get; set; }
    public string? AssignToUserGroupName { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public string? SubAreaName { get; set; }
    public DateOnly? NextGenerationDate { get; set; }
    public DateTime? LastGeneratedAt { get; set; }
    public string RecurrenceDescription { get; set; } = string.Empty;
    public bool IsReadyToGenerate { get; set; }
}

/// <summary>
/// Data Transfer Object for RecurrenceRule entity
/// </summary>
public class RecurrenceRuleDto : BaseDto<string>
{
    public FrequencyType FrequencyType { get; set; }
    public int Interval { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    
    // Weekly parameters
    public int? WeeklyDaysOfWeekMask { get; set; }
    
    // Monthly parameters
    public int? MonthlyDayOfMonth { get; set; }
    public int? MonthlyNthWeek { get; set; }
    public int? MonthlyDayOfWeek { get; set; }
    
    // Yearly parameters
    public int? YearlyMonth { get; set; }
    public int? YearlyDayOfMonth { get; set; }
    public int? YearlyNthWeek { get; set; }
    public int? YearlyDayOfWeek { get; set; }
    
    public string Description { get; set; } = string.Empty;
    public bool IsValid { get; set; }
}

/// <summary>
/// DTO for creating/updating recurrence rules
/// </summary>
public class CreateRecurrenceRuleDto
{
    public FrequencyType FrequencyType { get; set; }
    public int Interval { get; set; } = 1;
    public DateOnly StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    
    // Weekly parameters
    public int? WeeklyDaysOfWeekMask { get; set; }
    
    // Monthly parameters
    public int? MonthlyDayOfMonth { get; set; }
    public int? MonthlyNthWeek { get; set; }
    public int? MonthlyDayOfWeek { get; set; }
    
    // Yearly parameters
    public int? YearlyMonth { get; set; }
    public int? YearlyDayOfMonth { get; set; }
    public int? YearlyNthWeek { get; set; }
    public int? YearlyDayOfWeek { get; set; }
}
