using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// HTTP client service that automatically adds authentication headers
/// </summary>
public class AuthenticatedHttpClientService
{
    private readonly HttpClient _httpClient;
    private readonly IAuthenticationService _authService;
    private readonly ILogger<AuthenticatedHttpClientService> _logger;

    public AuthenticatedHttpClientService(
        HttpClient httpClient,
        IAuthenticationService authService,
        ILogger<AuthenticatedHttpClientService> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// Ensures the HTTP client has a valid authentication token
    /// </summary>
    public async Task<bool> EnsureAuthenticatedAsync()
    {
        try
        {
            var token = await _authService.GetTokenAsync();
            
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("No authentication token available");
                return false;
            }

            // Check if we need to refresh the token
            var isAuthenticated = await _authService.IsAuthenticatedAsync();
            if (!isAuthenticated)
            {
                _logger.LogWarning("Authentication check failed");
                return false;
            }

            // Get the current token (might be refreshed)
            token = await _authService.GetTokenAsync();
            
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new AuthenticationHeaderValue("Bearer", token);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring authentication");
            return false;
        }
    }

    /// <summary>
    /// Makes an authenticated GET request
    /// </summary>
    public async Task<HttpResponseMessage> GetAsync(string requestUri)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.GetAsync(requestUri);
    }

    /// <summary>
    /// Makes an authenticated POST request
    /// </summary>
    public async Task<HttpResponseMessage> PostAsync(string requestUri, HttpContent? content)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.PostAsync(requestUri, content);
    }

    /// <summary>
    /// Makes an authenticated POST request with JSON content
    /// </summary>
    public async Task<HttpResponseMessage> PostAsJsonAsync<T>(string requestUri, T value)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.PostAsJsonAsync(requestUri, value);
    }

    /// <summary>
    /// Makes an authenticated PUT request
    /// </summary>
    public async Task<HttpResponseMessage> PutAsync(string requestUri, HttpContent? content)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.PutAsync(requestUri, content);
    }

    /// <summary>
    /// Makes an authenticated PUT request with JSON content
    /// </summary>
    public async Task<HttpResponseMessage> PutAsJsonAsync<T>(string requestUri, T value)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.PutAsJsonAsync(requestUri, value);
    }

    /// <summary>
    /// Makes an authenticated DELETE request
    /// </summary>
    public async Task<HttpResponseMessage> DeleteAsync(string requestUri)
    {
        await EnsureAuthenticatedAsync();
        return await _httpClient.DeleteAsync(requestUri);
    }

    /// <summary>
    /// Makes an authenticated PATCH request
    /// </summary>
    public async Task<HttpResponseMessage> PatchAsync(string requestUri, HttpContent? content)
    {
        await EnsureAuthenticatedAsync();
        var request = new HttpRequestMessage(HttpMethod.Patch, requestUri)
        {
            Content = content
        };
        return await _httpClient.SendAsync(request);
    }

    /// <summary>
    /// Gets the underlying HTTP client for direct access
    /// </summary>
    public HttpClient HttpClient => _httpClient;
}
