using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;
using System.Diagnostics;

namespace HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;

/// <summary>
/// Handler for GenerateRecurringAuditsCommand
/// </summary>
public class GenerateRecurringAuditsCommandHandler : BaseCommandHandler<GenerateRecurringAuditsCommand, GenerateRecurringAuditsResult>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IRecurrenceCalculationService _recurrenceCalculationService;

    public GenerateRecurringAuditsCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IRecurrenceCalculationService recurrenceCalculationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _recurrenceCalculationService = recurrenceCalculationService;
    }

    public override async Task<GenerateRecurringAuditsResult> Handle(GenerateRecurringAuditsCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new GenerateRecurringAuditsResult
        {
            WasDryRun = request.DryRun
        };

        try
        {
            // Get recurring audit settings that are due for generation
            var dueSettings = await GetDueRecurringAuditSettingsAsync(request.RecurringAuditSettingId, cancellationToken);
            result.SettingsProcessed = dueSettings.Count;

            var auditsGenerated = 0;

            foreach (var setting in dueSettings)
            {
                if (auditsGenerated >= request.MaxAuditsToGenerate)
                {
                    result.Errors.Add($"Reached maximum audit generation limit of {request.MaxAuditsToGenerate}");
                    break;
                }

                try
                {
                    var generatedAuditIds = await GenerateAuditsForSettingAsync(setting, request.DryRun, cancellationToken);
                    result.GeneratedAuditIds.AddRange(generatedAuditIds);
                    auditsGenerated += generatedAuditIds.Count;

                    // Update the setting's last generated date and next generation date
                    if (!request.DryRun)
                    {
                        await UpdateSettingAfterGenerationAsync(setting, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorsEncountered++;
                    result.Errors.Add($"Error generating audits for setting '{setting.SettingName}': {ex.Message}");
                }
            }

            result.AuditsGenerated = auditsGenerated;
        }
        catch (Exception ex)
        {
            result.ErrorsEncountered++;
            result.Errors.Add($"Fatal error during audit generation: {ex.Message}");
        }

        stopwatch.Stop();
        result.ProcessingDuration = stopwatch.Elapsed;

        return result;
    }

    private async Task<List<RecurringAuditSetting>> GetDueRecurringAuditSettingsAsync(string? specificSettingId, CancellationToken cancellationToken)
    {
        var today = DateOnly.FromDateTime(DateTime.UtcNow);

        var query = _context.RecurringAuditSettings
            .Include(r => r.RecurrenceRule)
            .Include(r => r.AuditTemplate)
            .Include(r => r.AssignToUser)
            .Include(r => r.AssignToUserGroup)
            .Where(r => r.IsEnabled);

        // Filter by specific setting if provided
        if (!string.IsNullOrEmpty(specificSettingId))
        {
            query = query.Where(r => r.Id == specificSettingId);
        }
        else
        {
            // Only get settings that are due for generation
            query = query.Where(r => r.NextGenerationDate.HasValue && r.NextGenerationDate.Value <= today);
        }

        return await query.ToListAsync(cancellationToken);
    }

    private async Task<List<string>> GenerateAuditsForSettingAsync(RecurringAuditSetting setting, bool dryRun, CancellationToken cancellationToken)
    {
        var generatedAuditIds = new List<string>();

        switch (setting.AssignmentType)
        {
            case Domain.Enums.AssignmentType.Individual:
                var auditId = await GenerateIndividualAuditAsync(setting, dryRun, cancellationToken);
                if (!string.IsNullOrEmpty(auditId))
                {
                    generatedAuditIds.Add(auditId);
                }
                break;

            case Domain.Enums.AssignmentType.GroupAny:
                var groupAnyAuditId = await GenerateGroupAnyAuditAsync(setting, dryRun, cancellationToken);
                if (!string.IsNullOrEmpty(groupAnyAuditId))
                {
                    generatedAuditIds.Add(groupAnyAuditId);
                }
                break;

            case Domain.Enums.AssignmentType.GroupAllScheduled:
                var groupAllAuditIds = await GenerateGroupAllScheduledAuditsAsync(setting, dryRun, cancellationToken);
                generatedAuditIds.AddRange(groupAllAuditIds);
                break;
        }

        return generatedAuditIds;
    }

    private async Task<string?> GenerateIndividualAuditAsync(RecurringAuditSetting setting, bool dryRun, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(setting.AssignToUserId))
        {
            throw new InvalidOperationException("AssignToUserId is required for Individual assignment type");
        }

        var audit = CreateBaseAudit(setting);
        audit.AssignedToUserId = setting.AssignToUserId;

        if (!dryRun)
        {
            await _context.Audits.AddAsync(audit, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return audit.Id;
    }

    private async Task<string?> GenerateGroupAnyAuditAsync(RecurringAuditSetting setting, bool dryRun, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(setting.AssignToUserGroupId))
        {
            throw new InvalidOperationException("AssignToUserGroupId is required for GroupAny assignment type");
        }

        var audit = CreateBaseAudit(setting);
        audit.AssignedToUserGroupId = setting.AssignToUserGroupId;

        if (!dryRun)
        {
            await _context.Audits.AddAsync(audit, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return audit.Id;
    }

    private async Task<List<string>> GenerateGroupAllScheduledAuditsAsync(RecurringAuditSetting setting, bool dryRun, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(setting.AssignToUserGroupId))
        {
            throw new InvalidOperationException("AssignToUserGroupId is required for GroupAllScheduled assignment type");
        }

        // Get all active users in the group
        var groupUsers = await _context.UserGroupMembers
            .Include(ugm => ugm.User)
            .Where(ugm => ugm.UserGroupId == setting.AssignToUserGroupId &&
                         ugm.User.IsActive)
            .Select(ugm => ugm.User)
            .ToListAsync(cancellationToken);

        var generatedAuditIds = new List<string>();

        foreach (var user in groupUsers)
        {
            var audit = CreateBaseAudit(setting);
            audit.AssignedToUserId = user.Id;

            if (!dryRun)
            {
                await _context.Audits.AddAsync(audit, cancellationToken);
            }

            generatedAuditIds.Add(audit.Id);
        }

        if (!dryRun && generatedAuditIds.Any())
        {
            await _context.SaveChangesAsync(cancellationToken);
        }

        return generatedAuditIds;
    }

    private Audit CreateBaseAudit(RecurringAuditSetting setting)
    {
        // Validate required fields for audit creation
        if (!setting.AreaId.HasValue)
        {
            throw new InvalidOperationException($"Cannot create audit from recurring setting {setting.Id}: AreaId is required but not specified in the setting.");
        }

        var scheduledDate = DateTime.UtcNow;
        var dueDate = scheduledDate.AddDays(setting.DeadlineDays);

        return new Audit
        {
            Id = CuidGenerator.Generate(),
            AuditTemplateId = setting.AuditTemplateId,
            RecurringAuditSettingId = setting.Id,
            FactoryId = setting.FactoryId,
            AreaId = setting.AreaId.Value, // Safe to use .Value since we validated above
            SubAreaId = setting.SubAreaId,
            ScheduledDate = scheduledDate,
            DueDate = dueDate,
            OverallStatus = AuditOverallStatus.Scheduled,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = _currentUserService.UserId ?? "SYSTEM"
        };
    }

    private async Task UpdateSettingAfterGenerationAsync(RecurringAuditSetting setting, CancellationToken cancellationToken)
    {
        setting.LastGeneratedAt = DateTime.UtcNow;

        if (setting.RecurrenceRule != null)
        {
            setting.NextGenerationDate = _recurrenceCalculationService.CalculateNextGenerationDate(setting.RecurrenceRule);
        }

        await _context.SaveChangesAsync(cancellationToken);
    }


}
