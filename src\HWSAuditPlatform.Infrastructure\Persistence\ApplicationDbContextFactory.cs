using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence;

/// <summary>
/// Factory for creating ApplicationDbContext at design time (for migrations)
/// </summary>
public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        // Determine environment from args or environment variables
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")
                         ?? Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")
                         ?? "Development";

        // Check if environment is passed as argument
        for (int i = 0; i < args.Length - 1; i++)
        {
            if (args[i] == "--environment" && i + 1 < args.Length)
            {
                environment = args[i + 1];
                break;
            }
        }

        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile($"appsettings.{environment}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        var connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb;Trusted_Connection=true;MultipleActiveResultSets=true";

        optionsBuilder.UseSqlServer(connectionString, b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName));

        // Create a mock current user service for design time
        var mockCurrentUserService = new DesignTimeCurrentUserService();

        return new ApplicationDbContext(optionsBuilder.Options, mockCurrentUserService);
    }
}

/// <summary>
/// Mock current user service for design-time operations
/// </summary>
internal class DesignTimeCurrentUserService : ICurrentUserService
{
    public string? UserId => "design-time-user";
    public string? Username => "design-time";
    public string? Email => "<EMAIL>";
    public UserRole? Role => UserRole.DevAdmin;
    public int? FactoryId => null;
    public bool IsAuthenticated => true;

    public bool HasRole(UserRole role) => true;
    public bool HasAnyRole(params UserRole[] roles) => true;
}
