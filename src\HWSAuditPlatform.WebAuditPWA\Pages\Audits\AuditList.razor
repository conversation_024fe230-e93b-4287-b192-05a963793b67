@page "/audits"
@page "/audits/{StatusFilter?}"
@attribute [Authorize]
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components.Audit
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@inject IAuditApiService AuditService
@inject NavigationManager Navigation
@inject ILogger<AuditList> Logger

<PageTitle>My Audits - HWS Audit Platform PWA</PageTitle>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-clipboard-list me-2 text-primary"></i>
                        My Audits
                    </h1>
                    <p class="text-muted mb-0">Manage and execute your assigned audits</p>
                </div>
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary me-2" @onclick="RefreshAudits" disabled="@isLoading">
                        <i class="fas fa-sync-alt me-1 @(isLoading ? "fa-spin" : "")"></i>
                        Refresh
                    </button>
                    <button class="btn btn-primary" @onclick="SyncOfflineData" disabled="@isSyncing">
                        <i class="fas fa-cloud-upload-alt me-1 @(isSyncing ? "fa-spin" : "")"></i>
                        Sync
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Search -->
                        <div class="col-12 col-md-4">
                            <label class="form-label">Search</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Search audits..." 
                                       @bind="searchTerm" @onkeypress="@(async (e) => { if (e.Key == "Enter") await ApplyFilters(); })" />
                            </div>
                        </div>

                        <!-- Status Filter -->
                        <div class="col-12 col-md-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" @bind="selectedStatus">
                                <option value="">All Statuses</option>
                                <option value="Scheduled">Scheduled</option>
                                <option value="InProgress">In Progress</option>
                                <option value="Submitted">Submitted</option>
                                <option value="Closed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>

                        <!-- Date Range -->
                        <div class="col-12 col-md-2">
                            <label class="form-label">From Date</label>
                            <input type="date" class="form-control" @bind="fromDate" />
                        </div>
                        <div class="col-12 col-md-2">
                            <label class="form-label">To Date</label>
                            <input type="date" class="form-control" @bind="toDate" />
                        </div>

                        <!-- Apply Filters -->
                        <div class="col-12 col-md-1 d-flex align-items-end">
                            <button class="btn btn-primary w-100" @onclick="ApplyFilters" disabled="@isLoading">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills nav-fill">
                <li class="nav-item">
                    <button class="nav-link @(string.IsNullOrEmpty(activeTab) ? "active" : "")" 
                            @onclick="() => SetActiveTab(string.Empty)">
                        <i class="fas fa-list me-1"></i>
                        All (@totalCount)
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link @(activeTab == "pending" ? "active" : "")"
                            @onclick='() => SetActiveTab("pending")'>
                        <i class="fas fa-clock me-1"></i>
                        Pending (@pendingCount)
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link @(activeTab == "inprogress" ? "active" : "")"
                            @onclick='() => SetActiveTab("inprogress")'>
                        <i class="fas fa-play me-1"></i>
                        In Progress (@inProgressCount)
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link @(activeTab == "completed" ? "active" : "")"
                            @onclick='() => SetActiveTab("completed")'>
                        <i class="fas fa-check me-1"></i>
                        Completed (@completedCount)
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link @(activeTab == "overdue" ? "active" : "")"
                            @onclick='() => SetActiveTab("overdue")'>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Overdue (@overdueCount)
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Audit List -->
    <div class="row">
        <div class="col-12">
            @if (isLoading)
            {
                <LoadingSpinner Message="Loading audits..." />
            }
            else if (filteredAudits.Any())
            {
                <div class="row">
                    @foreach (var audit in filteredAudits)
                    {
                        <div class="col-12 col-lg-6 col-xl-4 mb-3">
                            <AuditCard AuditSummary="@audit" 
                                      ShowActions="true"
                                      OnCardClicked="@HandleAuditCardClick"
                                      OnStartClicked="@HandleStartAudit"
                                      OnContinueClicked="@HandleContinueAudit"
                                      OnViewClicked="@HandleViewAudit" />
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <div class="row mt-4">
                        <div class="col-12">
                            <nav aria-label="Audit pagination">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item @(currentPage <= 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage <= 1)">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </li>
                                    
                                    @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                    {
                                        var pageNumber = i;
                                        <li class="page-item @(currentPage == pageNumber ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(pageNumber)">
                                                @pageNumber
                                            </button>
                                        </li>
                                    }
                                    
                                    <li class="page-item @(currentPage >= totalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage >= totalPages)">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                            
                            <div class="text-center text-muted">
                                Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, totalCount) of @totalCount audits
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <EmptyState Title="@GetEmptyStateTitle()"
                           Description="@GetEmptyStateDescription()"
                           IconClass="@GetEmptyStateIcon()"
                           ActionText="Refresh"
                           ActionIcon="fas fa-sync-alt"
                           OnActionClick="@RefreshAudits" />
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public string? StatusFilter { get; set; }

    private List<AuditSummaryDto> allAudits = new();
    private List<AuditSummaryDto> filteredAudits = new();
    private bool isLoading = true;
    private bool isSyncing = false;
    private string activeTab = string.Empty;
    private string searchTerm = string.Empty;
    private string selectedStatus = string.Empty;
    private DateTime? fromDate;
    private DateTime? toDate;
    
    // Pagination
    private int currentPage = 1;
    private int pageSize = 12;
    private int totalCount = 0;
    private int totalPages = 0;
    
    // Counts for tabs
    private int pendingCount = 0;
    private int inProgressCount = 0;
    private int completedCount = 0;
    private int overdueCount = 0;

    protected override async Task OnInitializedAsync()
    {
        // Set initial tab based on route parameter
        if (!string.IsNullOrEmpty(StatusFilter))
        {
            activeTab = StatusFilter.ToLower();
        }
        
        await LoadAudits();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(StatusFilter))
        {
            activeTab = StatusFilter.ToLower();
            await ApplyFilters();
        }
    }

    private async Task LoadAudits()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await AuditService.GetAssignedAuditsAsync();
            if (result.IsSuccess && result.Data != null)
            {
                allAudits = result.Data.Select(ConvertToAuditSummary).ToList();
                UpdateCounts();
                await ApplyFilters();
            }
            else
            {
                Logger.LogWarning("Failed to load audits: {Error}", result.ErrorMessage);
                allAudits = new List<AuditSummaryDto>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audits");
            allAudits = new List<AuditSummaryDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshAudits()
    {
        await LoadAudits();
    }

    private async Task SyncOfflineData()
    {
        try
        {
            isSyncing = true;
            StateHasChanged();

            // TODO: Implement sync functionality
            await Task.Delay(2000); // Simulate sync

            // Refresh audits after sync
            await LoadAudits();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error syncing offline data");
        }
        finally
        {
            isSyncing = false;
            StateHasChanged();
        }
    }

    private Task ApplyFilters()
    {
        var filtered = allAudits.AsEnumerable();

        // Apply tab filter
        filtered = activeTab switch
        {
            "pending" => filtered.Where(a => a.OverallStatus == AuditOverallStatus.Scheduled && !a.IsOverdue),
            "inprogress" => filtered.Where(a => a.OverallStatus == AuditOverallStatus.InProgress),
            "completed" => filtered.Where(a => a.OverallStatus == AuditOverallStatus.Closed || a.OverallStatus == AuditOverallStatus.Submitted),
            "overdue" => filtered.Where(a => a.IsOverdue),
            _ => filtered
        };

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.ToLower();
            filtered = filtered.Where(a =>
                (a.AuditTemplateName?.ToLower().Contains(search) == true) ||
                (a.FactoryName?.ToLower().Contains(search) == true) ||
                (a.AreaName?.ToLower().Contains(search) == true) ||
                (a.SubAreaName?.ToLower().Contains(search) == true));
        }

        // Apply status filter
        if (!string.IsNullOrEmpty(selectedStatus) && Enum.TryParse<AuditOverallStatus>(selectedStatus, out var status))
        {
            filtered = filtered.Where(a => a.OverallStatus == status);
        }

        // Apply date filters
        if (fromDate.HasValue)
        {
            filtered = filtered.Where(a => a.ScheduledDate.Date >= fromDate.Value.Date);
        }
        if (toDate.HasValue)
        {
            filtered = filtered.Where(a => a.ScheduledDate.Date <= toDate.Value.Date);
        }

        // Apply pagination
        totalCount = filtered.Count();
        totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }

        filteredAudits = filtered
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        StateHasChanged();

        return Task.CompletedTask;
    }

    private void UpdateCounts()
    {
        pendingCount = allAudits.Count(a => a.OverallStatus == AuditOverallStatus.Scheduled && !a.IsOverdue);
        inProgressCount = allAudits.Count(a => a.OverallStatus == AuditOverallStatus.InProgress);
        completedCount = allAudits.Count(a => a.OverallStatus == AuditOverallStatus.Closed || a.OverallStatus == AuditOverallStatus.Submitted);
        overdueCount = allAudits.Count(a => a.IsOverdue);
    }

    private async Task SetActiveTab(string tab)
    {
        activeTab = tab;
        currentPage = 1; // Reset to first page when changing tabs
        await ApplyFilters();

        // Update URL
        var url = string.IsNullOrEmpty(tab) ? "/audits" : $"/audits/{tab}";
        Navigation.NavigateTo(url, replace: true);
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await ApplyFilters();
        }
    }

    private AuditSummaryDto ConvertToAuditSummary(Audit audit)
    {
        return new AuditSummaryDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignedToUserName = audit.AssignedToUser?.FullName,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory?.FactoryName,
            AreaName = audit.Area?.AreaName,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = (double?)audit.OverallScore,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now &&
                       audit.OverallStatus != AuditOverallStatus.Closed,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            TotalQuestions = audit.Answers?.Count ?? 0,
            AnsweredQuestions = audit.Answers?.Count(a => !string.IsNullOrEmpty(a.AnswerText) ||
                                                              a.AnswerBoolean.HasValue ||
                                                              a.AnswerNumeric.HasValue ||
                                                              a.AnswerDate.HasValue) ?? 0
        };
    }

    private async Task HandleAuditCardClick(AuditSummaryDto audit)
    {
        // Navigate to audit details or execution based on status
        if (audit.OverallStatus == AuditOverallStatus.Scheduled)
        {
            await HandleStartAudit(audit);
        }
        else if (audit.OverallStatus == AuditOverallStatus.InProgress)
        {
            await HandleContinueAudit(audit);
        }
        else
        {
            await HandleViewAudit(audit);
        }
    }

    private async Task HandleStartAudit(AuditSummaryDto audit)
    {
        try
        {
            var result = await AuditService.StartAuditAsync(audit.Id);
            if (result.IsSuccess)
            {
                // Navigate to audit execution
                Navigation.NavigateTo($"/audits/{audit.Id}/execute");
            }
            else
            {
                Logger.LogWarning("Failed to start audit: {AuditId}, Error: {Error}", audit.Id, result.ErrorMessage);
                // TODO: Show error message to user
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting audit: {AuditId}", audit.Id);
            // TODO: Show error message to user
        }
    }

    private async Task HandleContinueAudit(AuditSummaryDto audit)
    {
        // Navigate to audit execution
        Navigation.NavigateTo($"/audits/{audit.Id}/execute");
        await Task.CompletedTask;
    }

    private async Task HandleViewAudit(AuditSummaryDto audit)
    {
        // Navigate to audit results view
        Navigation.NavigateTo($"/audit-results/{audit.Id}");
        await Task.CompletedTask;
    }

    private string GetEmptyStateTitle()
    {
        return activeTab switch
        {
            "pending" => "No Pending Audits",
            "inprogress" => "No Audits In Progress",
            "completed" => "No Completed Audits",
            "overdue" => "No Overdue Audits",
            _ => "No Audits Found"
        };
    }

    private string GetEmptyStateDescription()
    {
        return activeTab switch
        {
            "pending" => "You don't have any pending audits at the moment. Check back later for new assignments.",
            "inprogress" => "You don't have any audits currently in progress. Start a pending audit to begin.",
            "completed" => "You haven't completed any audits yet. Complete some audits to see them here.",
            "overdue" => "Great! You don't have any overdue audits. Keep up the good work!",
            _ => !string.IsNullOrWhiteSpace(searchTerm) || !string.IsNullOrEmpty(selectedStatus) || fromDate.HasValue || toDate.HasValue
                ? "No audits match your current filters. Try adjusting your search criteria."
                : "You don't have any assigned audits at the moment. Check back later for new assignments."
        };
    }

    private string GetEmptyStateIcon()
    {
        return activeTab switch
        {
            "pending" => "fas fa-clock fa-3x text-warning",
            "inprogress" => "fas fa-play-circle fa-3x text-info",
            "completed" => "fas fa-check-circle fa-3x text-success",
            "overdue" => "fas fa-exclamation-triangle fa-3x text-danger",
            _ => "fas fa-clipboard-list fa-3x text-muted"
        };
    }
}

<style>
    /* Industrial Nav Pills Styling */
    .nav-pills .nav-link {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        margin: 0 0.25rem;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px var(--industrial-shadow-light);
    }

    .nav-pills .nav-link:hover {
        background: linear-gradient(135deg, rgba(20, 184, 166, 0.1) 0%, rgba(20, 184, 166, 0.05) 100%);
        border-color: var(--industrial-teal);
        color: var(--industrial-teal-light);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px var(--industrial-shadow);
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        border-color: var(--industrial-teal);
        color: var(--industrial-text-light);
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.4);
        transform: translateY(-1px);
        font-weight: 600;
    }

    .nav-pills .nav-link.active:hover {
        background: linear-gradient(135deg, var(--industrial-teal-light) 0%, var(--industrial-teal) 100%);
        box-shadow: 0 6px 16px rgba(20, 184, 166, 0.5);
        transform: translateY(-2px);
    }

    .nav-pills .nav-link i {
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }

    /* Responsive adjustments for nav pills */
    @@media (max-width: 767px) {
        .nav-pills .nav-link {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            margin: 0 0.125rem;
            min-height: 44px;
        }

        .nav-pills .nav-link i {
            margin-right: 0.25rem;
            font-size: 0.8rem;
        }
    }

    @@media (min-width: 768px) and (max-width: 1024px) {
        .nav-pills .nav-link {
            padding: 0.875rem 1.25rem;
            font-size: 0.9rem;
            margin: 0 0.375rem;
        }
    }

    /* Pagination styling */
    .pagination .page-link {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 6px;
        margin: 0 0.125rem;
        padding: 0.5rem 0.75rem;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-teal);
        color: var(--industrial-teal-light);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        border-color: var(--industrial-teal);
        color: var(--industrial-text-light);
        box-shadow: 0 2px 6px rgba(20, 184, 166, 0.3);
    }

    .pagination .page-item.disabled .page-link {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-border);
        color: var(--industrial-text-muted);
        opacity: 0.6;
    }

    /* Empty state icon colors */
    .text-warning { color: var(--industrial-orange) !important; }
    .text-info { color: var(--industrial-teal) !important; }
    .text-success { color: var(--industrial-green) !important; }
    .text-danger { color: var(--industrial-red) !important; }
    .text-muted { color: var(--industrial-text-muted) !important; }
</style>
