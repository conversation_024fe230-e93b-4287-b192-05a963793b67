using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateQuestion;

/// <summary>
/// Handler for UpdateQuestionCommand
/// </summary>
public class UpdateQuestionCommandHandler : BaseCommandHandler<UpdateQuestionCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateQuestionCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(UpdateQuestionCommand request, CancellationToken cancellationToken)
    {
        // Get the question with its template
        var question = await _context.Questions
            .Include(q => q.AuditTemplate)
            .FirstOrDefaultAsync(q => q.Id == request.QuestionId && q.IsActive, cancellationToken);

        if (question == null)
        {
            throw new NotFoundException(nameof(Question), request.QuestionId);
        }

        // Verify the template ID matches
        if (question.AuditTemplateId != request.TemplateId)
        {
            throw new InvalidOperationException("Question does not belong to the specified template");
        }

        // Check for optimistic concurrency
        if (question.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The question has been modified by another user. Please refresh and try again.");
        }

        // Don't allow updates to questions in published templates
        if (question.AuditTemplate.IsPublished)
        {
            throw new InvalidOperationException("Cannot update questions in a published template");
        }

        // Verify question group belongs to the template if specified
        if (request.QuestionGroupId.HasValue)
        {
            var questionGroupExists = await _context.QuestionGroups
                .AnyAsync(g => g.Id == request.QuestionGroupId.Value && 
                              g.AuditTemplateId == request.TemplateId && 
                              g.IsActive, cancellationToken);

            if (!questionGroupExists)
            {
                throw new InvalidOperationException("Question group does not exist or does not belong to the template");
            }
        }

        // Verify parent question exists and belongs to the same template if specified
        if (request.ParentQuestionId.HasValue)
        {
            var parentQuestionExists = await _context.Questions
                .AnyAsync(q => q.Id == request.ParentQuestionId.Value && 
                              q.AuditTemplateId == request.TemplateId && 
                              q.IsActive, cancellationToken);

            if (!parentQuestionExists)
            {
                throw new InvalidOperationException("Parent question does not exist or does not belong to the template");
            }

            // Prevent circular references
            if (request.ParentQuestionId.Value == request.QuestionId)
            {
                throw new InvalidOperationException("A question cannot be its own parent");
            }
        }

        // Update the question properties
        question.QuestionGroupId = request.QuestionGroupId;
        question.QuestionText = request.QuestionText;
        question.QuestionType = request.QuestionType;
        question.DisplayOrder = request.DisplayOrder;
        question.IsRequired = request.IsRequired;
        question.Weight = request.Weight;
        question.HelpText = request.HelpText;
        question.ParentQuestionId = request.ParentQuestionId;
        question.TriggerAnswerValue = request.TriggerAnswerValue;
        question.SeverityLevel = request.SeverityLevel;
        question.EvidenceRequired = request.EvidenceRequired;
        question.UpdatedAt = DateTime.UtcNow;
        question.UpdatedByUserId = _currentUserService.UserId;

        // Update allowed evidence types
        // Remove existing evidence types
        var existingEvidenceTypes = await _context.QuestionAllowedEvidenceTypes
            .Where(qaet => qaet.QuestionId == question.Id)
            .ToListAsync(cancellationToken);

        _context.QuestionAllowedEvidenceTypes.RemoveRange(existingEvidenceTypes);

        // Add new evidence types
        foreach (var evidenceTypeRequest in request.AllowedEvidenceTypes)
        {
            var allowedEvidenceType = new QuestionAllowedEvidenceType
            {
                QuestionId = question.Id,
                EvidenceType = evidenceTypeRequest.EvidenceType,
                DisplayOrder = evidenceTypeRequest.DisplayOrder,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.QuestionAllowedEvidenceTypes.AddAsync(allowedEvidenceType, cancellationToken);
        }

        // Update the template's modified timestamp
        question.AuditTemplate.UpdatedAt = DateTime.UtcNow;
        question.AuditTemplate.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
