using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Services;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Findings.Commands.AssignFindingCategory;

/// <summary>
/// Handler for assigning finding categories
/// </summary>
public class AssignFindingCategoryCommandHandler : BaseCommandHandler<AssignFindingCategoryCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IFindingCategorizationService _categorizationService;

    public AssignFindingCategoryCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IFindingCategorizationService categorizationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _categorizationService = categorizationService;
    }

    public override async Task<bool> Handle(AssignFindingCategoryCommand request, CancellationToken cancellationToken)
    {
        // Get the finding with its audit and template
        var finding = await _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.AuditTemplate)
            .FirstOrDefaultAsync(f => f.Id == request.FindingId, cancellationToken);

        if (finding == null)
        {
            throw new NotFoundException("Finding", request.FindingId);
        }

        var auditTemplate = finding.AuditAnswer.Audit.AuditTemplate;

        // If removing category
        if (!request.FindingCategoryId.HasValue)
        {
            _categorizationService.RemoveCategory(finding);
            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }

        // Validate that categorization is enabled for the template
        if (!auditTemplate.EnableFindingCategorization)
        {
            throw new ValidationException("FindingCategoryId", "Finding categorization is not enabled for this audit template.");
        }

        // Validate that the category exists and belongs to the correct template
        var category = await _context.FindingCategories
            .FirstOrDefaultAsync(fc => fc.Id == request.FindingCategoryId.Value && 
                                      fc.AuditTemplateId == auditTemplate.Id &&
                                      fc.IsActive, cancellationToken);

        if (category == null)
        {
            throw new NotFoundException("FindingCategory", request.FindingCategoryId.Value);
        }

        // Assign the category
        var success = _categorizationService.AssignCategory(finding, request.FindingCategoryId.Value, auditTemplate);

        if (success)
        {
            await _context.SaveChangesAsync(cancellationToken);
        }

        return success;
    }
}
