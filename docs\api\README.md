# HWS Audit Platform - API Documentation

## Overview

This section contains comprehensive documentation for all HWS Audit Platform APIs. The platform provides RESTful APIs for managing manufacturing quality audits with offline capabilities and enterprise integration.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **API Version**: `1.0`
- **Authentication**: JWT Bearer <PERSON>ken
- **Content-Type**: `application/json`
- **Interactive Documentation**: 
  - **Scalar UI** (Primary): `https://localhost:7490/scalar/v1`
  - **Swagger UI** (Fallback): `https://localhost:7490/swagger`

## API Documentation Sections

### [Authentication & User Management](authentication.md)
Complete documentation for user authentication and user lifecycle management.
- JWT token-based authentication
- Active Directory integration
- Role-based access control
- User lifecycle management

### [Audit Management](audit-management.md)
Comprehensive documentation for the complete audit lifecycle management.
- Complete audit lifecycle (Draft → InProgress → Submitted → Approved/Rejected)
- Advanced filtering and search capabilities
- Audit answer management with evidence attachments
- Overdue audit tracking

### [Organization Management](organization-management.md)
Documentation for managing the organizational hierarchy and structure.
- Four-level organizational hierarchy (Location → Factory → Area → SubArea)
- Hierarchical data retrieval
- Entity creation and management
- Active/inactive status management

### [Templates & Scheduling](templates-scheduling.md)
Documentation for audit template management and recurring audit scheduling.
- Template versioning and publishing workflow
- Multiple question types (YesNo, SingleSelect, MultiSelect, Numeric, Text)
- Evidence requirements for questions
- Recurring audit scheduling (Daily, Weekly, Monthly, Quarterly)

### [Area Responsibility Management](area-responsibility-endpoints.md)
Documentation for area-based responsibility assignment system.
- Automatic assignment of responsible users based on audit location
- Support for different responsibility types (corrective actions, analysis, etc.)
- Template-specific and general responsibility configurations
- Priority-based assignment when multiple users have same responsibility

### [Finding Categorization](finding-categorization-endpoints.md)
Documentation for finding classification and categorization system.
- Template-specific finding categories for better reporting
- Flexible categorization with color coding and icons
- Usage statistics and analytics
- Optional categorization based on organizational needs

### [File Management & Health Monitoring](files-health.md)
Documentation for file operations and system health monitoring.
- Secure file upload with type and size validation
- Temporary download URL generation
- File storage on external drive
- Comprehensive health monitoring

### [Integration Guide](integration-guide.md)
Complete guide for integrating with the platform APIs.
- Controller implementations
- Request/response flows
- Authentication and authorization
- File upload/download operations
- Offline synchronization patterns

## Quick Start

### 1. Authentication
```bash
curl -X POST "https://localhost:7490/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin.user",
    "password": "HWSAudit123!"
  }'
```

### 2. Use the Token
```bash
curl -X GET "https://localhost:7490/api/v1/audits" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "fieldName",
      "message": "Field-specific error message"
    }
  ]
}
```

## Authentication & Authorization

### JWT Token Structure
- **Algorithm**: HS256
- **Expiry**: 8 hours
- **Claims**: User ID, Username, Role, Factory ID

### Role Hierarchy
- **Admin**: Full system access
- **Manager**: Factory-level management access
- **Auditor**: Audit execution access

### Authorization Policies
- `AuditorOrAbove`: Auditor, Manager, or Admin
- `ManagerOrAbove`: Manager or Admin only
- `DevAdminOnly`: Admin access only

## Error Codes

- `200 OK`: Success
- `201 Created`: Resource created successfully
- `204 No Content`: Success with no response body
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (duplicate, concurrency)
- `413 Payload Too Large`: File too large
- `415 Unsupported Media Type`: Invalid file type
- `500 Internal Server Error`: Server error

## Support

- **Interactive Testing**: Use Scalar UI at `/scalar/v1`
- **Health Monitoring**: Check `/health` endpoint
- **API Information**: Visit `/health/info` endpoint
