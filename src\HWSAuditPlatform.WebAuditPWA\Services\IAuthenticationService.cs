using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Service for handling authentication operations in the PWA
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticates a user with username and password
    /// </summary>
    /// <param name="username">Username</param>
    /// <param name="password">Password</param>
    /// <returns>Authentication result</returns>
    Task<AuthenticationResult> LoginAsync(string username, string password);

    /// <summary>
    /// Logs out the current user
    /// </summary>
    Task LogoutAsync();

    /// <summary>
    /// Refreshes the current authentication token
    /// </summary>
    /// <returns>New authentication result</returns>
    Task<AuthenticationResult?> RefreshTokenAsync();

    /// <summary>
    /// Gets the current authentication token
    /// </summary>
    /// <returns>Current token or null if not authenticated</returns>
    Task<string?> GetTokenAsync();

    /// <summary>
    /// Gets the current user information
    /// </summary>
    /// <returns>Current user or null if not authenticated</returns>
    Task<UserInfo?> GetCurrentUserAsync();

    /// <summary>
    /// Checks if the user is currently authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    Task<bool> IsAuthenticatedAsync();
}
