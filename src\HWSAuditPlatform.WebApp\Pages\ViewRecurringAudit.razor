@page "/recurring-audits/{id}"
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Application.Scheduling.DTOs
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "ProcessOwnerOrAbove")]

@inject IRecurringAuditApiService RecurringAuditService
@inject NavigationManager Navigation
@inject ILogger<ViewRecurringAudit> Logger

<PageTitle>@(setting?.SettingName ?? "Recurring Audit Details") - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-calendar-event me-2"></i>
                        @(setting?.SettingName ?? "Loading...")
                    </h2>
                    <p class="text-muted mb-0">Recurring Audit Schedule Details</p>
                </div>
                <div class="d-flex gap-2">
                    @if (setting != null)
                    {
                        <button class="btn btn-outline-primary" @onclick="NavigateToEdit">
                            <i class="bi bi-pencil me-2"></i>
                            Edit
                        </button>
                        <button class="btn btn-outline-success" @onclick="GeneratePreview" disabled="@isGenerating">
                            @if (isGenerating)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-eye me-2"></i>
                            }
                            Preview Generation
                        </button>
                        <button class="btn btn-outline-warning" @onclick="GenerateNow" disabled="@isGenerating">
                            @if (isGenerating)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-play-fill me-2"></i>
                            }
                            Generate Now
                        </button>
                    }
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to List
                    </button>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    @errorMessage
                    <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    @successMessage
                    <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
                </div>
            }

            @if (isLoading)
            {
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="text-muted">Loading recurring audit details...</div>
                    </div>
                </div>
            }
            else if (setting == null)
            {
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Recurring audit setting not found.
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Basic Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Setting Name</label>
                                            <div class="fw-medium">@setting.SettingName</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Audit Template</label>
                                            <div class="fw-medium">@setting.AuditTemplateName</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Deadline Days</label>
                                            <div class="fw-medium">@setting.DeadlineDays days</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Status</label>
                                            <div>
                                                @if (setting.IsEnabled)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        Enabled
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-pause-circle me-1"></i>
                                                        Disabled
                                                    </span>
                                                }
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Ready to Generate</label>
                                            <div>
                                                @if (setting.IsReadyToGenerate)
                                                {
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="bi bi-clock me-1"></i>
                                                        Ready
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-light text-dark">
                                                        <i class="bi bi-clock me-1"></i>
                                                        Not Ready
                                                    </span>
                                                }
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Valid Assignment</label>
                                            <div>
                                                @if (setting.HasValidAssignment)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        Valid
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="bi bi-x-circle me-1"></i>
                                                        Invalid
                                                    </span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    Location
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Factory</label>
                                            <div class="fw-medium">@setting.FactoryName</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Area</label>
                                            <div class="fw-medium">@(setting.AreaName ?? "All Areas")</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Sub Area</label>
                                            <div class="fw-medium">@(setting.SubAreaName ?? "All Sub Areas")</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Assignment Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-check me-2"></i>
                                    Assignment
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Assignment Type</label>
                                            <div class="fw-medium">
                                                @switch (setting.AssignmentType)
                                                {
                                                    case AssignmentType.Individual:
                                                        <span class="badge bg-primary">
                                                            <i class="bi bi-person me-1"></i>
                                                            Individual
                                                        </span>
                                                        break;
                                                    case AssignmentType.GroupAny:
                                                        <span class="badge bg-info">
                                                            <i class="bi bi-people me-1"></i>
                                                            Group (Any)
                                                        </span>
                                                        break;
                                                    case AssignmentType.GroupAllScheduled:
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="bi bi-people-fill me-1"></i>
                                                            Group (All Scheduled)
                                                        </span>
                                                        break;
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Assigned To</label>
                                            <div class="fw-medium">
                                                @if (setting.AssignmentType == AssignmentType.Individual)
                                                {
                                                    @(setting.AssignToUserName ?? "Not assigned")
                                                }
                                                else
                                                {
                                                    @(setting.AssignToUserGroupName ?? "Not assigned")
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Schedule Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-calendar-week me-2"></i>
                                    Schedule
                                </h5>
                            </div>
                            <div class="card-body">
                                @if (setting.RecurrenceRule != null)
                                {
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Recurrence Pattern</label>
                                        <div class="fw-medium">@setting.RecurrenceRule.Description</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Start Date</label>
                                        <div class="fw-medium">@setting.RecurrenceRule.StartDate.ToString("MMM dd, yyyy")</div>
                                    </div>
                                    @if (setting.RecurrenceRule.EndDate.HasValue)
                                    {
                                        <div class="mb-3">
                                            <label class="form-label text-muted">End Date</label>
                                            <div class="fw-medium">@setting.RecurrenceRule.EndDate.Value.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="text-muted">No recurrence rule configured</div>
                                }
                            </div>
                        </div>

                        <!-- Generation Status -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Generation Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Next Generation Date</label>
                                    <div class="fw-medium">
                                        @if (setting.NextGenerationDate.HasValue)
                                        {
                                            @setting.NextGenerationDate.Value.ToString("MMM dd, yyyy")
                                        }
                                        else
                                        {
                                            <span class="text-muted">Not calculated</span>
                                        }
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Generated</label>
                                    <div class="fw-medium">
                                        @if (setting.LastGeneratedAt.HasValue)
                                        {
                                            @setting.LastGeneratedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                        }
                                        else
                                        {
                                            <span class="text-muted">Never generated</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Audit Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-file-text me-2"></i>
                                    Audit Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created By</label>
                                    <div class="fw-medium">@(setting.CreatedByUserId ?? "System")</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created At</label>
                                    <div class="fw-medium">@setting.CreatedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated</label>
                                    <div class="fw-medium">@setting.UpdatedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public string Id { get; set; } = string.Empty;

    private RecurringAuditSettingDto? setting;
    private bool isLoading = true;
    private bool isGenerating = false;
    private string? errorMessage;
    private string? successMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadRecurringAuditSetting();
    }

    private async Task LoadRecurringAuditSetting()
    {
        try
        {
            isLoading = true;
            setting = await RecurringAuditService.GetRecurringAuditSettingByIdAsync(Id);
            
            if (setting == null)
            {
                errorMessage = "Recurring audit setting not found.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading recurring audit setting {Id}", Id);
            errorMessage = "Failed to load recurring audit setting. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task GeneratePreview()
    {
        try
        {
            isGenerating = true;
            errorMessage = null;
            successMessage = null;

            var result = await RecurringAuditService.GenerateRecurringAuditsAsync(
                settingId: Id,
                dryRun: true);

            if (result?.GeneratedAuditIds?.Any() == true)
            {
                successMessage = $"Preview: Would generate {result.GeneratedAuditIds.Count} audit(s) on the next run.";
            }
            else
            {
                successMessage = "Preview: No audits would be generated at this time.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating preview for recurring audit setting {Id}", Id);
            errorMessage = "Failed to generate preview. Please try again.";
        }
        finally
        {
            isGenerating = false;
        }
    }

    private async Task GenerateNow()
    {
        try
        {
            isGenerating = true;
            errorMessage = null;
            successMessage = null;

            var result = await RecurringAuditService.GenerateRecurringAuditsAsync(
                settingId: Id,
                dryRun: false);

            if (result?.GeneratedAuditIds?.Any() == true)
            {
                successMessage = $"Successfully generated {result.GeneratedAuditIds.Count} audit(s).";
                // Reload the setting to update the LastGeneratedAt timestamp
                await LoadRecurringAuditSetting();
            }
            else
            {
                successMessage = "No audits were generated at this time.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating audits for recurring audit setting {Id}", Id);
            errorMessage = "Failed to generate audits. Please try again.";
        }
        finally
        {
            isGenerating = false;
        }
    }

    private void NavigateToEdit()
    {
        Navigation.NavigateTo($"/recurring-audits/{Id}/edit");
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/recurring-audits");
    }
}
