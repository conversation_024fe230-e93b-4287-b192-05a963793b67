//// Project: HWS Audit Platform - Application Support
//// Version: 1.1
//// Last Updated: 2025-06-02

// Note on UserIds: UserId fields in this database will typically store the CUID (varchar(25))
// from the Users table in the main HWSAP_DB.

// ENUMS
Enum ApiKeyStatus {
  Active
  Revoked
  Expired
}

Enum EventProcessingStatus {
  Pending
  Processed
  Failed
  Skipped
}
Enum FeatureFlagTargetingType {
  Global // Applies to all users
  User // Applies to specific UserIds
  Group // Applies to users belonging to specific groups (group CUIDs from HWSAP_DB)
  Percentage // Applies to a percentage of users (requires consistent hashing)
}

Enum TelemetryEventType {
  PageView
  FeatureUsed
  ErrorOccurred
  PerformanceMetric
  UserAction // Generic user action not covered by others
}

//// Table Definitions


// API Key Management (from previous design)
Table UserApiKeys {
ApiKeyId varchar(25) [pk, note: 'CUID, unique identifier for the API key record.']
UserId varchar(25) [not null, note: 'CUID of the user in HWSAP_DB this key belongs to.']
HashedApiKey varchar(255) [not null, note: 'Securely hashed representation of the API key.']
ApiKeyPrefix varchar(10) [not null, unique, note: 'Short, unique prefix of the raw API key for display.']
Description varchar(255) [note: 'User-provided description for this API key.']
CreatedAt timestamp [not null, default: `now()`]
ExpiresAt timestamp [note: 'Optional expiration timestamp.']
LastUsedAt timestamp [note: 'Timestamp of last successful use.']
IsActive boolean [not null, default: true]
RevokedAt timestamp [note: 'Timestamp if explicitly revoked.']

indexes {
(UserId) [name: 'idx_userapikeys_userid']
(ApiKeyPrefix) [name: 'uidx_userapikeys_prefix']
}
Note: 'Stores API keys issued to users for programmatic access.'
}


// Event Store (from previous design)
Table StoredEvents {
EventId long [pk, increment, note: 'Global, sequential identifier for the event.']
StreamId varchar(255) [not null, note: 'Identifier for the stream of events.']
StreamPosition long [not null, note: 'Version or sequence number within its stream.']
EventType varchar(255) [not null, note: 'Type of event.']
Data text [not null, note: 'Serialized event payload (e.g., JSON).']
Metadata text [note: 'Serialized metadata (e.g., CorrelationId, UserId).']
OccurredAt timestamp [not null, default: `now()`]

indexes {
(StreamId, StreamPosition) [unique, name: 'uidx_storedevents_stream_position']
(EventType) [name: 'idx_storedevents_eventtype']
(OccurredAt) [name: 'idx_storedevents_occurredat']
}
Note: 'Stores domain events or significant application events.'
}



Table EventConsumerProgress { // (from previous design)
ConsumerName varchar(255) [pk, note: 'Unique name identifying the event consumer.']
LastProcessedEventId long [not null, note: 'The EventId of the last event processed.']
LastProcessedAt timestamp [not null, default: `now()`]
Note: 'Tracks progress of event consumers.'
}
// Application-Level Caching (from previous design)
Table ApplicationCache {
CacheKey varchar(255) [pk, note: 'Unique key for the cached item.']
CacheValue varbinary(max) [not null, note: 'Serialized cached data.']
AbsoluteExpiration timestamp [note: 'Absolute expiration timestamp.']
SlidingExpirationSeconds integer [note: 'Sliding expiration in seconds.']
LastAccessedAt timestamp [not null, default: `now()`]
CreatedAt timestamp [not null, default: `now()`]
Tags varchar(500) [note: 'Comma-separated tags for group invalidation.']
indexes {
(AbsoluteExpiration) [name: 'idx_appcache_abs_exp']
(Tags) [name: 'idx_appcache_tags']
}
Note: 'Simple database-backed cache. Use with caution and clear invalidation strategies.'
}
// Dynamic Application Settings (from previous design, now can be used for global feature flags)
Table ApplicationSettings {
SettingKey varchar(255) [pk, note: 'Unique key for the application setting.']
SettingValue nvarchar(max) [not null, note: 'Value of the setting.']
Description nvarchar(500) [note: 'Description of the setting.']
IsSensitive boolean [not null, default: false, note: 'If true, value might be handled with extra care.']
LastUpdatedAt timestamp [not null, default: `now()`]
UpdatedByUserId varchar(25) [note: 'CUID of the user from HWSAP_DB who last updated.']
Note: 'Stores dynamic application settings. Can be used for globally enabled/disabled features.'
}
// Feature Flag Management
Table FeatureFlags {
FeatureFlagId varchar(25) [pk, note: 'CUID, unique identifier for the feature flag.']
FeatureKey varchar(100) [not null, unique, note: 'Unique, human-readable key for the feature (e.g., "NewReportingDashboard", "PWAOfflineMapSupport").']
Description text [note: 'Description of the feature controlled by this flag.']
IsEnabledGlobally boolean [not null, default: false, note: 'Master switch. If false, targeting rules are ignored and feature is off for everyone. If true, targeting rules apply.']
// IsEnabled is effectively ApplicationSettings.SettingValue for global flags. This table provides more granular control.
CreatedAt timestamp [not null, default: `now()`]
UpdatedAt timestamp [not null, default:`now()`]
UpdatedByUserId varchar(25) [note: 'CUID of the user from HWSAP_DB who last updated.']
Note: 'Defines available features that can be toggled.'
}
Table FeatureFlagTargetingRules {
RuleId varchar(25) [pk, note: 'CUID, unique identifier for the targeting rule.']
FeatureFlagId varchar(25) [not null, ref: > FeatureFlags.FeatureFlagId, note: 'Links to the feature flag.']
TargetingType FeatureFlagTargetingType [not null, note: 'Type of targeting (User, Group, Percentage).']
TargetValue text [not null, note: 'Value for targeting (e.g., comma-separated UserCUIDs, GroupCUIDs, or percentage value "10").']
IsEnabledForTarget boolean [not null, default: true, note: 'Whether the feature is enabled or disabled for this specific target segment.']
Priority integer [not null, default: 0, note: 'Order of rule evaluation if multiple rules could apply. Lower numbers evaluated first.']
CreatedAt timestamp [not null, default: `now()`]
UpdatedAt timestamp [not null, default: `now()`]
Note: 'Defines specific rules for enabling/disabling a feature for segments of users.'
}
// User-Specific Application Data / Preferences
Table UserApplicationData {
UserId varchar(25) [pk, note: 'CUID of the user in HWSAP_DB. This table is 1-to-1 with Users in HWSAP_DB.']
// Example User Preferences
PreferredTheme varchar(50) [note: 'e.g., "dark", "light", "system" for UI theme.']
LastAccessedPwaAuditId varchar(25) [note: 'CUID of the last audit the user was working on in the PWA.']
PwaListDensity varchar(20) [note: 'e.g., "compact", "comfortable" for PWA list views.']
ManagementPortalDashboardLayout text [note: 'JSON storing user_s custom dashboard layout for the management portal.']
DismissedNotifications text [note: 'JSON array of notification IDs the user has dismissed.']
// Other user-specific settings that don_t belong in the core User entity in HWSAP_DB
LastUpdatedAt timestamp [not null, default: `now()`]
Note: 'Stores user-specific preferences and application state not part of the core user profile.'
}
// Application Telemetry / Usage Data
Table TelemetryEvents {
TelemetryEventId varchar(25) [pk, note: 'CUID, unique identifier for this telemetry event.']
SessionId varchar(50) [note: 'Identifier for the user_s session.']
UserId varchar(25) [note: 'CUID of the user from HWSAP_DB, if authenticated.']
AnonymousId varchar(50) [note: 'Identifier for anonymous users.']
EventType TelemetryEventType [not null, note: 'Type of telemetry event.']
EventTimestamp timestamp [not null, default:`now()`, note: 'Timestamp when the event occurred on the client or was received by the server.']
ClientTimestamp timestamp [note: 'Timestamp from the client device if different from server receipt.']
ClientAppVersion varchar(50) [note: 'Version of the client app (PWA, Management Portal).']
OperatingSystem varchar(100) [note: 'User_s OS.']
Browser varchar(100) [note: 'User_s browser.']
DeviceType varchar(50) [note: 'e.g., "Desktop", "Tablet", "Mobile".']
ScreenResolution varchar(20) [note: 'e.g., "1920x1080".']
PageUrl text [note: 'URL of the page where the event occurred.']
FeatureKey varchar(100) [note: 'Identifier of the feature used, if applicable.']
DurationMs long [note: 'Duration of an event or operation in milliseconds.']
ErrorMessage text [note: 'Error message if EventType is ErrorOccurred.']
ErrorStackTrace text [note: 'Stack trace for errors.']
CustomData text [note: 'JSON blob for any additional event-specific data.']
IPAddress varchar(45) [note: 'User_s IP address (handle privacy implications).']
Note: 'Stores application usage telemetry and client-side events for analytics and diagnostics.'
}

// User Refresh Tokens (Replaces UserApiKeys for session management)
Table UserRefreshTokens {
RefreshTokenId varchar(25) [pk, note: 'CUID, unique identifier for the refresh token record.']
UserId varchar(25) [not null, note: 'CUID of the user in HWSAP_DB this token belongs to.']
TokenValue varchar(255) [not null, unique, note: 'The actual refresh token string. This should be a cryptographically secure random string. It is stored directly as it needs to be looked up. Ensure it is long and complex.']
// Alternatively, if you want to hash it for an extra layer (though lookup becomes harder): HashedTokenValue varchar(255)
JwtId varchar(255) [note: 'Identifier of the JWT access token this refresh token was issued with/for. Helps in invalidating specific token families.']
CreatedAt timestamp [not null, default: `now()`, note: 'Timestamp when the refresh token was generated.']
ExpiresAt timestamp [not null, note: 'Timestamp when this refresh token will automatically expire (e.g., 30 days from creation).']
IsRevoked boolean [not null, default: false, note: 'Indicates if this token has been explicitly revoked (e.g., user logs out everywhere, password change).']
RevokedAt timestamp [note: 'Timestamp if the token was revoked.']
ReplacedByTokenId varchar(25) [ref: > UserRefreshTokens.RefreshTokenId, note: 'If this token was used and replaced by a new refresh token (token rotation strategy).']
// DeviceInfo text [note: 'Optional: Information about the device/client this token was issued for (e.g., User-Agent, IP Address at time of issue). Helps in auditing and identifying suspicious activity.']
indexes {
  (UserId) [name: 'idx_userrefreshtokens_userid']
  (ExpiresAt) [name: 'idx_userrefreshtokens_expiresat'] // For cleanup jobs
  }
  Note: '''Stores refresh tokens issued to users after successful authentication.
These tokens are long-lived and used to obtain new short-lived access tokens
without requiring re-authentication, enabling persistent sessions for up to X days.
The TokenValue itself is stored for lookup; ensure it is cryptographically random and sufficiently long.
Consider security implications of storing these tokens carefully.'''
}

//// Table Groups (Updated)

TableGroup "Access & Security" [color: #C0392B] {
UserApiKeys
UserRefreshTokens
Note: 'Manages API keys for programmatic user access.'
}
TableGroup "Eventing & Auditing" [color: #2980B9] {
StoredEvents
EventConsumerProgress
Note: 'Handles event storage for system-wide event sourcing or detailed auditing.'
}
TableGroup "Operational Support & Configuration" [color: #27AE60] {
ApplicationCache
ApplicationSettings
FeatureFlags
FeatureFlagTargetingRules
Note: 'Provides support for application caching, dynamic configuration, and feature flagging.'
}
TableGroup "User Experience & Personalization" [color: #8E44AD] {
UserApplicationData
Note: 'Stores user-specific preferences and application state.'
}