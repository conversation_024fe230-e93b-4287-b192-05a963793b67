using AutoMapper;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Mappings;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Common;

public class MappingTests : BaseDbTestClass
{
    private readonly IMapper _mapper;

    public MappingTests()
    {
        var configuration = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<UserMappingProfile>();
        });

        _mapper = configuration.CreateMapper();
    }

    [Fact]
    public void MappingProfile_ShouldHaveValidConfiguration()
    {
        // Act & Assert
        _mapper.ConfigurationProvider.AssertConfigurationIsValid();
    }

    [Fact]
    public async Task Map_UserToUserDto_ShouldMapCorrectlyAsync()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .FirstAsync(u => u.Username == "admin");

        // Act
        var userDto = _mapper.Map<UserDto>(user);

        // Assert
        userDto.Should().NotBeNull();
        userDto.Id.Should().Be(user.Id);
        userDto.Username.Should().Be(user.Username);
        userDto.FirstName.Should().Be(user.FirstName);
        userDto.LastName.Should().Be(user.LastName);
        userDto.Email.Should().Be(user.Email);
        userDto.Role.Should().Be(user.Role.RoleName);
        userDto.FactoryId.Should().Be(user.FactoryId);
        userDto.FactoryName.Should().Be(user.Factory?.FactoryName);
        userDto.IsActive.Should().Be(user.IsActive);
        userDto.CreatedAt.Should().Be(user.CreatedAt);
        userDto.UpdatedAt.Should().Be(user.UpdatedAt);
        userDto.RecordVersion.Should().Be(user.RecordVersion);
    }

    [Fact]
    public async Task Map_UserToUserSummaryDto_ShouldMapCorrectlyAsync()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .FirstAsync(u => u.Username == "manager");

        // Act
        var userSummaryDto = _mapper.Map<UserSummaryDto>(user);

        // Assert
        userSummaryDto.Should().NotBeNull();
        userSummaryDto.Id.Should().Be(user.Id);
        userSummaryDto.Username.Should().Be(user.Username);
        userSummaryDto.FullName.Should().Be($"{user.FirstName} {user.LastName}");
        userSummaryDto.Email.Should().Be(user.Email);
        userSummaryDto.Role.Should().Be(user.Role.RoleName);
        userSummaryDto.FactoryName.Should().Be(user.Factory?.FactoryName);
        userSummaryDto.IsActive.Should().Be(user.IsActive);
    }

    [Fact]
    public void Map_UserWithNullFactory_ShouldMapCorrectly()
    {
        // Arrange
        var user = User.Create(
            username: "nofactory",
            firstName: "No",
            lastName: "Factory",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        // Mock role for mapping
        var role = new Role 
        { 
            Id = 1, 
            RoleName = UserRole.DevAdmin,
            Description = "Administrator" 
        };
        
        // Set role via reflection for testing
        var roleProperty = typeof(User).GetProperty("Role");
        roleProperty?.SetValue(user, role);

        // Act
        var userDto = _mapper.Map<UserDto>(user);

        // Assert
        userDto.Should().NotBeNull();
        userDto.FactoryId.Should().BeNull();
        userDto.FactoryName.Should().BeNull();
    }

    [Fact]
    public void Map_UserWithNullNames_ShouldMapToEmptyStrings()
    {
        // Arrange
        var user = User.Create(
            username: "nullnames",
            firstName: null,
            lastName: null,
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        // Mock role for mapping
        var role = new Role 
        { 
            Id = 1, 
            RoleName = UserRole.DevAdmin,
            Description = "Administrator" 
        };
        
        var roleProperty = typeof(User).GetProperty("Role");
        roleProperty?.SetValue(user, role);

        // Act
        var userDto = _mapper.Map<UserDto>(user);
        var userSummaryDto = _mapper.Map<UserSummaryDto>(user);

        // Assert
        userDto.FirstName.Should().Be(string.Empty);
        userDto.LastName.Should().Be(string.Empty);
        userSummaryDto.FullName.Should().Be("");
    }

    [Fact]
    public async Task Map_ListOfUsers_ShouldMapCorrectlyAsync()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var users = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .ToListAsync();

        // Act
        var userDtos = _mapper.Map<List<UserDto>>(users);
        var userSummaryDtos = _mapper.Map<List<UserSummaryDto>>(users);

        // Assert
        userDtos.Should().HaveCount(users.Count);
        userSummaryDtos.Should().HaveCount(users.Count);
        
        for (int i = 0; i < users.Count; i++)
        {
            userDtos[i].Id.Should().Be(users[i].Id);
            userSummaryDtos[i].Id.Should().Be(users[i].Id);
        }
    }

    [Fact]
    public void Map_UserWithAllAdFields_ShouldMapCorrectly()
    {
        // Arrange
        var user = User.Create(
            username: "aduser",
            firstName: "AD",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: "CN=AD User,OU=Users,DC=test,DC=com",
            createdByUserId: "system"
        );

        // Mock role for mapping
        var role = new Role 
        { 
            Id = 1, 
            RoleName = UserRole.DevAdmin,
            Description = "Administrator" 
        };
        
        var roleProperty = typeof(User).GetProperty("Role");
        roleProperty?.SetValue(user, role);

        // Act
        var userDto = _mapper.Map<UserDto>(user);

        // Assert
        userDto.AdObjectGuid.Should().Be(user.AdObjectGuid);
        userDto.AdDistinguishedName.Should().Be(user.AdDistinguishedName);
    }

    [Fact]
    public async Task Map_UserWithAuditFields_ShouldMapCorrectlyAsync()
    {
        // Arrange
        await SeedTestDataAsync();

        var user = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .FirstAsync();

        // Act
        var userDto = _mapper.Map<UserDto>(user);

        // Assert
        userDto.CreatedAt.Should().Be(user.CreatedAt);
        userDto.UpdatedAt.Should().Be(user.UpdatedAt);
        userDto.RecordVersion.Should().Be(user.RecordVersion);
        userDto.CreatedByUserId.Should().Be(user.CreatedByUserId);
        userDto.UpdatedByUserId.Should().Be(user.UpdatedByUserId);
    }

    [Theory]
    [InlineData(UserRole.DevAdmin)]
    [InlineData(UserRole.SystemManager)]
    [InlineData(UserRole.ProcessOwner)]
    [InlineData(UserRole.Auditor)]
    public void Map_UserWithDifferentRoles_ShouldMapCorrectly(UserRole roleType)
    {
        // Arrange
        var user = User.Create(
            username: $"user_{roleType}",
            firstName: "Test",
            lastName: "User",
            email: $"user_{roleType}@test.com",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        var role = new Role 
        { 
            Id = 1, 
            RoleName = roleType, 
            Description = roleType.ToString() 
        };
        
        var roleProperty = typeof(User).GetProperty("Role");
        roleProperty?.SetValue(user, role);

        // Act
        var userDto = _mapper.Map<UserDto>(user);
        var userSummaryDto = _mapper.Map<UserSummaryDto>(user);

        // Assert
        userDto.Role.Should().Be(roleType);
        userSummaryDto.Role.Should().Be(roleType);
    }

    [Fact]
    public void Map_NullUser_ShouldReturnNull()
    {
        // Arrange
        User? user = null;

        // Act
        var userDto = _mapper.Map<UserDto>(user);

        // Assert
        userDto.Should().BeNull();
    }

    [Fact]
    public void Map_EmptyUserList_ShouldReturnEmptyList()
    {
        // Arrange
        var users = new List<User>();

        // Act
        var userDtos = _mapper.Map<List<UserDto>>(users);

        // Assert
        userDtos.Should().NotBeNull();
        userDtos.Should().BeEmpty();
    }
}
