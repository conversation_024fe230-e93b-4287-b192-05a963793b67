﻿@page "/"
@attribute [Authorize]
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components.Audit
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@inject IAuditApiService AuditService
@inject NavigationManager Navigation
@inject ILogger<Home> Logger

<PageTitle>Audit Dashboard - HWS Audit Platform PWA</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-3">
                <i class="fas fa-clipboard-check me-2 text-primary"></i>
                Audit Dashboard
            </h1>
            <p class="text-muted">Manage and execute your assigned audits</p>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-6 col-md-3 mb-3">
            <div class="stat-card stat-card-primary">
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">@assignedAuditsCount</div>
                    <div class="stat-label">Assigned Audits</div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-3 mb-3">
            <div class="stat-card stat-card-warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">@pendingAuditsCount</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-3 mb-3">
            <div class="stat-card stat-card-info">
                <div class="stat-icon">
                    <i class="fas fa-play"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">@inProgressAuditsCount</div>
                    <div class="stat-label">In Progress</div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-3 mb-3">
            <div class="stat-card stat-card-success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">@completedAuditsCount</div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="quick-actions-card">
                <div class="quick-actions-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="quick-actions-grid">
                    <button class="btn btn-primary action-btn" @onclick="NavigateToAssignedAudits">
                        <i class="fas fa-list"></i>
                        <span>View Audits</span>
                    </button>
                    <button class="btn btn-secondary action-btn" @onclick="StartNewAudit">
                        <i class="fas fa-plus"></i>
                        <span>Start Audit</span>
                    </button>
                    <button class="btn btn-secondary action-btn" @onclick="SyncData">
                        <i class="fas fa-sync"></i>
                        <span>Sync Data</span>
                    </button>
                    <button class="btn btn-secondary action-btn" @onclick="ViewOfflineAudits">
                        <i class="fas fa-wifi-slash"></i>
                        <span>Offline Audits</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="activity-card">
                <div class="activity-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Activity
                    </h5>
                </div>
                <div class="activity-content">
                    @if (isLoading)
                    {
                        <div class="loading-container">
                            <LoadingSpinner Message="Loading recent activity..." />
                        </div>
                    }
                    else if (recentAudits.Any())
                    {
                        <div class="audit-grid">
                            @foreach (var audit in recentAudits.Take(6))
                            {
                                <div class="audit-item">
                                    <AuditCard AuditSummary="@ConvertToAuditSummary(audit)"
                                              ShowActions="true"
                                              OnCardClicked="@HandleAuditCardClick"
                                              OnStartClicked="@HandleStartAudit"
                                              OnContinueClicked="@HandleContinueAudit"
                                              OnViewClicked="@HandleViewAudit" />
                                </div>
                            }
                        </div>

                        @if (recentAudits.Count > 6)
                        {
                            <div class="view-all-container">
                                <button class="btn btn-secondary" @onclick="NavigateToAssignedAudits">
                                    <i class="fas fa-list me-2"></i>
                                    View All Audits (@recentAudits.Count)
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="empty-state-container">
                            <EmptyState Title="No Recent Activity"
                                       Description="You don't have any recent audit activity. Check back later or refresh to see new assignments."
                                       IconClass="fas fa-history fa-3x text-muted"
                                       ActionText="Refresh"
                                       ActionIcon="fas fa-sync-alt"
                                       OnActionClick="@LoadDashboardData" />
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<Audit> recentAudits = new();
    private bool isLoading = true;
    private int assignedAuditsCount = 0;
    private int pendingAuditsCount = 0;
    private int inProgressAuditsCount = 0;
    private int completedAuditsCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await AuditService.GetAssignedAuditsAsync();
            if (result.IsSuccess && result.Data != null)
            {
                recentAudits = result.Data;

                // Calculate stats
                assignedAuditsCount = recentAudits.Count;
                pendingAuditsCount = recentAudits.Count(a => a.OverallStatus == AuditOverallStatus.Scheduled);
                inProgressAuditsCount = recentAudits.Count(a => a.OverallStatus == AuditOverallStatus.InProgress);
                completedAuditsCount = recentAudits.Count(a => a.OverallStatus == AuditOverallStatus.Closed);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading dashboard data");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "warning",
            AuditOverallStatus.InProgress => "info",
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }

    private void NavigateToAssignedAudits()
    {
        Navigation.NavigateTo("/audits");
    }

    private void StartNewAudit()
    {
        // Navigate to audit list where users can start new audits
        Navigation.NavigateTo("/audits");
    }

    private async Task SyncData()
    {
        try
        {
            // TODO: Implement comprehensive sync functionality
            // For now, just refresh the page data
            await LoadDashboardData();
            Logger.LogInformation("Data sync completed");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during data sync");
        }
    }

    private void ViewOfflineAudits()
    {
        Navigation.NavigateTo("/offline");
    }

    private AuditSummaryDto ConvertToAuditSummary(Audit audit)
    {
        return new AuditSummaryDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignedToUserName = audit.AssignedToUser?.FullName,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory?.FactoryName,
            AreaName = audit.Area?.AreaName,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = (double?)audit.OverallScore,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now &&
                       audit.OverallStatus != AuditOverallStatus.Closed,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            TotalQuestions = audit.Answers?.Count ?? 0,
            AnsweredQuestions = audit.Answers?.Count(a => !string.IsNullOrEmpty(a.AnswerText) ||
                                                              a.AnswerBoolean.HasValue ||
                                                              a.AnswerNumeric.HasValue ||
                                                              a.AnswerDate.HasValue) ?? 0
        };
    }

    private async Task HandleAuditCardClick(AuditSummaryDto audit)
    {
        // Navigate to audit details or execution based on status
        if (audit.OverallStatus == AuditOverallStatus.Scheduled)
        {
            await HandleStartAudit(audit);
        }
        else if (audit.OverallStatus == AuditOverallStatus.InProgress)
        {
            await HandleContinueAudit(audit);
        }
        else
        {
            await HandleViewAudit(audit);
        }
    }

    private async Task HandleStartAudit(AuditSummaryDto audit)
    {
        try
        {
            var result = await AuditService.StartAuditAsync(audit.Id);
            if (result.IsSuccess)
            {
                // Navigate to audit execution
                Navigation.NavigateTo($"/audits/{audit.Id}/execute");
                Logger.LogInformation("Started audit: {AuditId}", audit.Id);
            }
            else
            {
                Logger.LogWarning("Failed to start audit: {AuditId}, Error: {Error}", audit.Id, result.ErrorMessage);
                // TODO: Show error message to user
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting audit: {AuditId}", audit.Id);
            // TODO: Show error message to user
        }
    }

    private async Task HandleContinueAudit(AuditSummaryDto audit)
    {
        // Navigate to audit execution
        Navigation.NavigateTo($"/audits/{audit.Id}/execute");
        Logger.LogInformation("Continuing audit: {AuditId}", audit.Id);
        await Task.CompletedTask;
    }

    private async Task HandleViewAudit(AuditSummaryDto audit)
    {
        // Navigate to audit view/summary page
        Navigation.NavigateTo($"/audits/{audit.Id}/summary");
        Logger.LogInformation("Viewing audit: {AuditId}", audit.Id);
        await Task.CompletedTask;
    }
}

<style>
    /* Enhanced Home Page Styles */

    /* Stat Cards */
    .stat-card {
        background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
        border: 1px solid var(--industrial-border-light);
        border-radius: 12px;
        padding: 1.25rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 80px;
        box-shadow: 0 4px 12px var(--industrial-shadow);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--industrial-teal);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px var(--industrial-shadow-heavy);
        border-color: var(--industrial-teal);
    }

    .stat-card-primary::before {
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }

    .stat-card-warning::before {
        background: linear-gradient(90deg, var(--industrial-orange) 0%, #f59e0b 100%);
    }

    .stat-card-info::before {
        background: linear-gradient(90deg, var(--industrial-blue) 0%, #3b82f6 100%);
    }

    .stat-card-success::before {
        background: linear-gradient(90deg, var(--industrial-green) 0%, #10b981 100%);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
    }

    .stat-card-primary .stat-icon {
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
    }

    .stat-card-warning .stat-icon {
        background: linear-gradient(135deg, var(--industrial-orange) 0%, #d97706 100%);
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }

    .stat-card-info .stat-icon {
        background: linear-gradient(135deg, var(--industrial-blue) 0%, #1e40af 100%);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .stat-card-success .stat-icon {
        background: linear-gradient(135deg, var(--industrial-green) 0%, #047857 100%);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .stat-content {
        flex: 1;
        min-width: 0;
    }

    .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--industrial-text-light);
        line-height: 1;
        margin-bottom: 0.25rem;
        font-family: 'Roboto Condensed', sans-serif;
    }

    .stat-label {
        font-size: 0.85rem;
        color: var(--industrial-text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    /* Quick Actions Card */
    .quick-actions-card {
        background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
        border: 1px solid var(--industrial-border-light);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px var(--industrial-shadow);
        position: relative;
    }

    .quick-actions-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }

    .quick-actions-header {
        padding: 1.25rem 1.5rem;
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 1px solid var(--industrial-border);
    }

    .quick-actions-header h5 {
        color: var(--industrial-text-light);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        margin: 0;
    }

    .quick-actions-grid {
        padding: 1.5rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1.25rem 1rem;
        min-height: 80px;
        border-radius: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-btn i {
        font-size: 1.5rem;
    }

    .action-btn span {
        font-size: 0.85rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px var(--industrial-shadow-heavy);
    }

    /* Activity Card */
    .activity-card {
        background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
        border: 1px solid var(--industrial-border-light);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px var(--industrial-shadow);
        position: relative;
    }

    .activity-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }

    .activity-header {
        padding: 1.25rem 1.5rem;
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 1px solid var(--industrial-border);
    }

    .activity-header h5 {
        color: var(--industrial-text-light);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        margin: 0;
    }

    .activity-content {
        padding: 1.5rem;
    }

    .audit-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .audit-item {
        /* Individual audit card styling will be handled by the AuditCard component */
    }

    .loading-container,
    .empty-state-container {
        padding: 2rem;
        text-align: center;
    }

    .view-all-container {
        text-align: center;
        padding-top: 1rem;
        border-top: 1px solid var(--industrial-border);
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .stat-card {
            padding: 1rem;
            min-height: 70px;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            font-size: 1.25rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .quick-actions-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            padding: 1rem;
        }

        .action-btn {
            padding: 1rem 0.75rem;
            min-height: 70px;
        }

        .action-btn i {
            font-size: 1.25rem;
        }

        .action-btn span {
            font-size: 0.8rem;
        }

        .audit-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .activity-content,
        .quick-actions-header,
        .activity-header {
            padding: 1rem;
        }
    }

    @@media (min-width: 768px) and (max-width: 1024px) {
        .stat-card {
            padding: 1.5rem;
            min-height: 90px;
        }

        .stat-icon {
            width: 52px;
            height: 52px;
            font-size: 1.75rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .action-btn {
            padding: 1.5rem 1.25rem;
            min-height: 90px;
        }

        .action-btn i {
            font-size: 1.75rem;
        }

        .action-btn span {
            font-size: 0.9rem;
        }

        .audit-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
