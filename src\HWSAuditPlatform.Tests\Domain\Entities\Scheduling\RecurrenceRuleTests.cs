using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Scheduling;

public class RecurrenceRuleTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var rule = new RecurrenceRule();

        // Assert
        rule.Interval.Should().Be(1);
        rule.StartDate.Should().Be(default);
    }

    [Fact]
    public void IsValid_WithDailyFrequency_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.DAILY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithWeeklyFrequencyAndDaysMask_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            WeeklyDaysOfWeekMask = 10, // Monday (2) + Wednesday (8) = 10
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithWeeklyFrequencyWithoutDaysMask_ShouldReturnFalse()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            WeeklyDaysOfWeekMask = null,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithWeeklyFrequencyAndZeroDaysMask_ShouldReturnFalse()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            WeeklyDaysOfWeekMask = 0,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithMonthlyFrequencyAndDayOfMonth_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.MONTHLY,
            Interval = 1,
            MonthlyDayOfMonth = 15,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithMonthlyFrequencyAndNthWeek_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.MONTHLY,
            Interval = 1,
            MonthlyNthWeek = 2, // Second week
            MonthlyDayOfWeek = 2, // Monday
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithMonthlyFrequencyWithoutParameters_ShouldReturnFalse()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.MONTHLY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithYearlyFrequencyAndDayOfMonth_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.YEARLY,
            Interval = 1,
            YearlyMonth = 6, // June
            YearlyDayOfMonth = 15,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithYearlyFrequencyAndNthWeek_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.YEARLY,
            Interval = 1,
            YearlyMonth = 6, // June
            YearlyNthWeek = 3, // Third week
            YearlyDayOfWeek = 2, // Monday
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithYearlyFrequencyWithoutMonth_ShouldReturnFalse()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.YEARLY,
            Interval = 1,
            YearlyDayOfMonth = 15,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithCustomFrequency_ShouldReturnTrue()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.COSTUM,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        };

        // Act & Assert
        rule.IsValid().Should().BeTrue();
    }

    [Fact]
    public void GetDescription_WithDailyFrequency_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.DAILY,
            Interval = 1
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every day(s)");
    }

    [Fact]
    public void GetDescription_WithDailyFrequencyAndInterval_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.DAILY,
            Interval = 3
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every 3 day(s)");
    }

    [Fact]
    public void GetDescription_WithWeeklyFrequency_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            WeeklyDaysOfWeekMask = 10 // Monday (2) + Wednesday (8) = 10
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every week(s) on Monday, Wednesday");
    }

    [Fact]
    public void GetDescription_WithMonthlyFrequencyAndDayOfMonth_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.MONTHLY,
            Interval = 2,
            MonthlyDayOfMonth = 15
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every 2 month(s) on day 15");
    }

    [Fact]
    public void GetDescription_WithMonthlyFrequencyAndNthWeek_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.MONTHLY,
            Interval = 1,
            MonthlyNthWeek = 2,
            MonthlyDayOfWeek = 2 // Monday
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every month(s) on the second Monday");
    }

    [Fact]
    public void GetDescription_WithYearlyFrequencyAndDayOfMonth_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.YEARLY,
            Interval = 1,
            YearlyMonth = 6,
            YearlyDayOfMonth = 15
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every year(s) on June 15");
    }

    [Fact]
    public void GetDescription_WithYearlyFrequencyAndNthWeek_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.YEARLY,
            Interval = 1,
            YearlyMonth = 11,
            YearlyNthWeek = 4,
            YearlyDayOfWeek = 5 // Thursday
        };

        // Act & Assert
        rule.GetDescription().Should().Be("every year(s) on the fourth Thursday of November");
    }

    [Fact]
    public void GetDescription_WithCustomFrequency_ShouldReturnCorrectDescription()
    {
        // Arrange
        var rule = new RecurrenceRule
        {
            FrequencyType = FrequencyType.COSTUM,
            Interval = 1
        };

        // Act & Assert
        rule.GetDescription().Should().Be("custom pattern");
    }

    [Fact]
    public void RecurrenceRule_InheritsFromBaseEntity()
    {
        // Arrange & Act
        var rule = new RecurrenceRule();

        // Assert
        rule.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        rule.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void EndDate_CanBeSetAndRetrieved()
    {
        // Arrange
        var rule = new RecurrenceRule();
        var endDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(1));

        // Act
        rule.EndDate = endDate;

        // Assert
        rule.EndDate.Should().Be(endDate);
    }
}
