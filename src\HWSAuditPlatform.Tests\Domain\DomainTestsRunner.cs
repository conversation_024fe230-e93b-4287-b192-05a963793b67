using FluentAssertions;

namespace HWSAuditPlatform.Tests.Domain;

/// <summary>
/// Test class to verify that all domain tests can be discovered and run
/// </summary>
public class DomainTestsRunner
{
    [Fact]
    public void DomainTests_ShouldBeDiscoverable()
    {
        // This test ensures that the test project is properly configured
        // and can discover domain tests
        
        // Arrange & Act
        var testAssembly = typeof(DomainTestsRunner).Assembly;
        
        // Assert
        testAssembly.Should().NotBeNull();
        testAssembly.GetName().Name.Should().Be("HWSAuditPlatform.Tests");
    }

    [Fact]
    public void DomainProject_ShouldBeReferenced()
    {
        // This test verifies that the domain project is properly referenced
        
        // Arrange & Act
        var domainAssembly = typeof(HWSAuditPlatform.Domain.Common.BaseEntity).Assembly;
        
        // Assert
        domainAssembly.Should().NotBeNull();
        domainAssembly.GetName().Name.Should().Be("HWSAuditPlatform.Domain");
    }

    [Fact]
    public void FluentAssertions_ShouldBeAvailable()
    {
        // This test verifies that FluentAssertions is properly configured
        
        // Arrange
        var testValue = "test";
        
        // Act & Assert
        testValue.Should().Be("test");
        testValue.Should().NotBeNull();
        testValue.Should().HaveLength(4);
    }

    [Theory]
    [InlineData("Common")]
    [InlineData("Entities")]
    [InlineData("ValueObjects")]
    [InlineData("Events")]
    [InlineData("Exceptions")]
    public void DomainTestCategories_ShouldExist(string category)
    {
        // This test documents the test categories we have
        // and ensures they are properly organized
        
        // Arrange & Act
        var testNamespace = $"HWSAuditPlatform.Tests.Domain.{category}";
        
        // Assert
        testNamespace.Should().NotBeNullOrEmpty();
        testNamespace.Should().StartWith("HWSAuditPlatform.Tests.Domain");
    }
}
