﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateToSecondaryPrefixes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdGroupRoleMappings_Roles_RoleId",
                table: "AdGroupRoleMappings");

            migrationBuilder.DropForeignKey(
                name: "FK_Areas_Factories_FactoryId",
                table: "Areas");

            migrationBuilder.DropForeignKey(
                name: "FK_Areas_Users_CreatedByUserId",
                table: "Areas");

            migrationBuilder.DropForeignKey(
                name: "FK_Areas_Users_UpdatedByUserId",
                table: "Areas");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswerFailureReasons_AuditAnswers_AuditAnswerId",
                table: "AuditAnswerFailureReasons");

            migrationBuilder.DropForeign<PERSON>ey(
                name: "FK_AuditAnswers_Audits_AuditId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_hwsap_question_options_SelectedOptionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswers_hwsap_questions_QuestionId",
                table: "AuditAnswers");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswerSelectedOptions_AuditAnswers_AuditAnswerId",
                table: "AuditAnswerSelectedOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAnswerSelectedOptions_hwsap_question_options_QuestionOptionId",
                table: "AuditAnswerSelectedOptions");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAttachments_AuditAnswers_AuditAnswerId",
                table: "AuditAttachments");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditAttachments_Users_UploadedByUserId",
                table: "AuditAttachments");

            migrationBuilder.DropForeignKey(
                name: "FK_AuditLogs_Users_UserId",
                table: "AuditLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_Areas_AreaId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_Factories_FactoryId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_SubAreas_SubAreaId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_UserGroups_AssignedToUserGroupId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_Users_AssignedToUserId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_Users_ReviewedByUserId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_hwsap_audit_templates_AuditTemplateId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Audits_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "Audits");

            migrationBuilder.DropForeignKey(
                name: "FK_Factories_Location_LocationId",
                table: "Factories");

            migrationBuilder.DropForeignKey(
                name: "FK_Factories_Users_CreatedByUserId",
                table: "Factories");

            migrationBuilder.DropForeignKey(
                name: "FK_Factories_Users_UpdatedByUserId",
                table: "Factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId1",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_RequestedByUserId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_ReviewedByUserId",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_corrective_actions_Users_AssignedToUserId",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_corrective_actions_hwsap_findings_FindingId",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_findings_AuditAnswers_AuditAnswerId",
                table: "hwsap_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_findings_Users_ReportedByUserId",
                table: "hwsap_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_question_groups_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_question_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_question_options_hwsap_questions_QuestionId",
                table: "hwsap_question_options");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_question_groups_QuestionGroupId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_questions_hwsap_questions_ParentQuestionId",
                table: "hwsap_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurrence_rules_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_recurrence_rules");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_UserGroups_AssignToUserGroupId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_Users_AssignToUserId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_recurring_audit_settings_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_SubAreas_Areas_AreaId",
                table: "SubAreas");

            migrationBuilder.DropForeignKey(
                name: "FK_SubAreas_Users_CreatedByUserId",
                table: "SubAreas");

            migrationBuilder.DropForeignKey(
                name: "FK_SubAreas_Users_UpdatedByUserId",
                table: "SubAreas");

            migrationBuilder.DropForeignKey(
                name: "FK_UserGroupMembers_UserGroups_UserGroupId",
                table: "UserGroupMembers");

            migrationBuilder.DropForeignKey(
                name: "FK_UserGroupMembers_Users_UserId",
                table: "UserGroupMembers");

            migrationBuilder.DropForeignKey(
                name: "FK_UserGroups_Users_CreatedByUserId",
                table: "UserGroups");

            migrationBuilder.DropForeignKey(
                name: "FK_UserGroups_Users_UpdatedByUserId",
                table: "UserGroups");

            migrationBuilder.DropForeignKey(
                name: "FK_Users_Factories_FactoryId",
                table: "Users");

            migrationBuilder.DropForeignKey(
                name: "FK_Users_Roles_RoleId",
                table: "Users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Users",
                table: "Users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UserGroups",
                table: "UserGroups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UserGroupMembers",
                table: "UserGroupMembers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SubAreas",
                table: "SubAreas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Roles",
                table: "Roles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Location",
                table: "Location");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_recurring_audit_settings",
                table: "hwsap_recurring_audit_settings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_recurrence_rules",
                table: "hwsap_recurrence_rules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_questions",
                table: "hwsap_questions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_question_options",
                table: "hwsap_question_options");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_question_groups",
                table: "hwsap_question_groups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_findings",
                table: "hwsap_findings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_corrective_actions",
                table: "hwsap_corrective_actions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_audit_templates",
                table: "hwsap_audit_templates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_audit_correction_requests",
                table: "hwsap_audit_correction_requests");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Factories",
                table: "Factories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Audits",
                table: "Audits");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditLogs",
                table: "AuditLogs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditAttachments",
                table: "AuditAttachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditAnswerSelectedOptions",
                table: "AuditAnswerSelectedOptions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditAnswers",
                table: "AuditAnswers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AuditAnswerFailureReasons",
                table: "AuditAnswerFailureReasons");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Areas",
                table: "Areas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AdGroupRoleMappings",
                table: "AdGroupRoleMappings");

            migrationBuilder.RenameTable(
                name: "Users",
                newName: "hwsap_usr_users");

            migrationBuilder.RenameTable(
                name: "UserGroups",
                newName: "hwsap_usr_groups");

            migrationBuilder.RenameTable(
                name: "UserGroupMembers",
                newName: "hwsap_usr_group_members");

            migrationBuilder.RenameTable(
                name: "SubAreas",
                newName: "hwsap_org_sub_areas");

            migrationBuilder.RenameTable(
                name: "Roles",
                newName: "hwsap_usr_roles");

            migrationBuilder.RenameTable(
                name: "Location",
                newName: "hwsap_org_locations");

            migrationBuilder.RenameTable(
                name: "hwsap_recurring_audit_settings",
                newName: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.RenameTable(
                name: "hwsap_recurrence_rules",
                newName: "hwsap_sch_recurrence_rules");

            migrationBuilder.RenameTable(
                name: "hwsap_questions",
                newName: "hwsap_tpl_questions");

            migrationBuilder.RenameTable(
                name: "hwsap_question_options",
                newName: "hwsap_tpl_question_options");

            migrationBuilder.RenameTable(
                name: "hwsap_question_groups",
                newName: "hwsap_tpl_question_groups");

            migrationBuilder.RenameTable(
                name: "hwsap_findings",
                newName: "hwsap_fnd_findings");

            migrationBuilder.RenameTable(
                name: "hwsap_corrective_actions",
                newName: "hwsap_fnd_corrective_actions");

            migrationBuilder.RenameTable(
                name: "hwsap_audit_templates",
                newName: "hwsap_tpl_audit_templates");

            migrationBuilder.RenameTable(
                name: "hwsap_audit_correction_requests",
                newName: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.RenameTable(
                name: "Factories",
                newName: "hwsap_org_factories");

            migrationBuilder.RenameTable(
                name: "Audits",
                newName: "hwsap_aud_audits");

            migrationBuilder.RenameTable(
                name: "AuditLogs",
                newName: "hwsap_wfl_audit_logs");

            migrationBuilder.RenameTable(
                name: "AuditAttachments",
                newName: "hwsap_aud_attachments");

            migrationBuilder.RenameTable(
                name: "AuditAnswerSelectedOptions",
                newName: "hwsap_aud_answer_selected_options");

            migrationBuilder.RenameTable(
                name: "AuditAnswers",
                newName: "hwsap_aud_answers");

            migrationBuilder.RenameTable(
                name: "AuditAnswerFailureReasons",
                newName: "hwsap_aud_answer_failure_reasons");

            migrationBuilder.RenameTable(
                name: "Areas",
                newName: "hwsap_org_areas");

            migrationBuilder.RenameTable(
                name: "AdGroupRoleMappings",
                newName: "hwsap_usr_ad_group_role_mappings");

            migrationBuilder.RenameIndex(
                name: "IX_UserGroups_UpdatedByUserId",
                table: "hwsap_usr_groups",
                newName: "IX_hwsap_usr_groups_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_UserGroups_CreatedByUserId",
                table: "hwsap_usr_groups",
                newName: "IX_hwsap_usr_groups_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_SubAreas_UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                newName: "IX_hwsap_org_sub_areas_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_SubAreas_CreatedByUserId",
                table: "hwsap_org_sub_areas",
                newName: "IX_hwsap_org_sub_areas_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_audit_correction_requests_AuditId1",
                table: "hwsap_wfl_audit_correction_requests",
                newName: "IX_hwsap_wfl_audit_correction_requests_AuditId1");

            migrationBuilder.RenameIndex(
                name: "IX_Factories_UpdatedByUserId",
                table: "hwsap_org_factories",
                newName: "IX_hwsap_org_factories_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Factories_CreatedByUserId",
                table: "hwsap_org_factories",
                newName: "IX_hwsap_org_factories_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Audits_SubAreaId",
                table: "hwsap_aud_audits",
                newName: "IX_hwsap_aud_audits_SubAreaId");

            migrationBuilder.RenameIndex(
                name: "IX_Audits_ReviewedByUserId",
                table: "hwsap_aud_audits",
                newName: "IX_hwsap_aud_audits_ReviewedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Audits_RecurringAuditSettingId",
                table: "hwsap_aud_audits",
                newName: "IX_hwsap_aud_audits_RecurringAuditSettingId");

            migrationBuilder.RenameIndex(
                name: "IX_AuditAttachments_UploadedByUserId",
                table: "hwsap_aud_attachments",
                newName: "IX_hwsap_aud_attachments_UploadedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_AuditAnswers_SelectedOptionId",
                table: "hwsap_aud_answers",
                newName: "IX_hwsap_aud_answers_SelectedOptionId");

            migrationBuilder.RenameIndex(
                name: "IX_Areas_UpdatedByUserId",
                table: "hwsap_org_areas",
                newName: "IX_hwsap_org_areas_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Areas_CreatedByUserId",
                table: "hwsap_org_areas",
                newName: "IX_hwsap_org_areas_CreatedByUserId");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "hwsap_wfl_audit_logs",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<DateTime>(
                name: "ServerReceivedAt",
                table: "hwsap_wfl_audit_logs",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "hwsap_wfl_audit_logs",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "hwsap_usr_ad_group_role_mappings",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "hwsap_usr_ad_group_role_mappings",
                type: "bit",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "hwsap_usr_ad_group_role_mappings",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETUTCDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_usr_users",
                table: "hwsap_usr_users",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_usr_groups",
                table: "hwsap_usr_groups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_usr_group_members",
                table: "hwsap_usr_group_members",
                columns: new[] { "UserGroupId", "UserId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_org_sub_areas",
                table: "hwsap_org_sub_areas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_usr_roles",
                table: "hwsap_usr_roles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_org_locations",
                table: "hwsap_org_locations",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_sch_recurring_audit_settings",
                table: "hwsap_sch_recurring_audit_settings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_sch_recurrence_rules",
                table: "hwsap_sch_recurrence_rules",
                column: "RecurringAuditSettingId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_tpl_questions",
                table: "hwsap_tpl_questions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_tpl_question_options",
                table: "hwsap_tpl_question_options",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_tpl_question_groups",
                table: "hwsap_tpl_question_groups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_fnd_findings",
                table: "hwsap_fnd_findings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_fnd_corrective_actions",
                table: "hwsap_fnd_corrective_actions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_tpl_audit_templates",
                table: "hwsap_tpl_audit_templates",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_wfl_audit_correction_requests",
                table: "hwsap_wfl_audit_correction_requests",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_org_factories",
                table: "hwsap_org_factories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_aud_audits",
                table: "hwsap_aud_audits",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_wfl_audit_logs",
                table: "hwsap_wfl_audit_logs",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_aud_attachments",
                table: "hwsap_aud_attachments",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_aud_answer_selected_options",
                table: "hwsap_aud_answer_selected_options",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_aud_answers",
                table: "hwsap_aud_answers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_aud_answer_failure_reasons",
                table: "hwsap_aud_answer_failure_reasons",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_org_areas",
                table: "hwsap_org_areas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_usr_ad_group_role_mappings",
                table: "hwsap_usr_ad_group_role_mappings",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityId",
                table: "hwsap_wfl_audit_logs",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityType",
                table: "hwsap_wfl_audit_logs",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityType_EntityId",
                table: "hwsap_wfl_audit_logs",
                columns: new[] { "EntityType", "EntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EventTimestamp",
                table: "hwsap_wfl_audit_logs",
                column: "EventTimestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AdGroupRoleMappings_AdGroupName",
                table: "hwsap_usr_ad_group_role_mappings",
                column: "AdGroupName",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answer_failure_reasons_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_answer_failure_reasons",
                column: "AuditAnswerId",
                principalTable: "hwsap_aud_answers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answer_selected_options_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_answer_selected_options",
                column: "AuditAnswerId",
                principalTable: "hwsap_aud_answers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answer_selected_options_hwsap_tpl_question_options_QuestionOptionId",
                table: "hwsap_aud_answer_selected_options",
                column: "QuestionOptionId",
                principalTable: "hwsap_tpl_question_options",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_aud_audits_AuditId",
                table: "hwsap_aud_answers",
                column: "AuditId",
                principalTable: "hwsap_aud_audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_tpl_question_options_SelectedOptionId",
                table: "hwsap_aud_answers",
                column: "SelectedOptionId",
                principalTable: "hwsap_tpl_question_options",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_tpl_questions_QuestionId",
                table: "hwsap_aud_answers",
                column: "QuestionId",
                principalTable: "hwsap_tpl_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_attachments_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_attachments",
                column: "AuditAnswerId",
                principalTable: "hwsap_aud_answers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_attachments_hwsap_usr_users_UploadedByUserId",
                table: "hwsap_aud_attachments",
                column: "UploadedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_areas_AreaId",
                table: "hwsap_aud_audits",
                column: "AreaId",
                principalTable: "hwsap_org_areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_factories_FactoryId",
                table: "hwsap_aud_audits",
                column: "FactoryId",
                principalTable: "hwsap_org_factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_sub_areas_SubAreaId",
                table: "hwsap_aud_audits",
                column: "SubAreaId",
                principalTable: "hwsap_org_sub_areas",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_sch_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_aud_audits",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_sch_recurring_audit_settings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_aud_audits",
                column: "AuditTemplateId",
                principalTable: "hwsap_tpl_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_groups_AssignedToUserGroupId",
                table: "hwsap_aud_audits",
                column: "AssignedToUserGroupId",
                principalTable: "hwsap_usr_groups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_fnd_findings_FindingId",
                table: "hwsap_fnd_corrective_actions",
                column: "FindingId",
                principalTable: "hwsap_fnd_findings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions",
                column: "AssignedToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_fnd_findings",
                column: "AuditAnswerId",
                principalTable: "hwsap_aud_answers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings",
                column: "ReportedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_org_factories_FactoryId",
                table: "hwsap_org_areas",
                column: "FactoryId",
                principalTable: "hwsap_org_factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_org_locations_LocationId",
                table: "hwsap_org_factories",
                column: "LocationId",
                principalTable: "hwsap_org_locations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_org_areas_AreaId",
                table: "hwsap_org_sub_areas",
                column: "AreaId",
                principalTable: "hwsap_org_areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurrence_rules_hwsap_sch_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_sch_recurrence_rules",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_sch_recurring_audit_settings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AuditTemplateId",
                principalTable: "hwsap_tpl_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_groups_AssignToUserGroupId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AssignToUserGroupId",
                principalTable: "hwsap_usr_groups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AssignToUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_tpl_question_groups_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_tpl_question_groups",
                column: "AuditTemplateId",
                principalTable: "hwsap_tpl_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_tpl_question_options_hwsap_tpl_questions_QuestionId",
                table: "hwsap_tpl_question_options",
                column: "QuestionId",
                principalTable: "hwsap_tpl_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_tpl_questions",
                column: "AuditTemplateId",
                principalTable: "hwsap_tpl_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_question_groups_QuestionGroupId",
                table: "hwsap_tpl_questions",
                column: "QuestionGroupId",
                principalTable: "hwsap_tpl_question_groups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_questions_ParentQuestionId",
                table: "hwsap_tpl_questions",
                column: "ParentQuestionId",
                principalTable: "hwsap_tpl_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_ad_group_role_mappings_hwsap_usr_roles_RoleId",
                table: "hwsap_usr_ad_group_role_mappings",
                column: "RoleId",
                principalTable: "hwsap_usr_roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_group_members_hwsap_usr_groups_UserGroupId",
                table: "hwsap_usr_group_members",
                column: "UserGroupId",
                principalTable: "hwsap_usr_groups",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_group_members_hwsap_usr_users_UserId",
                table: "hwsap_usr_group_members",
                column: "UserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups",
                column: "CreatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups",
                column: "UpdatedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_users_hwsap_org_factories_FactoryId",
                table: "hwsap_usr_users",
                column: "FactoryId",
                principalTable: "hwsap_org_factories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_usr_users_hwsap_usr_roles_RoleId",
                table: "hwsap_usr_users",
                column: "RoleId",
                principalTable: "hwsap_usr_roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "AuditId",
                principalTable: "hwsap_aud_audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId1",
                table: "hwsap_wfl_audit_correction_requests",
                column: "AuditId1",
                principalTable: "hwsap_aud_audits",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "RequestedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests",
                column: "ReviewedByUserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs",
                column: "UserId",
                principalTable: "hwsap_usr_users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answer_failure_reasons_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_answer_failure_reasons");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answer_selected_options_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_answer_selected_options");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answer_selected_options_hwsap_tpl_question_options_QuestionOptionId",
                table: "hwsap_aud_answer_selected_options");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_aud_audits_AuditId",
                table: "hwsap_aud_answers");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_tpl_question_options_SelectedOptionId",
                table: "hwsap_aud_answers");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_answers_hwsap_tpl_questions_QuestionId",
                table: "hwsap_aud_answers");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_attachments_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_aud_attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_attachments_hwsap_usr_users_UploadedByUserId",
                table: "hwsap_aud_attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_areas_AreaId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_factories_FactoryId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_org_sub_areas_SubAreaId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_sch_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_groups_AssignedToUserGroupId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_aud_audits_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_aud_audits");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_fnd_findings_FindingId",
                table: "hwsap_fnd_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_corrective_actions_hwsap_usr_users_AssignedToUserId",
                table: "hwsap_fnd_corrective_actions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_aud_answers_AuditAnswerId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_fnd_findings_hwsap_usr_users_ReportedByUserId",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_org_factories_FactoryId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_org_locations_LocationId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_factories_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_factories");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_org_areas_AreaId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_org_sub_areas_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurrence_rules_hwsap_sch_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_sch_recurrence_rules");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_groups_AssignToUserGroupId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_usr_users_AssignToUserId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_tpl_question_groups_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_tpl_question_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_tpl_question_options_hwsap_tpl_questions_QuestionId",
                table: "hwsap_tpl_question_options");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_audit_templates_AuditTemplateId",
                table: "hwsap_tpl_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_question_groups_QuestionGroupId",
                table: "hwsap_tpl_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_tpl_questions_hwsap_tpl_questions_ParentQuestionId",
                table: "hwsap_tpl_questions");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_ad_group_role_mappings_hwsap_usr_roles_RoleId",
                table: "hwsap_usr_ad_group_role_mappings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_group_members_hwsap_usr_groups_UserGroupId",
                table: "hwsap_usr_group_members");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_group_members_hwsap_usr_users_UserId",
                table: "hwsap_usr_group_members");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_CreatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_groups_hwsap_usr_users_UpdatedByUserId",
                table: "hwsap_usr_groups");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_users_hwsap_org_factories_FactoryId",
                table: "hwsap_usr_users");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_usr_users_hwsap_usr_roles_RoleId",
                table: "hwsap_usr_users");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_aud_audits_AuditId1",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_RequestedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_correction_requests_hwsap_usr_users_ReviewedByUserId",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_wfl_audit_logs_hwsap_usr_users_UserId",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_wfl_audit_logs",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EntityId",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EntityType",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EntityType_EntityId",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EventTimestamp",
                table: "hwsap_wfl_audit_logs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_wfl_audit_correction_requests",
                table: "hwsap_wfl_audit_correction_requests");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_usr_users",
                table: "hwsap_usr_users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_usr_roles",
                table: "hwsap_usr_roles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_usr_groups",
                table: "hwsap_usr_groups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_usr_group_members",
                table: "hwsap_usr_group_members");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_usr_ad_group_role_mappings",
                table: "hwsap_usr_ad_group_role_mappings");

            migrationBuilder.DropIndex(
                name: "IX_AdGroupRoleMappings_AdGroupName",
                table: "hwsap_usr_ad_group_role_mappings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_tpl_questions",
                table: "hwsap_tpl_questions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_tpl_question_options",
                table: "hwsap_tpl_question_options");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_tpl_question_groups",
                table: "hwsap_tpl_question_groups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_tpl_audit_templates",
                table: "hwsap_tpl_audit_templates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_sch_recurring_audit_settings",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_sch_recurrence_rules",
                table: "hwsap_sch_recurrence_rules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_org_sub_areas",
                table: "hwsap_org_sub_areas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_org_locations",
                table: "hwsap_org_locations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_org_factories",
                table: "hwsap_org_factories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_org_areas",
                table: "hwsap_org_areas");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_fnd_findings",
                table: "hwsap_fnd_findings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_fnd_corrective_actions",
                table: "hwsap_fnd_corrective_actions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_aud_audits",
                table: "hwsap_aud_audits");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_aud_attachments",
                table: "hwsap_aud_attachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_aud_answers",
                table: "hwsap_aud_answers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_aud_answer_selected_options",
                table: "hwsap_aud_answer_selected_options");

            migrationBuilder.DropPrimaryKey(
                name: "PK_hwsap_aud_answer_failure_reasons",
                table: "hwsap_aud_answer_failure_reasons");

            migrationBuilder.RenameTable(
                name: "hwsap_wfl_audit_logs",
                newName: "AuditLogs");

            migrationBuilder.RenameTable(
                name: "hwsap_wfl_audit_correction_requests",
                newName: "hwsap_audit_correction_requests");

            migrationBuilder.RenameTable(
                name: "hwsap_usr_users",
                newName: "Users");

            migrationBuilder.RenameTable(
                name: "hwsap_usr_roles",
                newName: "Roles");

            migrationBuilder.RenameTable(
                name: "hwsap_usr_groups",
                newName: "UserGroups");

            migrationBuilder.RenameTable(
                name: "hwsap_usr_group_members",
                newName: "UserGroupMembers");

            migrationBuilder.RenameTable(
                name: "hwsap_usr_ad_group_role_mappings",
                newName: "AdGroupRoleMappings");

            migrationBuilder.RenameTable(
                name: "hwsap_tpl_questions",
                newName: "hwsap_questions");

            migrationBuilder.RenameTable(
                name: "hwsap_tpl_question_options",
                newName: "hwsap_question_options");

            migrationBuilder.RenameTable(
                name: "hwsap_tpl_question_groups",
                newName: "hwsap_question_groups");

            migrationBuilder.RenameTable(
                name: "hwsap_tpl_audit_templates",
                newName: "hwsap_audit_templates");

            migrationBuilder.RenameTable(
                name: "hwsap_sch_recurring_audit_settings",
                newName: "hwsap_recurring_audit_settings");

            migrationBuilder.RenameTable(
                name: "hwsap_sch_recurrence_rules",
                newName: "hwsap_recurrence_rules");

            migrationBuilder.RenameTable(
                name: "hwsap_org_sub_areas",
                newName: "SubAreas");

            migrationBuilder.RenameTable(
                name: "hwsap_org_locations",
                newName: "Location");

            migrationBuilder.RenameTable(
                name: "hwsap_org_factories",
                newName: "Factories");

            migrationBuilder.RenameTable(
                name: "hwsap_org_areas",
                newName: "Areas");

            migrationBuilder.RenameTable(
                name: "hwsap_fnd_findings",
                newName: "hwsap_findings");

            migrationBuilder.RenameTable(
                name: "hwsap_fnd_corrective_actions",
                newName: "hwsap_corrective_actions");

            migrationBuilder.RenameTable(
                name: "hwsap_aud_audits",
                newName: "Audits");

            migrationBuilder.RenameTable(
                name: "hwsap_aud_attachments",
                newName: "AuditAttachments");

            migrationBuilder.RenameTable(
                name: "hwsap_aud_answers",
                newName: "AuditAnswers");

            migrationBuilder.RenameTable(
                name: "hwsap_aud_answer_selected_options",
                newName: "AuditAnswerSelectedOptions");

            migrationBuilder.RenameTable(
                name: "hwsap_aud_answer_failure_reasons",
                newName: "AuditAnswerFailureReasons");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_wfl_audit_correction_requests_AuditId1",
                table: "hwsap_audit_correction_requests",
                newName: "IX_hwsap_audit_correction_requests_AuditId1");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_usr_groups_UpdatedByUserId",
                table: "UserGroups",
                newName: "IX_UserGroups_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_usr_groups_CreatedByUserId",
                table: "UserGroups",
                newName: "IX_UserGroups_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_sub_areas_UpdatedByUserId",
                table: "SubAreas",
                newName: "IX_SubAreas_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_sub_areas_CreatedByUserId",
                table: "SubAreas",
                newName: "IX_SubAreas_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_factories_UpdatedByUserId",
                table: "Factories",
                newName: "IX_Factories_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_factories_CreatedByUserId",
                table: "Factories",
                newName: "IX_Factories_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_areas_UpdatedByUserId",
                table: "Areas",
                newName: "IX_Areas_UpdatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_org_areas_CreatedByUserId",
                table: "Areas",
                newName: "IX_Areas_CreatedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_aud_audits_SubAreaId",
                table: "Audits",
                newName: "IX_Audits_SubAreaId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_aud_audits_ReviewedByUserId",
                table: "Audits",
                newName: "IX_Audits_ReviewedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_aud_audits_RecurringAuditSettingId",
                table: "Audits",
                newName: "IX_Audits_RecurringAuditSettingId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_aud_attachments_UploadedByUserId",
                table: "AuditAttachments",
                newName: "IX_AuditAttachments_UploadedByUserId");

            migrationBuilder.RenameIndex(
                name: "IX_hwsap_aud_answers_SelectedOptionId",
                table: "AuditAnswers",
                newName: "IX_AuditAnswers_SelectedOptionId");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "AuditLogs",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<DateTime>(
                name: "ServerReceivedAt",
                table: "AuditLogs",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "AuditLogs",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "AdGroupRoleMappings",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "AdGroupRoleMappings",
                type: "bit",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldDefaultValue: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "AdGroupRoleMappings",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETUTCDATE()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditLogs",
                table: "AuditLogs",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_audit_correction_requests",
                table: "hwsap_audit_correction_requests",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Users",
                table: "Users",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Roles",
                table: "Roles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserGroups",
                table: "UserGroups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserGroupMembers",
                table: "UserGroupMembers",
                columns: new[] { "UserGroupId", "UserId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AdGroupRoleMappings",
                table: "AdGroupRoleMappings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_questions",
                table: "hwsap_questions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_question_options",
                table: "hwsap_question_options",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_question_groups",
                table: "hwsap_question_groups",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_audit_templates",
                table: "hwsap_audit_templates",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_recurring_audit_settings",
                table: "hwsap_recurring_audit_settings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_recurrence_rules",
                table: "hwsap_recurrence_rules",
                column: "RecurringAuditSettingId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SubAreas",
                table: "SubAreas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Location",
                table: "Location",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Factories",
                table: "Factories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Areas",
                table: "Areas",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_findings",
                table: "hwsap_findings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_hwsap_corrective_actions",
                table: "hwsap_corrective_actions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Audits",
                table: "Audits",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditAttachments",
                table: "AuditAttachments",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditAnswers",
                table: "AuditAnswers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditAnswerSelectedOptions",
                table: "AuditAnswerSelectedOptions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AuditAnswerFailureReasons",
                table: "AuditAnswerFailureReasons",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AdGroupRoleMappings_Roles_RoleId",
                table: "AdGroupRoleMappings",
                column: "RoleId",
                principalTable: "Roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Factories_FactoryId",
                table: "Areas",
                column: "FactoryId",
                principalTable: "Factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Users_CreatedByUserId",
                table: "Areas",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Areas_Users_UpdatedByUserId",
                table: "Areas",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerFailureReasons_AuditAnswers_AuditAnswerId",
                table: "AuditAnswerFailureReasons",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_Audits_AuditId",
                table: "AuditAnswers",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_hwsap_question_options_SelectedOptionId",
                table: "AuditAnswers",
                column: "SelectedOptionId",
                principalTable: "hwsap_question_options",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswers_hwsap_questions_QuestionId",
                table: "AuditAnswers",
                column: "QuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerSelectedOptions_AuditAnswers_AuditAnswerId",
                table: "AuditAnswerSelectedOptions",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAnswerSelectedOptions_hwsap_question_options_QuestionOptionId",
                table: "AuditAnswerSelectedOptions",
                column: "QuestionOptionId",
                principalTable: "hwsap_question_options",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAttachments_AuditAnswers_AuditAnswerId",
                table: "AuditAttachments",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditAttachments_Users_UploadedByUserId",
                table: "AuditAttachments",
                column: "UploadedByUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AuditLogs_Users_UserId",
                table: "AuditLogs",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Areas_AreaId",
                table: "Audits",
                column: "AreaId",
                principalTable: "Areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Factories_FactoryId",
                table: "Audits",
                column: "FactoryId",
                principalTable: "Factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_SubAreas_SubAreaId",
                table: "Audits",
                column: "SubAreaId",
                principalTable: "SubAreas",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_UserGroups_AssignedToUserGroupId",
                table: "Audits",
                column: "AssignedToUserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Users_AssignedToUserId",
                table: "Audits",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_Users_ReviewedByUserId",
                table: "Audits",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_hwsap_audit_templates_AuditTemplateId",
                table: "Audits",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Audits_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "Audits",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_recurring_audit_settings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Factories_Location_LocationId",
                table: "Factories",
                column: "LocationId",
                principalTable: "Location",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Factories_Users_CreatedByUserId",
                table: "Factories",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Factories_Users_UpdatedByUserId",
                table: "Factories",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId",
                table: "hwsap_audit_correction_requests",
                column: "AuditId",
                principalTable: "Audits",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Audits_AuditId1",
                table: "hwsap_audit_correction_requests",
                column: "AuditId1",
                principalTable: "Audits",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_RequestedByUserId",
                table: "hwsap_audit_correction_requests",
                column: "RequestedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_audit_correction_requests_Users_ReviewedByUserId",
                table: "hwsap_audit_correction_requests",
                column: "ReviewedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_corrective_actions_Users_AssignedToUserId",
                table: "hwsap_corrective_actions",
                column: "AssignedToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_corrective_actions_hwsap_findings_FindingId",
                table: "hwsap_corrective_actions",
                column: "FindingId",
                principalTable: "hwsap_findings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_findings_AuditAnswers_AuditAnswerId",
                table: "hwsap_findings",
                column: "AuditAnswerId",
                principalTable: "AuditAnswers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_findings_Users_ReportedByUserId",
                table: "hwsap_findings",
                column: "ReportedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_question_groups_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_question_groups",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_question_options_hwsap_questions_QuestionId",
                table: "hwsap_question_options",
                column: "QuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_questions",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_question_groups_QuestionGroupId",
                table: "hwsap_questions",
                column: "QuestionGroupId",
                principalTable: "hwsap_question_groups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_questions_hwsap_questions_ParentQuestionId",
                table: "hwsap_questions",
                column: "ParentQuestionId",
                principalTable: "hwsap_questions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurrence_rules_hwsap_recurring_audit_settings_RecurringAuditSettingId",
                table: "hwsap_recurrence_rules",
                column: "RecurringAuditSettingId",
                principalTable: "hwsap_recurring_audit_settings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_UserGroups_AssignToUserGroupId",
                table: "hwsap_recurring_audit_settings",
                column: "AssignToUserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_Users_AssignToUserId",
                table: "hwsap_recurring_audit_settings",
                column: "AssignToUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_recurring_audit_settings_hwsap_audit_templates_AuditTemplateId",
                table: "hwsap_recurring_audit_settings",
                column: "AuditTemplateId",
                principalTable: "hwsap_audit_templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_SubAreas_Areas_AreaId",
                table: "SubAreas",
                column: "AreaId",
                principalTable: "Areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_SubAreas_Users_CreatedByUserId",
                table: "SubAreas",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SubAreas_Users_UpdatedByUserId",
                table: "SubAreas",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserGroupMembers_UserGroups_UserGroupId",
                table: "UserGroupMembers",
                column: "UserGroupId",
                principalTable: "UserGroups",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserGroupMembers_Users_UserId",
                table: "UserGroupMembers",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserGroups_Users_CreatedByUserId",
                table: "UserGroups",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserGroups_Users_UpdatedByUserId",
                table: "UserGroups",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Factories_FactoryId",
                table: "Users",
                column: "FactoryId",
                principalTable: "Factories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Roles_RoleId",
                table: "Users",
                column: "RoleId",
                principalTable: "Roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
