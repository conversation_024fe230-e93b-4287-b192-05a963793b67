var builder = DistributedApplication.CreateBuilder(args);

// Check if we're running in Docker environment
var isDockerEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Docker" ||
                         Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") == "Docker";

IResourceBuilder<ProjectResource> apiService;

if (isDockerEnvironment)
{
    // In Docker environment, reference external SQL Server and AD containers
    var sqlServer = builder.AddConnectionString("DefaultConnection",
        "Server=hwsaudit-sqlserver,1433;Database=HWSAuditPlatformDb;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true");

    apiService = builder.AddProject<Projects.HWSAuditPlatform_ApiService>("apiservice")
        .WithEnvironment("ASPNETCORE_ENVIRONMENT", "Docker")
        .WithReference(sqlServer);
}
else
{
    // Standard development environment
    apiService = builder.AddProject<Projects.HWSAuditPlatform_ApiService>("apiservice");
}

builder.AddProject<Projects.HWSAuditPlatform_WebApp>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithReference(apiService)
    .WaitFor(apiService)
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", isDockerEnvironment ? "Docker" : "Development");

builder.AddProject<Projects.HWSAuditPlatform_WebAuditPWA>("AuditPWA")
    .WithExternalHttpEndpoints()
    .WithReference(apiService)
    .WaitFor(apiService)
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", isDockerEnvironment ? "Docker" : "Development");

var schedulerWorker = builder.AddProject<Projects.HWSAuditPlatform_SchedulerWorker>("hwsauditplatform-schedulerworker");

if (isDockerEnvironment)
{
    schedulerWorker.WithEnvironment("ASPNETCORE_ENVIRONMENT", "Docker");
}

builder.Build().Run();
