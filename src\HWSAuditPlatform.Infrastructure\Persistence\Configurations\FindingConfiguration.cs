using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Workflow;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Finding entity
/// </summary>
public class FindingConfiguration : IEntityTypeConfiguration<Finding>
{
    public void Configure(EntityTypeBuilder<Finding> builder)
    {
        builder.ToTable("hwsap_fnd_findings");

        // Primary Key - CUID with consistent length
        builder.HasKey(f => f.Id);
        builder.Property(f => f.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(f => f.AuditAnswerId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(f => f.FindingCode)
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(f => f.FindingDescription)
            .IsRequired();

        builder.Property(f => f.FindingSeverityLevel)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(f => f.RootCauseAnalysis)
            .IsRequired(false);

        builder.Property(f => f.ImmediateActionTaken)
            .IsRequired(false);

        builder.Property(f => f.Status)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(f => f.ReportedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(f => f.DueDate)
            .IsRequired(false);

        builder.Property(f => f.ResponsibleUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(f => f.RetrospectiveAnalystUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(f => f.FindingCategoryId)
            .IsRequired(false);

        // Auditable properties
        builder.Property(f => f.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(f => f.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(f => f.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(f => f.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(f => f.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(f => f.AuditAnswerId)
            .HasDatabaseName("IX_Findings_AuditAnswerId");

        builder.HasIndex(f => f.FindingCode)
            .HasDatabaseName("IX_Findings_FindingCode");

        builder.HasIndex(f => f.Status)
            .HasDatabaseName("IX_Findings_Status");

        builder.HasIndex(f => f.FindingSeverityLevel)
            .HasDatabaseName("IX_Findings_FindingSeverityLevel");

        builder.HasIndex(f => f.ReportedByUserId)
            .HasDatabaseName("IX_Findings_ReportedByUserId");

        builder.HasIndex(f => f.DueDate)
            .HasDatabaseName("IX_Findings_DueDate");

        builder.HasIndex(f => f.ResponsibleUserId)
            .HasDatabaseName("IX_Findings_ResponsibleUserId");

        builder.HasIndex(f => f.RetrospectiveAnalystUserId)
            .HasDatabaseName("IX_Findings_RetrospectiveAnalystUserId");

        builder.HasIndex(f => f.FindingCategoryId)
            .HasDatabaseName("IX_Findings_FindingCategoryId");

        // Relationships
        builder.HasOne(f => f.AuditAnswer)
            .WithMany()
            .HasForeignKey(f => f.AuditAnswerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(f => f.ReportedByUser)
            .WithMany()
            .HasForeignKey(f => f.ReportedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(f => f.ResponsibleUser)
            .WithMany()
            .HasForeignKey(f => f.ResponsibleUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(f => f.RetrospectiveAnalystUser)
            .WithMany()
            .HasForeignKey(f => f.RetrospectiveAnalystUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(f => f.FindingCategory)
            .WithMany(fc => fc.Findings)
            .HasForeignKey(f => f.FindingCategoryId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(f => f.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for CorrectiveAction entity
/// </summary>
public class CorrectiveActionConfiguration : IEntityTypeConfiguration<CorrectiveAction>
{
    public void Configure(EntityTypeBuilder<CorrectiveAction> builder)
    {
        builder.ToTable("hwsap_fnd_corrective_actions");

        // Primary Key - CUID with consistent length
        builder.HasKey(ca => ca.Id);
        builder.Property(ca => ca.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(ca => ca.FindingId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(ca => ca.ActionDescription)
            .IsRequired();

        builder.Property(ca => ca.AssignedToUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(ca => ca.DueDate)
            .IsRequired();

        builder.Property(ca => ca.CompletionDate)
            .IsRequired(false);

        builder.Property(ca => ca.EvidenceNotes)
            .IsRequired(false);

        builder.Property(ca => ca.Status)
            .HasConversion<int>()
            .IsRequired();

        // Auditable properties
        builder.Property(ca => ca.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ca => ca.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ca => ca.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(ca => ca.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(ca => ca.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(ca => ca.FindingId)
            .HasDatabaseName("IX_CorrectiveActions_FindingId");

        builder.HasIndex(ca => ca.AssignedToUserId)
            .HasDatabaseName("IX_CorrectiveActions_AssignedToUserId");

        builder.HasIndex(ca => ca.Status)
            .HasDatabaseName("IX_CorrectiveActions_Status");

        builder.HasIndex(ca => ca.DueDate)
            .HasDatabaseName("IX_CorrectiveActions_DueDate");

        builder.HasIndex(ca => ca.CompletionDate)
            .HasDatabaseName("IX_CorrectiveActions_CompletionDate");

        // Relationships
        builder.HasOne(ca => ca.Finding)
            .WithMany(f => f.CorrectiveActions)
            .HasForeignKey(ca => ca.FindingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ca => ca.AssignedToUser)
            .WithMany()
            .HasForeignKey(ca => ca.AssignedToUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction);

        // Ignore domain events
        builder.Ignore(ca => ca.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for AuditCorrectionRequest entity
/// </summary>
public class AuditCorrectionRequestConfiguration : IEntityTypeConfiguration<AuditCorrectionRequest>
{
    public void Configure(EntityTypeBuilder<AuditCorrectionRequest> builder)
    {
        builder.ToTable("hwsap_wfl_audit_correction_requests");

        // Primary Key - CUID with consistent length
        builder.HasKey(acr => acr.Id);
        builder.Property(acr => acr.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(acr => acr.AuditId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(acr => acr.RequestedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(acr => acr.RequestReason)
            .IsRequired();

        builder.Property(acr => acr.RequestedAt)
            .IsRequired();

        builder.Property(acr => acr.Status)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(acr => acr.ReviewedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(acr => acr.ReviewedAt)
            .IsRequired(false);

        builder.Property(acr => acr.ManagerComments)
            .IsRequired(false);

        // Auditable properties
        builder.Property(acr => acr.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(acr => acr.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(acr => acr.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(acr => acr.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(acr => acr.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(acr => acr.AuditId)
            .IsUnique()
            .HasDatabaseName("IX_AuditCorrectionRequests_AuditId");

        builder.HasIndex(acr => acr.RequestedByUserId)
            .HasDatabaseName("IX_AuditCorrectionRequests_RequestedByUserId");

        builder.HasIndex(acr => acr.ReviewedByUserId)
            .HasDatabaseName("IX_AuditCorrectionRequests_ReviewedByUserId");

        builder.HasIndex(acr => acr.Status)
            .HasDatabaseName("IX_AuditCorrectionRequests_Status");

        builder.HasIndex(acr => acr.RequestedAt)
            .HasDatabaseName("IX_AuditCorrectionRequests_RequestedAt");

        // Relationships
        // Note: The relationship to Audit is configured from the Audit side to avoid conflicts

        builder.HasOne(acr => acr.RequestedByUser)
            .WithMany()
            .HasForeignKey(acr => acr.RequestedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(acr => acr.ReviewedByUser)
            .WithMany()
            .HasForeignKey(acr => acr.ReviewedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(acr => acr.DomainEvents);
    }
}
