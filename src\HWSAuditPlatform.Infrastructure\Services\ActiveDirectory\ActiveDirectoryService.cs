using System.DirectoryServices;
using System.Runtime.Versioning;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory.Models;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;

/// <summary>
/// Active Directory service implementation using System.DirectoryServices
/// </summary>
[SupportedOSPlatform("windows")]
public class ActiveDirectoryService : Application.Interfaces.IActiveDirectoryService
{
    private readonly ActiveDirectoryOptions _options;
    private readonly ILogger<ActiveDirectoryService> _logger;

    public ActiveDirectoryService(IOptions<ActiveDirectoryOptions> options, ILogger<ActiveDirectoryService> logger)
    {
        ArgumentNullException.ThrowIfNull(options, nameof(options));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));

        _options = options.Value;
        _logger = logger;

        ValidateConfiguration();
    }

    private void ValidateConfiguration()
    {
        if (string.IsNullOrWhiteSpace(_options.Domain))
        {
            throw new ArgumentException("Domain is required and cannot be empty.", nameof(_options.Domain));
        }

        if (string.IsNullOrWhiteSpace(_options.Username))
        {
            throw new ArgumentException("Username is required and cannot be empty.", nameof(_options.Username));
        }

        // Support both single SearchBase and multiple SearchBases
        var hasSearchBases = _options.SearchBases?.Any() == true;
        var hasSearchBase = !string.IsNullOrWhiteSpace(_options.SearchBase);

        if (!hasSearchBases && !hasSearchBase)
        {
            throw new ArgumentException("At least one SearchBase is required.", nameof(_options.SearchBase));
        }
    }

    public async Task<IEnumerable<AdUserDto>> GetUsersAsync(CancellationToken cancellationToken = default)
    {
        var users = new List<AdUserDto>();
        var searchBases = GetSearchBases();

        try
        {
            // Search in parallel across all search bases
            var tasks = searchBases.Select(searchBase =>
            {
                return Task.Run(() =>
                {
                    var searchResults = new List<AdUserDto>();

                    try
                    {
                        using var directoryEntry = CreateDirectoryEntry(searchBase);
                        using var directorySearcher = new DirectorySearcher(directoryEntry)
                        {
                            Filter = "(&(objectClass=user)(objectCategory=person))",
                            SearchScope = SearchScope.Subtree,
                            PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail",
                                           "distinguishedName", "userAccountControl", "lastLogon",
                                           "whenCreated", "whenChanged", "department", "title", "manager",
                                           "telephoneNumber", "memberOf" }
                        };

                        using var results = directorySearcher.FindAll();
                        foreach (SearchResult result in results)
                        {
                            var user = MapSearchResultToAdUser(result);
                            if (user != null)
                            {
                                searchResults.Add(MapToDto(user));
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error retrieving users from search base {SearchBase}", searchBase);
                        // Continue with other search bases instead of throwing
                    }

                    return searchResults;
                }, cancellationToken);
            });

            var allResults = await Task.WhenAll(tasks);
            users = allResults.SelectMany(r => r).ToList();

            // Remove duplicates based on ObjectGuid
            users = users.GroupBy(u => u.ObjectGuid)
                         .Select(g => g.First())
                         .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users from Active Directory");
            throw;
        }

        return users;
    }

    public async Task<AdUserDto?> GetUserByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(objectGuid, nameof(objectGuid));

        var searchBases = GetSearchBases();

        foreach (var searchBase in searchBases)
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry(searchBase);
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = $"(&(objectClass=user)(objectGUID={objectGuid}))",
                    SearchScope = SearchScope.Subtree,
                    PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail",
                                   "distinguishedName", "userAccountControl", "lastLogon",
                                   "whenCreated", "whenChanged", "department", "title", "manager",
                                   "telephoneNumber", "memberOf" }
                };

                var searchResult = directorySearcher.FindOne();
                if (searchResult != null)
                {
                    var adUser = MapSearchResultToAdUser(searchResult);
                    return adUser != null ? MapToDto(adUser) : null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by ObjectGUID {ObjectGuid} from search base {SearchBase}", objectGuid, searchBase);
                // Continue with next search base
            }
        }

        return null;
    }
    public async Task<AdUserDto?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username, nameof(username));

        var searchBases = GetSearchBases();

        foreach (var searchBase in searchBases)
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry(searchBase);
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = $"(&(objectClass=user)(sAMAccountName={username}))",
                    SearchScope = SearchScope.Subtree,
                    PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail",
                                   "distinguishedName", "userAccountControl", "lastLogon",
                                   "whenCreated", "whenChanged", "department", "title", "manager",
                                   "telephoneNumber", "memberOf" }
                };

                var searchResult = directorySearcher.FindOne();
                if (searchResult != null)
                {
                    var adUser = MapSearchResultToAdUser(searchResult);
                    return adUser != null ? MapToDto(adUser) : null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by username {Username} from search base {SearchBase}", username, searchBase);
                // Continue with next search base
            }
        }

        return null;
    }
    public async Task<bool> ValidateCredentialsAsync(string username, string password, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username, nameof(username));
        ArgumentException.ThrowIfNullOrWhiteSpace(password, nameof(password));

        return await Task.Run(() =>
        {
            try
            {
                // Use Server if specified, otherwise use Domain
                var serverOrDomain = !string.IsNullOrEmpty(_options.Server) ? _options.Server : _options.Domain;

                // Try different authentication formats
                var authFormats = new[]
                {
                    username, // Simple username
                    $"{username}@{_options.Domain}", // UPN format
                    $"{_options.Domain}\\{username}", // Domain\username format
                    $"CN={username},OU=HWSUsers,OU=HWSAudit,DC=hwsaudit,DC=local" // Full DN format
                };

                foreach (var authUsername in authFormats)
                {
                    try
                    {
                        _logger.LogDebug("Trying authentication with format: {AuthUsername}", authUsername);

                        using var directoryEntry = new DirectoryEntry($"LDAP://{serverOrDomain}:{_options.Port}", authUsername, password);

                        // Try to bind to the directory
                        var nativeObject = directoryEntry.NativeObject;

                        _logger.LogInformation("Successfully authenticated user {Username} with format {AuthUsername}", username, authUsername);
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Authentication failed for user {Username} with format {AuthUsername}", username, authUsername);
                        // Continue to next format
                    }
                }

                _logger.LogWarning("All authentication formats failed for user {Username}", username);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during credential validation for user {Username}", username);
                return false;
            }
        }, cancellationToken);
    }

    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                var serverOrDomain = !string.IsNullOrEmpty(_options.Server) ? _options.Server : _options.Domain;
                _logger.LogInformation("Testing AD connection to {Server}:{Port}", serverOrDomain, _options.Port);

                using var directoryEntry = new DirectoryEntry($"LDAP://{serverOrDomain}:{_options.Port}", _options.Username, _options.Password);
                var nativeObject = directoryEntry.NativeObject;

                _logger.LogInformation("Successfully connected to Active Directory");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to Active Directory at {Server}:{Port}",
                    !string.IsNullOrEmpty(_options.Server) ? _options.Server : _options.Domain, _options.Port);
                return false;
            }
        }, cancellationToken);
    }

    private DirectoryEntry CreateDirectoryEntry(string? searchBase = null)
    {
        var serverOrDomain = !string.IsNullOrEmpty(_options.Server) ? _options.Server : _options.Domain;
        var ldapPath = $"LDAP://{serverOrDomain}:{_options.Port}";

        if (!string.IsNullOrEmpty(searchBase))
        {
            ldapPath += $"/{searchBase}";
        }

        return new DirectoryEntry(ldapPath, _options.Username, _options.Password);
    }

    private static AdUser? MapSearchResultToAdUser(SearchResult result)
    {
        try
        {
            return new AdUser
            {
                ObjectGuid = GetGuidPropertyValue(result, "objectGUID"),
                Username = GetPropertyValue(result, "sAMAccountName"),
                FirstName = GetPropertyValue(result, "givenName"),
                LastName = GetPropertyValue(result, "sn"),
                Email = GetPropertyValue(result, "mail"),
                DistinguishedName = GetPropertyValue(result, "distinguishedName"),
                IsActive = !IsAccountDisabled(result),
                Department = GetPropertyValue(result, "department"),
                Title = GetPropertyValue(result, "title"),
                Manager = GetPropertyValue(result, "manager"),
                Phone = GetPropertyValue(result, "telephoneNumber"),
                WhenCreated = GetDateTimeProperty(result, "whenCreated"),
                WhenChanged = GetDateTimeProperty(result, "whenChanged"),
                MemberOf = GetMultiValueProperty(result, "memberOf")
            };
        }
        catch (Exception)
        {
            return null;
        }
    }



    private static string GetPropertyValue(SearchResult result, string propertyName)
    {
        return result.Properties[propertyName].Count > 0 ? result.Properties[propertyName][0]?.ToString() ?? string.Empty : string.Empty;
    }

    private static string GetGuidPropertyValue(SearchResult result, string propertyName)
    {
        if (result.Properties[propertyName].Count > 0 && result.Properties[propertyName][0] is byte[] guidBytes)
        {
            return new Guid(guidBytes).ToString();
        }
        return string.Empty;
    }
    private List<string> GetSearchBases()
    {
        // Prioritize SearchBases array, fallback to single SearchBase for backward compatibility
        if (_options.SearchBases?.Any() == true)
        {
            return _options.SearchBases;
        }

        if (!string.IsNullOrWhiteSpace(_options.SearchBase))
        {
            return new List<string> { _options.SearchBase };
        }

        throw new InvalidOperationException("No search bases configured");
    }

    private static DateTime? GetDateTimeProperty(SearchResult result, string propertyName)
    {
        if (result.Properties[propertyName].Count > 0 && result.Properties[propertyName][0] is DateTime dateTime)
        {
            return dateTime;
        }
        return null;
    }

    private static List<string> GetMultiValueProperty(SearchResult result, string propertyName)
    {
        var values = new List<string>();
        for (int i = 0; i < result.Properties[propertyName].Count; i++)
        {
            var value = result.Properties[propertyName][i]?.ToString();
            if (!string.IsNullOrEmpty(value))
            {
                values.Add(value);
            }
        }
        return values;
    }

    private static bool IsAccountDisabled(SearchResult result)
    {
        if (result.Properties["userAccountControl"].Count > 0 &&
            int.TryParse(result.Properties["userAccountControl"][0]?.ToString(), out var userAccountControl))
        {
            // Check if the ACCOUNTDISABLE flag (0x2) is set
            return (userAccountControl & 0x2) != 0;
        }
        return false;
    }

    /// <summary>
    /// Maps an AdUser model to an AdUserDto
    /// </summary>
    /// <param name="adUser">The AdUser model</param>
    /// <returns>The mapped AdUserDto</returns>
    private static AdUserDto MapToDto(AdUser adUser)
    {
        return new AdUserDto
        {
            ObjectGuid = adUser.ObjectGuid,
            Username = adUser.Username,
            FirstName = adUser.FirstName,
            LastName = adUser.LastName,
            Email = adUser.Email,
            DistinguishedName = adUser.DistinguishedName,
            IsEnabled = adUser.IsActive,
            MemberOf = adUser.MemberOf ?? new List<string>(),
            Department = adUser.Department,
            Title = adUser.Title,
            Manager = adUser.Manager,
            PhoneNumber = adUser.Phone,
            WhenCreated = adUser.WhenCreated,
            WhenChanged = adUser.WhenChanged,
            LastLogon = adUser.LastLogon
        };
    }
}
