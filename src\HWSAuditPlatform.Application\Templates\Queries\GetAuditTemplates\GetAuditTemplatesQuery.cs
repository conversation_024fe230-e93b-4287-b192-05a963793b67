using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;

/// <summary>
/// Query to get a paginated list of audit templates
/// </summary>
public class GetAuditTemplatesQuery : BaseQuery<PaginatedResult<AuditTemplateSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term to filter templates
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by published status
    /// </summary>
    public bool? IsPublished { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Show only templates that can be used for new audits
    /// </summary>
    public bool? CanBeUsed { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "TemplateName";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "asc";
}
