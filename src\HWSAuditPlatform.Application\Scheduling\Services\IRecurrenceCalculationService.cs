using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Application.Scheduling.Services;

/// <summary>
/// Service for calculating recurrence dates and patterns
/// </summary>
public interface IRecurrenceCalculationService
{
    /// <summary>
    /// Calculate the next generation date based on a recurrence rule
    /// </summary>
    /// <param name="rule">The recurrence rule</param>
    /// <param name="fromDate">The date to calculate from (defaults to today)</param>
    /// <returns>The next generation date</returns>
    DateOnly CalculateNextGenerationDate(RecurrenceRule rule, DateOnly? fromDate = null);

    /// <summary>
    /// Calculate the next occurrence after a specific date
    /// </summary>
    /// <param name="rule">The recurrence rule</param>
    /// <param name="afterDate">The date to calculate after</param>
    /// <returns>The next occurrence date</returns>
    DateOnly CalculateNextOccurrence(RecurrenceRule rule, DateOnly afterDate);

    /// <summary>
    /// Validate if a recurrence rule is properly configured
    /// </summary>
    /// <param name="rule">The recurrence rule to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    bool IsValidRecurrenceRule(RecurrenceRule rule);

    /// <summary>
    /// Get all occurrence dates within a date range
    /// </summary>
    /// <param name="rule">The recurrence rule</param>
    /// <param name="startDate">Start of the range</param>
    /// <param name="endDate">End of the range</param>
    /// <param name="maxOccurrences">Maximum number of occurrences to return</param>
    /// <returns>List of occurrence dates</returns>
    List<DateOnly> GetOccurrencesInRange(RecurrenceRule rule, DateOnly startDate, DateOnly endDate, int maxOccurrences = 100);
}
