namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the status of an audit correction request.
/// Maps to the correction_request_status enum in the database.
/// </summary>
public enum CorrectionRequestStatus
{
    /// <summary>
    /// Request is pending approval from a manager
    /// </summary>
    PendingApproval,

    /// <summary>
    /// Request has been approved by a manager
    /// </summary>
    Approved,

    /// <summary>
    /// Request has been denied by a manager
    /// </summary>
    Denied,

    /// <summary>
    /// Auditor has submitted the requested changes
    /// </summary>
    ChangesSubmitted
}
