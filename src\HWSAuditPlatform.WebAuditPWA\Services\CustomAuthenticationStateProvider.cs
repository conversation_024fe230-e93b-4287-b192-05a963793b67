using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Custom authentication state provider for PWA
/// </summary>
public class CustomAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<CustomAuthenticationStateProvider> _logger;
    private ClaimsPrincipal _currentUser = new(new ClaimsIdentity());

    public CustomAuthenticationStateProvider(
        IAuthenticationService authenticationService,
        ILogger<CustomAuthenticationStateProvider> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            var user = await _authenticationService.GetCurrentUserAsync();
            var token = await _authenticationService.GetTokenAsync();

            if (user != null && !string.IsNullOrEmpty(token))
            {
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, user.UserId),
                    new(ClaimTypes.Name, user.Username),
                    new(ClaimTypes.Email, user.Email),
                    new(ClaimTypes.Role, user.Role),
                    new("FullName", user.FullName)
                };

                if (!string.IsNullOrEmpty(user.FactoryId))
                {
                    claims.Add(new Claim("FactoryId", user.FactoryId));
                }

                var identity = new ClaimsIdentity(claims, "jwt");
                _currentUser = new ClaimsPrincipal(identity);

                _logger.LogDebug("User authenticated: {Username} with role: {Role}", user.Username, user.Role);
            }
            else
            {
                _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
                _logger.LogDebug("No authenticated user found");
            }

            return new AuthenticationState(_currentUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting authentication state");
            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            return new AuthenticationState(_currentUser);
        }
    }

    /// <summary>
    /// Marks the user as authenticated
    /// </summary>
    /// <param name="user">User information</param>
    public void MarkUserAsAuthenticated(Models.UserInfo user)
    {
        try
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.UserId),
                new(ClaimTypes.Name, user.Username),
                new(ClaimTypes.Email, user.Email),
                new(ClaimTypes.Role, user.Role),
                new("FullName", user.FullName)
            };

            if (!string.IsNullOrEmpty(user.FactoryId))
            {
                claims.Add(new Claim("FactoryId", user.FactoryId));
            }

            var identity = new ClaimsIdentity(claims, "jwt");
            _currentUser = new ClaimsPrincipal(identity);

            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
            
            _logger.LogInformation("User marked as authenticated: {Username}", user.Username);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking user as authenticated");
        }
    }

    /// <summary>
    /// Marks the user as logged out
    /// </summary>
    public void MarkUserAsLoggedOut()
    {
        try
        {
            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
            
            _logger.LogInformation("User marked as logged out");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking user as logged out");
        }
    }

    /// <summary>
    /// Gets the current user's role
    /// </summary>
    /// <returns>User role or null if not authenticated</returns>
    public string? GetCurrentUserRole()
    {
        return _currentUser.FindFirst(ClaimTypes.Role)?.Value;
    }

    /// <summary>
    /// Gets the current user's ID
    /// </summary>
    /// <returns>User ID or null if not authenticated</returns>
    public string? GetCurrentUserId()
    {
        return _currentUser.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    /// <summary>
    /// Gets the current user's username
    /// </summary>
    /// <returns>Username or null if not authenticated</returns>
    public string? GetCurrentUsername()
    {
        return _currentUser.FindFirst(ClaimTypes.Name)?.Value;
    }

    /// <summary>
    /// Checks if the current user has a specific role
    /// </summary>
    /// <param name="role">Role to check</param>
    /// <returns>True if user has the role, false otherwise</returns>
    public bool IsInRole(string role)
    {
        return _currentUser.IsInRole(role);
    }

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    public bool IsAuthenticated()
    {
        return _currentUser.Identity?.IsAuthenticated == true;
    }
}
