using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Scheduling.Commands.UpdateRecurringAuditSetting;

/// <summary>
/// Command to update an existing recurring audit setting
/// </summary>
public class UpdateRecurringAuditSettingCommand : BaseCommand
{
    /// <summary>
    /// The ID of the recurring audit setting to update
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// User-defined name for this recurring schedule
    /// </summary>
    public string SettingName { get; set; } = string.Empty;

    /// <summary>
    /// The audit template to be used for generated audits
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Number of days from the generated ScheduledDate to the audit's DueDate
    /// </summary>
    public int DeadlineDays { get; set; } = 7;

    /// <summary>
    /// If false, the scheduler will not generate audits for this setting
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Determines how the scheduler generates/assigns audits from this setting
    /// </summary>
    public AssignmentType AssignmentType { get; set; } = AssignmentType.Individual;

    /// <summary>
    /// Default user if AssignmentType is Individual
    /// </summary>
    public string? AssignToUserId { get; set; }

    /// <summary>
    /// Default group if AssignmentType is GroupAny or GroupAllScheduled
    /// </summary>
    public string? AssignToUserGroupId { get; set; }

    /// <summary>
    /// Factory where audits should be generated
    /// </summary>
    public int FactoryId { get; set; }

    /// <summary>
    /// Area where audits should be generated (optional)
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// SubArea where audits should be generated (optional)
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Recurrence rule configuration
    /// </summary>
    public CreateRecurrenceRuleDto RecurrenceRule { get; set; } = new();

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }
}
