using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetSubAreasByArea;

/// <summary>
/// Handler for GetSubAreasByAreaQuery
/// </summary>
public class GetSubAreasByAreaQueryHandler : BaseQueryHandler<GetSubAreasByAreaQuery, List<SubAreaSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetSubAreasByAreaQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<SubAreaSummaryDto>> Handle(GetSubAreasByAreaQuery request, CancellationToken cancellationToken)
    {
        var query = _context.SubAreas
            .Include(sa => sa.Area)
            .Where(sa => sa.AreaId == request.AreaId)
            .AsQueryable();

        // Apply filters
        if (request.IsActive.HasValue)
        {
            query = query.Where(sa => sa.IsActive == request.IsActive.Value);
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(sa =>
                EF.Functions.Like(sa.SubAreaName, searchPattern) ||
                (sa.Description != null && EF.Functions.Like(sa.Description, searchPattern)));
        }

        // Get sub-areas
        var subAreas = await query
            .Select(sa => new SubAreaSummaryDto
            {
                Id = sa.Id,
                SubAreaName = sa.SubAreaName,
                AreaId = sa.AreaId,
                AreaName = sa.Area != null ? sa.Area.AreaName : null,
                Description = sa.Description,
                IsActive = sa.IsActive
            })
            .OrderBy(sa => sa.SubAreaName)
            .ToListAsync(cancellationToken);

        return subAreas;
    }
}
