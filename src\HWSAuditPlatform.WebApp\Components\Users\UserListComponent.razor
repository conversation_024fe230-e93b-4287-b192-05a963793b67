@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Users
@using HWSAuditPlatform.Domain.Enums
@inject IUserApiService UserApiService
@inject ILogger<UserListComponent> Logger

<div class="user-management-container">
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>User Management</h1>
                <p class="text-muted">Manage users, roles, and permissions</p>
            </div>
            <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                <i class="bi bi-plus-circle me-2"></i>Add User
            </button>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <HWSAuditPlatform.WebApp.Components.Shared.ErrorComponent 
        HasError="@(!string.IsNullOrEmpty(errorMessage))"
        Title="Error"
        Message="@errorMessage"
        AlertType="danger"
        OnDismiss="DismissMessages" />

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@successMessage
            <button type="button" class="btn-close" @onclick="DismissMessages"></button>
        </div>
    }

    <!-- Users Table -->
    <div class="card">
        <div class="card-body">
            <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
                IsLoading="@isLoading" 
                Message="Loading users..." />

            @if (!isLoading && users?.Any() == true)
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var user in users)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                @GetUserInitials(user.FullName)
                                            </div>
                                            <div>
                                                <div class="fw-semibold">@user.FullName</div>
                                                <small class="text-muted">@user.Username</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>@user.Email</td>
                                    <td>
                                        <span class="badge @GetRoleBadgeClass(user.RoleId)">
                                            @user.RoleId
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge @(user.IsActive ? "bg-success" : "bg-secondary")">
                                            @(user.IsActive ? "Active" : "Inactive")
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewUser(user.Id)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" @onclick="() => EditUser(user.Id)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteUser(user.Id)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else if (!isLoading)
            {
                <div class="text-center p-4">
                    <i class="bi bi-people" style="font-size: 3rem; color: #ccc;"></i>
                    <h5 class="mt-3">No users found</h5>
                    <p class="text-muted">Add a new user to get started.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create User Modal Placeholder -->
@if (showCreateModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>Add New User
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseCreateModal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-info">
                        <i class="bi bi-info-circle me-2"></i>
                        User creation functionality will be implemented when the comprehensive API client is available.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseCreateModal">Close</button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .user-management-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #555;
        background-color: #f8f9fa;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }
</style>

@code {
    private IEnumerable<User>? users;
    private bool isLoading = true;
    private bool showCreateModal = false;
    private string? successMessage = null;
    private string? errorMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            users = await UserApiService.GetUsersAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading users");
            ShowErrorMessage("Failed to load users. Please try again.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetUserInitials(string fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "??";

        var parts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        
        return parts.Length > 0 ? parts[0][0].ToString().ToUpper() : "?";
    }

    private string GetRoleBadgeClass(int roleId)
    {
        return roleId switch
        {
            1 => "bg-danger",    // DevAdmin
            2 => "bg-warning",   // SystemManager
            3 => "bg-info",      // ProcessOwner
            4 => "bg-primary",   // Auditor
            _ => "bg-secondary"  // Viewer or other
        };
    }

    private void ShowCreateUserModal()
    {
        showCreateModal = true;
    }

    private void CloseCreateModal()
    {
        showCreateModal = false;
    }

    private void ViewUser(string userId)
    {
        // TODO: Navigate to user details page
        Logger.LogInformation("View user requested for {UserId}", userId);
    }

    private void EditUser(string userId)
    {
        // TODO: Navigate to user edit page
        Logger.LogInformation("Edit user requested for {UserId}", userId);
    }

    private async Task DeleteUser(string userId)
    {
        try
        {
            var success = await UserApiService.DeleteUserAsync(userId);
            if (success)
            {
                ShowSuccessMessage("User deleted successfully!");
                await LoadUsers();
            }
            else
            {
                ShowErrorMessage("Failed to delete user. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting user {UserId}", userId);
            ShowErrorMessage("An error occurred while deleting the user.");
        }
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
        StateHasChanged();

        _ = Task.Delay(5000).ContinueWith(_ =>
        {
            successMessage = null;
            InvokeAsync(StateHasChanged);
        });
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();
    }

    private void DismissMessages()
    {
        successMessage = null;
        errorMessage = null;
        StateHasChanged();
    }
}
