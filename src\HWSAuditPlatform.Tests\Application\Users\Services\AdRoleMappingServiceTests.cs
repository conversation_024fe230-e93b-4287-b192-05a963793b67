using FluentAssertions;
using HWSAuditPlatform.Application.Users.Services;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;

namespace HWSAuditPlatform.Tests.Application.Users.Services;

/// <summary>
/// Tests for AdRoleMappingService database-first role assignment
/// </summary>
public class AdRoleMappingServiceTests : BaseDbTestClass
{
    private readonly AdRoleMappingService _service;
    private readonly Mock<ILogger<AdRoleMappingService>> _mockLogger;

    public AdRoleMappingServiceTests()
    {
        _mockLogger = new Mock<ILogger<AdRoleMappingService>>();
        _service = new AdRoleMappingService(Context, _mockLogger.Object);
    }

    [Fact]
    public async Task GetUserRoleAsync_WhenUserExistsInDatabase_ShouldReturnDatabaseRole()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************";
        var testUser = User.Create(
            username: "test.user",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 2, // SystemManager role
            factoryId: null,
            isActive: true,
            adObjectGuid: testAdObjectGuid,
            adDistinguishedName: "CN=test.user,OU=Users,DC=test,DC=local",
            createdByUserId: "system");

        await Context.Users.AddAsync(testUser);
        await Context.SaveChangesAsync();

        var adGroups = new[] { "HWSAudit-ProcessOwners" }; // This would map to ProcessOwner, but user has SystemManager in DB

        // Act
        var result = await _service.GetUserRoleAsync(testAdObjectGuid, adGroups);

        // Assert
        result.Should().Be(UserRole.SystemManager); // Should return database role, not AD group mapped role
    }

    [Fact]
    public async Task GetUserRoleAsync_WhenUserNotInDatabase_ShouldFallbackToAdGroupMapping()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************"; // User not in database
        var adGroups = new[] { "HWSAudit-ProcessOwners" }; // This should map to ProcessOwner

        // Act
        var result = await _service.GetUserRoleAsync(testAdObjectGuid, adGroups);

        // Assert
        result.Should().Be(UserRole.ProcessOwner); // Should return AD group mapped role
    }

    [Fact]
    public async Task GetUserRoleWithExistenceAsync_WhenUserExistsInDatabase_ShouldReturnRoleAndTrue()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************";
        var testUser = User.Create(
            username: "test.user",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1, // DevAdmin role
            factoryId: null,
            isActive: true,
            adObjectGuid: testAdObjectGuid,
            adDistinguishedName: "CN=test.user,OU=Users,DC=test,DC=local",
            createdByUserId: "system");

        await Context.Users.AddAsync(testUser);
        await Context.SaveChangesAsync();

        var adGroups = new[] { "HWSAudit-Auditors" }; // This would map to Auditor, but user has DevAdmin in DB

        // Act
        var (role, userExistsInDb) = await _service.GetUserRoleWithExistenceAsync(testAdObjectGuid, adGroups);

        // Assert
        role.Should().Be(UserRole.DevAdmin);
        userExistsInDb.Should().BeTrue();
    }

    [Fact]
    public async Task GetUserRoleWithExistenceAsync_WhenUserNotInDatabase_ShouldReturnRoleAndFalse()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************"; // User not in database
        var adGroups = new[] { "HWSAudit-Auditors" }; // This should map to Auditor

        // Act
        var (role, userExistsInDb) = await _service.GetUserRoleWithExistenceAsync(testAdObjectGuid, adGroups);

        // Assert
        role.Should().Be(UserRole.Auditor);
        userExistsInDb.Should().BeFalse();
    }

    [Fact]
    public async Task GetUserRoleAsync_WhenUserNotInDatabaseAndNoAdGroupMappings_ShouldReturnViewer()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************"; // User not in database
        var adGroups = new[] { "UnknownGroup" }; // No mapping for this group

        // Act
        var result = await _service.GetUserRoleAsync(testAdObjectGuid, adGroups);

        // Assert
        result.Should().Be(UserRole.Viewer); // Should default to Viewer
    }

    [Fact]
    public async Task GetUserRoleAsync_WhenUserInDatabaseButInactive_ShouldStillReturnDatabaseRole()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var testAdObjectGuid = "550e8400-e29b-41d4-a716-************";
        var testUser = User.Create(
            username: "inactive.user",
            firstName: "Inactive",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 3, // ProcessOwner role
            factoryId: null,
            isActive: false, // User is inactive
            adObjectGuid: testAdObjectGuid,
            adDistinguishedName: "CN=inactive.user,OU=Users,DC=test,DC=local",
            createdByUserId: "system");

        await Context.Users.AddAsync(testUser);
        await Context.SaveChangesAsync();

        var adGroups = new[] { "HWSAudit-DevAdministrators" }; // This would map to DevAdmin

        // Act
        var result = await _service.GetUserRoleAsync(testAdObjectGuid, adGroups);

        // Assert
        result.Should().Be(UserRole.ProcessOwner); // Should return database role even if user is inactive
    }

    private async Task SeedTestDataAsync()
    {
        // Seed roles
        var roles = new[]
        {
            new Role { Id = 1, RoleName = UserRole.DevAdmin, Description = "Developer Administrator" },
            new Role { Id = 2, RoleName = UserRole.SystemManager, Description = "System Manager" },
            new Role { Id = 3, RoleName = UserRole.ProcessOwner, Description = "Process Owner" },
            new Role { Id = 4, RoleName = UserRole.Auditor, Description = "Auditor" },
            new Role { Id = 5, RoleName = UserRole.Viewer, Description = "Viewer" }
        };

        await Context.Roles.AddRangeAsync(roles);

        // Seed AD group role mappings
        var mappings = new[]
        {
            new AdGroupRoleMapping { Id = 1, AdGroupName = "HWSAudit-DevAdministrators", RoleId = 1, IsActive = true },
            new AdGroupRoleMapping { Id = 2, AdGroupName = "HWSAudit-SystemManagers", RoleId = 2, IsActive = true },
            new AdGroupRoleMapping { Id = 3, AdGroupName = "HWSAudit-ProcessOwners", RoleId = 3, IsActive = true },
            new AdGroupRoleMapping { Id = 4, AdGroupName = "HWSAudit-Auditors", RoleId = 4, IsActive = true },
            new AdGroupRoleMapping { Id = 5, AdGroupName = "HWSAudit-Viewers", RoleId = 5, IsActive = true }
        };

        await Context.AdGroupRoleMappings.AddRangeAsync(mappings);
        await Context.SaveChangesAsync();
    }
}
