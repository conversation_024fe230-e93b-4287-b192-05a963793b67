using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Runtime.Versioning;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Infrastructure.Repositories;
using HWSAuditPlatform.Infrastructure.Identity;
using HWSAuditPlatform.Infrastructure.Services;
using HWSAuditPlatform.Infrastructure.Services.FileStorage;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;
using HWSAuditPlatform.Infrastructure.HealthChecks;
using HWSAuditPlatform.Domain.Services;

namespace HWSAuditPlatform.Infrastructure;

/// <summary>
/// Dependency injection configuration for the Infrastructure layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds Infrastructure layer services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)));

        // Register DbContext as IApplicationDbContext
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        // Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Repositories
        services.AddScoped(typeof(IRepository<,>), typeof(Repository<,>));

        // Specialized Repositories
        services.AddScoped<IAuditRepository, AuditRepository>();
        services.AddScoped<IAuditTemplateRepository, AuditTemplateRepository>();
        services.AddScoped<IQuestionRepository, QuestionRepository>();

        // Identity Services
        services.AddHttpContextAccessor();
        services.AddScoped<ICurrentUserService, CurrentUserService>();

        // Domain Event Service
        services.AddScoped<IDomainEventService, DomainEventService>();

        // Domain Services
        services.AddScoped<IAreaResponsibilityService, AreaResponsibilityService>();
        services.AddScoped<IFindingCategorizationService, FindingCategorizationService>();

        // Business Services
        services.AddScoped<IAuditAttachmentService, AuditAttachmentService>();

        // File Storage
        AddFileStorage(services, configuration);

        // Active Directory Services (Windows only)
        if (OperatingSystem.IsWindows())
        {
            AddActiveDirectoryServices(services, configuration);
        }

        // Health Checks
        AddHealthChecks(services);

        return services;
    }

    private static void AddFileStorage(IServiceCollection services, IConfiguration configuration)
    {
        // Configure Local File Storage (only supported option)
        services.Configure<LocalFileStorageOptions>(
            configuration.GetSection(LocalFileStorageOptions.SectionName));

        services.AddScoped<IFileStorageService, LocalFileStorageService>();
    }

    [SupportedOSPlatform("windows")]
    private static void AddActiveDirectoryServices(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ActiveDirectoryOptions>(
            configuration.GetSection(ActiveDirectoryOptions.SectionName));

        services.AddScoped<Application.Interfaces.IActiveDirectoryService, ActiveDirectoryService>();
    }

    private static void AddHealthChecks(IServiceCollection services)
    {
        services.AddScoped<ActiveDirectoryHealthCheck>();
        services.AddScoped<FileStorageHealthCheck>();
    }
}

/// <summary>
/// Configuration options for Active Directory
/// </summary>
public class ActiveDirectoryOptions
{
    public const string SectionName = "ActiveDirectory";

    public string Domain { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public List<string> SearchBases { get; set; } = [];
    public string SearchBase { get; set; } = string.Empty;
    public bool UseSSL { get; set; } = true;
    public int Port { get; set; } = 636;
    public int TimeoutSeconds { get; set; } = 30;
    public string Server { get; set; } = string.Empty;
}
