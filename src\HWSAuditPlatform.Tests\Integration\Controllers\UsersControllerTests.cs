using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using HWSAuditPlatform.Application.Users.Commands.CreateUser;
using HWSAuditPlatform.Application.Users.Commands.UpdateUser;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using Microsoft.VisualStudio.TestPlatform.TestHost;
using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Tests.Integration.Controllers;

public class UsersControllerTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ApplicationDbContext _context;

    public UsersControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the real database context
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb_" + Guid.NewGuid());
                });
            });
        });

        _client = _factory.CreateClient();
        
        // Get the test database context
        var scope = _factory.Services.CreateScope();
        _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Seed test data
        SeedTestData().Wait();
    }

    private async Task SeedTestData()
    {
        await TestDbContextFactory.SeedTestDataAsync(_context);
    }

    [Fact]
    public async Task GetUsers_ShouldReturnOkWithUserList()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<UserSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(3); // admin, manager, auditor
        result.TotalCount.Should().Be(3);
    }

    [Fact]
    public async Task GetUsers_WithSearchTerm_ShouldFilterResults()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/users?searchTerm=admin");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<UserSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(1);
        result.Items.First().Username.Should().Be("admin");
    }

    [Fact]
    public async Task GetUsers_WithRoleFilter_ShouldFilterByRole()
    {
        // Act
        var response = await _client.GetAsync($"/api/v1/users?role={UserRole.ProcessOwner}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<UserSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(1);
        result.Items.First().Role.Should().Be(UserRole.ProcessOwner);
    }

    [Fact]
    public async Task GetUser_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var existingUser = await _context.Users.FirstAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/users/{existingUser.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<UserDto>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Id.Should().Be(existingUser.Id);
        result.Username.Should().Be(existingUser.Username);
    }

    [Fact]
    public async Task GetUser_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/users/invalid-id");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateUser_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Username = "newuser",
            FirstName = "New",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            AdObjectGuid = Guid.NewGuid().ToString(),
            AdDistinguishedName = "CN=New User,OU=Users,DC=test,DC=com"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/users", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var userId = await response.Content.ReadAsStringAsync();
        userId.Should().NotBeNullOrEmpty();
        
        // Verify user was created
        var createdUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId.Trim('"'));
        createdUser.Should().NotBeNull();
        createdUser!.Username.Should().Be(command.Username);
    }

    [Fact]
    public async Task CreateUser_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Username = "", // Invalid - empty username
            FirstName = "New",
            LastName = "User",
            Email = "invalid-email", // Invalid email format
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/users", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateUser_WithDuplicateUsername_ShouldReturnConflict()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Username = "admin", // Duplicate username
            FirstName = "New",
            LastName = "Admin",
            Email = "<EMAIL>",
            Role = UserRole.DevAdmin,
            FactoryId = null,
            IsActive = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/users", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Conflict);
    }

    [Fact]
    public async Task UpdateUser_WithValidData_ShouldReturnNoContent()
    {
        // Arrange
        var existingUser = await _context.Users.FirstAsync(u => u.Username == "auditor");
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.ProcessOwner,
            FactoryId = 1,
            IsActive = false,
            RecordVersion = existingUser.RecordVersion
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/v1/users/{existingUser.Id}", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify user was updated
        var updatedUser = await _context.Users.FirstAsync(u => u.Id == existingUser.Id);
        updatedUser.FirstName.Should().Be(command.FirstName);
        updatedUser.LastName.Should().Be(command.LastName);
        updatedUser.Email.Should().Be(command.Email);
        updatedUser.IsActive.Should().Be(command.IsActive);
    }

    [Fact]
    public async Task UpdateUser_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var command = new UpdateUserCommand
        {
            Id = "invalid-id",
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.ProcessOwner,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = 1
        };

        // Act
        var response = await _client.PutAsJsonAsync("/api/v1/users/invalid-id", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task UpdateUser_WithStaleVersion_ShouldReturnConflict()
    {
        // Arrange
        var existingUser = await _context.Users.FirstAsync(u => u.Username == "auditor");
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.ProcessOwner,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion - 1 // Stale version
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/v1/users/{existingUser.Id}", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Conflict);
    }

    [Fact]
    public async Task DeleteUser_WithValidId_ShouldReturnNoContent()
    {
        // Arrange
        var existingUser = await _context.Users.FirstAsync(u => u.Username == "auditor");

        // Act
        var response = await _client.DeleteAsync($"/api/v1/users/{existingUser.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify user was soft deleted
        var deletedUser = await _context.Users.FirstAsync(u => u.Id == existingUser.Id);
        deletedUser.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task DeleteUser_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.DeleteAsync("/api/v1/users/invalid-id");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetUsers_WithPagination_ShouldReturnCorrectPage()
    {
        // Act
        var response = await _client.GetAsync("/api/v1/users?pageNumber=1&pageSize=2");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PaginatedResult<UserSummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Items.Should().HaveCount(2);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(2);
        result.TotalCount.Should().Be(3);
        result.TotalPages.Should().Be(2);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _client?.Dispose();
    }
}
