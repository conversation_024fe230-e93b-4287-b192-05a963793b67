@using Microsoft.AspNetCore.Components
@inject NavigationManager Navigation
@inject ILogger<RedirectToLogin> <PERSON><PERSON>

@code {
    protected override void OnInitialized()
    {
        try
        {
            var returnUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
            
            // Don't redirect if already on login page
            if (returnUrl.StartsWith("login", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }

            var loginUrl = returnUrl == "" ? "/login" : $"/login?returnUrl={Uri.EscapeDataString(returnUrl)}";
            
            Logger.LogInformation("Redirecting unauthenticated user to login page. Return URL: {ReturnUrl}", returnUrl);
            Navigation.NavigateTo(loginUrl, forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error redirecting to login page");
            Navigation.NavigateTo("/login", forceLoad: true);
        }
    }
}
