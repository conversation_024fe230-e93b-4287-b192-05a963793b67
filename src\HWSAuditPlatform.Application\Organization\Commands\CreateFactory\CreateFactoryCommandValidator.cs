using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateFactory;

/// <summary>
/// Validator for CreateFactoryCommand
/// </summary>
public class CreateFactoryCommandValidator : AbstractValidator<CreateFactoryCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateFactoryCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.FactoryName)
            .NotEmpty().WithMessage("Factory name is required")
            .MaximumLength(150).WithMessage("Factory name must not exceed 150 characters");

        RuleFor(x => x.FactoryProcess)
            .MaximumLength(2000).WithMessage("Factory process must not exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.FactoryProcess));

        // RuleFor(x => x.LocationId)
        //     .GreaterThan(0).WithMessage("Location ID must be greater than 0")
        //     .MustAsync(BeValidLocation).WithMessage("Location does not exist or is not active");

        RuleFor(x => x.AddressLine1)
            .MaximumLength(255).WithMessage("Address line 1 must not exceed 255 characters")
            .When(x => !string.IsNullOrEmpty(x.AddressLine1));

        RuleFor(x => x.City)
            .MaximumLength(100).WithMessage("City must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.City));

        RuleFor(x => x.PostalCode)
            .MaximumLength(20).WithMessage("Postal code must not exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.PostalCode));
    }

    // private async Task<bool> BeValidLocation(int locationId, CancellationToken cancellationToken)
    // {
    //     return await _context.Locations.AnyAsync(l => l.Id == locationId && l.IsActive, cancellationToken);
    // }
}
