# HWS Audit Platform - Developer Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Development Environment Setup](#development-environment-setup)
3. [Project Structure](#project-structure)
4. [Development Workflow](#development-workflow)
5. [Adding New Features](#adding-new-features)
6. [Database Management](#database-management)
7. [Testing Guidelines](#testing-guidelines)
8. [Code Standards](#code-standards)
9. [Debugging Tips](#debugging-tips)
10. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites
- **.NET 9 SDK** - Latest version
- **Visual Studio 2024** or **VS Code** with C# extension
- **SQL Server** - LocalDB, Express, or full version
- **Git** - Version control


### Quick Start
```bash
# Clone the repository
git clone https://github.com/company/hws-audit-platform.git
cd hws-audit-platform

# Restore packages
dotnet restore

# Set up database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Run the application
dotnet run --project src/HWSAuditPlatform.ApiService
```

## Development Environment Setup

### 1. Database Configuration

**appsettings.Development.json**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  }
}
```

### 2. File Storage Configuration

**Local Development (Recommended)**
```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}


### 3. Active Directory Configuration

**Development (Optional)**
```json
{
  "ActiveDirectory": {
    "Domain": "dev.local",
    "Username": "<EMAIL>",
    "Password": "dev-password",
    "SearchBase": "OU=Users,DC=dev,DC=local"
  }
}
```

### 4. User Secrets Setup
```bash
# Initialize user secrets
dotnet user-secrets init --project src/HWSAuditPlatform.ApiService

# Add sensitive configuration
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Server=localhost;Database=HWSAuditPlatformDb;Trusted_Connection=true" --project src/HWSAuditPlatform.ApiService
```

## Project Structure

### Solution Architecture
```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Domain entities and business logic
│   ├── HWSAuditPlatform.Application/     # Use cases and application logic
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns implementation
│   ├── HWSAuditPlatform.ApiService/      # Web API and controllers
│   └── HWSAuditPlatform.Web/            # Blazor web application (future)
├── tests/
│   ├── HWSAuditPlatform.Domain.Tests/
│   ├── HWSAuditPlatform.Application.Tests/
│   ├── HWSAuditPlatform.Infrastructure.Tests/
│   └── HWSAuditPlatform.Integration.Tests/
├── docs/                                 # Documentation
└── scripts/                             # Deployment and utility scripts
```

### Layer Dependencies
```
ApiService → Application → Domain
         ↘ Infrastructure → Domain
```

**Key Principles:**
- **Domain** has no dependencies
- **Application** depends only on Domain
- **Infrastructure** implements Application interfaces
- **ApiService** orchestrates all layers

## Development Workflow

### 1. Feature Development Process

**Step 1: Domain First**
```csharp
// 1. Create or modify domain entities
public class NewEntity : AuditableEntity<string>
{
    public string Name { get; private set; }
    
    public static NewEntity Create(string name, string createdByUserId)
    {
        var entity = new NewEntity { Name = name };
        entity.AddDomainEvent(new NewEntityCreatedEvent(entity.Id));
        return entity;
    }
}
```

**Step 2: Application Layer**
```csharp
// 2. Create DTOs
public class NewEntityDto : AuditableDto<string>
{
    public string Name { get; set; }
}

// 3. Create commands/queries
public class CreateNewEntityCommand : BaseCommand<string>
{
    public string Name { get; set; }
}

// 4. Create handlers
public class CreateNewEntityCommandHandler : BaseCommandHandler<CreateNewEntityCommand, string>
{
    // Implementation
}

// 5. Create validators
public class CreateNewEntityCommandValidator : AbstractValidator<CreateNewEntityCommand>
{
    // Validation rules
}
```

**Step 3: Infrastructure Layer**
```csharp
// 6. Create entity configuration
public class NewEntityConfiguration : IEntityTypeConfiguration<NewEntity>
{
    public void Configure(EntityTypeBuilder<NewEntity> builder)
    {
        // EF configuration
    }
}

// 7. Add DbSet to ApplicationDbContext
public DbSet<NewEntity> NewEntities => Set<NewEntity>();
```

**Step 4: API Layer**
```csharp
// 8. Create controller
[ApiController]
[Route("api/[controller]")]
public class NewEntitiesController : ControllerBase
{
    [HttpPost]
    public async Task<ActionResult<string>> Create(CreateNewEntityCommand command)
    {
        var id = await _mediator.Send(command);
        return CreatedAtAction(nameof(Get), new { id }, id);
    }
}
```

### 2. Database Migration Workflow

```bash
# 1. Add migration
dotnet ef migrations add AddNewEntity --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# 2. Review generated migration
# Check the generated files in Infrastructure/Migrations/

# 3. Update database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

```

## Adding New Features

### Example: Adding Notification System

**1. Domain Layer**
```csharp
// Domain/Entities/Notifications/Notification.cs
public class Notification : AuditableEntity<string>
{
    public string Title { get; private set; }
    public string Message { get; private set; }
    public NotificationType Type { get; private set; }
    public string UserId { get; private set; }
    public bool IsRead { get; private set; }

    public static Notification Create(string title, string message, NotificationType type, string userId)
    {
        var notification = new Notification
        {
            Id = Cuid.Generate(),
            Title = title,
            Message = message,
            Type = type,
            UserId = userId,
            IsRead = false
        };

        notification.AddDomainEvent(new NotificationCreatedEvent(notification.Id, notification.UserId));
        return notification;
    }

    public void MarkAsRead()
    {
        IsRead = true;
        AddDomainEvent(new NotificationReadEvent(Id, UserId));
    }
}
```

**2. Application Layer**
```csharp
// Application/Notifications/DTOs/NotificationDto.cs
public class NotificationDto : AuditableDto<string>
{
    public string Title { get; set; }
    public string Message { get; set; }
    public NotificationType Type { get; set; }
    public string UserId { get; set; }
    public bool IsRead { get; set; }
}

// Application/Notifications/Commands/CreateNotification/CreateNotificationCommand.cs
public class CreateNotificationCommand : BaseCommand<string>
{
    public string Title { get; set; }
    public string Message { get; set; }
    public NotificationType Type { get; set; }
    public string UserId { get; set; }
}

// Application/Notifications/Commands/CreateNotification/CreateNotificationCommandHandler.cs
public class CreateNotificationCommandHandler : BaseCommandHandler<CreateNotificationCommand, string>
{
    public override async Task<string> Handle(CreateNotificationCommand request, CancellationToken cancellationToken)
    {
        var notification = Notification.Create(request.Title, request.Message, request.Type, request.UserId);
        
        await _context.Notifications.AddAsync(notification, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        
        return notification.Id;
    }
}
```

**3. Infrastructure Layer**
```csharp
// Infrastructure/Persistence/Configurations/NotificationConfiguration.cs
public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.ToTable("Notifications");
        
        builder.HasKey(n => n.Id);
        builder.Property(n => n.Id).HasMaxLength(25);
        
        builder.Property(n => n.Title).HasMaxLength(200).IsRequired();
        builder.Property(n => n.Message).HasColumnType("text").IsRequired();
        builder.Property(n => n.Type).HasConversion<string>();
        
        builder.HasIndex(n => new { n.UserId, n.IsRead });
        builder.HasIndex(n => n.CreatedAt);
    }
}

// Add to ApplicationDbContext
public DbSet<Notification> Notifications => Set<Notification>();
```

**4. API Layer**
```csharp
// ApiService/Controllers/NotificationsController.cs
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class NotificationsController : ControllerBase
{
    [HttpGet("my-notifications")]
    public async Task<ActionResult<PaginatedResult<NotificationDto>>> GetMyNotifications(
        [FromQuery] GetMyNotificationsQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPost("{id}/mark-read")]
    public async Task<ActionResult> MarkAsRead(string id)
    {
        var command = new MarkNotificationAsReadCommand { NotificationId = id };
        await _mediator.Send(command);
        return NoContent();
    }
}
```

## Database Management

### Migration Best Practices

**1. Naming Conventions**
```bash
# Good migration names
dotnet ef migrations add AddUserTable
dotnet ef migrations add UpdateAuditStatusEnum
dotnet ef migrations add AddIndexesToAuditAnswers

# Avoid generic names
dotnet ef migrations add Update1
dotnet ef migrations add Changes
```

**2. Review Migrations**
Always review generated migrations before applying:
```csharp
// Check for:
// - Correct column types
// - Proper indexes
// - Foreign key constraints
// - Data migration scripts if needed

public partial class AddUserTable : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "Users",
            columns: table => new
            {
                Id = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                Username = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                // ... other columns
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Users", x => x.Id);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Users_Username",
            table: "Users",
            column: "Username",
            unique: true);
    }
}
```

**3. Data Migrations**
```csharp
// For data migrations, use raw SQL
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Schema changes first
    migrationBuilder.AddColumn<string>(
        name: "NewColumn",
        table: "Users",
        nullable: true);

    // Data migration
    migrationBuilder.Sql(@"
        UPDATE Users 
        SET NewColumn = 'DefaultValue' 
        WHERE NewColumn IS NULL
    ");

    // Make column non-nullable after data migration
    migrationBuilder.AlterColumn<string>(
        name: "NewColumn",
        table: "Users",
        nullable: false,
        oldNullable: true);
}
```

### Database Seeding

**Development Data**
```csharp
// Infrastructure/Persistence/ApplicationDbContextSeed.cs
public static class ApplicationDbContextSeed
{
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        if (!context.Roles.Any())
        {
            await context.Roles.AddRangeAsync(
                new Role { RoleName = UserRole.Admin, Description = "System Administrator" },
                new Role { RoleName = UserRole.Manager, Description = "Audit Manager" },
                new Role { RoleName = UserRole.Auditor, Description = "Auditor" }
            );
        }

        if (!context.Users.Any())
        {
            var adminRole = await context.Roles.FirstAsync(r => r.RoleName == UserRole.Admin);
            await context.Users.AddAsync(User.Create(
                "admin",
                "System",
                "Administrator",
                "<EMAIL>",
                adminRole.Id,
                null,
                true,
                null,
                null,
                "system"
            ));
        }

        await context.SaveChangesAsync();
    }
}
```

## Testing Guidelines

### Unit Testing Structure
```
Tests/
├── Domain.Tests/
│   ├── Entities/
│   │   ├── UserTests.cs
│   │   └── AuditTests.cs
│   └── ValueObjects/
│       └── AddressTests.cs
├── Application.Tests/
│   ├── Users/
│   │   ├── Commands/
│   │   │   └── CreateUserCommandHandlerTests.cs
│   │   └── Queries/
│   │       └── GetUsersQueryHandlerTests.cs
│   └── Common/
│       └── MappingTests.cs
└── Integration.Tests/
    ├── Controllers/
    │   └── UsersControllerTests.cs
    └── Infrastructure/
        └── RepositoryTests.cs
```

### Testing Examples

**Domain Entity Test**
```csharp
public class UserTests
{
    [Fact]
    public void Create_WithValidData_ShouldCreateUser()
    {
        // Arrange
        var username = "testuser";
        var email = "<EMAIL>";

        // Act
        var user = User.Create(username, "Test", "User", email, 1, null, true, null, null, "creator");

        // Assert
        user.Username.Should().Be(username);
        user.Email.Should().Be(email);
        user.IsActive.Should().BeTrue();
        user.DomainEvents.Should().ContainSingle(e => e is UserCreatedEvent);
    }
}
```

**Application Handler Test**
```csharp
public class CreateUserCommandHandlerTests
{
    private readonly Mock<IApplicationDbContext> _mockContext;
    private readonly Mock<ICurrentUserService> _mockCurrentUser;
    private readonly CreateUserCommandHandler _handler;

    public CreateUserCommandHandlerTests()
    {
        _mockContext = new Mock<IApplicationDbContext>();
        _mockCurrentUser = new Mock<ICurrentUserService>();
        _handler = new CreateUserCommandHandler(_mockContext.Object, _mockCurrentUser.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateUser()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Username = "testuser",
            FirstName = "Test",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor
        };

        _mockCurrentUser.Setup(x => x.UserId).Returns("creator-id");

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        _mockContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
```

## Code Standards

### Naming Conventions
- **Classes**: PascalCase (`UserService`, `CreateUserCommand`)
- **Methods**: PascalCase (`GetUserAsync`, `ValidateInput`)
- **Properties**: PascalCase (`FirstName`, `IsActive`)
- **Fields**: camelCase with underscore (`_context`, `_logger`)
- **Parameters**: camelCase (`userId`, `cancellationToken`)
- **Constants**: PascalCase (`MaxFileSize`, `DefaultPageSize`)

### File Organization
```csharp
// File header (if required)
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Domain.Entities;

namespace HWSAuditPlatform.Application.Users.Commands.CreateUser;

/// <summary>
/// Command handler for creating new users
/// </summary>
public class CreateUserCommandHandler : BaseCommandHandler<CreateUserCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    public CreateUserCommandHandler(
        IApplicationDbContext context,
        ILogger<CreateUserCommandHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Implementation
    }
}
```

### Documentation Standards
```csharp
/// <summary>
/// Creates a new user in the system
/// </summary>
/// <param name="request">The user creation request</param>
/// <param name="cancellationToken">Cancellation token</param>
/// <returns>The ID of the created user</returns>
/// <exception cref="ValidationException">Thrown when validation fails</exception>
/// <exception cref="ConflictException">Thrown when username or email already exists</exception>
public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
```

## Debugging Tips

### Logging Configuration
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information",
      "HWSAuditPlatform": "Debug"
    }
  }
}
```

### Common Debugging Scenarios

**1. EF Core Query Issues**
```csharp
// Enable sensitive data logging in development
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    if (_environment.IsDevelopment())
    {
        optionsBuilder.EnableSensitiveDataLogging();
        optionsBuilder.EnableDetailedErrors();
    }
}
```

**2. MediatR Pipeline Issues**
```csharp
// Add logging to pipeline behaviors
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling {RequestName} with data: {@Request}", typeof(TRequest).Name, request);
        
        var response = await next();
        
        _logger.LogInformation("Handled {RequestName} with response: {@Response}", typeof(TRequest).Name, response);
        
        return response;
    }
}
```

## Troubleshooting

### Common Issues

**1. Migration Issues**
```bash
# Reset migrations (development only)
dotnet ef database drop --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
dotnet ef migrations remove --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
dotnet ef migrations add InitialCreate --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

**2. Dependency Injection Issues**
```csharp
// Check service registration order
services.AddApplication();      // Must be before Infrastructure
services.AddInfrastructure(configuration);

// Verify interface implementations
services.AddScoped<IRepository<User, string>, Repository<User, string>>();
```

**3. File Storage Issues**
```bash
# Check file permissions for local storage
# Verify file storage configuration in appsettings.json
# Verify connection strings are correct
```

This developer guide provides comprehensive guidance for working with the HWS Audit Platform codebase effectively.
