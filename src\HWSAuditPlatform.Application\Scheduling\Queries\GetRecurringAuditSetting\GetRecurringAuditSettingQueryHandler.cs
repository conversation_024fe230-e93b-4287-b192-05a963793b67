using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSetting;

/// <summary>
/// Handler for GetRecurringAuditSettingQuery
/// </summary>
public class GetRecurringAuditSettingQueryHandler : BaseQueryHandler<GetRecurringAuditSettingQuery, RecurringAuditSettingDto>
{
    private readonly IApplicationDbContext _context;

    public GetRecurringAuditSettingQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<RecurringAuditSettingDto> Handle(GetRecurringAuditSettingQuery request, CancellationToken cancellationToken)
    {
        var setting = await _context.RecurringAuditSettings
            .Include(r => r.AuditTemplate)
            .Include(r => r.RecurrenceRule)
            .Include(r => r.AssignToUser)
            .Include(r => r.AssignToUserGroup)
            .Include(r => r.Factory)
            .Include(r => r.Area)
            .Include(r => r.SubArea)
            .FirstOrDefaultAsync(r => r.Id == request.Id && r.IsEnabled, cancellationToken);

        if (setting == null)
        {
            throw new NotFoundException(nameof(RecurringAuditSetting), request.Id);
        }

        return MapToDto(setting);
    }

    private static RecurringAuditSettingDto MapToDto(RecurringAuditSetting setting)
    {
        return new RecurringAuditSettingDto
        {
            Id = setting.Id,
            SettingName = setting.SettingName,
            AuditTemplateId = setting.AuditTemplateId,
            AuditTemplateName = setting.AuditTemplate?.TemplateName,
            DeadlineDays = setting.DeadlineDays,
            IsEnabled = setting.IsEnabled,
            AssignmentType = setting.AssignmentType,
            AssignToUserId = setting.AssignToUserId,
            AssignToUserName = setting.AssignToUser != null
                ? $"{setting.AssignToUser.FirstName} {setting.AssignToUser.LastName}".Trim()
                : null,
            AssignToUserGroupId = setting.AssignToUserGroupId,
            AssignToUserGroupName = setting.AssignToUserGroup?.GroupName,
            FactoryId = setting.FactoryId,
            FactoryName = setting.Factory?.FactoryName,
            AreaId = setting.AreaId,
            AreaName = setting.Area?.AreaName,
            SubAreaId = setting.SubAreaId,
            SubAreaName = setting.SubArea?.SubAreaName,
            NextGenerationDate = setting.NextGenerationDate,
            LastGeneratedAt = setting.LastGeneratedAt,
            IsReadyToGenerate = setting.IsEnabled && 
                               setting.NextGenerationDate.HasValue && 
                               setting.NextGenerationDate.Value <= DateOnly.FromDateTime(DateTime.UtcNow),
            HasValidAssignment = ValidateAssignment(setting),
            RecurrenceRule = setting.RecurrenceRule != null ? MapRecurrenceRuleToDto(setting.RecurrenceRule) : null,
            CreatedAt = setting.CreatedAt,
            UpdatedAt = setting.UpdatedAt,
            RecordVersion = setting.RecordVersion
        };
    }

    private static RecurrenceRuleDto MapRecurrenceRuleToDto(RecurrenceRule rule)
    {
        return new RecurrenceRuleDto
        {
            Id = rule.Id,
            FrequencyType = rule.FrequencyType,
            Interval = rule.Interval,
            StartDate = rule.StartDate,
            EndDate = rule.EndDate,
            WeeklyDaysOfWeekMask = rule.WeeklyDaysOfWeekMask,
            MonthlyDayOfMonth = rule.MonthlyDayOfMonth,
            MonthlyNthWeek = rule.MonthlyNthWeek,
            MonthlyDayOfWeek = rule.MonthlyDayOfWeek,
            YearlyMonth = rule.YearlyMonth,
            YearlyDayOfMonth = rule.YearlyDayOfMonth,
            YearlyNthWeek = rule.YearlyNthWeek,
            YearlyDayOfWeek = rule.YearlyDayOfWeek,
            Description = GenerateRecurrenceDescription(rule),
            IsValid = ValidateRecurrenceRule(rule),
            CreatedAt = rule.CreatedAt,
            UpdatedAt = rule.UpdatedAt
        };
    }

    private static bool ValidateAssignment(RecurringAuditSetting setting)
    {
        return setting.AssignmentType switch
        {
            Domain.Enums.AssignmentType.Individual => !string.IsNullOrEmpty(setting.AssignToUserId) && setting.AssignToUser != null,
            Domain.Enums.AssignmentType.GroupAny or Domain.Enums.AssignmentType.GroupAllScheduled => 
                !string.IsNullOrEmpty(setting.AssignToUserGroupId) && setting.AssignToUserGroup != null,
            _ => false
        };
    }

    private static bool ValidateRecurrenceRule(RecurrenceRule rule)
    {
        // Basic validation - could be expanded
        if (rule.Interval <= 0)
            return false;

        if (rule.EndDate.HasValue && rule.EndDate.Value <= rule.StartDate)
            return false;

        return rule.FrequencyType switch
        {
            Domain.Enums.FrequencyType.WEEKLY => rule.WeeklyDaysOfWeekMask.HasValue && rule.WeeklyDaysOfWeekMask.Value > 0,
            Domain.Enums.FrequencyType.MONTHLY => rule.MonthlyDayOfMonth.HasValue || 
                                                  (rule.MonthlyNthWeek.HasValue && rule.MonthlyDayOfWeek.HasValue),
            Domain.Enums.FrequencyType.YEARLY => rule.YearlyMonth.HasValue && 
                                                 (rule.YearlyDayOfMonth.HasValue || 
                                                  (rule.YearlyNthWeek.HasValue && rule.YearlyDayOfWeek.HasValue)),
            _ => true
        };
    }

    private static string GenerateRecurrenceDescription(RecurrenceRule rule)
    {
        return rule.FrequencyType switch
        {
            Domain.Enums.FrequencyType.DAILY => $"Every {rule.Interval} day(s)",
            Domain.Enums.FrequencyType.WEEKLY => GenerateWeeklyDescription(rule),
            Domain.Enums.FrequencyType.MONTHLY => GenerateMonthlyDescription(rule),
            Domain.Enums.FrequencyType.YEARLY => GenerateYearlyDescription(rule),
            _ => "Unknown frequency"
        };
    }

    private static string GenerateWeeklyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} week(s)";
        
        if (rule.WeeklyDaysOfWeekMask.HasValue)
        {
            var days = new List<string>();
            var mask = rule.WeeklyDaysOfWeekMask.Value;
            var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
            
            for (int i = 0; i < 7; i++)
            {
                if ((mask & (1 << i)) != 0)
                {
                    days.Add(dayNames[i]);
                }
            }
            
            if (days.Any())
            {
                description += $" on {string.Join(", ", days)}";
            }
        }
        
        return description;
    }

    private static string GenerateMonthlyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} month(s)";
        
        if (rule.MonthlyDayOfMonth.HasValue)
        {
            description += $" on day {rule.MonthlyDayOfMonth.Value}";
        }
        else if (rule.MonthlyNthWeek.HasValue && rule.MonthlyDayOfWeek.HasValue)
        {
            var weekNames = new[] { "", "1st", "2nd", "3rd", "4th", "5th" };
            var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
            
            if (rule.MonthlyNthWeek.Value <= 5 && rule.MonthlyDayOfWeek.Value <= 6)
            {
                description += $" on the {weekNames[rule.MonthlyNthWeek.Value]} {dayNames[rule.MonthlyDayOfWeek.Value]}";
            }
        }
        
        return description;
    }

    private static string GenerateYearlyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} year(s)";
        
        if (rule.YearlyMonth.HasValue)
        {
            var monthNames = new[] { "", "January", "February", "March", "April", "May", "June",
                                   "July", "August", "September", "October", "November", "December" };
            
            if (rule.YearlyMonth.Value <= 12)
            {
                description += $" in {monthNames[rule.YearlyMonth.Value]}";
                
                if (rule.YearlyDayOfMonth.HasValue)
                {
                    description += $" on day {rule.YearlyDayOfMonth.Value}";
                }
                else if (rule.YearlyNthWeek.HasValue && rule.YearlyDayOfWeek.HasValue)
                {
                    var weekNames = new[] { "", "1st", "2nd", "3rd", "4th", "5th" };
                    var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
                    
                    if (rule.YearlyNthWeek.Value <= 5 && rule.YearlyDayOfWeek.Value <= 6)
                    {
                        description += $" on the {weekNames[rule.YearlyNthWeek.Value]} {dayNames[rule.YearlyDayOfWeek.Value]}";
                    }
                }
            }
        }
        
        return description;
    }
}
