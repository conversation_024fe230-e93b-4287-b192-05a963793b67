using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for AreaResponsibility entity
/// </summary>
public class AreaResponsibilityConfiguration : IEntityTypeConfiguration<AreaResponsibility>
{
    public void Configure(EntityTypeBuilder<AreaResponsibility> builder)
    {
        builder.ToTable("hwsap_org_area_responsibilities");

        // Primary Key - CUID with consistent length
        builder.HasKey(ar => ar.Id);
        builder.Property(ar => ar.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(ar => ar.AreaId)
            .IsRequired();

        builder.Property(ar => ar.ResponsibilityType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(ar => ar.ResponsibleUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(ar => ar.AuditTemplateId)
            .IsRequired(false);

        builder.Property(ar => ar.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(ar => ar.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(ar => ar.Priority)
            .IsRequired()
            .HasDefaultValue(0);

        // Auditable properties
        builder.Property(ar => ar.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ar => ar.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ar => ar.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(ar => ar.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(ar => ar.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(ar => ar.AreaId)
            .HasDatabaseName("IX_AreaResponsibilities_AreaId");

        builder.HasIndex(ar => ar.ResponsibleUserId)
            .HasDatabaseName("IX_AreaResponsibilities_ResponsibleUserId");

        builder.HasIndex(ar => ar.AuditTemplateId)
            .HasDatabaseName("IX_AreaResponsibilities_AuditTemplateId");

        builder.HasIndex(ar => new { ar.AreaId, ar.ResponsibilityType, ar.AuditTemplateId })
            .HasDatabaseName("IX_AreaResponsibilities_Area_Type_Template");

        builder.HasIndex(ar => ar.IsActive)
            .HasDatabaseName("IX_AreaResponsibilities_IsActive");

        // Relationships
        builder.HasOne(ar => ar.Area)
            .WithMany(a => a.AreaResponsibilities)
            .HasForeignKey(ar => ar.AreaId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ar => ar.ResponsibleUser)
            .WithMany()
            .HasForeignKey(ar => ar.ResponsibleUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(ar => ar.AuditTemplate)
            .WithMany(at => at.AreaResponsibilities)
            .HasForeignKey(ar => ar.AuditTemplateId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired(false);

        // Ignore computed properties
        builder.Ignore(ar => ar.IsTemplateSpecific);
        builder.Ignore(ar => ar.IsEffective);

        // Ignore domain events
        builder.Ignore(ar => ar.DomainEvents);
    }
}
