using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Scheduling.Commands.DeleteRecurringAuditSetting;

/// <summary>
/// Command to soft delete a recurring audit setting
/// </summary>
public class DeleteRecurringAuditSettingCommand : BaseCommand
{
    /// <summary>
    /// The ID of the recurring audit setting to delete
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }

    public DeleteRecurringAuditSettingCommand(string id, int recordVersion)
    {
        Id = id;
        RecordVersion = recordVersion;
    }
}
