@page "/audit-results/{auditId}"
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IAuditApiService AuditService
@inject ICorrectionRequestService CorrectionRequestService
@inject NavigationManager Navigation
@inject ILogger<AuditResults> Logger

<PageTitle>Audit Results - HWS Audit PWA</PageTitle>

<div class="audit-results-container">
    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading audit results...</p>
        </div>
    }
    else if (audit == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Audit Not Found</h4>
            <p>The requested audit could not be found or you don't have permission to view it.</p>
            <hr>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" @onclick="GoBackToAudits">
                    <i class="fas fa-arrow-left me-2"></i>Back to Audits
                </button>
            </div>
        </div>
    }
    else
    {
        <!-- Header Section -->
        <div class="card mb-4 industrial-audit-results-card">
            <div class="card-header industrial-audit-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="fas fa-clipboard-check me-2"></i>
                            @audit.AuditTemplateName
                        </h4>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            @audit.LocationName
                            <span class="ms-3">
                                <i class="fas fa-calendar me-1"></i>
                                Completed: @audit.CompletedAt?.ToString("MMM dd, yyyy HH:mm")
                            </span>
                        </p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-@GetStatusBadgeClass() fs-6 mb-2">
                            @audit.OverallStatus
                        </span>
                        @if (audit.OverallScore.HasValue)
                        {
                            <div class="h3 mb-0">
                                <span class="badge bg-@GetScoreBadgeClass()">
                                    @audit.OverallScore.Value.ToString("F1")%
                                </span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-primary">
                            @GetTotalQuestions()
                        </div>
                        <h6 class="card-title">Total Questions</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-success">
                            @GetAnsweredQuestions()
                        </div>
                        <h6 class="card-title">Answered</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-info">
                            @GetEvidenceCount()
                        </div>
                        <h6 class="card-title">Evidence Items</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-warning">
                            @GetNonConformanceCount()
                        </div>
                        <h6 class="card-title">Non-Conformances</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics (if available) -->
        @if (audit.PassedQuestions.HasValue || audit.FailedQuestions.HasValue)
        {
            <div class="row mb-4">
                @if (audit.PassedQuestions.HasValue)
                {
                    <div class="col-md-3 col-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2 text-success">
                                    @audit.PassedQuestions.Value
                                </div>
                                <h6 class="card-title">Passed Questions</h6>
                            </div>
                        </div>
                    </div>
                }
                @if (audit.FailedQuestions.HasValue)
                {
                    <div class="col-md-3 col-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2 text-danger">
                                    @audit.FailedQuestions.Value
                                </div>
                                <h6 class="card-title">Failed Questions</h6>
                            </div>
                        </div>
                    </div>
                }
                @if (audit.NotApplicableQuestions.HasValue && audit.NotApplicableQuestions.Value > 0)
                {
                    <div class="col-md-3 col-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2 text-secondary">
                                    @audit.NotApplicableQuestions.Value
                                </div>
                                <h6 class="card-title">Not Applicable</h6>
                            </div>
                        </div>
                    </div>
                }
                @if (audit.OverallScore.HasValue)
                {
                    <div class="col-md-3 col-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2 text-@GetScoreBadgeClass()">
                                    @audit.OverallScore.Value.ToString("F1")%
                                </div>
                                <h6 class="card-title">Overall Score</h6>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }

        <!-- Audit Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Audit Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Assigned To:</dt>
                            <dd class="col-sm-7">@audit.AssignedToUserName</dd>

                            <dt class="col-sm-5">Scheduled Date:</dt>
                            <dd class="col-sm-7">@audit.ScheduledDate.ToString("MMM dd, yyyy")</dd>

                            <dt class="col-sm-5">Due Date:</dt>
                            <dd class="col-sm-7">@audit.DueDate?.ToString("MMM dd, yyyy")</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Started At:</dt>
                            <dd class="col-sm-7">@audit.StartedAt?.ToString("MMM dd, yyyy HH:mm")</dd>
                            
                            <dt class="col-sm-5">Completed At:</dt>
                            <dd class="col-sm-7">@audit.CompletedAt?.ToString("MMM dd, yyyy HH:mm")</dd>
                            
                            <dt class="col-sm-5">Duration:</dt>
                            <dd class="col-sm-7">@GetAuditDuration()</dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(audit.ManagerComments))
                {
                    <hr>
                    <div class="mt-3">
                        <h6><i class="fas fa-comment me-2"></i>Manager Comments</h6>
                        <div class="alert alert-info">
                            <p class="mb-0">@audit.ManagerComments</p>
                            @if (audit.ReviewedAt.HasValue && !string.IsNullOrEmpty(audit.ReviewedByUserName))
                            {
                                <small class="text-muted d-block mt-2">
                                    <i class="fas fa-user me-1"></i>@audit.ReviewedByUserName
                                    <span class="ms-2">
                                        <i class="fas fa-clock me-1"></i>@audit.ReviewedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                    </span>
                                </small>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Audit Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Audit Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-3"><i class="fas fa-tasks me-2"></i>Question Analysis</h6>
                        <div class="row g-3">
                            @if (audit.PassedQuestions.HasValue)
                            {
                                <div class="col-6">
                                    <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                        <div class="h5 text-success mb-1">@audit.PassedQuestions</div>
                                        <small class="text-muted">Passed</small>
                                    </div>
                                </div>
                            }
                            @if (audit.FailedQuestions.HasValue)
                            {
                                <div class="col-6">
                                    <div class="text-center p-2 bg-danger bg-opacity-10 rounded">
                                        <div class="h5 text-danger mb-1">@audit.FailedQuestions</div>
                                        <small class="text-muted">Failed</small>
                                    </div>
                                </div>
                            }
                            @if (audit.NotApplicableQuestions.HasValue && audit.NotApplicableQuestions > 0)
                            {
                                <div class="col-6">
                                    <div class="text-center p-2 bg-secondary bg-opacity-10 rounded">
                                        <div class="h5 text-secondary mb-1">@audit.NotApplicableQuestions</div>
                                        <small class="text-muted">N/A</small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-3"><i class="fas fa-chart-pie me-2"></i>Completion Status</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Progress</span>
                                <span>@audit.ProgressPercentage.ToString("F1")%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" role="progressbar" style="width: @(audit.ProgressPercentage)%"
                                     aria-valuenow="@audit.ProgressPercentage" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        @if (audit.OverallScore.HasValue)
                        {
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Overall Score</span>
                                    <span>@audit.OverallScore.Value.ToString("F1")%</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-@GetScoreBadgeClass()" role="progressbar"
                                         style="width: @(audit.OverallScore.Value)%"
                                         aria-valuenow="@audit.OverallScore.Value" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                @if (audit.OverallStatus == AuditOverallStatus.Closed)
                {
                    <hr>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Audit Completed</strong> - This audit has been successfully completed and reviewed.
                        @if (audit.CompletedAt.HasValue)
                        {
                            <br><small class="text-muted">Completed on @audit.CompletedAt.Value.ToString("MMMM dd, yyyy 'at' HH:mm")</small>
                        }
                    </div>
                }
                else if (audit.OverallStatus == AuditOverallStatus.Submitted)
                {
                    <hr>
                    <div class="alert alert-info">
                        <i class="fas fa-clock me-2"></i>
                        <strong>Pending Review</strong> - This audit has been submitted and is awaiting manager review.
                    </div>
                }
                else if (audit.OverallStatus == AuditOverallStatus.InProgress)
                {
                    <hr>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>In Progress</strong> - This audit is still being completed.
                    </div>
                }
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex gap-2 justify-content-between">
                    <button class="btn btn-outline-secondary" @onclick="GoBackToAudits">
                        <i class="fas fa-arrow-left me-2"></i>Back to Audits
                    </button>
                    
                    <div class="d-flex gap-2">
                        @if (CanRequestCorrection())
                        {
                            <button class="btn btn-warning" @onclick="ShowCorrectionRequestModal">
                                <i class="fas fa-edit me-2"></i>Request Correction
                            </button>
                        }

                        <button class="btn btn-primary" @onclick="ExportResults">
                            <i class="fas fa-download me-2"></i>Export Results
                        </button>

                        <button class="btn btn-success" @onclick="ShareResults">
                            <i class="fas fa-share me-2"></i>Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Correction Request Modal -->
<HWSAuditPlatform.WebAuditPWA.Components.Audit.CorrectionRequestModal
    IsVisible="@showCorrectionModal"
    AuditSummary="@GetAuditSummary()"
    OnClose="@CloseCorrectionRequestModal"
    OnCorrectionRequested="@HandleCorrectionRequested" />

@code {
    [Parameter] public string AuditId { get; set; } = string.Empty;
    
    private AuditSummaryDto? audit;
    private bool isLoading = true;
    private bool showCorrectionModal = false;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadAuditResults();
    }
    
    private async Task LoadAuditResults()
    {
        try
        {
            isLoading = true;
            Logger.LogInformation("Loading audit results for audit: {AuditId}", AuditId);

            var result = await AuditService.GetAuditForResultsAsync(AuditId);
            if (result.IsSuccess && result.Data != null)
            {
                audit = result.Data;
                Logger.LogInformation("Successfully loaded audit results with {TotalQuestions} total questions, {AnsweredQuestions} answered, {EvidenceCount} evidence items",
                    audit.TotalQuestions, audit.AnsweredQuestions, audit.EvidenceCount);
            }
            else
            {
                Logger.LogWarning("Failed to load audit results: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit results");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void GoBackToAudits()
    {
        Navigation.NavigateTo("/audits");
    }

    private string GetStatusBadgeClass()
    {
        if (audit == null) return "secondary";

        return audit.OverallStatus switch
        {
            AuditOverallStatus.Scheduled => "warning",
            AuditOverallStatus.InProgress => "info",
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.Cancelled => "secondary",
            _ => "secondary"
        };
    }

    private string GetScoreBadgeClass()
    {
        if (audit?.OverallScore == null) return "secondary";

        var score = (double)audit.OverallScore.Value;
        return score switch
        {
            >= 90 => "success",
            >= 80 => "info",
            >= 70 => "warning",
            _ => "danger"
        };
    }

    private int GetTotalQuestions()
    {
        return audit?.TotalQuestions ?? 0;
    }

    private int GetAnsweredQuestions()
    {
        return audit?.AnsweredQuestions ?? 0;
    }

    private int GetEvidenceCount()
    {
        return audit?.EvidenceCount ?? 0;
    }

    private int GetNonConformanceCount()
    {
        return audit?.NonConformanceCount ?? 0;
    }

    private string GetAuditDuration()
    {
        if (audit?.StartedAt == null || audit?.CompletedAt == null)
            return "N/A";

        var duration = audit.CompletedAt.Value - audit.StartedAt.Value;

        if (duration.TotalDays >= 1)
            return $"{duration.Days}d {duration.Hours}h {duration.Minutes}m";
        else if (duration.TotalHours >= 1)
            return $"{duration.Hours}h {duration.Minutes}m";
        else
            return $"{duration.Minutes}m";
    }

    private bool CanRequestCorrection()
    {
        return audit?.OverallStatus == AuditOverallStatus.Submitted ||
               audit?.OverallStatus == AuditOverallStatus.Closed;
    }

    private void ShowCorrectionRequestModal()
    {
        showCorrectionModal = true;
    }

    private void CloseCorrectionRequestModal()
    {
        showCorrectionModal = false;
    }

    private async Task HandleCorrectionRequested(CorrectionRequestDto correctionRequest)
    {
        try
        {
            Logger.LogInformation("Processing correction request for audit: {AuditId}", AuditId);

            var result = await CorrectionRequestService.SubmitCorrectionRequestAsync(correctionRequest);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Correction request submitted successfully: {RequestId}", result.Data);

                // Optionally refresh the audit data to show updated status
                await LoadAuditResults();
            }
            else
            {
                Logger.LogWarning("Failed to submit correction request: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling correction request");
        }
        finally
        {
            showCorrectionModal = false;
        }
    }

    private AuditSummaryDto? GetAuditSummary()
    {
        return audit;
    }

    private Task ExportResults()
    {
        // TODO: Implement export functionality
        Logger.LogInformation("Export requested for audit: {AuditId}", AuditId);

        return Task.CompletedTask;
    }

    private Task ShareResults()
    {
        // TODO: Implement share functionality
        Logger.LogInformation("Share requested for audit: {AuditId}", AuditId);

        return Task.CompletedTask;
    }
}

<style>
    .industrial-audit-results-card {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 12px;
        box-shadow: 0 4px 12px var(--industrial-shadow);
    }

    .industrial-audit-header {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 2px solid var(--industrial-teal);
        color: var(--industrial-text-light);
        padding: 1.5rem;
        border-radius: 12px 12px 0 0;
    }

    .industrial-audit-header h4 {
        color: var(--industrial-text-light);
        text-shadow: 0 2px 4px var(--industrial-shadow);
        margin-bottom: 0.5rem;
    }

    .industrial-audit-header p {
        color: var(--industrial-text);
        opacity: 0.9;
    }

    /* Industrial badge styling for audit results */
    .badge.bg-warning { background-color: var(--industrial-orange) !important; }
    .badge.bg-info { background-color: var(--industrial-teal) !important; }
    .badge.bg-primary { background-color: var(--industrial-blue) !important; }
    .badge.bg-success { background-color: var(--industrial-green) !important; }
    .badge.bg-danger { background-color: var(--industrial-red) !important; }
    .badge.bg-secondary { background-color: var(--industrial-steel) !important; }

    /* Card styling for results sections */
    .card {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 12px;
        box-shadow: 0 2px 8px var(--industrial-shadow-light);
    }

    .card-header {
        background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-steel) 100%);
        border-bottom: 1px solid var(--industrial-border);
        color: var(--industrial-text-light);
        border-radius: 12px 12px 0 0;
    }

    .card-body {
        background-color: var(--industrial-gray);
        color: var(--industrial-text);
    }

    /* Progress bars */
    .progress {
        background-color: var(--industrial-light-gray);
        border-radius: 6px;
        height: 8px;
    }

    .progress-bar {
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
        border-radius: 6px;
    }

    /* Button styling */
    .btn-outline-primary {
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
    }

    .btn-outline-primary:hover {
        background-color: var(--industrial-teal);
        border-color: var(--industrial-teal);
        color: white;
    }

    .btn-outline-secondary {
        border-color: var(--industrial-steel);
        color: var(--industrial-text);
    }

    .btn-outline-secondary:hover {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-steel);
        color: white;
    }
</style>
