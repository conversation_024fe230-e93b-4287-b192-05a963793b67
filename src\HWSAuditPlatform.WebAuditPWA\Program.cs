using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Blazored.LocalStorage;
using HWSAuditPlatform.WebAuditPWA;
using HWSAuditPlatform.WebAuditPWA.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Configure root components
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configure logging
builder.Logging.SetMinimumLevel(LogLevel.Information);

// Configure HTTP client
var apiBaseUrl = builder.Configuration["ApiBaseUrl"] ?? "https://localhost:7490/";
builder.Services.AddScoped(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(apiBaseUrl) };
    httpClient.DefaultRequestHeaders.Add("User-Agent", "HWS-Audit-PWA/1.0");
    return httpClient;
});

// Configure local storage
builder.Services.AddBlazoredLocalStorage();

// Configure authentication
builder.Services.AddAuthorizationCore();

// Check if debug mode is enabled
var skipAuthentication = builder.Configuration.GetValue<bool>("Debug:SkipAuthentication");

if (skipAuthentication)
{
    Console.WriteLine("🚨 DEBUG MODE ENABLED: Using mock authentication and API services");

    // Use debug services
    builder.Services.AddScoped<AuthenticationStateProvider, DebugAuthenticationStateProvider>();
    builder.Services.AddScoped<IAuthenticationService, DebugAuthenticationService>();
}
else
{
    // Use real services
    builder.Services.AddScoped<CustomAuthenticationStateProvider>();
    builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());
    builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
}

builder.Services.AddScoped<AuthenticatedHttpClientService>();

// Configure API services
if (skipAuthentication)
{
    // Use debug API services
    builder.Services.AddScoped<IAuditApiService, DebugAuditApiService>();
    builder.Services.AddScoped<ITemplateApiService, DebugTemplateApiService>();
    builder.Services.AddScoped<IUserApiService, DebugUserApiService>();

    Console.WriteLine("🚨 DEBUG MODE: Mock API services configured");
}
else
{
    // Use real API services
    builder.Services.AddScoped<IAuditApiService>(provider =>
    {
        var httpClient = provider.GetRequiredService<AuthenticatedHttpClientService>();
        var logger = provider.GetRequiredService<ILogger<AuditApiService>>();
        return new AuditApiService(httpClient, logger);
    });
    builder.Services.AddScoped<ITemplateApiService, TemplateApiService>();
    builder.Services.AddScoped<IUserApiService, UserApiService>();
}

// Configure offline services
builder.Services.AddScoped<IOfflineStorageService>(provider =>
{
    var localStorage = provider.GetRequiredService<ILocalStorageService>();
    var logger = provider.GetRequiredService<ILogger<OfflineStorageService>>();
    return new OfflineStorageService(localStorage, logger);
});
builder.Services.AddScoped<ISyncService>(provider =>
{
    var auditService = provider.GetRequiredService<IAuditApiService>();
    var offlineStorage = provider.GetRequiredService<IOfflineStorageService>();
    var localStorage = provider.GetRequiredService<ILocalStorageService>();
    var pwaService = provider.GetRequiredService<IPwaService>();
    var backgroundSyncService = provider.GetRequiredService<IBackgroundSyncService>();
    var logger = provider.GetRequiredService<ILogger<SyncServiceImplementation>>();
    return new SyncServiceImplementation(auditService, offlineStorage, localStorage, pwaService, backgroundSyncService, logger);
});

// Configure PWA services
builder.Services.AddScoped<IPwaService>(provider =>
{
    var jsRuntime = provider.GetRequiredService<IJSRuntime>();
    var logger = provider.GetRequiredService<ILogger<PwaService>>();
    return new PwaService(jsRuntime, logger);
});
builder.Services.AddScoped<ICameraService>(provider =>
{
    var jsRuntime = provider.GetRequiredService<IJSRuntime>();
    var logger = provider.GetRequiredService<ILogger<CameraServiceImplementation>>();
    return new EnhancedCameraService(jsRuntime, logger);
});
builder.Services.AddScoped<IEnhancedCameraService>(provider =>
{
    var jsRuntime = provider.GetRequiredService<IJSRuntime>();
    var logger = provider.GetRequiredService<ILogger<CameraServiceImplementation>>();
    return new EnhancedCameraService(jsRuntime, logger);
});
builder.Services.AddScoped<IBackgroundSyncService>(provider =>
{
    var jsRuntime = provider.GetRequiredService<IJSRuntime>();
    var logger = provider.GetRequiredService<ILogger<BackgroundSyncService>>();
    return new BackgroundSyncService(jsRuntime, logger);
});
builder.Services.AddScoped<ICacheManagementService>(provider =>
{
    var jsRuntime = provider.GetRequiredService<IJSRuntime>();
    var logger = provider.GetRequiredService<ILogger<CacheManagementService>>();
    return new CacheManagementService(jsRuntime, logger);
});
builder.Services.AddScoped<ICorrectionRequestService>(provider =>
{
    var httpClient = provider.GetRequiredService<AuthenticatedHttpClientService>();
    var logger = provider.GetRequiredService<ILogger<CorrectionRequestService>>();
    var offlineStorage = provider.GetRequiredService<IOfflineStorageService>();
    return new CorrectionRequestService(httpClient, logger, offlineStorage);
});
builder.Services.AddScoped<IFindingCategoryService>(provider =>
{
    var httpClient = provider.GetRequiredService<AuthenticatedHttpClientService>();
    var logger = provider.GetRequiredService<ILogger<FindingCategoryService>>();
    return new FindingCategoryService(httpClient, logger);
});

var host = builder.Build();

// Initialize services
var logger = host.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("HWS Audit Platform PWA starting...");

try
{
    await host.RunAsync();
}
catch (Exception ex)
{
    logger.LogCritical(ex, "Application terminated unexpectedly");
    throw;
}
