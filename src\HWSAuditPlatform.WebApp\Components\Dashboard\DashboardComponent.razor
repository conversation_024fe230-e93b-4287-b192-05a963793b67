@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Entities.Users
@inject IAuditApiService AuditService
@inject ITemplateApiService TemplateService
@inject IUserApiService UserService
@inject ILogger<DashboardComponent> Logger
@inject NavigationManager Navigation

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1><i class="bi bi-speedometer2 me-3"></i>Control Center</h1>
        <p class="dashboard-subtitle">Industrial audit monitoring and performance analytics</p>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading dashboard data...</p>
        </div>
    }
    else
    {
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card stat-card-primary">
                    <div class="stat-icon">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@totalAudits</h3>
                        <p>Total Inspections</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-warning">
                    <div class="stat-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@pendingAudits</h3>
                        <p>Pending Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-success">
                    <div class="stat-icon">
                        <i class="bi bi-file-earmark-ruled"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@totalTemplates</h3>
                        <p>Templates</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-info">
                    <div class="stat-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@totalUsers</h3>
                        <p>Active Users</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Quick Actions -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-activity me-2"></i>Recent Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (recentAudits.Any())
                        {
                            <div class="audit-list">
                                @foreach (var audit in recentAudits.Take(5))
                                {
                                    <div class="audit-item">
                                        <div class="audit-info">
                                            <h6 class="audit-title">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</h6>
                                            <p class="audit-details">
                                                <span class="text-muted">@(audit.Factory?.FactoryName ?? "Unknown Factory")</span>
                                                @if (audit.Area?.AreaName != null)
                                                {
                                                    <span class="text-muted"> • @audit.Area.AreaName</span>
                                                }
                                                @if (audit.AssignedToUser?.FullName != null)
                                                {
                                                    <span class="text-primary"> • Assigned to @audit.AssignedToUser.FullName</span>
                                                }
                                            </p>
                                            <small class="text-muted">@audit.ScheduledDate.ToString("MMM dd, yyyy")</small>
                                        </div>
                                        <div class="audit-status">
                                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                                @audit.OverallStatus
                                            </span>
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="text-center mt-3">
                                <a href="/audits" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-arrow-right me-1"></i>View All Audits
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="bi bi-clipboard-x text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mt-2">No audits scheduled yet</p>
                                <button class="btn btn-primary btn-sm" @onclick="NavigateToCreateAudit">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Audit
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning-fill me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" @onclick="NavigateToCreateAudit">
                                <i class="bi bi-plus-circle me-2"></i>Create New Audit
                            </button>
                            <button class="btn btn-outline-primary" @onclick="NavigateToTemplates">
                                <i class="bi bi-file-earmark-ruled me-2"></i>Manage Templates
                            </button>
                            <button class="btn btn-outline-primary" @onclick="NavigateToUsers">
                                <i class="bi bi-people me-2"></i>Manage Personnel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private int totalAudits = 0;
    private int totalTemplates = 0;
    private int totalUsers = 0;
    private int pendingAudits = 0;
    private List<Audit> recentAudits = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            Logger.LogInformation("Loading dashboard data...");

            // Load data from API services
            var auditsResult = await AuditService.GetAuditsAsync();
            var templates = await TemplateService.GetTemplatesAsync();
            var users = await UserService.GetUsersAsync();

            var audits = auditsResult?.ToList() ?? new List<Audit>();
            totalAudits = audits.Count;
            totalTemplates = templates?.Count() ?? 0;
            totalUsers = users?.Count() ?? 0;
            recentAudits = audits.OrderByDescending(a => a.ScheduledDate).ToList();
            pendingAudits = audits.Count(a => a.OverallStatus == Domain.Enums.AuditOverallStatus.PendingManagerReview);

            Logger.LogInformation("Dashboard data loaded successfully. Total audits: {TotalAudits}", totalAudits);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading dashboard data: {Message}", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetStatusBadgeClass(Domain.Enums.AuditOverallStatus status)
    {
        return status switch
        {
            Domain.Enums.AuditOverallStatus.Scheduled => "bg-secondary",
            Domain.Enums.AuditOverallStatus.InProgress => "bg-primary",
            Domain.Enums.AuditOverallStatus.PendingManagerReview => "bg-warning",
            Domain.Enums.AuditOverallStatus.Closed => "bg-success",
            Domain.Enums.AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    // Navigation methods
    private void NavigateToCreateAudit()
    {
        Navigation.NavigateTo("/audits");
    }

    private void NavigateToTemplates()
    {
        Navigation.NavigateTo("/templates");
    }

    private void NavigateToUsers()
    {
        Navigation.NavigateTo("/users");
    }
}
