using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetMyAuditHistory;

/// <summary>
/// Query to get audit history for the current user (completed audits)
/// </summary>
public class GetMyAuditHistoryQuery : BaseQuery<PaginatedResult<AuditSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term to filter audits
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by audit status
    /// </summary>
    public AuditOverallStatus? Status { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Filter by completion date from
    /// </summary>
    public DateTime? CompletedDateFrom { get; set; }

    /// <summary>
    /// Filter by completion date to
    /// </summary>
    public DateTime? CompletedDateTo { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CompletedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
