using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Templates.Queries.GetTemplateQuestions;

/// <summary>
/// Handler for GetTemplateQuestionsQuery
/// </summary>
public class GetTemplateQuestionsQueryHandler : BaseQueryHandler<GetTemplateQuestionsQuery, List<QuestionDto>>
{
    private readonly IApplicationDbContext _context;

    public GetTemplateQuestionsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<QuestionDto>> Handle(GetTemplateQuestionsQuery request, CancellationToken cancellationToken)
    {
        // Verify the audit template exists
        var templateExists = await _context.AuditTemplates
            .AnyAsync(t => t.Id == request.TemplateId && t.IsActive, cancellationToken);

        if (!templateExists)
        {
            throw new NotFoundException(nameof(AuditTemplate), request.TemplateId);
        }

        // Build the query
        var query = _context.Questions
            .Where(q => q.AuditTemplateId == request.TemplateId);

        // Filter by active status
        if (!request.IncludeInactive)
        {
            query = query.Where(q => q.IsActive);
        }

        // Filter by question group if specified
        if (request.QuestionGroupId.HasValue)
        {
            query = query.Where(q => q.QuestionGroupId == request.QuestionGroupId.Value);
        }

        // Include related data and order by display order
        var questions = await query
            .Include(q => q.Options.Where(o => o.IsActive))
            .Include(q => q.QuestionGroup)
            .Include(q => q.ChildQuestions.Where(cq => cq.IsActive))
            .Include(q => q.AllowedEvidenceTypes.Where(aet => aet.IsActive))
            .OrderBy(q => q.DisplayOrder)
            .ToListAsync(cancellationToken);

        // Map to DTOs
        return questions.Select(MapQuestionToDto).ToList();
    }

    private static QuestionDto MapQuestionToDto(Question question)
    {
        return new QuestionDto
        {
            Id = question.Id,
            AuditTemplateId = question.AuditTemplateId,
            QuestionGroupId = question.QuestionGroupId,
            QuestionGroupName = question.QuestionGroup?.GroupName,
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType,
            DisplayOrder = question.DisplayOrder,
            IsRequired = question.IsRequired,
            Weight = question.Weight,
            HelpText = question.HelpText,
            ParentQuestionId = question.ParentQuestionId,
            TriggerAnswerValue = question.TriggerAnswerValue,
            SeverityLevel = question.SeverityLevel,
            EvidenceRequired = question.EvidenceRequired,
            AllowedEvidenceTypes = question.AllowedEvidenceTypes
                .Where(aet => aet.IsActive)
                .OrderBy(aet => aet.DisplayOrder)
                .Select(aet => new EvidenceTypeDto
                {
                    EvidenceType = aet.EvidenceType,
                    DisplayOrder = aet.DisplayOrder,
                    IsActive = aet.IsActive
                })
                .ToList(),
            IsActive = question.IsActive,
            IsConditional = question.IsConditional,
            Options = question.Options
                .OrderBy(o => o.DisplayOrder)
                .Select(o => new QuestionOptionDto
                {
                    Id = o.Id,
                    QuestionId = o.QuestionId,
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                }).ToList(),
            ChildQuestions = question.ChildQuestions
                .Where(cq => cq.IsActive)
                .OrderBy(cq => cq.DisplayOrder)
                .Select(MapQuestionToDto)
                .ToList()
        };
    }
}
