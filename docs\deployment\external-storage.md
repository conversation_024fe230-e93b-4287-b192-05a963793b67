# External File Storage Setup

This document explains how to configure the HWS Audit Platform to use an external drive (e.g., D:\uploads) for file storage instead of the default local wwwroot/uploads folder.

## Overview

The HWS Audit Platform has been configured to store uploaded files on an external drive to:
- Separate file storage from application files
- Provide more storage space
- Enable easier backup and maintenance
- Improve security by isolating user uploads

## Configuration

### Current Settings

The following configuration files have been updated to use external storage:

**Development/Local Environment:**
- `src/HWSAuditPlatform.ApiService/appsettings.json`
- `src/HWSAuditPlatform.ApiService/appsettings.Development.json`
- `src/HWSAuditPlatform.SchedulerWorker/appsettings.Development.json`

**Docker Environment:**
- `src/HWSAuditPlatform.ApiService/appsettings.Docker.json`
- `src/HWSAuditPlatform.SchedulerWorker/appsettings.Docker.json`

### Storage Paths

| Environment | Storage Path |
|-------------|--------------|
| Development/Local | `D:\uploads` |
| Docker | `/app/uploads` (mounted volume) |

## Setup Instructions

### 1. Automated Setup (Recommended)

Run the provided PowerShell script to automatically set up the external storage:

```powershell
# Run as Administrator
.\scripts\setup-external-storage.ps1
```

This script will:
- Check if the D: drive is available
- Create the `D:\uploads` directory
- Set appropriate permissions for IIS_IUSRS
- Test write permissions
- Create necessary subdirectories

### 2. Manual Setup

If you prefer to set up manually:

#### Step 1: Create Directory
```powershell
New-Item -Path "D:\uploads" -ItemType Directory -Force
```

#### Step 2: Set Permissions
```powershell
# Get current ACL
$acl = Get-Acl "D:\uploads"

# Add permissions for IIS_IUSRS
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
    "IIS_IUSRS",
    "FullControl",
    "ContainerInherit,ObjectInherit",
    "None",
    "Allow"
)
$acl.SetAccessRule($accessRule)

# Apply ACL
Set-Acl -Path "D:\uploads" -AclObject $acl
```

#### Step 3: Test Permissions
```powershell
# Test write access
"Test" | Out-File -FilePath "D:\uploads\test.txt"
Remove-Item "D:\uploads\test.txt"
```

## Docker Configuration

For Docker environments, you'll need to mount the external storage as a volume:

### Docker Compose Example

```yaml
version: '3.8'
services:
  apiservice:
    image: hwsauditplatform-apiservice
    volumes:
      - D:\uploads:/app/uploads
    environment:
      - LocalFileStorage__StoragePath=/app/uploads
```

### Docker Run Example

```bash
docker run -v D:\uploads:/app/uploads hwsauditplatform-apiservice
```

## Troubleshooting

### Common Issues

#### 1. Drive Not Available
**Error:** `Drive 'D:\' is not available`
**Solution:** 
- Ensure the external drive is connected and accessible
- Check if the drive letter is correct
- Try accessing the drive manually in File Explorer

#### 2. Permission Denied
**Error:** `Access denied to file storage location`
**Solution:**
- Run the setup script as Administrator
- Manually set permissions for IIS_IUSRS or the application pool identity
- Check Windows Event Logs for detailed error information

#### 3. Directory Not Found
**Error:** `Storage directory not accessible`
**Solution:**
- Verify the drive is connected
- Check the configuration path in appsettings.json
- Restart the application after connecting the drive

### Verification Steps

1. **Check Configuration:**
   ```json
   {
     "LocalFileStorage": {
       "StoragePath": "D:\\uploads",
       "BaseUrl": "https://localhost:5001"
     }
   }
   ```

2. **Test File Upload:**
   - Use the API endpoint `/api/v1/files/upload`
   - Check if files appear in `D:\uploads`
   - Verify subdirectories are created automatically

3. **Check Logs:**
   - Look for storage-related log messages
   - Check for permission or access errors
   - Monitor file upload success/failure

## Security Considerations

### Permissions
- Grant minimum necessary permissions
- Use specific service accounts instead of broad groups
- Regularly audit file access permissions

### Backup
- Include the external storage in your backup strategy
- Consider automated backup of uploaded files
- Test restore procedures regularly

### Monitoring
- Monitor disk space on the external drive
- Set up alerts for storage issues
- Log file access for audit purposes

## Maintenance

### Regular Tasks
- Monitor disk space usage
- Clean up temporary files
- Verify backup integrity
- Check file permissions

### Storage Management
- Implement file retention policies
- Archive old files as needed
- Monitor for orphaned files

## Configuration Reference

### Complete Configuration Example

```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "D:\\uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

### Environment Variables (Alternative)

You can also configure via environment variables:

```bash
LocalFileStorage__StoragePath=D:\uploads
LocalFileStorage__BaseUrl=https://localhost:5001
```

## Support

If you encounter issues with external file storage:

1. Check the application logs for detailed error messages
2. Verify drive connectivity and permissions
3. Test with the provided PowerShell script
4. Consult the troubleshooting section above

For additional support, refer to the main documentation or contact the development team.
