using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;

/// <summary>
/// Handler for UpdateAuditTemplateCommand
/// </summary>
public class UpdateAuditTemplateCommandHandler : BaseCommandHandler<UpdateAuditTemplateCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateAuditTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(UpdateAuditTemplateCommand request, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (template == null || !template.IsActive)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.AuditTemplate), request.Id);
        }

        // Check for optimistic concurrency
        if (template.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The template has been modified by another user. Please refresh and try again.");
        }

        // Don't allow updates to published templates
        if (template.IsPublished)
        {
            throw new InvalidOperationException("Cannot update a published template. Create a new version instead.");
        }

        // Update template properties
        template.TemplateName = request.TemplateName;
        template.Description = request.Description;
        template.Version = request.Version;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
