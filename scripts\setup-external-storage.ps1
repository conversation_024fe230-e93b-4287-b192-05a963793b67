# Setup External File Storage for HWS Audit Platform
# This script creates the D:\uploads directory and sets appropriate permissions

param(
    [string]$StoragePath = "D:\uploads",
    [string]$ServiceAccount = "IIS_IUSRS"
)

Write-Host "Setting up external file storage for HWS Audit Platform..." -ForegroundColor Green
Write-Host "Storage Path: $StoragePath" -ForegroundColor Yellow

# Check if D: drive exists
$driveLetter = Split-Path $StoragePath -Qualifier
if (-not (Test-Path $driveLetter)) {
    Write-Error "Drive $driveLetter is not available. Please ensure the external drive is connected."
    exit 1
}

# Create the directory if it doesn't exist
if (-not (Test-Path $StoragePath)) {
    Write-Host "Creating directory: $StoragePath" -ForegroundColor Yellow
    try {
        New-Item -Path $StoragePath -ItemType Directory -Force | Out-Null
        Write-Host "Directory created successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to create directory: $_"
        exit 1
    }
}
else {
    Write-Host "Directory already exists: $StoragePath" -ForegroundColor Green
}

# Set permissions for IIS/Application Pool
Write-Host "Setting permissions for $ServiceAccount..." -ForegroundColor Yellow
try {
    # Get current ACL
    $acl = Get-Acl $StoragePath
    
    # Create access rule for IIS_IUSRS (or specified service account)
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
        $ServiceAccount,
        "FullControl",
        "ContainerInherit,ObjectInherit",
        "None",
        "Allow"
    )
    
    # Add the access rule to ACL
    $acl.SetAccessRule($accessRule)
    
    # Apply the ACL
    Set-Acl -Path $StoragePath -AclObject $acl
    
    Write-Host "Permissions set successfully for $ServiceAccount" -ForegroundColor Green
}
catch {
    Write-Warning "Failed to set permissions: $_"
    Write-Host "You may need to manually set permissions for the application to access $StoragePath" -ForegroundColor Yellow
}

# Test write permissions
Write-Host "Testing write permissions..." -ForegroundColor Yellow
$testFile = Join-Path $StoragePath "test-write-permissions.txt"
try {
    "Test file created at $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
    Remove-Item $testFile -Force
    Write-Host "Write permissions test successful." -ForegroundColor Green
}
catch {
    Write-Warning "Write permissions test failed: $_"
    Write-Host "Please ensure the application has write permissions to $StoragePath" -ForegroundColor Yellow
}

# Create subdirectories that might be needed
$subDirs = @("temp", "audit-attachments", "reports")
foreach ($subDir in $subDirs) {
    $subDirPath = Join-Path $StoragePath $subDir
    if (-not (Test-Path $subDirPath)) {
        Write-Host "Creating subdirectory: $subDir" -ForegroundColor Yellow
        New-Item -Path $subDirPath -ItemType Directory -Force | Out-Null
    }
}

Write-Host ""
Write-Host "External file storage setup completed!" -ForegroundColor Green
Write-Host "Storage location: $StoragePath" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Ensure your application configuration points to: $StoragePath" -ForegroundColor White
Write-Host "2. Restart your HWS Audit Platform services" -ForegroundColor White
Write-Host "3. Test file upload functionality" -ForegroundColor White
Write-Host ""
Write-Host "Configuration example for appsettings.json:" -ForegroundColor Cyan
Write-Host @"
{
  "LocalFileStorage": {
    "StoragePath": "$($StoragePath.Replace('\', '\\'))",
    "BaseUrl": "https://localhost:5001"
  }
}
"@ -ForegroundColor White
