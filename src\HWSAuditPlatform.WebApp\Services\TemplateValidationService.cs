using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Client-side template validation service
/// </summary>
public class TemplateValidationService : ITemplateValidationService
{
    public Task<TemplateValidationResult> ValidateForPublishingAsync(AuditTemplate template)
    {
        var result = new TemplateValidationResult { IsValid = true };

        // Validate basic template info
        var basicValidation = ValidateTemplateInfo(template);
        result.Errors.AddRange(basicValidation.Errors);
        result.Warnings.AddRange(basicValidation.Warnings);
        if (!basicValidation.IsValid)
        {
            result.IsValid = false;
        }

        // Check if template has questions
        var activeQuestions = template.Questions?.Where(q => q.IsActive).ToList() ?? new List<Question>();
        if (!activeQuestions.Any())
        {
            result.AddError("Template must have at least one question to be published.");
            return Task.FromResult(result);
        }

        // Validate each question
        foreach (var question in activeQuestions)
        {
            var questionValidation = ValidateQuestion(question);
            if (!questionValidation.IsValid)
            {
                result.IsValid = false;
                foreach (var error in questionValidation.Errors)
                {
                    result.AddError($"Question '{question.QuestionText}': {error}");
                }
            }
            foreach (var warning in questionValidation.Warnings)
            {
                result.AddWarning($"Question '{question.QuestionText}': {warning}");
            }
        }

        // Check for duplicate question orders
        var duplicateOrders = activeQuestions
            .GroupBy(q => q.DisplayOrder)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var order in duplicateOrders)
        {
            result.AddWarning($"Multiple questions have the same display order: {order}");
        }

        // Check for missing display orders
        var maxOrder = activeQuestions.Max(q => q.DisplayOrder);
        var expectedOrders = Enumerable.Range(1, maxOrder).ToHashSet();
        var actualOrders = activeQuestions.Select(q => q.DisplayOrder).ToHashSet();
        var missingOrders = expectedOrders.Except(actualOrders);

        if (missingOrders.Any())
        {
            result.AddWarning($"Missing display orders: {string.Join(", ", missingOrders)}");
        }

        return Task.FromResult(result);
    }

    public TemplateValidationResult ValidateTemplateInfo(AuditTemplate template)
    {
        var result = new TemplateValidationResult { IsValid = true };

        // Check template name
        if (string.IsNullOrWhiteSpace(template.TemplateName))
        {
            result.AddError("Template name is required.");
        }
        else if (template.TemplateName.Length > 255)
        {
            result.AddError("Template name cannot exceed 255 characters.");
        }

        // Check description length
        if (!string.IsNullOrEmpty(template.Description) && template.Description.Length > 2000)
        {
            result.AddError("Template description cannot exceed 2000 characters.");
        }

        // Check version
        if (template.Version <= 0)
        {
            result.AddError("Template version must be greater than 0.");
        }

        // Warnings
        if (string.IsNullOrWhiteSpace(template.Description))
        {
            result.AddWarning("Consider adding a description to help users understand the template's purpose.");
        }

        return result;
    }

    public QuestionValidationResult ValidateQuestion(Question question)
    {
        var result = new QuestionValidationResult { IsValid = true };

        // Check question text
        if (string.IsNullOrWhiteSpace(question.QuestionText))
        {
            result.AddError("Question text is required.");
        }
        else if (question.QuestionText.Length > 1000)
        {
            result.AddError("Question text cannot exceed 1000 characters.");
        }

        // Check help text length
        if (!string.IsNullOrEmpty(question.HelpText) && question.HelpText.Length > 500)
        {
            result.AddError("Help text cannot exceed 500 characters.");
        }

        // Check display order
        if (question.DisplayOrder <= 0)
        {
            result.AddError("Display order must be greater than 0.");
        }

        // Check weight
        if (question.Weight.HasValue && question.Weight.Value <= 0)
        {
            result.AddError("Question weight must be greater than 0 if specified.");
        }

        // Validate question type specific requirements
        switch (question.QuestionType)
        {
            case QuestionType.SingleSelect:
            case QuestionType.MultiSelect:
                ValidateSelectQuestion(question, result);
                break;
            case QuestionType.Numeric:
                ValidateNumericQuestion(question, result);
                break;
        }

        // Check conditional question logic
        if (question.ParentQuestionId.HasValue)
        {
            if (string.IsNullOrWhiteSpace(question.TriggerAnswerValue))
            {
                result.AddWarning("Conditional questions should specify a trigger answer value.");
            }
        }

        return result;
    }

    private void ValidateSelectQuestion(Question question, QuestionValidationResult result)
    {
        var activeOptions = question.Options?.Where(o => o.IsActive).ToList() ?? new List<QuestionOption>();

        if (activeOptions.Count < 2)
        {
            result.AddError("Single/Multi-select questions must have at least 2 options.");
        }

        foreach (var option in activeOptions)
        {
            if (string.IsNullOrWhiteSpace(option.OptionText))
            {
                result.AddError("All options must have text.");
            }
            else if (option.OptionText.Length > 500)
            {
                result.AddError("Option text cannot exceed 500 characters.");
            }
        }

        // Check for duplicate option texts
        var duplicateTexts = activeOptions
            .GroupBy(o => o.OptionText.Trim(), StringComparer.OrdinalIgnoreCase)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var duplicateText in duplicateTexts)
        {
            result.AddWarning($"Duplicate option text: '{duplicateText}'");
        }

        // Check for duplicate display orders
        var duplicateOrders = activeOptions
            .GroupBy(o => o.DisplayOrder)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var order in duplicateOrders)
        {
            result.AddWarning($"Multiple options have the same display order: {order}");
        }
    }

    private void ValidateNumericQuestion(Question question, QuestionValidationResult result)
    {
        // Add any numeric-specific validation here
        // For example, checking for min/max value constraints if they exist in the domain
        
        if (question.IsRequired && string.IsNullOrWhiteSpace(question.HelpText))
        {
            result.AddWarning("Consider adding help text to specify the expected numeric format or range.");
        }
    }
}
