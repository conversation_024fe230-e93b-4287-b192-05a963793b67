using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Audits.DTOs;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAuditAnswer;

/// <summary>
/// Query to get a specific audit answer by audit ID and question ID
/// </summary>
public class GetAuditAnswerQuery : BaseQuery<AuditAnswerDto?>
{
    /// <summary>
    /// The ID of the audit
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// The ID of the question
    /// </summary>
    public int QuestionId { get; set; }

    public GetAuditAnswerQuery(string auditId, int questionId)
    {
        AuditId = auditId;
        QuestionId = questionId;
    }

    public GetAuditAnswerQuery()
    {
    }
}
