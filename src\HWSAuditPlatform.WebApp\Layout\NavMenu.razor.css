.navbar-toggler {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: 2px solid #ed8936;
    background: linear-gradient(135deg, #4a5568 0%, #2d2d2d 100%);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23ed8936' stroke-linecap='round' stroke-miterlimit='10' stroke-width='3' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 1.75rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.navbar-toggler:checked {
    background: linear-gradient(135deg, #ed8936 0%, #d69e2e 100%);
    box-shadow: 0 0 15px #ed8936;
}

.navbar-toggler:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.top-row {
    min-height: 3.5rem;
    background: linear-gradient(135deg, #0f0f0f 0%, #2d2d2d 100%);
    border-bottom: 2px solid #ed8936;
    position: relative;
}

.top-row::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: #ed8936;
    box-shadow: 0 0 10px #ed8936;
}

.navbar-brand {
    font-size: 1.1rem;
    font-family: 'Roboto Condensed', sans-serif;
    font-weight: 700;
    color: #e0e0e0 !important;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.navbar-brand:hover {
    color: #ed8936 !important;
    text-shadow: 0 0 15px #ed8936;
}

.bi {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.nav-item {
    font-size: 0.95rem;
    padding-bottom: 0.3rem;
    position: relative;
}

    .nav-item:first-of-type {
        padding-top: 1rem;
    }

    .nav-item:last-of-type {
        padding-bottom: 1rem;
    }

    .nav-item ::deep a {
        color: #e0e0e0;
        border-radius: 4px;
        height: 3.2rem;
        display: flex;
        align-items: center;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        border: 1px solid transparent;
        background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.02) 100%);
    }

    .nav-item ::deep a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 0;
        background: #ed8936;
        transition: height 0.3s ease;
        border-radius: 0 2px 2px 0;
    }

.nav-item ::deep a.active {
    background: linear-gradient(135deg, #ed8936 0%, rgba(237, 137, 54, 0.8) 100%);
    color: white;
    border-color: #ed8936;
    box-shadow: 0 0 15px rgba(237, 137, 54, 0.4);
    font-weight: 700;
}

    .nav-item ::deep a.active::before {
        height: 70%;
        box-shadow: 0 0 10px #ed8936;
    }

.nav-item ::deep a:hover {
    background: linear-gradient(135deg, rgba(237, 137, 54, 0.2) 0%, rgba(255,255,255,0.1) 100%);
    color: #ed8936;
    border-color: rgba(237, 137, 54, 0.3);
    transform: translateX(5px);
    text-shadow: 0 0 8px #ed8936;
}

    .nav-item ::deep a:hover::before {
        height: 50%;
    }

.nav-scrollable {
    display: none;
}

.navbar-toggler:checked ~ .nav-scrollable {
    display: block;
}

@media (min-width: 641px) {
    .navbar-toggler {
        display: none;
    }

    .nav-scrollable {
        /* Never collapse the sidebar for wide screens */
        display: block;

        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
