using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Scheduling;

/// <summary>
/// Represents a recurring audit schedule configuration.
/// Maps to the RecurringAuditSettings table in the database.
/// </summary>
public class RecurringAuditSetting : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// User-defined name for this recurring schedule
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string SettingName { get; set; } = string.Empty;

    /// <summary>
    /// The audit template to be used for generated audits
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// Number of days from the generated ScheduledDate to the audit's DueDate
    /// </summary>
    public int DeadlineDays { get; set; } = 7;

    /// <summary>
    /// If false, the scheduler will not generate audits for this setting
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Determines how the scheduler generates/assigns audits from this setting
    /// </summary>
    public AssignmentType AssignmentType { get; set; } = AssignmentType.Individual;

    /// <summary>
    /// Default user if AssignmentType is Individual (AD ObjectGUID FK)
    /// </summary>
    [MaxLength(36)]
    public string? AssignToUserId { get; set; }

    /// <summary>
    /// Navigation property for the assigned user
    /// </summary>
    public virtual User? AssignToUser { get; set; }

    /// <summary>
    /// Default group if AssignmentType is GroupAny or GroupAllScheduled (CUID FK)
    /// </summary>
    [MaxLength(25)]
    public string? AssignToUserGroupId { get; set; }

    /// <summary>
    /// Navigation property for the assigned user group
    /// </summary>
    public virtual UserGroup? AssignToUserGroup { get; set; }

    /// <summary>
    /// Factory where audits should be generated
    /// </summary>
    public int FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the factory
    /// </summary>
    public virtual Factory Factory { get; set; } = null!;

    /// <summary>
    /// Area where audits should be generated (optional)
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Navigation property for the area
    /// </summary>
    public virtual Area? Area { get; set; }

    /// <summary>
    /// SubArea where audits should be generated (optional)
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Navigation property for the sub area
    /// </summary>
    public virtual SubArea? SubArea { get; set; }

    /// <summary>
    /// Calculated date for when the NEXT audit instance(s) should be generated by the scheduler
    /// </summary>
    public DateOnly? NextGenerationDate { get; set; }

    /// <summary>
    /// Timestamp of when the last audit instance(s) were actually generated by the scheduler for this setting
    /// </summary>
    public DateTime? LastGeneratedAt { get; set; }

    /// <summary>
    /// Navigation property for the recurrence rule (1-to-1 relationship)
    /// </summary>
    public virtual RecurrenceRule? RecurrenceRule { get; set; }

    /// <summary>
    /// Indicates if this setting is ready to generate audits
    /// </summary>
    public bool IsReadyToGenerate => IsEnabled && NextGenerationDate.HasValue && NextGenerationDate.Value <= DateOnly.FromDateTime(DateTime.UtcNow);

    /// <summary>
    /// Indicates if assignment is valid based on assignment type
    /// </summary>
    public bool HasValidAssignment => AssignmentType switch
    {
        AssignmentType.Individual => !string.IsNullOrEmpty(AssignToUserId),
        AssignmentType.GroupAny or AssignmentType.GroupAllScheduled => !string.IsNullOrEmpty(AssignToUserGroupId),
        _ => false
    };
}
