using FluentValidation;
using System.Text.RegularExpressions;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFindingCategory;

/// <summary>
/// Validator for CreateFindingCategoryCommand
/// </summary>
public class CreateFindingCategoryCommandValidator : AbstractValidator<CreateFindingCategoryCommand>
{
    public CreateFindingCategoryCommandValidator()
    {
        RuleFor(x => x.CategoryName)
            .NotEmpty()
            .WithMessage("Category name is required.")
            .MaximumLength(100)
            .WithMessage("Category name cannot exceed 100 characters.");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters.");

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0)
            .WithMessage("Audit template ID must be greater than 0.");

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Display order must be 0 or greater.");

        RuleFor(x => x.ColorCode)
            .Must(BeValidHexColor)
            .When(x => !string.IsNullOrEmpty(x.ColorCode))
            .WithMessage("Color code must be a valid hex color (e.g., #FF5733).");

        RuleFor(x => x.IconName)
            .MaximumLength(50)
            .WithMessage("Icon name cannot exceed 50 characters.");
    }

    private static bool BeValidHexColor(string? colorCode)
    {
        if (string.IsNullOrEmpty(colorCode))
            return true;

        return Regex.IsMatch(colorCode, @"^#[0-9A-Fa-f]{6}$");
    }
}
