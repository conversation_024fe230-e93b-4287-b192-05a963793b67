# SearchDropdown Component

A reusable generic search dropdown component for Blazor WebAssembly applications that provides real-time search functionality with debouncing and customizable display options.

## Features

- **Generic Type Support**: Works with any data type using `@typeparam TItem`
- **Real-time Search**: Debounced search input to avoid excessive API calls
- **Server-side Filtering**: Calls API endpoints with search terms for dynamic filtering
- **Customizable Display**: Configurable primary text, secondary text, and additional info
- **Keyboard Navigation**: Supports focus/blur handling and dropdown positioning
- **Industrial Styling**: Matches the HWS Audit Platform's industrial theme
- **Form Integration**: Supports validation and two-way binding

## Basic Usage

```razor
<SearchDropdown TItem="UserSummary"
              Placeholder="Type to search users..."
              Required="true"
              MaxResults="5"
              DebounceMs="300"
              SearchFunction="SearchUsersAsync"
              GetPrimaryText="@(user => user.FullName)"
              GetSecondaryText="@(user => user.Username)"
              GetAdditionalInfo="@(user => user.FactoryName ?? string.Empty)"
              GetDisplayText="@(user => $"{user.FullName} ({user.Username})")"
              @bind-SelectedItem="selectedUser"
              OnItemSelected="OnUserSelected"
              OnCleared="OnUserCleared" />
```

## Parameters

### Required Parameters

- **`SearchFunction`**: `Func<string, int, Task<IEnumerable<TItem>>>`
  - Function that performs the search operation
  - Parameters: search term, max results
  - Returns: Enumerable of search results

- **`GetPrimaryText`**: `Func<TItem, string>`
  - Function to extract the primary display text from an item

### Optional Parameters

- **`Placeholder`**: `string` (default: "Type to search...")
  - Placeholder text for the input field

- **`CssClass`**: `string` (default: "")
  - Additional CSS classes for the input field

- **`Required`**: `bool` (default: false)
  - Whether the input is required for form validation

- **`MaxResults`**: `int` (default: 5)
  - Maximum number of results to display in dropdown

- **`DebounceMs`**: `int` (default: 300)
  - Debounce delay in milliseconds for search input

- **`GetSecondaryText`**: `Func<TItem, string>` (optional)
  - Function to extract secondary text (shown below primary text)

- **`GetAdditionalInfo`**: `Func<TItem, string>` (optional)
  - Function to extract additional info (shown on the right side)

- **`GetDisplayText`**: `Func<TItem, string>` (optional)
  - Function to format the text shown in input after selection
  - Defaults to `GetPrimaryText` if not provided

### Event Callbacks

- **`OnItemSelected`**: `EventCallback<TItem>`
  - Called when an item is selected from the dropdown

- **`OnCleared`**: `EventCallback`
  - Called when the selection is cleared

- **`SelectedItem`** / **`SelectedItemChanged`**: Two-way binding for the selected item

## Implementation Example

```csharp
@code {
    private UserSummary? selectedUser = null;

    // Search function that calls your API service
    private async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int maxResults)
    {
        try
        {
            return await OrganizationApiService.SearchUsersAsync(searchTerm, maxResults);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error searching users with term: {SearchTerm}", searchTerm);
            return Enumerable.Empty<UserSummary>();
        }
    }

    // Handle item selection
    private void OnUserSelected(UserSummary user)
    {
        selectedUser = user;
        // Update your form model or perform other actions
        auditRequest.AssignedToUserId = user.AdObjectGuid;
        StateHasChanged();
    }

    // Handle clearing selection
    private void OnUserCleared()
    {
        selectedUser = null;
        auditRequest.AssignedToUserId = null;
        StateHasChanged();
    }
}
```

## API Service Requirements

Your search function should call an API endpoint that supports server-side filtering:

```csharp
public async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int pageSize = 10)
{
    var response = await _httpClient.GetAsync(
        $"api/v1/users/search?searchTerm={Uri.EscapeDataString(searchTerm)}&pageSize={pageSize}");
    
    // Handle response and return results
}
```

## Styling

The component uses CSS classes that match the industrial theme:
- `.search-dropdown`: Main dropdown container
- `.dropdown-item`: Individual result items
- `.fw-medium`: Primary text styling
- `.text-muted`: Secondary text styling

Custom CSS can be applied via the `CssClass` parameter or by overriding the component's CSS classes.

## Notes

- The component automatically handles loading states and error scenarios
- Debouncing prevents excessive API calls during typing
- The dropdown automatically hides on blur with a delay to allow for click events
- Supports keyboard navigation and accessibility features
- Integrates seamlessly with Blazor forms and validation
