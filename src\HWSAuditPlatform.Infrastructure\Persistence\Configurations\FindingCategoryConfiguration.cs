using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Findings;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for FindingCategory entity
/// </summary>
public class FindingCategoryConfiguration : IEntityTypeConfiguration<FindingCategory>
{
    public void Configure(EntityTypeBuilder<FindingCategory> builder)
    {
        builder.ToTable("hwsap_fnd_finding_categories");

        // Primary Key - Identity integer
        builder.HasKey(fc => fc.Id);
        builder.Property(fc => fc.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(fc => fc.CategoryName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(fc => fc.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(fc => fc.AuditTemplateId)
            .IsRequired();

        builder.Property(fc => fc.DisplayOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(fc => fc.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(fc => fc.ColorCode)
            .HasMaxLength(7)
            .IsRequired(false);

        builder.Property(fc => fc.IconName)
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(fc => fc.RequiresDocumentation)
            .IsRequired()
            .HasDefaultValue(false);

        // Auditable properties
        builder.Property(fc => fc.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(fc => fc.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(fc => fc.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(fc => fc.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(fc => fc.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(fc => fc.AuditTemplateId)
            .HasDatabaseName("IX_FindingCategories_AuditTemplateId");

        builder.HasIndex(fc => new { fc.AuditTemplateId, fc.CategoryName })
            .HasDatabaseName("IX_FindingCategories_Template_Name")
            .IsUnique();

        builder.HasIndex(fc => new { fc.AuditTemplateId, fc.DisplayOrder })
            .HasDatabaseName("IX_FindingCategories_Template_DisplayOrder");

        builder.HasIndex(fc => fc.IsActive)
            .HasDatabaseName("IX_FindingCategories_IsActive");

        // Relationships
        builder.HasOne(fc => fc.AuditTemplate)
            .WithMany(at => at.FindingCategories)
            .HasForeignKey(fc => fc.AuditTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(fc => fc.CreatedByUser)
            .WithMany()
            .HasForeignKey(fc => fc.CreatedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(fc => fc.UpdatedByUser)
            .WithMany()
            .HasForeignKey(fc => fc.UpdatedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Ignore computed properties
        builder.Ignore(fc => fc.FindingCount);
        builder.Ignore(fc => fc.OpenFindingCount);
        builder.Ignore(fc => fc.IsUsable);

        // Ignore domain events
        builder.Ignore(fc => fc.DomainEvents);
    }
}
