using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Infrastructure.Services;

namespace HWSAuditPlatform.Tests.Infrastructure.Services;

/// <summary>
/// Tests for DomainEventService focusing on event publishing and error handling
/// </summary>
public class DomainEventServiceTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<DomainEventService>> _mockLogger;
    private readonly DomainEventService _service;

    public DomainEventServiceTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockLogger = new Mock<ILogger<DomainEventService>>();
        _service = new DomainEventService(_mockMediator.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task PublishAsync_WithSingleEvent_ShouldPublishSuccessfully()
    {
        // Arrange
        var domainEvent = new TestDomainEvent("Test Event");
        
        _mockMediator
            .Setup(m => m.Publish(domainEvent, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublishAsync(domainEvent);

        // Assert
        _mockMediator.Verify(m => m.Publish(domainEvent, It.IsAny<CancellationToken>()), Times.Once);
        
        VerifyLogMessage(LogLevel.Information, "Publishing domain event: TestDomainEvent");
        VerifyLogMessage(LogLevel.Information, "Successfully published domain event: TestDomainEvent");
    }

    [Fact]
    public async Task PublishAsync_WithSingleEvent_WhenMediatorThrows_ShouldLogErrorAndRethrow()
    {
        // Arrange
        var domainEvent = new TestDomainEvent("Test Event");
        var exception = new InvalidOperationException("Mediator error");
        
        _mockMediator
            .Setup(m => m.Publish(domainEvent, It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _service.PublishAsync(domainEvent));
        
        thrownException.Should().Be(exception);
        
        VerifyLogMessage(LogLevel.Information, "Publishing domain event: TestDomainEvent");
        VerifyLogMessage(LogLevel.Error, "Error publishing domain event: TestDomainEvent");
    }

    [Fact]
    public async Task PublishAsync_WithCancellation_ShouldPassCancellationToken()
    {
        // Arrange
        var domainEvent = new TestDomainEvent("Test Event");
        using var cts = new CancellationTokenSource();
        
        _mockMediator
            .Setup(m => m.Publish(domainEvent, cts.Token))
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublishAsync(domainEvent, cts.Token);

        // Assert
        _mockMediator.Verify(m => m.Publish(domainEvent, cts.Token), Times.Once);
    }

    [Fact]
    public async Task PublishAsync_WithMultipleEvents_ShouldPublishAllEvents()
    {
        // Arrange
        var events = new List<IDomainEvent>
        {
            new TestDomainEvent("Event 1"),
            new TestDomainEvent("Event 2"),
            new TestDomainEvent("Event 3")
        };
        
        _mockMediator
            .Setup(m => m.Publish(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublishAsync(events);

        // Assert
        _mockMediator.Verify(m => m.Publish(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(3));
        
        VerifyLogMessage(LogLevel.Information, "Publishing 3 domain events");
        VerifyLogMessage(LogLevel.Information, "Successfully published all 3 domain events");
    }

    [Fact]
    public async Task PublishAsync_WithEmptyEventCollection_ShouldNotPublishAnything()
    {
        // Arrange
        var events = new List<IDomainEvent>();

        // Act
        await _service.PublishAsync(events);

        // Assert
        _mockMediator.Verify(m => m.Publish(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        
        // Should not log anything for empty collection
        _mockLogger.Verify(
            x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Never);
    }

    [Fact]
    public async Task PublishAsync_WithMultipleEvents_WhenOneEventFails_ShouldStopAndRethrow()
    {
        // Arrange
        var events = new List<IDomainEvent>
        {
            new TestDomainEvent("Event 1"),
            new TestDomainEvent("Event 2"),
            new TestDomainEvent("Event 3")
        };
        
        var exception = new InvalidOperationException("Event 2 failed");
        
        _mockMediator
            .SetupSequence(m => m.Publish(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask)  // Event 1 succeeds
            .ThrowsAsync(exception);      // Event 2 fails

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _service.PublishAsync(events));
        
        thrownException.Should().Be(exception);
        
        // Should only call mediator twice (Event 1 and Event 2)
        _mockMediator.Verify(m => m.Publish(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task PublishAsync_WithNullEvent_ShouldNotThrow()
    {
        // Arrange
        IDomainEvent? nullEvent = null;

        // Act & Assert
        var act = async () => await _service.PublishAsync(nullEvent!);
        await act.Should().NotThrowAsync();
        
        _mockMediator.Verify(m => m.Publish(nullEvent!, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task PublishAsync_WithNullEventCollection_ShouldThrowArgumentNullException()
    {
        // Arrange
        IEnumerable<IDomainEvent>? nullEvents = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.PublishAsync(nullEvents!));
    }

    private void VerifyLogMessage(LogLevel logLevel, string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    /// <summary>
    /// Test implementation of IDomainEvent for testing purposes
    /// </summary>
    private class TestDomainEvent : IDomainEvent
    {
        public string Message { get; }
        public DateTime OccurredOn { get; }

        public TestDomainEvent(string message)
        {
            Message = message;
            OccurredOn = DateTime.UtcNow;
        }
    }
}
