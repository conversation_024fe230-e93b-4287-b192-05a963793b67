using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAudits;

/// <summary>
/// Handler for GetAuditsQuery
/// </summary>
public class GetAuditsQueryHandler : BaseQueryHandler<GetAuditsQuery, PaginatedResult<AuditSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAuditsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<AuditSummaryDto>> Handle(GetAuditsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(a =>
                EF.Functions.Like(a.AuditTemplate.TemplateName, searchPattern) ||
                (a.AssignedToUser != null && EF.Functions.Like(a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName, searchPattern)) ||
                EF.Functions.Like(a.Factory.FactoryName, searchPattern) ||
                EF.Functions.Like(a.Area.AreaName, searchPattern));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == request.Status.Value);
        }

        if (!string.IsNullOrEmpty(request.AssignedToUserId))
        {
            query = query.Where(a => a.AssignedToUserId == request.AssignedToUserId);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(a => a.AreaId == request.AreaId.Value);
        }

        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(a => a.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.ScheduledDateFrom.HasValue)
        {
            query = query.Where(a => a.ScheduledDate >= request.ScheduledDateFrom.Value);
        }

        if (request.ScheduledDateTo.HasValue)
        {
            query = query.Where(a => a.ScheduledDate <= request.ScheduledDateTo.Value);
        }

        if (request.DueDateFrom.HasValue)
        {
            query = query.Where(a => a.DueDate >= request.DueDateFrom.Value);
        }

        if (request.DueDateTo.HasValue)
        {
            query = query.Where(a => a.DueDate <= request.DueDateTo.Value);
        }

        if (request.IsOverdue.HasValue && request.IsOverdue.Value)
        {
            var now = DateTime.UtcNow;
            query = query.Where(a => a.DueDate.HasValue && a.DueDate.Value < now && 
                a.OverallStatus != Domain.Enums.AuditOverallStatus.Closed && 
                a.OverallStatus != Domain.Enums.AuditOverallStatus.Cancelled);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "templatename" => request.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.AuditTemplate.TemplateName)
                : query.OrderBy(a => a.AuditTemplate.TemplateName),
            "assigneduser" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.AssignedToUser != null ? a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName : "")
                : query.OrderBy(a => a.AssignedToUser != null ? a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName : ""),
            "status" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.OverallStatus)
                : query.OrderBy(a => a.OverallStatus),
            "duedate" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.DueDate)
                : query.OrderBy(a => a.DueDate),
            "factory" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.Factory.FactoryName)
                : query.OrderBy(a => a.Factory.FactoryName),
            _ => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.ScheduledDate)
                : query.OrderBy(a => a.ScheduledDate)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var audits = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(a => new AuditSummaryDto
            {
                Id = a.Id,
                AuditTemplateName = a.AuditTemplate.TemplateName,
                AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                ScheduledDate = a.ScheduledDate,
                DueDate = a.DueDate,
                OverallStatus = a.OverallStatus,
                FactoryName = a.Factory.FactoryName,
                AreaName = a.Area.AreaName,
                OverallScore = a.OverallScore,
                IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow && 
                    a.OverallStatus != Domain.Enums.AuditOverallStatus.Closed && 
                    a.OverallStatus != Domain.Enums.AuditOverallStatus.Cancelled
            })
            .ToListAsync(cancellationToken);

        return new PaginatedResult<AuditSummaryDto>(audits, totalCount, request.PageNumber, request.PageSize);
    }
}
