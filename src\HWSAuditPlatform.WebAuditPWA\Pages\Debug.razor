@page "/debug"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Configuration
@using HWSAuditPlatform.WebAuditPWA.Services
@inject IConfiguration Configuration
@inject AuthenticationStateProvider AuthStateProvider
@inject IAuthenticationService AuthService
@inject IAuditApiService AuditApiService
@inject ITemplateApiService TemplateApiService
@inject IUserApiService UserApiService
@inject IPwaService PwaService
@inject IJSRuntime JSRuntime

<PageTitle>Debug Information - HWS Audit PWA</PageTitle>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <i class="fas fa-bug text-warning me-2 fs-4"></i>
                <h1 class="mb-0">Debug Information</h1>
                @if (skipAuthentication)
                {
                    <span class="badge bg-warning text-dark ms-3 fs-6">🚨 DEBUG MODE ACTIVE</span>
                }
            </div>
            <p class="text-muted">Development and debugging information for the HWS Audit PWA</p>
        </div>
    </div>

    <div class="row g-4">
        <!-- Environment Information -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Environment</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Environment:</strong></td>
                            <td><span class="badge bg-info">@environment</span></td>
                        </tr>
                        <tr>
                            <td><strong>API Base URL:</strong></td>
                            <td><code>@apiBaseUrl</code></td>
                        </tr>
                        <tr>
                            <td><strong>Skip Authentication:</strong></td>
                            <td>
                                @if (skipAuthentication)
                                {
                                    <span class="badge bg-warning text-dark">Enabled</span>
                                }
                                else
                                {
                                    <span class="badge bg-success">Disabled</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <td><strong>PWA Mode:</strong></td>
                            <td>
                                @if (isPwaMode)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Online Status:</strong></td>
                            <td>
                                @if (isOnline)
                                {
                                    <span class="badge bg-success">Online</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Offline</span>
                                }
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Authentication Information -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>Authentication</h5>
                </div>
                <div class="card-body">
                    @if (currentUser != null)
                    {
                        <table class="table table-sm">
                            <tr>
                                <td><strong>User ID:</strong></td>
                                <td><code>@currentUser.UserId</code></td>
                            </tr>
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td>@currentUser.Username</td>
                            </tr>
                            <tr>
                                <td><strong>Full Name:</strong></td>
                                <td>@currentUser.FullName</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>@currentUser.Email</td>
                            </tr>
                            <tr>
                                <td><strong>Role:</strong></td>
                                <td><span class="badge bg-primary">@currentUser.Role</span></td>
                            </tr>
                            <tr>
                                <td><strong>Factory ID:</strong></td>
                                <td>@currentUser.FactoryId</td>
                            </tr>
                        </table>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No authenticated user found
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Service Information -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Services</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Auth Service:</strong></td>
                            <td><code>@authServiceType</code></td>
                        </tr>
                        <tr>
                            <td><strong>Auth State Provider:</strong></td>
                            <td><code>@authStateProviderType</code></td>
                        </tr>
                        <tr>
                            <td><strong>Audit API Service:</strong></td>
                            <td><code>@auditApiServiceType</code></td>
                        </tr>
                        <tr>
                            <td><strong>Template API Service:</strong></td>
                            <td><code>@templateApiServiceType</code></td>
                        </tr>
                        <tr>
                            <td><strong>User API Service:</strong></td>
                            <td><code>@userApiServiceType</code></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                        <a href="/" class="btn btn-outline-success">
                            <i class="fas fa-home me-2"></i>Go to Home
                        </a>
                        <button class="btn btn-outline-info" @onclick="TestApiConnection">
                            <i class="fas fa-plug me-2"></i>Test API Connection
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="RefreshData">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Data
                        </button>
                    </div>
                    
                    <hr>
                    
                    <h6>Configuration Toggle</h6>
                    <p class="small text-muted">
                        To toggle debug mode, edit <code>wwwroot/appsettings.Development.json</code> and change 
                        <code>Debug:SkipAuthentication</code> to <code>@(!skipAuthentication)</code>, then refresh the application.
                    </p>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(testResult))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-flask me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>@testResult</code></pre>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool skipAuthentication;
    private string environment = "Unknown";
    private string apiBaseUrl = "Unknown";
    private string authServiceType = "Unknown";
    private string authStateProviderType = "Unknown";
    private string auditApiServiceType = "Unknown";
    private string templateApiServiceType = "Unknown";
    private string userApiServiceType = "Unknown";
    private bool isPwaMode;
    private bool isOnline;
    private Models.UserInfo? currentUser;
    private string testResult = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadDebugInformation();
    }

    private async Task LoadDebugInformation()
    {
        try
        {
            // Load configuration
            skipAuthentication = Configuration.GetValue<bool>("Debug:SkipAuthentication");
            apiBaseUrl = Configuration["ApiBaseUrl"] ?? "Unknown";
            environment = "Development"; // PWA is always in development mode when debugging

            // Load service types
            authServiceType = AuthService.GetType().Name;
            authStateProviderType = AuthStateProvider.GetType().Name;
            auditApiServiceType = AuditApiService.GetType().Name;
            templateApiServiceType = TemplateApiService.GetType().Name;
            userApiServiceType = UserApiService.GetType().Name;

            // Load PWA status
            isPwaMode = await PwaService.IsPwaAsync();
            isOnline = await PwaService.IsOnlineAsync();

            // Load current user
            currentUser = await AuthService.GetCurrentUserAsync();
        }
        catch (Exception ex)
        {
            testResult = $"Error loading debug information: {ex.Message}";
        }
    }

    private async Task TestApiConnection()
    {
        try
        {
            testResult = "Testing API connection...\n";
            StateHasChanged();

            // Test authentication
            var isAuthenticated = await AuthService.IsAuthenticatedAsync();
            testResult += $"Authentication Status: {isAuthenticated}\n";

            // Test audit API
            var auditsResult = await AuditApiService.GetAssignedAuditsAsync();
            testResult += $"Audits API: {(auditsResult.IsSuccess ? "Success" : "Failed")} - {auditsResult.Data?.Count ?? 0} audits\n";

            // Test template API
            var templatesResult = await TemplateApiService.GetAvailableTemplatesAsync();
            testResult += $"Templates API: {(templatesResult.IsSuccess ? "Success" : "Failed")} - {templatesResult.Data?.Count ?? 0} templates\n";

            // Test user API
            var userResult = await UserApiService.GetCurrentUserProfileAsync();
            testResult += $"User API: {(userResult.IsSuccess ? "Success" : "Failed")} - User: {userResult.Data?.Username ?? "None"}\n";

            testResult += "\nAPI connection test completed.";
        }
        catch (Exception ex)
        {
            testResult = $"API connection test failed: {ex.Message}";
        }

        StateHasChanged();
    }

    private async Task RefreshData()
    {
        testResult = "Refreshing debug data...";
        StateHasChanged();
        
        await LoadDebugInformation();
        
        testResult = "Debug data refreshed successfully.";
        StateHasChanged();
    }
}
