using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestion;

/// <summary>
/// Command to add a question to an audit template
/// </summary>
public class AddQuestionCommand : BaseCommand<int>
{
    /// <summary>
    /// The ID of the audit template to add the question to
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Optional question group ID to organize the question
    /// </summary>
    public int? QuestionGroupId { get; set; }

    /// <summary>
    /// The actual text of the question
    /// </summary>
    public string QuestionText { get; set; } = string.Empty;

    /// <summary>
    /// The type of answer expected
    /// </summary>
    public QuestionType QuestionType { get; set; }

    /// <summary>
    /// Order in which this question appears
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Whether the question is required
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Optional weight for scoring
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Additional guidance for answering the question
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Parent question ID for conditional questions
    /// </summary>
    public int? ParentQuestionId { get; set; }

    /// <summary>
    /// Answer value that triggers this conditional question
    /// </summary>
    public string? TriggerAnswerValue { get; set; }

    /// <summary>
    /// Default severity level for failures
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Whether evidence is required for this question
    /// </summary>
    public bool EvidenceRequired { get; set; } = false;

    /// <summary>
    /// Allowed evidence types for this question
    /// </summary>
    public List<AddEvidenceTypeRequest> AllowedEvidenceTypes { get; set; } = new();

    /// <summary>
    /// Question options for select-type questions
    /// </summary>
    public List<AddQuestionOptionRequest> Options { get; set; } = new();
}

/// <summary>
/// Request DTO for adding question options during question creation
/// </summary>
public class AddQuestionOptionRequest
{
    public string OptionText { get; set; } = string.Empty;
    public string? OptionValue { get; set; }
    public int DisplayOrder { get; set; }
}
