using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Workflow.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Workflow.Queries.GetMyCorrectionRequests;

/// <summary>
/// Query to get correction requests for the current user
/// </summary>
public class GetMyCorrectionRequestsQuery : BaseQuery<PaginatedResult<CorrectionRequestSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter by correction request status
    /// </summary>
    public CorrectionRequestStatus? Status { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "RequestedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
