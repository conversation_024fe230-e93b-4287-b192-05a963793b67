using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Audits.DTOs;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAuditAnswers;

/// <summary>
/// Query to get all answers for a specific audit
/// </summary>
public class GetAuditAnswersQuery : BaseQuery<List<AuditAnswerDto>>
{
    /// <summary>
    /// The ID of the audit to get answers for
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Optional filter by question ID
    /// </summary>
    public int? QuestionId { get; set; }

    /// <summary>
    /// Whether to include only answered questions or all questions
    /// </summary>
    public bool IncludeUnanswered { get; set; } = false;

    public GetAuditAnswersQuery(string auditId)
    {
        AuditId = auditId;
    }

    public GetAuditAnswersQuery()
    {
    }
}
