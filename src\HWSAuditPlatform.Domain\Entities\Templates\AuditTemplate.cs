using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents an audit template that defines the structure and content of audits.
/// Maps to the AuditTemplates table in the database.
/// </summary>
public class AuditTemplate : AuditableEntity<int>, IAggregateRoot
{
    /// <summary>
    /// Name of the audit template
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the template's purpose and scope
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Design version of the template, not for concurrency control.
    /// Incremented for significant changes to questions/structure.
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Indicates if this template version is finalized and ready for use in new audits
    /// </summary>
    public bool IsPublished { get; set; } = false;

    /// <summary>
    /// Indicates if this template version can be used (for soft deletion or retiring old versions)
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property for question groups in this template
    /// </summary>
    public virtual ICollection<QuestionGroup> QuestionGroups { get; set; } = new List<QuestionGroup>();

    /// <summary>
    /// Navigation property for questions in this template
    /// </summary>
    public virtual ICollection<Question> Questions { get; set; } = new List<Question>();

    /// <summary>
    /// Checks if this template can be published
    /// </summary>
    /// <returns>True if the template can be published, false otherwise</returns>
    public bool CanBePublished()
    {
        // Template must be active
        if (!IsActive)
            return false;

        // Template must not already be published
        if (IsPublished)
            return false;

        // Template must have at least one active question
        if (!Questions.Any(q => q.IsActive))
            return false;

        return true;
    }

    /// <summary>
    /// Gets the reasons why this template cannot be published
    /// </summary>
    /// <returns>List of validation error messages</returns>
    public List<string> GetPublishValidationErrors()
    {
        var errors = new List<string>();

        if (!IsActive)
            errors.Add("Template is not active");

        if (IsPublished)
            errors.Add("Template is already published");

        if (!Questions.Any(q => q.IsActive))
            errors.Add("Template must have at least one question to be published");

        return errors;
    }

    /// <summary>
    /// Publishes this template if validation passes
    /// </summary>
    /// <param name="userId">ID of the user publishing the template</param>
    /// <returns>True if published successfully, false if validation failed</returns>
    public bool TryPublish(string userId)
    {
        if (!CanBePublished())
            return false;

        IsPublished = true;
        UpdatedAt = DateTime.UtcNow;
        UpdatedByUserId = userId;

        return true;
    }

    /// <summary>
    /// Indicates if area-based responsibility assignment is enabled for this template
    /// </summary>
    public bool EnableAreaBasedResponsibility { get; set; } = false;

    /// <summary>
    /// Indicates if finding categorization is enabled for this template
    /// </summary>
    public bool EnableFindingCategorization { get; set; } = false;

    /// <summary>
    /// Navigation property for template-specific area responsibilities
    /// </summary>
    public virtual ICollection<AreaResponsibility> AreaResponsibilities { get; set; } = new List<AreaResponsibility>();

    /// <summary>
    /// Navigation property for finding categories defined for this template
    /// </summary>
    public virtual ICollection<FindingCategory> FindingCategories { get; set; } = new List<FindingCategory>();

    /// <summary>
    /// Gets the full template identifier including version
    /// </summary>
    public string FullName => $"{TemplateName} v{Version}";

    /// <summary>
    /// Indicates if this template can be used for new audits
    /// </summary>
    public bool CanBeUsed => IsPublished && IsActive;
}
