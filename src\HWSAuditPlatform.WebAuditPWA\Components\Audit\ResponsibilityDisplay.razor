@using HWSAuditPlatform.WebAuditPWA.Models

<div class="responsibility-display">
    @if (HasAnyResponsibilities)
    {
        <div class="responsibility-header">
            <h6 class="responsibility-title">
                <i class="fas fa-users me-2"></i>
                Assigned Responsibilities
            </h6>
            @if (ShowCompactView)
            {
                <button class="btn btn-sm btn-outline-light toggle-btn" @onclick="ToggleExpanded">
                    <i class="fas @(isExpanded ? "fa-chevron-up" : "fa-chevron-down")"></i>
                </button>
            }
        </div>

        <div class="responsibility-content @(ShowCompactView && !isExpanded ? "collapsed" : "")">
            @if (Responsibilities?.Any() == true)
            {
                @foreach (var responsibility in Responsibilities)
                {
                    <div class="responsibility-item @GetResponsibilityTypeClass(responsibility.ResponsibilityType)">
                        <div class="responsibility-info">
                            <div class="responsibility-role">
                                <i class="@GetResponsibilityIcon(responsibility.ResponsibilityType) me-2"></i>
                                @GetResponsibilityDisplayName(responsibility.ResponsibilityType)
                            </div>
                            <div class="responsibility-user">
                                <strong>@responsibility.ResponsibleUserFullName</strong>
                                <small class="d-block text-muted">@responsibility.ResponsibleUserName</small>
                            </div>
                        </div>
                        @if (responsibility.IsTemplateSpecific && !string.IsNullOrEmpty(responsibility.AuditTemplateName))
                        {
                            <div class="template-badge">
                                <small>@responsibility.AuditTemplateName</small>
                            </div>
                        }
                    </div>
                }
            }

            @if (!string.IsNullOrEmpty(ResponsibleUserName) || !string.IsNullOrEmpty(RetrospectiveAnalystUserName))
            {
                @if (!string.IsNullOrEmpty(ResponsibleUserName))
                {
                    <div class="responsibility-item corrective-action">
                        <div class="responsibility-info">
                            <div class="responsibility-role">
                                <i class="fas fa-user-check me-2"></i>
                                Corrective Actions
                            </div>
                            <div class="responsibility-user">
                                <strong>@ResponsibleUserName</strong>
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(RetrospectiveAnalystUserName))
                {
                    <div class="responsibility-item retrospective-analysis">
                        <div class="responsibility-info">
                            <div class="responsibility-role">
                                <i class="fas fa-chart-line me-2"></i>
                                Retrospective Analysis
                            </div>
                            <div class="responsibility-user">
                                <strong>@RetrospectiveAnalystUserName</strong>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    }
    else if (ShowEmptyState)
    {
        <div class="no-responsibilities">
            <i class="fas fa-user-slash fa-2x mb-2"></i>
            <p class="mb-0">No responsibilities assigned</p>
            <small class="text-muted">Responsibilities will be assigned automatically based on area configuration</small>
        </div>
    }
</div>

<style>
    .responsibility-display {
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        border: 2px solid var(--industrial-border);
        border-radius: 12px;
        border-left: 4px solid var(--industrial-teal);
        padding: 1.25rem;
        margin: 1rem 0;
    }

    .responsibility-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .responsibility-title {
        display: flex;
        align-items: center;
        color: var(--industrial-teal);
        font-size: 1rem;
        font-weight: 700;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .toggle-btn {
        border-color: var(--industrial-border-light);
        color: var(--industrial-text-muted);
        padding: 0.25rem 0.5rem;
    }

    .toggle-btn:hover {
        background-color: rgba(20, 184, 166, 0.1);
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
    }

    .responsibility-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        transition: all 0.3s ease;
    }

    .responsibility-content.collapsed {
        max-height: 0;
        overflow: hidden;
        margin: 0;
        padding: 0;
        gap: 0;
    }

    .responsibility-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(20, 184, 166, 0.1);
        border: 1px solid var(--industrial-border-light);
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.2s ease;
    }

    .responsibility-item:hover {
        background: rgba(20, 184, 166, 0.15);
        border-color: var(--industrial-teal);
    }

    .responsibility-item.corrective-action {
        border-left: 3px solid #10b981;
    }

    .responsibility-item.retrospective-analysis {
        border-left: 3px solid #3b82f6;
    }

    .responsibility-item.finding-reviewer {
        border-left: 3px solid #8b5cf6;
    }

    .responsibility-item.escalation-contact {
        border-left: 3px solid #f59e0b;
    }

    .responsibility-info {
        flex: 1;
    }

    .responsibility-role {
        display: flex;
        align-items: center;
        color: var(--industrial-text);
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .responsibility-user {
        color: var(--industrial-text-muted);
        font-size: 0.85rem;
    }

    .responsibility-user strong {
        color: var(--industrial-text);
    }

    .template-badge {
        background: var(--industrial-teal);
        color: white;
        border-radius: 12px;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .no-responsibilities {
        text-align: center;
        color: var(--industrial-text-muted);
        padding: 2rem 1rem;
    }

    .no-responsibilities i {
        color: var(--industrial-border);
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .responsibility-display {
            padding: 1rem;
        }

        .responsibility-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .responsibility-role,
        .responsibility-user {
            font-size: 0.85rem;
        }

        .template-badge {
            align-self: flex-end;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .responsibility-item {
            padding: 1rem;
        }

        .toggle-btn {
            padding: 0.5rem;
            min-width: 44px;
            min-height: 44px;
        }
    }
</style>

@code {
    [Parameter] public List<AreaResponsibilityModel>? Responsibilities { get; set; }
    [Parameter] public string? ResponsibleUserName { get; set; }
    [Parameter] public string? RetrospectiveAnalystUserName { get; set; }
    [Parameter] public bool ShowCompactView { get; set; } = false;
    [Parameter] public bool ShowEmptyState { get; set; } = true;

    private bool isExpanded = false;

    private bool HasAnyResponsibilities => 
        (Responsibilities?.Any() == true) || 
        !string.IsNullOrEmpty(ResponsibleUserName) || 
        !string.IsNullOrEmpty(RetrospectiveAnalystUserName);

    private void ToggleExpanded()
    {
        isExpanded = !isExpanded;
    }

    private static string GetResponsibilityTypeClass(ResponsibilityType type)
    {
        return type.ToString().ToLower().Replace("_", "-");
    }

    private static string GetResponsibilityIcon(ResponsibilityType type)
    {
        return type switch
        {
            ResponsibilityType.CorrectiveActionOwner => "fas fa-user-check",
            ResponsibilityType.RetrospectiveAnalyst => "fas fa-chart-line",
            ResponsibilityType.FindingReviewer => "fas fa-user-edit",
            ResponsibilityType.EscalationContact => "fas fa-exclamation-triangle",
            _ => "fas fa-user"
        };
    }

    private static string GetResponsibilityDisplayName(ResponsibilityType type)
    {
        return type switch
        {
            ResponsibilityType.CorrectiveActionOwner => "Corrective Actions",
            ResponsibilityType.RetrospectiveAnalyst => "Retrospective Analysis",
            ResponsibilityType.FindingReviewer => "Finding Review",
            ResponsibilityType.EscalationContact => "Escalation Contact",
            _ => type.ToString()
        };
    }
}
