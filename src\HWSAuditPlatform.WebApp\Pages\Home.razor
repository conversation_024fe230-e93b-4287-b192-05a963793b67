﻿@page "/"
@using HWSAuditPlatform.WebApp.Components.Dashboard
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject IConfiguration Configuration
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Dashboard - HWS Audit Platform</PageTitle>

@if (skipAuthentication)
{
    <!-- Debug mode: Skip authorization check -->
    <DashboardComponent />
}
else
{
    <!-- Normal mode: Require authorization -->
    <AuthorizeView>
        <Authorized>
            <DashboardComponent />
        </Authorized>
        <NotAuthorized>
            <div class="text-center p-4">
                <h3>Access Denied</h3>
                <p>You need to be logged in to access the dashboard.</p>
                <a href="/login" class="btn btn-primary">Go to Login</a>
            </div>
        </NotAuthorized>
    </AuthorizeView>
}

@code {
    private bool skipAuthentication = false;

    protected override void OnInitialized()
    {
        skipAuthentication = Configuration.GetValue<bool>("Debug:Ski<PERSON>Authentication");
    }
}
