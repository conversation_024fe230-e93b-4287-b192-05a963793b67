using HWSAuditPlatform.Application.Users.DTOs;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Service for interacting with Active Directory
/// </summary>
public interface IActiveDirectoryService
{
    /// <summary>
    /// Gets all users from Active Directory
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of AD users</returns>
    Task<IEnumerable<AdUserDto>> GetUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific user by ObjectGUID
    /// </summary>
    /// <param name="objectGuid">The ObjectGUID to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AD user if found, null otherwise</returns>
    Task<AdUserDto?> GetUserByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific user by username
    /// </summary>
    /// <param name="username">The username to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AD user if found, null otherwise</returns>
    Task<AdUserDto?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates user credentials against Active Directory
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="password">The password</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if credentials are valid, false otherwise</returns>
    Task<bool> ValidateCredentialsAsync(string username, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// Tests connectivity to Active Directory
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is successful</returns>
    Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);
}
