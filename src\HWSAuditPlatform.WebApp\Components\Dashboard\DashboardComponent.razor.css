.dashboard-container {
    padding: 20px;
    background-color: var(--industrial-dark, #1a1a1a);
    color: var(--industrial-text, #e0e0e0);
}

.dashboard-header {
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, var(--industrial-gray, #2d2d2d) 0%, var(--industrial-steel, #4a5568) 100%);
    border-radius: 8px;
    border: 1px solid var(--industrial-border, #404040);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    position: relative;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--industrial-orange, #ed8936) 0%, #fbb040 50%, var(--industrial-orange, #ed8936) 100%);
    border-radius: 8px 8px 0 0;
}

.dashboard-header h1 {
    color: var(--industrial-text, #e0e0e0);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    margin-bottom: 10px;
}

.dashboard-subtitle {
    color: #c0c0c0;
    font-weight: 500;
    margin-bottom: 0;
    font-size: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--industrial-gray, #2d2d2d) 0%, var(--industrial-steel, #4a5568) 100%);
    border: 1px solid var(--industrial-border, #404040);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--industrial-orange, #ed8936);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
    border-color: var(--industrial-orange, #ed8936);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.stat-card-primary .stat-icon { 
    background: linear-gradient(135deg, var(--industrial-blue, #2b6cb0) 0%, var(--industrial-steel, #4a5568) 100%); 
}
.stat-card-warning .stat-icon { 
    background: linear-gradient(135deg, var(--industrial-orange, #ed8936) 0%, #d69e2e 100%); 
}
.stat-card-success .stat-icon { 
    background: linear-gradient(135deg, var(--industrial-green, #38a169) 0%, #2f855a 100%); 
}
.stat-card-info .stat-icon { 
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); 
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--industrial-text, #e0e0e0);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.stat-content p {
    margin: 0;
    color: var(--industrial-text-muted, #a0a0a0);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.card {
    background: linear-gradient(135deg, var(--industrial-darker, #0f0f0f) 0%, var(--industrial-gray, #2d2d2d) 100%);
    border: 1px solid var(--industrial-border, #404040);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-radius: 8px;
    color: var(--industrial-text, #e0e0e0);
}

.card-body {
    background: linear-gradient(135deg, var(--industrial-darker, #0f0f0f) 0%, var(--industrial-gray, #2d2d2d) 100%);
    color: var(--industrial-text, #e0e0e0);
}

.card-header {
    background: linear-gradient(135deg, var(--industrial-steel, #4a5568) 0%, var(--industrial-light-gray, #404040) 100%);
    border-bottom: 2px solid var(--industrial-orange, #ed8936);
    border-radius: 8px 8px 0 0 !important;
    padding: 20px;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--industrial-orange, #ed8936);
    box-shadow: 0 0 10px var(--industrial-orange, #ed8936);
}

.card-title {
    color: var(--industrial-text, #e0e0e0);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.audit-list {
    max-height: 400px;
    overflow-y: auto;
    background: var(--industrial-darker, #0f0f0f);
    border-radius: 4px;
    padding: 10px;
    border: 1px solid var(--industrial-border, #404040);
}

.audit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--industrial-border, #404040);
    background: linear-gradient(135deg, var(--industrial-light-gray, #404040) 0%, var(--industrial-gray, #2d2d2d) 100%);
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    border: 1px solid var(--industrial-border, #404040);
}

.audit-item:hover {
    border-color: var(--industrial-orange, #ed8936);
    background: linear-gradient(135deg, var(--industrial-steel, #4a5568) 0%, var(--industrial-light-gray, #404040) 100%);
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.audit-item:last-child {
    border-bottom: 1px solid var(--industrial-border, #404040);
}

.audit-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--industrial-text, #e0e0e0);
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.audit-details {
    margin: 0 0 5px 0;
    font-size: 13px;
    color: #c0c0c0;
    font-weight: 500;
}

.audit-details .text-muted {
    color: #c0c0c0 !important;
    font-weight: 500;
}

.badge {
    font-size: 11px;
    padding: 6px 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}
