using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFindingCategory;

/// <summary>
/// Command to create a new finding category
/// </summary>
public class CreateFindingCategoryCommand : BaseCommand<FindingCategoryDto>
{
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int AuditTemplateId { get; set; }
    public int DisplayOrder { get; set; } = 0;
    public bool IsActive { get; set; } = true;
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool RequiresDocumentation { get; set; } = false;
}
