@page "/correction-requests"
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@inject ICorrectionRequestService CorrectionRequestService
@inject NavigationManager Navigation
@inject ILogger<CorrectionRequests> Logger

<PageTitle>Correction Requests - HWS Audit PWA</PageTitle>

<div class="correction-requests-container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-edit me-2"></i>
                Correction Requests
            </h2>
            <p class="text-muted mb-0">Manage audit correction requests</p>
        </div>
        <button class="btn btn-outline-secondary" @onclick="RefreshRequests">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>

    <!-- Filter Tabs -->
    <ul class="nav nav-tabs mb-4">
        <li class="nav-item">
            <button class="nav-link @(activeTab == "my" ? "active" : "")" @onclick="@(() => SetActiveTab("my"))">
                <i class="fas fa-user me-1"></i>
                My Requests
                @if (myRequests?.Count > 0)
                {
                    <span class="badge industrial-badge-primary ms-2">@myRequests.Count</span>
                }
            </button>
        </li>
        <li class="nav-item">
            <button class="nav-link @(activeTab == "pending" ? "active" : "")" @onclick="@(() => SetActiveTab("pending"))">
                <i class="fas fa-clock me-1"></i>
                Pending Review
                @if (pendingRequests?.Count > 0)
                {
                    <span class="badge industrial-badge-warning ms-2">@pendingRequests.Count</span>
                }
            </button>
        </li>
    </ul>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border industrial-text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading correction requests...</p>
        </div>
    }
    else
    {
        <!-- My Requests Tab -->
        @if (activeTab == "my")
        {
            @if (myRequests?.Any() != true)
            {
                <div class="text-center p-5">
                    <i class="fas fa-edit fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Correction Requests</h5>
                    <p class="text-muted">You haven't submitted any correction requests yet.</p>
                </div>
            }
            else
            {
                <div class="row">
                    @foreach (var request in myRequests)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 fw-bold">Request #@request.Id.Substring(0, 8)</h6>
                                    <span class="badge <EMAIL>">@request.StatusDisplayName</span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-calendar me-1"></i>
                                            Requested: @request.RequestedAt.ToString("MMM dd, yyyy HH:mm")
                                        </small>
                                        @if (request.IsReviewed)
                                        {
                                            <small class="text-muted d-block">
                                                <i class="fas fa-user-check me-1"></i>
                                                Reviewed by: @request.ReviewedByUserName
                                            </small>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-clock me-1"></i>
                                                Reviewed: @request.ReviewedAt?.ToString("MMM dd, yyyy HH:mm")
                                            </small>
                                        }
                                    </div>

                                    <div class="mb-3">
                                        <strong>Reason:</strong>
                                        <p class="mt-1 text-muted small">
                                            @(request.RequestReason.Length > 100 ? 
                                              request.RequestReason.Substring(0, 100) + "..." : 
                                              request.RequestReason)
                                        </p>
                                    </div>

                                    @if (!string.IsNullOrEmpty(request.ManagerComments))
                                    {
                                        <div class="mb-3">
                                            <strong>Manager Comments:</strong>
                                            <div class="mt-1 bg-light p-2 rounded small">
                                                @request.ManagerComments
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm flex-fill" 
                                                @onclick="() => ViewAuditResults(request.AuditId)">
                                            <i class="fas fa-eye me-1"></i>View Audit
                                        </button>
                                        @if (request.IsApproved)
                                        {
                                            <button class="btn btn-success btn-sm" 
                                                    @onclick="() => EditAudit(request.AuditId)">
                                                <i class="fas fa-edit me-1"></i>Edit
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        }

        <!-- Pending Review Tab -->
        @if (activeTab == "pending")
        {
            @if (pendingRequests?.Any() != true)
            {
                <div class="text-center p-5">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Pending Requests</h5>
                    <p class="text-muted">There are no correction requests pending your review.</p>
                </div>
            }
            else
            {
                <div class="row">
                    @foreach (var request in pendingRequests)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 industrial-border-warning">
                                <div class="card-header industrial-header-warning d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 fw-bold">Request #@request.Id.Substring(0, 8)</h6>
                                    <span class="badge industrial-badge-secondary">Pending Review</span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-user me-1"></i>
                                            Requested by: @request.RequestedByUserName
                                        </small>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-calendar me-1"></i>
                                            Requested: @request.RequestedAt.ToString("MMM dd, yyyy HH:mm")
                                        </small>
                                    </div>

                                    <div class="mb-3">
                                        <strong>Reason:</strong>
                                        <p class="mt-1 text-muted small">
                                            @request.RequestReason
                                        </p>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                @onclick="() => ViewAuditResults(request.AuditId)">
                                            <i class="fas fa-eye me-1"></i>View Audit
                                        </button>
                                        <button class="btn btn-success btn-sm" 
                                                @onclick="() => ApproveRequest(request.Id)">
                                            <i class="fas fa-check me-1"></i>Approve
                                        </button>
                                        <button class="btn btn-danger btn-sm" 
                                                @onclick="() => DenyRequest(request.Id)">
                                            <i class="fas fa-times me-1"></i>Deny
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        }
    }
</div>

@code {
    private List<CorrectionRequestDto>? myRequests;
    private List<CorrectionRequestDto>? pendingRequests;
    private bool isLoading = true;
    private string activeTab = "my";
    
    protected override async Task OnInitializedAsync()
    {
        await LoadCorrectionRequests();
    }
    
    private async Task LoadCorrectionRequests()
    {
        try
        {
            isLoading = true;
            Logger.LogInformation("Loading correction requests");
            
            // Load my requests
            var myRequestsResult = await CorrectionRequestService.GetMyCorrectionRequestsAsync();
            if (myRequestsResult.IsSuccess)
            {
                myRequests = myRequestsResult.Data;
                Logger.LogInformation("Loaded {Count} my correction requests", myRequests?.Count ?? 0);
            }
            else
            {
                Logger.LogWarning("Failed to load my correction requests: {Error}", myRequestsResult.ErrorMessage);
                myRequests = new List<CorrectionRequestDto>();
            }
            
            // Load pending requests (for managers)
            var pendingRequestsResult = await CorrectionRequestService.GetPendingCorrectionRequestsAsync();
            if (pendingRequestsResult.IsSuccess)
            {
                pendingRequests = pendingRequestsResult.Data;
                Logger.LogInformation("Loaded {Count} pending correction requests", pendingRequests?.Count ?? 0);
            }
            else
            {
                Logger.LogWarning("Failed to load pending correction requests: {Error}", pendingRequestsResult.ErrorMessage);
                pendingRequests = new List<CorrectionRequestDto>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading correction requests");
            myRequests = new List<CorrectionRequestDto>();
            pendingRequests = new List<CorrectionRequestDto>();
        }
        finally
        {
            isLoading = false;
        }
    }
    
    private async Task RefreshRequests()
    {
        await LoadCorrectionRequests();
    }
    
    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }
    
    private void ViewAuditResults(string auditId)
    {
        Navigation.NavigateTo($"/audit-results/{auditId}");
    }
    
    private void EditAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}/execute");
    }
    
    private async Task ApproveRequest(string requestId)
    {
        try
        {
            Logger.LogInformation("Approving correction request: {RequestId}", requestId);
            
            var result = await CorrectionRequestService.ApproveCorrectionRequestAsync(requestId, "Approved via PWA");
            
            if (result.IsSuccess)
            {
                Logger.LogInformation("Successfully approved correction request: {RequestId}", requestId);
                await LoadCorrectionRequests(); // Refresh the list
            }
            else
            {
                Logger.LogWarning("Failed to approve correction request: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error approving correction request: {RequestId}", requestId);
        }
    }
    
    private async Task DenyRequest(string requestId)
    {
        try
        {
            Logger.LogInformation("Denying correction request: {RequestId}", requestId);
            
            var result = await CorrectionRequestService.DenyCorrectionRequestAsync(requestId, "Denied via PWA");
            
            if (result.IsSuccess)
            {
                Logger.LogInformation("Successfully denied correction request: {RequestId}", requestId);
                await LoadCorrectionRequests(); // Refresh the list
            }
            else
            {
                Logger.LogWarning("Failed to deny correction request: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error denying correction request: {RequestId}", requestId);
        }
    }
}

<style>
    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
        border: 1px solid transparent;
        color: var(--industrial-text);
    }
    
    .nav-tabs .nav-link.active {
        background-color: var(--industrial-teal);
        border-color: var(--industrial-teal);
        color: white;
    }
    
    .nav-tabs .nav-link:hover {
        background-color: rgba(20, 184, 166, 0.1);
        border-color: var(--industrial-teal);
    }
    
    .card {
        border-radius: 12px;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        box-shadow: 0 2px 8px var(--industrial-shadow-light);
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--industrial-shadow-heavy);
    }

    .card-header {
        border-radius: 12px 12px 0 0;
        border-bottom: 1px solid var(--industrial-border);
        background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-steel) 100%);
        color: var(--industrial-text-light);
    }

    .badge {
        font-size: 0.75rem;
    }

    /* Industrial styling classes */
    .industrial-badge-primary {
        background-color: var(--industrial-blue) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-warning {
        background-color: var(--industrial-orange) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-secondary {
        background-color: var(--industrial-steel) !important;
        color: var(--industrial-text-light);
    }

    .industrial-text-primary { color: var(--industrial-teal) !important; }

    .industrial-border-warning { border-color: var(--industrial-orange) !important; }

    .industrial-header-warning {
        background: linear-gradient(135deg, var(--industrial-orange) 0%, #d97706 100%);
        color: var(--industrial-text-light);
        border-bottom: 1px solid var(--industrial-border);
    }
    
    /* Mobile optimizations */
    .mobile-card-footer .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .mobile-btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
</style>
