using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.DeleteQuestion;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Audits;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class DeleteQuestionCommandHandlerTests : BaseDbTestClass
{
    private readonly DeleteQuestionCommandHandler _handler;

    public DeleteQuestionCommandHandlerTests()
    {
        _handler = new DeleteQuestionCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldSoftDeleteQuestion()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var deletedQuestion = await Context.Questions.FirstOrDefaultAsync(q => q.Id == question.Id);
        deletedQuestion.Should().NotBeNull();
        deletedQuestion!.IsActive.Should().BeFalse();
        deletedQuestion.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithInvalidQuestionId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new DeleteQuestionCommand(999, template.Id, 1);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*Question*999*");
    }

    [Fact]
    public async Task Handle_WithMismatchedTemplateId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template1 = await CreateTestTemplateAsync();
        var template2 = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template1.Id);
        
        var command = new DeleteQuestionCommand(question.Id, template2.Id, question.RecordVersion);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*does not belong to the specified template*");
    }

    [Fact]
    public async Task Handle_WithPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsPublished = true;
        await Context.SaveChangesAsync();
        
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*published template*");
    }

    [Fact]
    public async Task Handle_WithStaleRecordVersion_ShouldThrowConcurrencyException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion - 1);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<ConcurrencyException>()
            .WithMessage("*modified by another user*");
    }

    [Fact]
    public async Task Handle_WithQuestionUsedInAudits_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        // Create an audit answer that references this question
        var auditAnswer = new AuditAnswer
        {
            AuditId = "clh7ckb0x0001qh08w5t6h5zy", // Sample CUID
            QuestionId = question.Id,
            AnswerText = "Yes"
        };
        Context.AuditAnswers.Add(auditAnswer);
        await Context.SaveChangesAsync();
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*answered in audits*");
    }

    [Fact]
    public async Task Handle_WithActiveChildQuestions_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var parentQuestion = await CreateTestQuestionAsync(template.Id);
        var childQuestion = await CreateTestQuestionAsync(template.Id, parentQuestionId: parentQuestion.Id);
        
        var command = new DeleteQuestionCommand(parentQuestion.Id, template.Id, parentQuestion.RecordVersion);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*active child questions*");
    }

    [Fact]
    public async Task Handle_ShouldSoftDeleteQuestionOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        
        var option1 = new QuestionOption
        {
            QuestionId = question.Id,
            OptionText = "Option 1",
            OptionValue = "opt1",
            DisplayOrder = 1,
            IsActive = true
        };
        var option2 = new QuestionOption
        {
            QuestionId = question.Id,
            OptionText = "Option 2",
            OptionValue = "opt2",
            DisplayOrder = 2,
            IsActive = true
        };
        
        Context.QuestionOptions.AddRange(option1, option2);
        await Context.SaveChangesAsync();
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var deletedOptions = await Context.QuestionOptions
            .Where(o => o.QuestionId == question.Id)
            .ToListAsync();
        
        deletedOptions.Should().HaveCount(2);
        deletedOptions.Should().OnlyContain(o => !o.IsActive);
    }

    [Fact]
    public async Task Handle_ShouldUpdateTemplateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        var originalTemplateUpdateTime = template.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new DeleteQuestionCommand(question.Id, template.Id, question.RecordVersion);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedAt.Should().BeAfter(originalTemplateUpdateTime);
    }

    [Fact]
    public async Task Handle_WithInactiveChildQuestions_ShouldSucceed()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var parentQuestion = await CreateTestQuestionAsync(template.Id);
        var childQuestion = await CreateTestQuestionAsync(template.Id, parentQuestionId: parentQuestion.Id);
        
        // Make child question inactive
        childQuestion.IsActive = false;
        await Context.SaveChangesAsync();
        
        var command = new DeleteQuestionCommand(parentQuestion.Id, template.Id, parentQuestion.RecordVersion);

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().NotThrowAsync();
        
        var deletedQuestion = await Context.Questions.FirstOrDefaultAsync(q => q.Id == parentQuestion.Id);
        deletedQuestion!.IsActive.Should().BeFalse();
    }

    private async Task<Question> CreateTestQuestionAsync(int templateId, QuestionType questionType = QuestionType.YesNo, int? parentQuestionId = null)
    {
        var question = new Question
        {
            AuditTemplateId = templateId,
            QuestionText = $"Test Question {Guid.NewGuid()}",
            QuestionType = questionType,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            ParentQuestionId = parentQuestionId
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return question;
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isPublished = false)
    {
        var template = new AuditTemplate
        {
            TemplateName = $"Test Template {Guid.NewGuid()}",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
