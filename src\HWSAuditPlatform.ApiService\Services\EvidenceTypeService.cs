using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Services;

/// <summary>
/// Service for mapping between MIME types and evidence types
/// </summary>
public interface IEvidenceTypeService
{
    /// <summary>
    /// Maps a MIME type to an evidence type
    /// </summary>
    EvidenceType GetEvidenceTypeFromMimeType(string mimeType);

    /// <summary>
    /// Gets all allowed MIME types for a given evidence type
    /// </summary>
    IEnumerable<string> GetMimeTypesForEvidenceType(EvidenceType evidenceType);

    /// <summary>
    /// Checks if a MIME type is allowed for a given evidence type
    /// </summary>
    bool IsMimeTypeAllowedForEvidenceType(string mimeType, EvidenceType evidenceType);

    /// <summary>
    /// Gets all allowed MIME types for a collection of evidence types
    /// </summary>
    IEnumerable<string> GetAllowedMimeTypes(IEnumerable<EvidenceType> evidenceTypes);
}

/// <summary>
/// Implementation of evidence type service
/// </summary>
public class EvidenceTypeService : IEvidenceTypeService
{
    private static readonly Dictionary<EvidenceType, string[]> EvidenceTypeMimeTypes = new()
    {
        {
            EvidenceType.Image, new[]
            {
                "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
            }
        },
        {
            EvidenceType.Document, new[]
            {
                "application/pdf", 
                "application/msword", 
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "text/plain", "text/rtf"
            }
        },
        {
            EvidenceType.Video, new[]
            {
                "video/mp4", "video/avi", "video/mov", "video/wmv", "video/webm"
            }
        },
        {
            EvidenceType.Audio, new[]
            {
                "audio/mp3", "audio/wav", "audio/aac", "audio/ogg", "audio/m4a"
            }
        },
        {
            EvidenceType.Spreadsheet, new[]
            {
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "text/csv"
            }
        }
    };

    public EvidenceType GetEvidenceTypeFromMimeType(string mimeType)
    {
        var normalizedMimeType = mimeType.ToLowerInvariant();
        
        foreach (var kvp in EvidenceTypeMimeTypes)
        {
            if (kvp.Value.Contains(normalizedMimeType))
            {
                return kvp.Key;
            }
        }

        return EvidenceType.Any; // Default for unknown types
    }

    public IEnumerable<string> GetMimeTypesForEvidenceType(EvidenceType evidenceType)
    {
        if (evidenceType == EvidenceType.Any)
        {
            return EvidenceTypeMimeTypes.Values.SelectMany(types => types);
        }

        return EvidenceTypeMimeTypes.TryGetValue(evidenceType, out var mimeTypes) 
            ? mimeTypes 
            : Enumerable.Empty<string>();
    }

    public bool IsMimeTypeAllowedForEvidenceType(string mimeType, EvidenceType evidenceType)
    {
        if (evidenceType == EvidenceType.Any)
            return true;

        var allowedMimeTypes = GetMimeTypesForEvidenceType(evidenceType);
        return allowedMimeTypes.Contains(mimeType.ToLowerInvariant());
    }

    public IEnumerable<string> GetAllowedMimeTypes(IEnumerable<EvidenceType> evidenceTypes)
    {
        var allMimeTypes = new HashSet<string>();
        
        foreach (var evidenceType in evidenceTypes)
        {
            var mimeTypes = GetMimeTypesForEvidenceType(evidenceType);
            foreach (var mimeType in mimeTypes)
            {
                allMimeTypes.Add(mimeType);
            }
        }

        return allMimeTypes;
    }
}
