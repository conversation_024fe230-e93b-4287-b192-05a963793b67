using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Audits.Commands.ReviewAudit;

/// <summary>
/// Command to review and approve/reject an audit
/// </summary>
public class ReviewAuditCommand : BaseCommand
{
    /// <summary>
    /// The ID of the audit to review
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the audit is approved or rejected
    /// </summary>
    public bool Approved { get; set; }

    /// <summary>
    /// Optional comments from the reviewer
    /// </summary>
    public string? Comments { get; set; }
}
