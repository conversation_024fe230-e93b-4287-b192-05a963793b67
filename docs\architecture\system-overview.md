# HWS Audit Platform - Architecture Overview

## Table of Contents

1. [System Overview](#system-overview)
2. [Clean Architecture](#clean-architecture)
3. [Layer Responsibilities](#layer-responsibilities)
4. [Technology Stack](#technology-stack)
5. [Design Patterns](#design-patterns)
6. [Data Flow](#data-flow)
7. [Security Architecture](#security-architecture)
8. [Scalability Considerations](#scalability-considerations)
9. [Integration Points](#integration-points)
10. [Future Architecture](#future-architecture)

## System Overview

The HWS Audit Platform is a comprehensive audit management system designed to support manufacturing quality audits with offline capabilities, Active Directory integration, and enterprise-scale requirements.

### Key Architectural Goals

- **Clean Architecture**: Maintainable, testable, and loosely coupled
- **Offline-First**: PWA capabilities with synchronization
- **Enterprise Integration**: Active Directory, file storage, monitoring
- **Scalability**: Designed for multi-tenant, high-volume usage
- **Security**: Role-based access, audit trails, data protection

### System Context

```
┌─────────────────────────────────────────────────────────────┐
│                    HWS Audit Platform                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Web App   │  │  Mobile     │  │    API Service      │ │
│  │  (Blazor)   │  │   PWA       │  │   (ASP.NET Core)    │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 External Systems                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ SQL Server  │  │   Local Fs  │  │  Active Directory   │ │
│  │  Database   │  │   Storage   │  │      (LDAP)         │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Clean Architecture

### Architectural Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│              (Controllers, Views, APIs)                     │
├─────────────────────────────────────────────────────────────┤
│                   Application Layer                         │
│           (Use Cases, Commands, Queries, DTOs)              │
├─────────────────────────────────────────────────────────────┤
│                     Domain Layer                            │
│         (Entities, Value Objects, Business Rules)           │
├─────────────────────────────────────────────────────────────┤
│                 Infrastructure Layer                        │
│        (Database, File Storage, External Services)          │
└─────────────────────────────────────────────────────────────┘
```

### Dependency Rules

- **Inward Dependencies Only**: Outer layers depend on inner layers
- **Stable Dependencies**: Inner layers are more stable than outer layers
- **Interface Segregation**: Dependencies are on abstractions, not concretions

### Benefits

- **Testability**: Each layer can be tested in isolation
- **Maintainability**: Changes in outer layers don't affect inner layers
- **Flexibility**: Easy to swap implementations (e.g., database providers)
- **Business Logic Protection**: Core business rules are isolated

## Layer Responsibilities

### 1. Domain Layer

**Purpose**: Contains enterprise business rules and entities

**Responsibilities:**
- Define business entities and their behavior
- Implement business rules and invariants
- Define domain events
- Value objects and enumerations
- Domain services for complex business logic

**Key Components:**
```csharp
// Entities
public class Audit : AuditableEntity<string>
{
    public void StartAudit() { /* Business logic */ }
    public void SubmitAnswer(Question question, string answer) { /* Business logic */ }
}

// Value Objects
public class Address : ValueObject
{
    public string AddressLine1 { get; }
    public string City { get; }
    public string PostalCode { get; }
}

// Domain Events
public class AuditStartedEvent : IDomainEvent
{
    public string AuditId { get; }
    public DateTime StartedAt { get; }
}
```

### 2. Application Layer

**Purpose**: Orchestrates business workflows and use cases

**Responsibilities:**
- Define application use cases (commands and queries)
- Coordinate between domain and infrastructure
- Handle cross-cutting concerns (validation, logging)
- Transform data between layers (DTOs)
- Manage transactions

**Key Components:**
```csharp
// Commands (Write Operations)
public class CreateAuditCommand : BaseCommand<string>
{
    public int AuditTemplateId { get; set; }
    public string AssignedToUserId { get; set; }
}

// Queries (Read Operations)
public class GetAuditsQuery : BaseQuery<PaginatedResult<AuditDto>>
{
    public int PageNumber { get; set; }
    public string? SearchTerm { get; set; }
}

// Handlers
public class CreateAuditCommandHandler : BaseCommandHandler<CreateAuditCommand, string>
{
    // Orchestrates the audit creation process
}
```

### 3. Infrastructure Layer

**Purpose**: Implements external concerns and technical details

**Responsibilities:**
- Database access and persistence
- External service integration
- File storage operations
- Identity and authentication
- Logging and monitoring
- Configuration management

**Key Components:**
```csharp
// Database Context
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    public DbSet<Audit> Audits { get; set; }
    // Implements Application layer interfaces
}

// External Services
public class ActiveDirectoryService : IActiveDirectoryService
{
    // Integrates with AD for user synchronization
}

// File Storage
public class FileStorageService : IFileStorageService
{
    // Handles file upload/download operations
}
```

### 4. Presentation Layer (API Service)

**Purpose**: Handles HTTP requests and responses

**Responsibilities:**
- HTTP request/response handling
- Authentication and authorization
- Input validation and model binding
- Error handling and status codes
- API documentation (Swagger)

**Key Components:**
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AuditsController : ControllerBase
{
    [HttpPost]
    public async Task<ActionResult<string>> CreateAudit(CreateAuditCommand command)
    {
        var auditId = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetAudit), new { id = auditId }, auditId);
    }
}
```

## Technology Stack

### Core Technologies

- **.NET 9**: Latest framework with performance improvements
- **ASP.NET Core**: Web API framework
- **Entity Framework Core 8**: Object-relational mapping
- **SQL Server**: Primary database
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Input validation

### Infrastructure Technologies

- **Local File Storage**: File storage (production)
- **System.DirectoryServices**: Active Directory integration
- **Serilog**: Structured logging

### Development Tools

- **Visual Studio 2024**: Primary IDE
- **Postman**: API testing
- **xUnit**: Unit testing framework
- **FluentAssertions**: Test assertions

## Design Patterns

### 1. CQRS (Command Query Responsibility Segregation)

**Purpose**: Separate read and write operations

**Implementation:**
```csharp
// Commands modify state
public class CreateUserCommand : BaseCommand<string> { }

// Queries return data
public class GetUsersQuery : BaseQuery<PaginatedResult<UserDto>> { }
```

**Benefits:**
- Optimized read and write models
- Scalability through separate data stores
- Clear separation of concerns

### 2. Repository Pattern

**Purpose**: Abstract data access logic

**Implementation:**
```csharp
public interface IRepository<TEntity, TKey>
{
    Task<TEntity?> GetByIdAsync(TKey id);
    Task<TEntity> AddAsync(TEntity entity);
    // Other CRUD operations
}
```

**Benefits:**
- Testability through mocking
- Consistent data access patterns
- Database independence

### 3. Unit of Work Pattern

**Purpose**: Manage transactions across multiple repositories

**Implementation:**
```csharp
public interface IUnitOfWork
{
    Task<int> SaveChangesAsync();
    Task<IDbTransaction> BeginTransactionAsync();
}
```

**Benefits:**
- Consistent transaction boundaries
- Atomic operations across entities
- Resource management

### 4. Mediator Pattern

**Purpose**: Decouple request/response handling

**Implementation:**
```csharp
// Controller
var result = await _mediator.Send(new GetUsersQuery());

// Handler
public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PaginatedResult<UserDto>>
```

**Benefits:**
- Loose coupling between layers
- Cross-cutting concerns through behaviors
- Single responsibility principle

### 5. Domain Events

**Purpose**: Decouple domain logic from side effects

**Implementation:**
```csharp
// Domain Entity
public void CompleteAudit()
{
    Status = AuditStatus.Completed;
    AddDomainEvent(new AuditCompletedEvent(Id));
}

// Event Handler
public class AuditCompletedEventHandler : INotificationHandler<AuditCompletedEvent>
{
    // Handle side effects (notifications, reports, etc.)
}
```

## Data Flow

### Command Flow (Write Operations)

```
1. HTTP Request → Controller
2. Controller → MediatR.Send(Command)
3. ValidationBehavior → Validate Input
4. CommandHandler → Business Logic
5. Repository → Database
6. DomainEventBehavior → Publish Events
7. Response → Controller → HTTP Response
```

### Query Flow (Read Operations)

```
1. HTTP Request → Controller
2. Controller → MediatR.Send(Query)
3. QueryHandler → Repository
4. Repository → Database
5. AutoMapper → DTO Mapping
6. Response → Controller → HTTP Response
```

### File Upload Flow

```
1. HTTP Request (multipart/form-data) → Controller
2. Controller → FileStorageService
3. FileStorageService → Local Storage
4. Database → Store file metadata
5. Response → File path/URL
```

### Offline Sync Flow

```
1. PWA → Collect local changes
2. HTTP Request → Sync endpoint
3. SyncHandler → Conflict detection
4. Repository → Apply changes
5. Response → Sync result with conflicts
6. PWA → Resolve conflicts and retry
```

## Security Architecture

### Authentication

- **JWT Tokens**: Stateless authentication
- **Claims-Based**: Role and permission information in tokens
- **Active Directory**: Enterprise user authentication

### Authorization

- **Role-Based Access Control (RBAC)**: Admin, Manager, Auditor roles
- **Resource-Based**: Users can only access their assigned audits
- **Factory-Based**: Users limited to their assigned factory

### Data Protection

- **Encryption at Rest**: Database and file storage encryption
- **Encryption in Transit**: HTTPS for all communications
- **Audit Trails**: Complete change tracking for all entities
- **Data Retention**: Configurable retention policies

### Security Headers

```csharp
app.UseSecurityHeaders(options =>
{
    options.AddDefaultSecurePolicy()
           .AddContentSecurityPolicy("default-src 'self'")
           .AddStrictTransportSecurity(maxAge: 365);
});
```

## Scalability Considerations

### Database Scalability

- **Read Replicas**: Separate read and write databases
- **Partitioning**: Partition by factory or date ranges
- **Indexing**: Optimized indexes for common queries
- **Connection Pooling**: Efficient database connections

### Application Scalability

- **Stateless Design**: No server-side session state
- **Caching**: Memory caching for frequently accessed data
- **Load Balancing**: Multiple application instances
- **Background Processing**: Async processing for heavy operations

### File Storage Scalability

- **CDN**: Content delivery network for file downloads
- **Compression**: File compression for large attachments

### Monitoring and Observability

- **Application Insights**: Performance and error monitoring
- **Health Checks**: Endpoint monitoring for dependencies
- **Structured Logging**: Searchable and filterable logs
- **Metrics**: Custom metrics for business KPIs

## Integration Points

### Active Directory Integration

```csharp
public interface IActiveDirectoryService
{
    Task<IEnumerable<AdUser>> GetUsersAsync();
    Task<bool> ValidateCredentialsAsync(string username, string password);
    Task<IEnumerable<AdGroup>> GetGroupsAsync();
}
```

### File Storage Integration

```csharp
public interface IFileStorageService
{
    Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream);
    Task<Stream> DownloadFileAsync(string filePath);
    Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime);
}
```

### Email Integration (Future)

```csharp
public interface IEmailService
{
    Task SendAuditNotificationAsync(string to, AuditNotification notification);
    Task SendReportAsync(string to, AuditReport report);
}
```

## Future Architecture

### Microservices Evolution

```
Current: Modular Monolith
Future: Microservices

┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  User Service   │  │  Audit Service  │  │ Template Service│
│                 │  │                 │  │                 │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │  API Gateway    │
                    │                 │
                    └─────────────────┘
```

### Event-Driven Architecture

```csharp
// Event Sourcing for Audit Trail
public class AuditEventStore
{
    Task AppendEventAsync(string streamId, IDomainEvent @event);
    Task<IEnumerable<IDomainEvent>> GetEventsAsync(string streamId);
}

// Message Bus for Service Communication
public interface IMessageBus
{
    Task PublishAsync<T>(T message) where T : class;
    Task SubscribeAsync<T>(Func<T, Task> handler) where T : class;
}
```

This architecture overview provides a comprehensive understanding of the HWS Audit Platform's design, patterns, and future evolution path.

## Related Documentation

- **[Application Layer Documentation](Application-Layer-Documentation.md)**: Detailed guide to the Application layer
- **[Infrastructure Layer Documentation](Infrastructure-Layer-Documentation.md)**: Comprehensive Infrastructure layer guide
- **[API Integration Guide](API-Integration-Guide.md)**: How to integrate and use the APIs
- **[Developer Guide](Developer-Guide.md)**: Development workflow and best practices
