using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for RecurringAuditSetting entity
/// </summary>
public class RecurringAuditSettingConfiguration : IEntityTypeConfiguration<RecurringAuditSetting>
{
    public void Configure(EntityTypeBuilder<RecurringAuditSetting> builder)
    {
        builder.ToTable("hwsap_sch_recurring_audit_settings");

        // Primary Key - CUID with consistent length
        builder.HasKey(ras => ras.Id);
        builder.Property(ras => ras.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(ras => ras.SettingName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(ras => ras.AuditTemplateId)
            .IsRequired();

        builder.Property(ras => ras.DeadlineDays)
            .IsRequired()
            .HasDefaultValue(7);

        builder.Property(ras => ras.IsEnabled)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(ras => ras.AssignmentType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(ras => ras.AssignToUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(ras => ras.AssignToUserGroupId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Location properties
        builder.Property(ras => ras.FactoryId)
            .IsRequired();

        builder.Property(ras => ras.AreaId)
            .IsRequired(false);

        builder.Property(ras => ras.SubAreaId)
            .IsRequired(false);

        builder.Property(ras => ras.NextGenerationDate)
            .IsRequired(false);

        builder.Property(ras => ras.LastGeneratedAt)
            .IsRequired(false);

        // Auditable properties
        builder.Property(ras => ras.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ras => ras.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ras => ras.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(ras => ras.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(ras => ras.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(ras => ras.AuditTemplateId)
            .HasDatabaseName("IX_RecurringAuditSettings_AuditTemplateId");

        builder.HasIndex(ras => ras.AssignToUserId)
            .HasDatabaseName("IX_RecurringAuditSettings_AssignToUserId");

        builder.HasIndex(ras => ras.AssignToUserGroupId)
            .HasDatabaseName("IX_RecurringAuditSettings_AssignToUserGroupId");

        builder.HasIndex(ras => ras.NextGenerationDate)
            .HasDatabaseName("IX_RecurringAuditSettings_NextGenerationDate");

        builder.HasIndex(ras => ras.IsEnabled)
            .HasDatabaseName("IX_RecurringAuditSettings_IsEnabled");

        builder.HasIndex(ras => ras.FactoryId)
            .HasDatabaseName("IX_RecurringAuditSettings_FactoryId");

        builder.HasIndex(ras => ras.AreaId)
            .HasDatabaseName("IX_RecurringAuditSettings_AreaId");

        builder.HasIndex(ras => ras.SubAreaId)
            .HasDatabaseName("IX_RecurringAuditSettings_SubAreaId");

        // Relationships
        builder.HasOne(ras => ras.AuditTemplate)
            .WithMany()
            .HasForeignKey(ras => ras.AuditTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ras => ras.AssignToUser)
            .WithMany()
            .HasForeignKey(ras => ras.AssignToUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.HasOne(ras => ras.AssignToUserGroup)
            .WithMany()
            .HasForeignKey(ras => ras.AssignToUserGroupId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Location relationships
        builder.HasOne(ras => ras.Factory)
            .WithMany()
            .HasForeignKey(ras => ras.FactoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ras => ras.Area)
            .WithMany()
            .HasForeignKey(ras => ras.AreaId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder.HasOne(ras => ras.SubArea)
            .WithMany()
            .HasForeignKey(ras => ras.SubAreaId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        // Note: CreatedByUser and UpdatedByUser navigation properties are not defined in the entity
        // The foreign key relationships are handled by the base AuditableEntity properties

        // 1-to-1 relationship with RecurrenceRule
        builder.HasOne(ras => ras.RecurrenceRule)
            .WithOne(rr => rr.RecurringAuditSetting)
            .HasForeignKey<RecurrenceRule>(rr => rr.RecurringAuditSettingId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(ras => ras.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for RecurrenceRule entity
/// </summary>
public class RecurrenceRuleConfiguration : IEntityTypeConfiguration<RecurrenceRule>
{
    public void Configure(EntityTypeBuilder<RecurrenceRule> builder)
    {
        builder.ToTable("hwsap_sch_recurrence_rules");

        // Primary Key - Use RecurringAuditSettingId as PK for 1-to-1 relationship
        builder.HasKey(rr => rr.RecurringAuditSettingId);

        // Foreign Key property with consistent length
        builder.Property(rr => rr.RecurringAuditSettingId)
            .HasMaxLength(25)
            .IsRequired();

        // Ignore the inherited Id property since we're using RecurringAuditSettingId as PK
        builder.Ignore(rr => rr.Id);

        // Properties
        builder.Property(rr => rr.FrequencyType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(rr => rr.Interval)
            .IsRequired()
            .HasDefaultValue(1);

        builder.Property(rr => rr.StartDate)
            .IsRequired();

        builder.Property(rr => rr.EndDate)
            .IsRequired(false);

        // Weekly frequency parameters
        builder.Property(rr => rr.WeeklyDaysOfWeekMask)
            .IsRequired(false);

        // Monthly frequency parameters
        builder.Property(rr => rr.MonthlyDayOfMonth)
            .IsRequired(false);

        builder.Property(rr => rr.MonthlyNthWeek)
            .IsRequired(false);

        builder.Property(rr => rr.MonthlyDayOfWeek)
            .IsRequired(false);

        // Yearly frequency parameters
        builder.Property(rr => rr.YearlyMonth)
            .IsRequired(false);

        builder.Property(rr => rr.YearlyDayOfMonth)
            .IsRequired(false);

        builder.Property(rr => rr.YearlyNthWeek)
            .IsRequired(false);

        builder.Property(rr => rr.YearlyDayOfWeek)
            .IsRequired(false);

        // Base entity properties
        builder.Property(rr => rr.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(rr => rr.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(rr => rr.FrequencyType)
            .HasDatabaseName("IX_RecurrenceRules_FrequencyType");

        builder.HasIndex(rr => rr.StartDate)
            .HasDatabaseName("IX_RecurrenceRules_StartDate");

        builder.HasIndex(rr => rr.EndDate)
            .HasDatabaseName("IX_RecurrenceRules_EndDate");

        // Ignore domain events
        builder.Ignore(rr => rr.DomainEvents);
    }
}
