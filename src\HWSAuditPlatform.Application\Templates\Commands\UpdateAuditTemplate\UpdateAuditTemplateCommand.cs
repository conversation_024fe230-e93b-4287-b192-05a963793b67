using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;

/// <summary>
/// Command to update an existing audit template
/// </summary>
public class UpdateAuditTemplateCommand : BaseCommand
{
    /// <summary>
    /// The ID of the audit template to update
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Name of the audit template
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the template's purpose and scope
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Design version of the template
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }
}
