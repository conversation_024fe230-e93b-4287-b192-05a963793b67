{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "HWSAuditPlatform": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost,1433;Database=HWSAuditPlatformDb;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true", "SupportConnection": "Server=localhost,1433;Database=HWSAuditPlatformSupport;User Id=hwsaudit_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "ActiveDirectory": {"Domain": "hwsaudit.local", "Username": "<EMAIL>", "Password": "************", "SearchBase": "OU=Users,OU=HWSAudit,DC=hwsaudit,DC=local", "UseSSL": false, "Port": 389, "TimeoutSeconds": 30, "Server": "localhost"}, "FileStorage": {"Type": "Local"}, "LocalFileStorage": {"StoragePath": "D:\\uploads", "BaseUrl": "https://localhost:5001"}, "AdSync": {"SyncIntervalMinutes": 30, "Enabled": true}, "RecurringAudits": {"GenerationIntervalMinutes": 5, "Enabled": true, "MaxAuditsPerBatch": 50}}