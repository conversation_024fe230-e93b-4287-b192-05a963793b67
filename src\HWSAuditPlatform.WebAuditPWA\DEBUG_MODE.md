# Debug Mode for HWS Audit Platform PWA

This document describes the debug mode functionality implemented for the HWS Audit Platform PWA, which allows developers to work with mock data and bypass authentication during development.

## Overview

The debug mode provides:
- **Mock Authentication**: Bypasses real authentication and uses a configurable mock user
- **Mock API Services**: Returns predefined mock data instead of making real API calls
- **Debug Information Page**: Displays detailed information about the application state
- **Easy Toggle**: Simple configuration-based enable/disable

## Configuration

### Enabling Debug Mode

Edit `wwwroot/appsettings.Development.json`:

```json
{
  "Debug": {
    "SkipAuthentication": true,
    "MockUser": {
      "UserId": "debug-user-001",
      "Username": "debug.user",
      "Email": "<EMAIL>",
      "FullName": "Debug User",
      "Role": "Admin",
      "FactoryId": "1"
    }
  }
}
```

### Disabling Debug Mode

Set `SkipAuthentication` to `false`:

```json
{
  "Debug": {
    "SkipAuthentication": false,
    "MockUser": {
      // ... mock user configuration (still used for debug page display)
    }
  }
}
```

## Features

### Mock Authentication

When debug mode is enabled:
- Users are automatically authenticated with the configured mock user
- No login process is required
- Authentication state is always valid
- JWT tokens are simulated

**Services Used:**
- `DebugAuthenticationService` - Provides mock authentication methods
- `DebugAuthenticationStateProvider` - Manages mock authentication state

### Mock API Services

Debug mode provides mock implementations for all API services:

**Audit API Service (`DebugAuditApiService`)**:
- Returns 3 predefined mock audits with different statuses
- Simulates network delays (100-500ms)
- Supports all audit operations (start, submit, complete)
- Mock file upload functionality

**Template API Service (`DebugTemplateApiService`)**:
- Returns 3 predefined audit templates
- Simulates template retrieval operations

**User API Service (`DebugUserApiService`)**:
- Returns the configured mock user profile
- Supports profile update operations

### Debug Information Page

Access the debug page at `/debug` to view:

**Environment Information:**
- Current environment (Development)
- API base URL configuration
- Debug mode status
- PWA mode detection
- Network connectivity status

**Authentication Details:**
- Current user information
- User roles and permissions
- Authentication token status

**Service Information:**
- Active service implementations
- Service type names for debugging

**Quick Actions:**
- Test API connectivity
- Refresh debug information
- Navigate to key pages

## Implementation Details

### Service Registration

The PWA's `Program.cs` conditionally registers services based on the debug configuration:

```csharp
var skipAuthentication = builder.Configuration.GetValue<bool>("Debug:SkipAuthentication");

if (skipAuthentication)
{
    // Debug services
    builder.Services.AddScoped<AuthenticationStateProvider, DebugAuthenticationStateProvider>();
    builder.Services.AddScoped<IAuthenticationService, DebugAuthenticationService>();
    builder.Services.AddScoped<IAuditApiService, DebugAuditApiService>();
    builder.Services.AddScoped<ITemplateApiService, DebugTemplateApiService>();
    builder.Services.AddScoped<IUserApiService, DebugUserApiService>();
}
else
{
    // Real services
    // ... normal service registration
}
```

### Mock Data

**Mock Audits:**
- Safety Inspection (Scheduled)
- Quality Check (In Progress)  
- Environmental Compliance (Completed)

**Mock Templates:**
- Safety Inspection Template
- Quality Control Template
- Environmental Compliance Template

**Mock User:**
- Configurable through appsettings
- Default: Admin role with factory access
- Supports all user properties

### Navigation Integration

Debug mode is accessible through:
- Main navigation "More" dropdown
- Mobile navigation menu
- Direct URL access (`/debug`)

## Development Workflow

### Starting Development with Debug Mode

1. **Enable debug mode** in `appsettings.Development.json`
2. **Start the PWA**: `dotnet run --project src/HWSAuditPlatform.WebAuditPWA`
3. **Access the application**: http://localhost:5112
4. **View debug information**: http://localhost:5112/debug

### Testing API Integration

1. **Disable debug mode** in configuration
2. **Ensure API is running**: `dotnet run --project src/HWSAuditPlatform.ApiService`
3. **Restart PWA** to use real services
4. **Test authentication** with real credentials

### Debugging Issues

1. **Check service types** on the debug page
2. **Verify configuration** values
3. **Test API connectivity** using debug page actions
4. **Review browser console** for errors

## Security Considerations

⚠️ **Important**: Debug mode should NEVER be enabled in production environments.

- Debug mode bypasses all authentication
- Mock data may contain sensitive information patterns
- Debug endpoints expose internal application state
- Always verify debug mode is disabled before deployment

## Troubleshooting

### Debug Mode Not Working

1. Check `appsettings.Development.json` syntax
2. Verify `SkipAuthentication` is set to `true`
3. Restart the application after configuration changes
4. Check browser console for JavaScript errors

### API Connection Issues

1. Verify API base URL in configuration
2. Ensure API service is running
3. Check CORS configuration
4. Test API endpoints directly

### Authentication Problems

1. Clear browser local storage
2. Check mock user configuration
3. Verify service registration in Program.cs
4. Review authentication state provider logs

## Files Modified/Added

**New Files:**
- `Services/DebugAuthenticationService.cs` - Mock authentication service
- `Services/DebugAuthenticationStateProvider.cs` - Mock authentication state
- `Services/DebugApiServices.cs` - Mock API service implementations
- `Pages/Debug.razor` - Debug information page
- `DEBUG_MODE.md` - This documentation

**Modified Files:**
- `Program.cs` - Conditional service registration
- `wwwroot/appsettings.Development.json` - Debug configuration
- `Components/Navigation/NavMenu.razor` - Debug page navigation

## Comparison with Main Web App

The PWA debug mode follows the same pattern as the main web application:
- Similar configuration structure
- Consistent mock data patterns
- Same debug page layout and functionality
- Equivalent service abstraction approach

This ensures a consistent development experience across both applications.
