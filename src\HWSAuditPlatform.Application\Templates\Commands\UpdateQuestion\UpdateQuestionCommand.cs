using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.UpdateQuestion;

/// <summary>
/// Command to update an existing question in an audit template
/// </summary>
public class UpdateQuestionCommand : BaseCommand
{
    /// <summary>
    /// The ID of the question to update
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// The ID of the audit template (for validation)
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// Optional question group ID to organize the question
    /// </summary>
    public int? QuestionGroupId { get; set; }

    /// <summary>
    /// The actual text of the question
    /// </summary>
    public string QuestionText { get; set; } = string.Empty;

    /// <summary>
    /// The type of answer expected
    /// </summary>
    public QuestionType QuestionType { get; set; }

    /// <summary>
    /// Order in which this question appears
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Whether this question is required
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Weight/importance of this question for scoring
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Help text or instructions for the auditor
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Parent question ID for conditional questions
    /// </summary>
    public int? ParentQuestionId { get; set; }

    /// <summary>
    /// Specific answer value that triggers this conditional question
    /// </summary>
    public string? TriggerAnswerValue { get; set; }

    /// <summary>
    /// Severity level for this question
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Whether evidence is required for this question
    /// </summary>
    public bool EvidenceRequired { get; set; } = false;

    /// <summary>
    /// Allowed evidence types for this question
    /// </summary>
    public List<AddEvidenceTypeRequest> AllowedEvidenceTypes { get; set; } = new();

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }
}
