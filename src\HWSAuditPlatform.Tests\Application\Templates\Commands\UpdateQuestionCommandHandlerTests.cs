using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.UpdateQuestion;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class UpdateQuestionCommandHandlerTests : BaseDbTestClass
{
    private readonly UpdateQuestionCommandHandler _handler;

    public UpdateQuestionCommandHandlerTests()
    {
        _handler = new UpdateQuestionCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldUpdateQuestion()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.Numeric,
            DisplayOrder = 5,
            IsRequired = false,
            Weight = 15.5m,
            HelpText = "Updated help text",
            SeverityLevel = SeverityLevel.Major,
            EvidenceRequired = true,
            RecordVersion = question.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedQuestion = await Context.Questions.FirstOrDefaultAsync(q => q.Id == question.Id);
        updatedQuestion.Should().NotBeNull();
        updatedQuestion!.QuestionText.Should().Be("Updated Question Text");
        updatedQuestion.QuestionType.Should().Be(QuestionType.Numeric);
        updatedQuestion.DisplayOrder.Should().Be(5);
        updatedQuestion.IsRequired.Should().BeFalse();
        updatedQuestion.Weight.Should().Be(15.5m);
        updatedQuestion.HelpText.Should().Be("Updated help text");
        updatedQuestion.SeverityLevel.Should().Be(SeverityLevel.Major);
        updatedQuestion.EvidenceRequired.Should().BeTrue();
        updatedQuestion.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithInvalidQuestionId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = 999,
            TemplateId = template.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = 1
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*Question*999*");
    }

    [Fact]
    public async Task Handle_WithMismatchedTemplateId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template1 = await CreateTestTemplateAsync();
        var template2 = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template1.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template2.Id, // Different template
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*does not belong to the specified template*");
    }

    [Fact]
    public async Task Handle_WithPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsPublished = true;
        await Context.SaveChangesAsync();
        
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*published template*");
    }

    [Fact]
    public async Task Handle_WithStaleRecordVersion_ShouldThrowConcurrencyException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion - 1 // Stale version
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<ConcurrencyException>()
            .WithMessage("*modified by another user*");
    }

    [Fact]
    public async Task Handle_WithValidQuestionGroup_ShouldUpdateQuestionGroup()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var questionGroup = await CreateTestQuestionGroupAsync(template.Id);
        var question = await CreateTestQuestionAsync(template.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            QuestionGroupId = questionGroup.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedQuestion = await Context.Questions.FirstOrDefaultAsync(q => q.Id == question.Id);
        updatedQuestion.Should().NotBeNull();
        updatedQuestion!.QuestionGroupId.Should().Be(questionGroup.Id);
    }

    [Fact]
    public async Task Handle_WithInvalidQuestionGroup_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template1 = await CreateTestTemplateAsync();
        var template2 = await CreateTestTemplateAsync();
        var questionGroup = await CreateTestQuestionGroupAsync(template2.Id); // Different template
        var question = await CreateTestQuestionAsync(template1.Id);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template1.Id,
            QuestionGroupId = questionGroup.Id, // Belongs to different template
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*Question group does not exist*");
    }

    [Fact]
    public async Task Handle_ShouldUpdateTemplateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id);
        var originalTemplateUpdateTime = template.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new UpdateQuestionCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            QuestionText = "Updated Question Text",
            QuestionType = QuestionType.YesNo,
            RecordVersion = question.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedAt.Should().BeAfter(originalTemplateUpdateTime);
    }

    private async Task<Question> CreateTestQuestionAsync(int templateId)
    {
        var question = new Question
        {
            AuditTemplateId = templateId,
            QuestionText = "Original Question Text",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return question;
    }

    private async Task<QuestionGroup> CreateTestQuestionGroupAsync(int templateId)
    {
        var questionGroup = new QuestionGroup
        {
            AuditTemplateId = templateId,
            GroupName = $"Test Group {Guid.NewGuid()}",
            DisplayOrder = 1,
            IsActive = true
        };

        Context.QuestionGroups.Add(questionGroup);
        await Context.SaveChangesAsync();
        return questionGroup;
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isPublished = false)
    {
        var template = new AuditTemplate
        {
            TemplateName = $"Test Template {Guid.NewGuid()}",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
