using Xunit;
using Moq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Blazored.LocalStorage;
using HWSAuditPlatform.WebAuditPWA.Services;
using HWSAuditPlatform.WebAuditPWA.Models;
using System.Net;
using System.Text;
using System.Text.Json;

namespace HWSAuditPlatform.WebAuditPWA.Tests.Services;

public class AuthenticationServiceTests
{
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly Mock<ILocalStorageService> _mockLocalStorage;
    private readonly Mock<ILogger<AuthenticationService>> _mockLogger;
    private readonly AuthenticationService _authenticationService;

    public AuthenticationServiceTests()
    {
        _mockHttpClient = new Mock<HttpClient>();
        _mockLocalStorage = new Mock<ILocalStorageService>();
        _mockLogger = new Mock<ILogger<AuthenticationService>>();
        
        // Note: In a real test, you'd need to properly mock HttpClient
        // This is a simplified example
        _authenticationService = new AuthenticationService(
            _mockHttpClient.Object,
            _mockLocalStorage.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task IsAuthenticatedAsync_WithValidTokenAndUser_ReturnsTrue()
    {
        // Arrange
        var token = "valid-jwt-token";
        var user = new UserInfo
        {
            UserId = "user123",
            Username = "testuser",
            Email = "<EMAIL>",
            FullName = "Test User",
            Role = "Auditor",
            IsActive = true
        };

        _mockLocalStorage.Setup(x => x.GetItemAsync<string>("auth_token"))
            .ReturnsAsync(token);
        _mockLocalStorage.Setup(x => x.GetItemAsync<UserInfo>("user_info"))
            .ReturnsAsync(user);

        // Act
        var result = await _authenticationService.IsAuthenticatedAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthenticatedAsync_WithNoToken_ReturnsFalse()
    {
        // Arrange
        _mockLocalStorage.Setup(x => x.GetItemAsync<string>("auth_token"))
            .ReturnsAsync((string?)null);

        // Act
        var result = await _authenticationService.IsAuthenticatedAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetCurrentUserAsync_ReturnsStoredUser()
    {
        // Arrange
        var expectedUser = new UserInfo
        {
            UserId = "user123",
            Username = "testuser",
            Email = "<EMAIL>",
            FullName = "Test User",
            Role = "Auditor",
            IsActive = true
        };

        _mockLocalStorage.Setup(x => x.GetItemAsync<UserInfo>("user_info"))
            .ReturnsAsync(expectedUser);

        // Act
        var result = await _authenticationService.GetCurrentUserAsync();

        // Assert
        result.Should().BeEquivalentTo(expectedUser);
    }

    [Fact]
    public async Task LogoutAsync_ClearsStoredData()
    {
        // Act
        await _authenticationService.LogoutAsync();

        // Assert
        _mockLocalStorage.Verify(x => x.RemoveItemAsync("auth_token"), Times.Once);
        _mockLocalStorage.Verify(x => x.RemoveItemAsync("user_info"), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task GetTokenAsync_WithInvalidToken_ReturnsNull(string? token)
    {
        // Arrange
        _mockLocalStorage.Setup(x => x.GetItemAsync<string>("auth_token"))
            .ReturnsAsync(token);

        // Act
        var result = await _authenticationService.GetTokenAsync();

        // Assert
        result.Should().Be(token);
    }
}

public class ApiResponseTests
{
    [Fact]
    public void ApiResponse_Success_SetsPropertiesCorrectly()
    {
        // Arrange
        var data = "test data";
        var message = "Success";

        // Act
        var response = new ApiResponse<string>
        {
            Success = true,
            Data = data,
            Message = message,
            StatusCode = 200
        };

        // Assert
        response.Success.Should().BeTrue();
        response.Data.Should().Be(data);
        response.Message.Should().Be(message);
        response.StatusCode.Should().Be(200);
        response.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Result_Success_CreatesSuccessfulResult()
    {
        // Arrange
        var data = "test data";

        // Act
        var result = Result<string>.Success(data);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().Be(data);
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void Result_Failure_CreatesFailedResult()
    {
        // Arrange
        var errorMessage = "Something went wrong";

        // Act
        var result = Result<string>.Failure(errorMessage);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Data.Should().BeNull();
        result.ErrorMessage.Should().Be(errorMessage);
    }
}

public class AuditSummaryDtoTests
{
    [Fact]
    public void ProgressPercentage_WithQuestions_CalculatesCorrectly()
    {
        // Arrange
        var auditSummary = new AuditSummaryDto
        {
            TotalQuestions = 10,
            AnsweredQuestions = 7
        };

        // Act
        var progress = auditSummary.ProgressPercentage;

        // Assert
        progress.Should().Be(70.0);
    }

    [Fact]
    public void ProgressPercentage_WithNoQuestions_ReturnsZero()
    {
        // Arrange
        var auditSummary = new AuditSummaryDto
        {
            TotalQuestions = 0,
            AnsweredQuestions = 0
        };

        // Act
        var progress = auditSummary.ProgressPercentage;

        // Assert
        progress.Should().Be(0.0);
    }

    [Theory]
    [InlineData(10, 10, 100.0)]
    [InlineData(5, 3, 60.0)]
    [InlineData(1, 0, 0.0)]
    public void ProgressPercentage_WithVariousValues_CalculatesCorrectly(int total, int answered, double expected)
    {
        // Arrange
        var auditSummary = new AuditSummaryDto
        {
            TotalQuestions = total,
            AnsweredQuestions = answered
        };

        // Act
        var progress = auditSummary.ProgressPercentage;

        // Assert
        progress.Should().Be(expected);
    }
}
