using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Tests.Common;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace HWSAuditPlatform.Tests.Integration;

/// <summary>
/// Integration tests to validate that area-based responsibility and finding categorization
/// work correctly with the existing audit workflow
/// </summary>
public class AreaResponsibilityIntegrationTests : BaseDbTestClass
{
    private readonly IAreaResponsibilityService _areaResponsibilityService;
    private readonly IFindingCategorizationService _categorizationService;

    public AreaResponsibilityIntegrationTests()
    {
        _areaResponsibilityService = new AreaResponsibilityService();
        _categorizationService = new FindingCategorizationService();
    }

    [Fact]
    public async Task CompleteAuditWorkflow_WithAreaResponsibilityAndCategorization_ShouldWorkCorrectly()
    {
        // Arrange - Set up organizational structure
        var location = new Location { Id = 1, LocationName = "Test Location" };
        var factory = new Factory { Id = 1, FactoryName = "Test Factory", LocationId = 1 };
        var area = new Area { Id = 1, AreaName = "Production Line A", FactoryId = 1, IsActive = true };

        // Set up users
        var auditor = new User 
        { 
            Id = "auditor1", 
            AdObjectGuid = "11111111-1111-1111-1111-111111111111",
            Username = "auditor",
            FirstName = "John",
            LastName = "Auditor"
        };
        var responsibleUser = new User 
        { 
            Id = "responsible1", 
            AdObjectGuid = "*************-2222-2222-************",
            Username = "responsible",
            FirstName = "Jane",
            LastName = "Responsible"
        };
        var analyst = new User 
        { 
            Id = "analyst1", 
            AdObjectGuid = "*************-3333-3333-************",
            Username = "analyst",
            FirstName = "Bob",
            LastName = "Analyst"
        };

        // Set up audit template with new features enabled
        var template = new AuditTemplate
        {
            Id = 1,
            TemplateName = "Safety Audit",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            EnableAreaBasedResponsibility = true,
            EnableFindingCategorization = true
        };

        // Set up area responsibilities
        var correctiveActionResponsibility = new AreaResponsibility
        {
            Id = "ar1",
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = responsibleUser.AdObjectGuid,
            AuditTemplateId = 1,
            IsActive = true,
            Priority = 0
        };

        var retrospectiveResponsibility = new AreaResponsibility
        {
            Id = "ar2",
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.RetrospectiveAnalyst,
            ResponsibleUserId = analyst.AdObjectGuid,
            AuditTemplateId = 1,
            IsActive = true,
            Priority = 0
        };

        // Set up finding categories
        var safetyCategory = new FindingCategory
        {
            Id = 1,
            CategoryName = "Safety Violation",
            Description = "Safety-related issues",
            AuditTemplateId = 1,
            DisplayOrder = 1,
            IsActive = true,
            ColorCode = "#FF5733"
        };

        var qualityCategory = new FindingCategory
        {
            Id = 2,
            CategoryName = "Quality Issue",
            Description = "Quality-related problems",
            AuditTemplateId = 1,
            DisplayOrder = 2,
            IsActive = true,
            ColorCode = "#33FF57"
        };

        // Add entities to context
        Context.Locations.Add(location);
        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.AddRange(auditor, responsibleUser, analyst);
        Context.AuditTemplates.Add(template);
        Context.AreaResponsibilities.AddRange(correctiveActionResponsibility, retrospectiveResponsibility);
        Context.FindingCategories.AddRange(safetyCategory, qualityCategory);

        // Create an audit
        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = auditor.AdObjectGuid,
            ScheduledDate = DateTime.UtcNow,
            OverallStatus = AuditOverallStatus.InProgress
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();

        // Act - Create audit answer and finding
        var auditAnswer = new AuditAnswer
        {
            Id = "aa1",
            AuditId = audit.Id,
            QuestionId = 1,
            OriginalAnswerValue = "No",
            SeverityLevel = SeverityLevel.Major
        };

        Context.AuditAnswers.Add(auditAnswer);
        await Context.SaveChangesAsync();

        var finding = new Finding
        {
            Id = "finding1",
            AuditAnswerId = auditAnswer.Id,
            FindingDescription = "Safety equipment not properly maintained",
            FindingSeverityLevel = SeverityLevel.Major,
            Status = FindingStatus.Open,
            ReportedByUserId = auditor.AdObjectGuid
        };

        Context.Findings.Add(finding);
        await Context.SaveChangesAsync();

        // Test area-based responsibility assignment
        var responsibilities = await Context.AreaResponsibilities
            .Where(ar => ar.AreaId == audit.AreaId && ar.IsActive)
            .ToListAsync();

        await _areaResponsibilityService.AssignResponsibilitiesAsync(finding, audit, responsibilities);

        // Test finding categorization
        var success = _categorizationService.AssignCategory(finding, safetyCategory.Id, template);

        await Context.SaveChangesAsync();

        // Assert - Verify automatic assignments worked
        var updatedFinding = await Context.Findings
            .Include(f => f.ResponsibleUser)
            .Include(f => f.RetrospectiveAnalystUser)
            .Include(f => f.FindingCategory)
            .FirstOrDefaultAsync(f => f.Id == finding.Id);

        Assert.NotNull(updatedFinding);

        // Verify responsible user assignment
        Assert.Equal(responsibleUser.AdObjectGuid, updatedFinding.ResponsibleUserId);
        Assert.Equal(responsibleUser.Username, updatedFinding.ResponsibleUser?.Username);

        // Verify retrospective analyst assignment
        Assert.Equal(analyst.AdObjectGuid, updatedFinding.RetrospectiveAnalystUserId);
        Assert.Equal(analyst.Username, updatedFinding.RetrospectiveAnalystUser?.Username);

        // Verify category assignment
        Assert.True(success);
        Assert.Equal(safetyCategory.Id, updatedFinding.FindingCategoryId);
        Assert.Equal(safetyCategory.CategoryName, updatedFinding.FindingCategory?.CategoryName);

        // Verify backward compatibility - existing audit workflow still works
        Assert.Equal(AuditOverallStatus.InProgress, audit.OverallStatus);
        Assert.Equal(auditor.AdObjectGuid, audit.AssignedToUserId);
        Assert.Equal(FindingStatus.Open, updatedFinding.Status);
    }

    [Fact]
    public async Task ExistingAuditsWithoutNewFeatures_ShouldContinueToWork()
    {
        // Arrange - Create audit template without new features enabled
        var template = new AuditTemplate
        {
            Id = 1,
            TemplateName = "Legacy Audit",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            EnableAreaBasedResponsibility = false, // Disabled
            EnableFindingCategorization = false   // Disabled
        };

        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "11111111-1111-1111-1111-111111111111",
            Username = "user"
        };

        Context.AuditTemplates.Add(template);
        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = user.AdObjectGuid,
            ScheduledDate = DateTime.UtcNow,
            OverallStatus = AuditOverallStatus.Scheduled
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();

        // Act - Create finding without new features
        var auditAnswer = new AuditAnswer
        {
            Id = "aa1",
            AuditId = audit.Id,
            QuestionId = 1,
            OriginalAnswerValue = "No"
        };

        var finding = new Finding
        {
            Id = "finding1",
            AuditAnswerId = auditAnswer.Id,
            FindingDescription = "Legacy finding",
            FindingSeverityLevel = SeverityLevel.Minor,
            Status = FindingStatus.Open,
            ReportedByUserId = user.AdObjectGuid
        };

        Context.AuditAnswers.Add(auditAnswer);
        Context.Findings.Add(finding);
        await Context.SaveChangesAsync();

        // Assert - Verify legacy functionality still works
        var savedFinding = await Context.Findings.FirstOrDefaultAsync(f => f.Id == finding.Id);
        Assert.NotNull(savedFinding);
        Assert.Equal("Legacy finding", savedFinding.FindingDescription);
        Assert.Equal(FindingStatus.Open, savedFinding.Status);
        Assert.Null(savedFinding.ResponsibleUserId); // No automatic assignment
        Assert.Null(savedFinding.RetrospectiveAnalystUserId); // No automatic assignment
        Assert.Null(savedFinding.FindingCategoryId); // No categorization

        // Verify audit workflow is unaffected
        Assert.Equal(AuditOverallStatus.Scheduled, audit.OverallStatus);
        Assert.Equal(user.AdObjectGuid, audit.AssignedToUserId);
    }

    [Fact]
    public async Task MixedTemplateConfiguration_ShouldWorkCorrectly()
    {
        // Arrange - Template with only one feature enabled
        var template = new AuditTemplate
        {
            Id = 1,
            TemplateName = "Mixed Feature Audit",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            EnableAreaBasedResponsibility = true,  // Enabled
            EnableFindingCategorization = false   // Disabled
        };

        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1, IsActive = true };
        var user = new User 
        { 
            Id = "user1", 
            AdObjectGuid = "11111111-1111-1111-1111-111111111111",
            Username = "user"
        };

        var responsibility = new AreaResponsibility
        {
            Id = "ar1",
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = user.AdObjectGuid,
            AuditTemplateId = 1,
            IsActive = true
        };

        Context.AuditTemplates.Add(template);
        Context.Factories.Add(factory);
        Context.Areas.Add(area);
        Context.Users.Add(user);
        Context.AreaResponsibilities.Add(responsibility);

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = user.AdObjectGuid,
            ScheduledDate = DateTime.UtcNow,
            OverallStatus = AuditOverallStatus.InProgress
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();

        // Act
        var finding = new Finding
        {
            Id = "finding1",
            AuditAnswerId = "aa1",
            FindingDescription = "Mixed feature finding",
            FindingSeverityLevel = SeverityLevel.Major,
            Status = FindingStatus.Open,
            ReportedByUserId = user.AdObjectGuid
        };

        Context.Findings.Add(finding);
        await Context.SaveChangesAsync();

        // Test responsibility assignment (should work)
        var responsibilities = new List<AreaResponsibility> { responsibility };
        await _areaResponsibilityService.AssignResponsibilitiesAsync(finding, audit, responsibilities);

        // Test categorization (should not work due to disabled feature)
        var categorySuccess = _categorizationService.AssignCategory(finding, 1, template);

        await Context.SaveChangesAsync();

        // Assert
        var savedFinding = await Context.Findings.FirstOrDefaultAsync(f => f.Id == finding.Id);
        Assert.NotNull(savedFinding);

        // Responsibility assignment should work
        Assert.Equal(user.AdObjectGuid, savedFinding.ResponsibleUserId);

        // Categorization should not work
        Assert.False(categorySuccess);
        Assert.Null(savedFinding.FindingCategoryId);
    }
}
