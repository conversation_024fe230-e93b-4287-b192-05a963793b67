@page "/audits/{auditId}/review"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.Application.Audits.DTOs
@inject IAuditApiService AuditApiService
@inject ILogger<AuditReview> Logger
@inject NavigationManager Navigation

<PageTitle>Audit Review - HWS Audit Platform</PageTitle>

<div class="audit-review-container">
    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading audit for review..." />

    @if (!isLoading && audit == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Audit Not Found</h4>
            <p>The requested audit could not be found or is not available for review.</p>
            <hr>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" @onclick="GoBackToAudits">
                    <i class="bi bi-arrow-left me-2"></i>Back to Audits
                </button>
            </div>
        </div>
    }
    else if (!isLoading && audit != null)
    {
        <!-- Page Header -->
        <div class="page-header mb-4">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/audits" class="text-decoration-none">Audit Management</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="/audits/@AuditId" class="text-decoration-none">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</a>
                            </li>
                            <li class="breadcrumb-item active">Review</li>
                        </ol>
                    </nav>
                    <h1 class="mb-2">
                        <i class="bi bi-clipboard-check me-2"></i>Review Audit
                    </h1>
                    <p class="text-muted mb-0">
                        <i class="bi bi-building me-1"></i>@(audit.Factory?.FactoryName ?? "Unknown Factory")
                        @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                        {
                            <span> • @audit.Area.AreaName</span>
                        }
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" @onclick="GoBackToAudit">
                        <i class="bi bi-arrow-left me-2"></i>Back to Audit
                    </button>
                </div>
            </div>
        </div>

        <!-- User Feedback Messages -->
        <HWSAuditPlatform.WebApp.Components.Shared.ErrorComponent 
            HasError="@(!string.IsNullOrEmpty(errorMessage))"
            Title="Error"
            Message="@errorMessage"
            AlertType="danger"
            OnDismiss="DismissMessages" />

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>@successMessage
                <button type="button" class="btn-close" @onclick="DismissMessages"></button>
            </div>
        }

        <!-- Audit Summary -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">
                            @if (audit.OverallScore.HasValue)
                            {
                                <span class="badge @GetScoreBadgeClass(audit.OverallScore.Value) fs-4">
                                    @audit.OverallScore.Value.ToString("F1")%
                                </span>
                            }
                            else
                            {
                                <span class="text-muted">No Score</span>
                            }
                        </div>
                        <h6 class="card-title">Overall Score</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">
                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                @audit.OverallStatus
                            </span>
                        </div>
                        <h6 class="card-title">Status</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-info">
                            @if (audit.CompletedAt.HasValue)
                            {
                                @audit.CompletedAt.Value.ToString("MMM dd")
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </div>
                        <h6 class="card-title">Completed</h6>
                        @if (audit.CompletedAt.HasValue)
                        {
                            <small class="text-muted">@audit.CompletedAt.Value.ToString("yyyy")</small>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Questions and Answers -->
        @if (auditDetails?.Answers?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-question-circle me-2"></i>Audit Questions & Answers
                    </h5>
                </div>
                <div class="card-body">
                    @foreach (var answer in auditDetails.Answers.OrderBy(a => a.QuestionId))
                    {
                        <div class="question-review-item mb-4 pb-3 border-bottom">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="question-text">
                                        <span class="badge bg-secondary me-2" title="Question @(answer.QuestionId)">Q@(answer.QuestionId)</span>
                                        @answer.QuestionText
                                    </h6>
                                    @if (!string.IsNullOrEmpty(answer.Comments))
                                    {
                                        <p class="text-muted small mb-2">Question comments: @answer.Comments</p>
                                    }

                                    <div class="answer-display">
                                        <div class="answer-value">
                                            <strong>Answer: </strong>
                                            @switch (answer.QuestionType)
                                            {
                                                case QuestionType.YesNo:
                                                    <span class="badge @(answer.AnswerValue == "True" ? "bg-success" : answer.AnswerValue == "False" ? "bg-danger" : "bg-secondary")"
                                                          title="@(answer.AnswerValue == "True" ? "Yes" : answer.AnswerValue == "False" ? "No" : "Not Answered")">
                                                        @(answer.AnswerValue == "True" ? "Yes" : answer.AnswerValue == "False" ? "No" : "Not Answered")
                                                    </span>
                                                    break;
                                                case QuestionType.ShortText:
                                                case QuestionType.LongText:
                                                case QuestionType.Numeric:
                                                case QuestionType.SingleSelect:
                                                    <span class="text-primary">@(answer.AnswerValue ?? "No answer provided")</span>
                                                    break;
                                                case QuestionType.MultiSelect:
                                                    @if (answer.SelectedOptions?.Any() == true)
                                                    {
                                                        var optionsText = string.Join(", ", answer.SelectedOptions.Select(so => so.OptionText));
                                                        <span class="text-primary" title="@optionsText">
                                                            @(optionsText.Length > 50 ? optionsText.Substring(0, 47) + "..." : optionsText)
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">No options selected</span>
                                                    }
                                                    break;
                                                case QuestionType.Date:
                                                    <span class="text-primary">@(answer.AnswerValue ?? "No date provided")</span>
                                                    break;
                                            }
                                        </div>

                                        @if (answer.FailureReasons?.Any() == true)
                                        {
                                            <div class="findings-section mt-2">
                                                <strong class="text-danger">Findings:</strong>
                                                <ul class="list-unstyled mt-1">
                                                    @foreach (var finding in answer.FailureReasons)
                                                    {
                                                        <li class="text-danger">
                                                            <i class="bi bi-exclamation-triangle me-1"></i>@finding.FailureReasonText
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        }

                                        @if (!string.IsNullOrEmpty(answer.Comments))
                                        {
                                            <div class="comments-section mt-2">
                                                <strong>Comments:</strong>
                                                <p class="text-muted mb-0">@answer.Comments</p>
                                            </div>
                                        }

                                        @if (answer.Attachments?.Any() == true)
                                        {
                                            <div class="attachments-section mt-2">
                                                <strong>Evidence:</strong>
                                                <div class="d-flex flex-wrap gap-2 mt-1">
                                                    @foreach (var attachment in answer.Attachments)
                                                    {
                                                        <span class="badge bg-info" title="@attachment.FileName">
                                                            <i class="bi bi-paperclip me-1"></i>
                                                            @(attachment.FileName.Length > 15 ? attachment.FileName.Substring(0, 12) + "..." : attachment.FileName)
                                                        </span>
                                                    }
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    </div>
                                <div class="col-md-4">
                                    <div class="question-meta">
                                        <div class="small text-muted">Question ID: @answer.QuestionId</div>
                                        @if (answer.SeverityLevel.HasValue)
                                        {
                                            <div class="severity-indicator mt-1">
                                                <span class="badge @GetSeverityBadgeClass(answer.SeverityLevel.Value)"
                                                      title="Severity Level: @answer.SeverityLevel.Value">
                                                    @answer.SeverityLevel.Value
                                                </span>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Review Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-square-text me-2"></i>Manager Review
                </h5>
            </div>
            <div class="card-body">
                <form @onsubmit="SubmitReview" @onsubmit:preventDefault="true">
                    <div class="mb-4">
                        <label class="form-label">Review Comments</label>
                        <textarea class="form-control" rows="4" @bind="reviewRequest.ManagerComments"
                                  placeholder="Enter your review comments here..."></textarea>
                        <div class="form-text">
                            Provide feedback on the audit quality, findings, and any recommendations.
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label">Review Decision</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="reviewDecision" id="approve"
                                   @onchange="() => reviewRequest.Approved = true" checked="@reviewRequest.Approved" />
                            <label class="form-check-label text-success" for="approve">
                                <i class="bi bi-check-circle me-1"></i>
                                <strong>Approve Audit</strong> - Accept the audit results as satisfactory
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="reviewDecision" id="reject"
                                   @onchange="() => reviewRequest.Approved = false" checked="@(!reviewRequest.Approved)" />
                            <label class="form-check-label text-warning" for="reject">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                <strong>Request Changes</strong> - Send back for revision or additional work
                            </label>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn @(reviewRequest.Approved ? "btn-success" : "btn-warning")"
                                disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-@(reviewRequest.Approved ? "check-circle" : "exclamation-triangle") me-2"></i>
                            }
                            @(reviewRequest.Approved ? "Approve Audit" : "Request Changes")
                        </button>
                        <button type="button" class="btn btn-outline-secondary" @onclick="GoBackToAudits">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    }
</div>

<style>
    /* Industrial Audit Review Styling */
    .audit-review-container {
        background-color: var(--industrial-dark);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 2px solid var(--industrial-orange);
        color: var(--industrial-text);
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .page-header h1 {
        color: var(--industrial-text);
        font-family: 'Roboto Condensed', sans-serif;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    /* Industrial Cards - Use global styling from app.css */
    .card-title {
        color: var(--industrial-text) !important;
        font-family: 'Roboto Condensed', sans-serif;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin: 0;
    }

    .form-check {
        padding: 1rem;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .form-check:hover {
        background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
        border-color: #3498db;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
    }

    .form-check-input:checked + .form-check-label {
        font-weight: 700;
        color: #2c3e50;
    }

    .form-control {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 0.75rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .form-label {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .form-text {
        color: #7f8c8d;
        font-size: 0.85rem;
        font-style: italic;
    }

    /* Question Review Items */
    .question-review-item {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 4px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .question-text {
        color: var(--industrial-text);
        font-family: 'Roboto Condensed', sans-serif;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .answer-display {
        margin-top: 0.75rem;
    }

    /* Industrial Sections */
    .findings-section {
        background-color: rgba(229, 62, 62, 0.1);
        border-left: 4px solid var(--industrial-red);
        border: 1px solid var(--industrial-red);
        padding: 1rem;
        border-radius: 4px;
        margin-top: 1rem;
        box-shadow: 0 2px 4px rgba(229, 62, 62, 0.2);
    }

    .findings-section strong {
        color: var(--industrial-red);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .comments-section {
        background-color: rgba(43, 108, 176, 0.1);
        border-left: 4px solid var(--industrial-blue);
        border: 1px solid var(--industrial-blue);
        padding: 1rem;
        border-radius: 4px;
        margin-top: 1rem;
        box-shadow: 0 2px 4px rgba(43, 108, 176, 0.2);
    }

    .comments-section strong {
        color: var(--industrial-blue);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .attachments-section {
        background-color: rgba(56, 161, 105, 0.1);
        border-left: 4px solid var(--industrial-green);
        border: 1px solid var(--industrial-green);
        padding: 1rem;
        border-radius: 4px;
        margin-top: 1rem;
        box-shadow: 0 2px 4px rgba(56, 161, 105, 0.2);
    }

    .attachments-section strong {
        color: var(--industrial-green);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .question-meta {
        text-align: right;
    }

    .severity-indicator {
        margin-top: 0.5rem;
    }

    /* Industrial Badge Styling with Text Overflow Handling */
    .badge {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: middle;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    .badge.bg-secondary {
        background-color: var(--industrial-steel) !important;
        color: var(--industrial-text) !important;
        font-size: 0.7rem;
        max-width: 120px;
    }

    .badge.bg-success {
        background-color: var(--industrial-green) !important;
        color: var(--industrial-text) !important;
    }

    .badge.bg-danger {
        background-color: var(--industrial-red) !important;
        color: var(--industrial-text) !important;
    }

    .badge.bg-info {
        background-color: var(--industrial-blue) !important;
        color: var(--industrial-text) !important;
        max-width: 150px;
        font-size: 0.7rem;
    }

    .badge.bg-warning {
        background-color: var(--industrial-orange) !important;
        color: var(--industrial-text) !important;
    }

    .badge.bg-dark {
        background-color: var(--industrial-darker) !important;
        color: var(--industrial-text) !important;
    }

    /* Answer value styling */
    .answer-value {
        margin-bottom: 1rem;
    }

    .answer-value strong {
        color: var(--industrial-text);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .text-primary {
        color: var(--industrial-orange) !important;
        font-weight: 500;
        word-wrap: break-word;
    }

    .text-muted {
        color: var(--industrial-text-muted) !important;
    }

    .text-danger {
        color: var(--industrial-red) !important;
        font-weight: 600;
    }

    .text-success {
        color: var(--industrial-green) !important;
        font-weight: 600;
    }

    /* Form Styling */
    .form-check {
        padding: 1rem;
        border: 1px solid var(--industrial-border);
        border-radius: 4px;
        margin-bottom: 1rem;
        background-color: var(--industrial-gray);
        transition: all 0.3s ease;
    }

    .form-check:hover {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-orange);
    }

    .form-check-input:checked + .form-check-label {
        font-weight: 700;
        color: var(--industrial-text);
    }

    .form-check-label {
        color: var(--industrial-text);
        font-weight: 600;
    }

    .form-label {
        color: var(--industrial-text);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .form-text {
        color: var(--industrial-text-muted);
        font-style: italic;
    }

    /* Question Meta */
    .question-meta {
        text-align: right;
        padding: 0.75rem;
        background-color: rgba(64, 64, 64, 0.3);
        border: 1px solid var(--industrial-border);
        border-radius: 4px;
    }

    .question-meta .small {
        color: var(--industrial-text-muted);
        font-weight: 500;
    }

    /* Severity Indicator */
    .severity-indicator {
        margin-top: 0.5rem;
    }
</style>

@code {
    [Parameter] public string AuditId { get; set; } = string.Empty;

    private Audit? audit;
    private AuditReviewDto? auditDetails;
    private bool isLoading = true;
    private bool isSubmitting = false;
    private string? successMessage = null;
    private string? errorMessage = null;
    private ReviewAuditRequest reviewRequest = new() { Approved = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadAudit();
    }

    private async Task LoadAudit()
    {
        try
        {
            isLoading = true;

            // Load basic audit info
            audit = await AuditApiService.GetAuditByIdAsync(AuditId);

            if (audit != null && audit.OverallStatus != AuditOverallStatus.PendingManagerReview)
            {
                ShowErrorMessage("This audit is not available for review.");
                return;
            }

            // Load detailed audit info for review
            auditDetails = await AuditApiService.GetAuditForReviewAsync(AuditId);

            if (auditDetails == null)
            {
                ShowErrorMessage("Failed to load detailed audit information for review.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit {AuditId} for review", AuditId);
            ShowErrorMessage("Failed to load audit for review. Please try again.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SubmitReview()
    {
        try
        {
            isSubmitting = true;

            var success = await AuditApiService.ReviewAuditAsync(AuditId, reviewRequest);

            if (success)
            {
                ShowSuccessMessage($"Audit review submitted successfully! The audit has been {(reviewRequest.Approved ? "approved" : "sent back for changes")}.");

                // Navigate back to audits list after a short delay
                _ = Task.Delay(2000).ContinueWith(_ =>
                {
                    InvokeAsync(() => Navigation.NavigateTo("/audits"));
                });
            }
            else
            {
                ShowErrorMessage("Failed to submit review. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error submitting review for audit {AuditId}", AuditId);
            ShowErrorMessage("An error occurred while submitting the review. Please try again.");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private string GetScoreBadgeClass(decimal score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-primary",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private void GoBackToAudits()
    {
        Navigation.NavigateTo("/audits");
    }

    private void GoBackToAudit()
    {
        Navigation.NavigateTo($"/audits/{AuditId}");
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
        StateHasChanged();
    }

    private void DismissMessages()
    {
        successMessage = null;
        errorMessage = null;
        StateHasChanged();
    }

    private string GetSeverityBadgeClass(SeverityLevel severity)
    {
        return severity switch
        {
            SeverityLevel.Observation => "bg-info",
            SeverityLevel.Minor => "bg-warning",
            SeverityLevel.Major => "bg-danger",
            SeverityLevel.Critical => "bg-dark",
            _ => "bg-secondary"
        };
    }
}
