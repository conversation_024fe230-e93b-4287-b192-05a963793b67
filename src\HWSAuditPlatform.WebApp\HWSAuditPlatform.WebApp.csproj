<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>HWS Audit Platform - Web App</AssemblyTitle>
    <Product>HWS Audit Platform Web App</Product>
    <Description>Blazor WebAssembly application for audit and template management</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" PrivateAssets="all" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" />
    <PackageReference Include="Microsoft.Extensions.Http" />
    <PackageReference Include="Blazored.LocalStorage" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Forms" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HWSAuditPlatform.Domain\HWSAuditPlatform.Domain.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Application\HWSAuditPlatform.Application.csproj" />
  </ItemGroup>

</Project>
