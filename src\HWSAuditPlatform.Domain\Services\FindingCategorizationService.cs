using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Domain.Services;

/// <summary>
/// Domain service implementation for managing finding categorization
/// </summary>
public class FindingCategorizationService : IFindingCategorizationService
{
    public FindingCategorizationValidationResult ValidateFindingCategories(AuditTemplate auditTemplate)
    {
        var result = new FindingCategorizationValidationResult { IsValid = true };

        if (!auditTemplate.EnableFindingCategorization)
        {
            return result; // No validation needed if categorization is disabled
        }

        var categories = auditTemplate.FindingCategories.Where(c => c.IsActive).ToList();

        if (!categories.Any())
        {
            result.ValidationWarnings.Add("Finding categorization is enabled but no active categories are defined.");
        }

        // Check for duplicate category names
        var duplicateNames = categories
            .GroupBy(c => c.CategoryName.ToLowerInvariant())
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        foreach (var duplicateName in duplicateNames)
        {
            result.ValidationErrors.Add($"Duplicate category name found: '{duplicateName}'");
            result.IsValid = false;
        }

        // Check for invalid display orders
        var invalidOrders = categories
            .GroupBy(c => c.DisplayOrder)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        foreach (var invalidOrder in invalidOrders)
        {
            result.ValidationWarnings.Add($"Multiple categories have the same display order: {invalidOrder}");
        }

        return result;
    }

    public IEnumerable<FindingCategory> GetAvailableCategories(Finding finding, AuditTemplate auditTemplate)
    {
        if (!auditTemplate.EnableFindingCategorization)
        {
            return Enumerable.Empty<FindingCategory>();
        }

        return auditTemplate.FindingCategories
            .Where(c => c.IsActive)
            .OrderBy(c => c.DisplayOrder)
            .ThenBy(c => c.CategoryName);
    }

    public bool AssignCategory(Finding finding, int categoryId, AuditTemplate auditTemplate)
    {
        if (!auditTemplate.EnableFindingCategorization)
        {
            return false;
        }

        var category = auditTemplate.FindingCategories
            .FirstOrDefault(c => c.Id == categoryId && c.IsActive);

        if (category == null)
        {
            return false;
        }

        finding.FindingCategoryId = categoryId;
        return true;
    }

    public void RemoveCategory(Finding finding)
    {
        finding.FindingCategoryId = null;
    }

    public FindingCategoryStatistics GetCategoryStatistics(AuditTemplate auditTemplate, IEnumerable<Finding> findings)
    {
        var findingsList = findings.ToList();
        var categories = auditTemplate.FindingCategories.ToList();

        var statistics = new FindingCategoryStatistics
        {
            TotalCategories = categories.Count,
            ActiveCategories = categories.Count(c => c.IsActive),
            TotalFindings = findingsList.Count,
            CategorizedFindings = findingsList.Count(f => f.FindingCategoryId.HasValue),
            UncategorizedFindings = findingsList.Count(f => !f.FindingCategoryId.HasValue)
        };

        foreach (var category in categories.Where(c => c.IsActive))
        {
            var categoryFindings = findingsList.Where(f => f.FindingCategoryId == category.Id).ToList();
            
            statistics.CategoryUsage[category.Id] = new CategoryUsage
            {
                CategoryId = category.Id,
                CategoryName = category.CategoryName,
                FindingCount = categoryFindings.Count,
                OpenFindingCount = categoryFindings.Count(f => f.IsOpen),
                ClosedFindingCount = categoryFindings.Count(f => f.IsClosed),
                UsagePercentage = statistics.TotalFindings > 0 
                    ? (double)categoryFindings.Count / statistics.TotalFindings * 100 
                    : 0
            };
        }

        return statistics;
    }
}
