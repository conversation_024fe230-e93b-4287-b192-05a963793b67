<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="description" content="HWS Audit Platform - Progressive Web App for audit execution and management in manufacturing environments" />
    <meta name="keywords" content="audit, manufacturing, quality, inspection, PWA" />
    <meta name="author" content="HWS Audit Platform" />

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="HWS Audit PWA" />
    <meta name="application-name" content="HWS Audit PWA" />
    <meta name="msapplication-TileColor" content="#14b8a6" />
    <meta name="theme-color" content="#14b8a6" />

    <title>HWS Audit Platform - Audit Execution</title>
    <base href="/" />

    <!-- Stylesheets -->
    <link rel="stylesheet" href="lib/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="css/fonts.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="css/audit-execution.css" />

    <!-- PWA Manifest -->
    <link href="manifest.webmanifest" rel="manifest" />

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
</head>

<body>
    <div id="app">
        <div class="loading-progress">
            <svg viewBox="0 0 100 100">
                <circle r="40%" cx="50%" cy="50%" />
                <circle r="40%" cx="50%" cy="50%" />
            </svg>
        </div>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred in the audit application.
        <a href="." class="reload">Reload Application</a>
        <button class="dismiss" onclick="document.getElementById('blazor-error-ui').style.display='none'">✕</button>
    </div>

    <script src="_framework/blazor.webassembly.js"></script>
    <script src="lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="js/pwa.js"></script>
    <script>
        // Register service worker with error handling
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('service-worker.js')
                .then(registration => {
                    console.log('Service Worker registered successfully:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    </script>
</body>

</html>
