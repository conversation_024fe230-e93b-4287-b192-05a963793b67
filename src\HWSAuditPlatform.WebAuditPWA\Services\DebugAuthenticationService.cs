using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.WebAuditPWA.Models;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Debug authentication service that returns mock data for development/testing
/// </summary>
public class DebugAuthenticationService : IAuthenticationService
{
    private readonly ILogger<DebugAuthenticationService> _logger;
    private readonly IConfiguration _configuration;
    private UserInfo? _mockUser;
    private string? _mockToken;

    public DebugAuthenticationService(ILogger<DebugAuthenticationService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        InitializeMockData();
    }

    private void InitializeMockData()
    {
        var mockUserSection = _configuration.GetSection("Debug:MockUser");
        _mockUser = new UserInfo
        {
            UserId = mockUserSection["UserId"] ?? "debug-user-001",
            Username = mockUserSection["Username"] ?? "debug.user",
            Email = mockUserSection["Email"] ?? "<EMAIL>",
            FullName = mockUserSection["FullName"] ?? "Debug User",
            Role = mockUserSection["Role"] ?? "Admin",
            FactoryId = mockUserSection["FactoryId"] ?? "1",
            IsActive = true
        };

        _mockToken = "debug-jwt-token-12345";
        _logger.LogWarning("🚨 DEBUG MODE: Mock authentication service initialized for user: {Username}", _mockUser.Username);
    }

    public async Task<AuthenticationResult> LoginAsync(string username, string password)
    {
        await Task.Delay(100); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Mock login for username: {Username}", username);

        return new AuthenticationResult
        {
            Token = _mockToken!,
            Username = _mockUser!.Username,
            Email = _mockUser.Email,
            FullName = _mockUser.FullName,
            Role = _mockUser.Role,
            FactoryId = _mockUser.FactoryId,
            ExpiresAt = DateTime.UtcNow.AddHours(8),
            IsSuccess = true
        };
    }

    public async Task LogoutAsync()
    {
        await Task.Delay(50); // Simulate network delay
        _logger.LogWarning("🚨 DEBUG MODE: Mock logout called");
    }

    public async Task<string?> GetTokenAsync()
    {
        await Task.CompletedTask;
        _logger.LogDebug("🚨 DEBUG MODE: Returning mock token");
        return _mockToken;
    }

    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        await Task.CompletedTask;
        _logger.LogDebug("🚨 DEBUG MODE: Returning mock user: {Username}", _mockUser?.Username);
        return _mockUser;
    }

    public async Task<AuthenticationResult?> RefreshTokenAsync()
    {
        await Task.Delay(100); // Simulate network delay
        
        _logger.LogWarning("🚨 DEBUG MODE: Mock token refresh");

        return new AuthenticationResult
        {
            Token = _mockToken!,
            Username = _mockUser!.Username,
            Email = _mockUser.Email,
            FullName = _mockUser.FullName,
            Role = _mockUser.Role,
            FactoryId = _mockUser.FactoryId,
            ExpiresAt = DateTime.UtcNow.AddHours(8),
            IsSuccess = true
        };
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        await Task.CompletedTask;
        _logger.LogDebug("🚨 DEBUG MODE: Always returning authenticated = true");
        return true;
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        await Task.CompletedTask;
        _logger.LogDebug("🚨 DEBUG MODE: Token validation always returns true");
        return true;
    }
}
