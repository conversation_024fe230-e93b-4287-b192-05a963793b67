using FluentAssertions;
using HWSAuditPlatform.Application.Audits.Queries.GetAudits;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Queries;

public class GetAuditsQueryHandlerTests : BaseDbTestClass
{
    private readonly GetAuditsQueryHandler _handler;

    public GetAuditsQueryHandlerTests()
    {
        _handler = new GetAuditsQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidQuery_ShouldReturnPaginatedAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().NotBeEmpty();
        result.TotalCount.Should().BeGreaterThan(0);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(10);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_ShouldFilterAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            SearchTerm = "Safety",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(a =>
            (a.AuditTemplateName != null && a.AuditTemplateName.Contains("Safety", StringComparison.OrdinalIgnoreCase)) ||
            (a.FactoryName != null && a.FactoryName.Contains("Safety", StringComparison.OrdinalIgnoreCase)) ||
            (a.AreaName != null && a.AreaName.Contains("Safety", StringComparison.OrdinalIgnoreCase)));
    }

    [Fact]
    public async Task Handle_WithStatusFilter_ShouldFilterByStatus()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            Status = AuditOverallStatus.InProgress,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(a => a.OverallStatus == AuditOverallStatus.InProgress);
    }

    [Fact]
    public async Task Handle_WithAssignedUserFilter_ShouldFilterByUser()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            AssignedToUserId = "test-user-1",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().NotBeEmpty();
        // Note: We can't directly check AssignedToUserId in the DTO, but we can verify the filter worked
        result.TotalCount.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_WithFactoryFilter_ShouldFilterByFactory()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            FactoryId = 1,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(a => a.FactoryName == "Test Factory");
    }

    [Fact]
    public async Task Handle_WithDateRangeFilter_ShouldFilterByDateRange()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var fromDate = DateTime.UtcNow.AddDays(-1);
        var toDate = DateTime.UtcNow.AddDays(5);
        
        var query = new GetAuditsQuery
        {
            ScheduledDateFrom = fromDate,
            ScheduledDateTo = toDate,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(a => 
            a.ScheduledDate >= fromDate && a.ScheduledDate <= toDate);
    }

    [Fact]
    public async Task Handle_WithOverdueFilter_ShouldFilterOverdueAudits()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateOverdueAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            IsOverdue = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(a => a.IsOverdue);
    }

    [Fact]
    public async Task Handle_WithSorting_ShouldSortCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestAuditsAsync();
        
        var query = new GetAuditsQuery
        {
            SortBy = "TemplateName",
            SortDirection = "asc",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeInAscendingOrder(a => a.AuditTemplateName);
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateManyTestAuditsAsync(15); // Create more than one page
        
        var query = new GetAuditsQuery
        {
            PageNumber = 2,
            PageSize = 5
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(5);
        result.Items.Should().HaveCount(5);
        result.TotalCount.Should().Be(15);
        result.TotalPages.Should().Be(3);
    }

    private async Task CreateTestAuditsAsync()
    {
        var template1 = new AuditTemplate
        {
            TemplateName = "Safety Audit Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var template2 = new AuditTemplate
        {
            TemplateName = "Quality Audit Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.AddRange(template1, template2);
        await Context.SaveChangesAsync();

        var audits = new[]
        {
            new Audit
            {
                Id = $"c{Guid.NewGuid():N}"[..25],
                AuditTemplateId = template1.Id,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "test-user-1",
                ScheduledDate = DateTime.UtcNow.AddDays(1),
                DueDate = DateTime.UtcNow.AddDays(7),
                OverallStatus = AuditOverallStatus.Scheduled,
                FactoryId = 1,
                AreaId = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Audit
            {
                Id = $"c{Guid.NewGuid():N}"[..25],
                AuditTemplateId = template2.Id,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "test-user-2",
                ScheduledDate = DateTime.UtcNow.AddDays(2),
                DueDate = DateTime.UtcNow.AddDays(8),
                OverallStatus = AuditOverallStatus.InProgress,
                FactoryId = 1,
                AreaId = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.Audits.AddRange(audits);
        await Context.SaveChangesAsync();
    }

    private async Task CreateOverdueAuditsAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Overdue Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var overdueAudit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-1",
            ScheduledDate = DateTime.UtcNow.AddDays(-5),
            DueDate = DateTime.UtcNow.AddDays(-1), // Overdue
            OverallStatus = AuditOverallStatus.InProgress,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(overdueAudit);
        await Context.SaveChangesAsync();
    }

    private async Task CreateManyTestAuditsAsync(int count)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Bulk Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audits = new List<Audit>();
        for (int i = 0; i < count; i++)
        {
            audits.Add(new Audit
            {
                Id = $"c{Guid.NewGuid():N}"[..25],
                AuditTemplateId = template.Id,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "test-user-1",
                ScheduledDate = DateTime.UtcNow.AddDays(i),
                DueDate = DateTime.UtcNow.AddDays(i + 7),
                OverallStatus = AuditOverallStatus.Scheduled,
                FactoryId = 1,
                AreaId = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        Context.Audits.AddRange(audits);
        await Context.SaveChangesAsync();
    }
}
