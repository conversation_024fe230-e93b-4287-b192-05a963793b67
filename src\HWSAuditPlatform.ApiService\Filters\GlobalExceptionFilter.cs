using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Filters;

/// <summary>
/// Global exception filter for handling application exceptions
/// </summary>
public class GlobalExceptionFilter : IExceptionFilter
{
    private readonly ILogger<GlobalExceptionFilter> _logger;

    public GlobalExceptionFilter(ILogger<GlobalExceptionFilter> logger)
    {
        _logger = logger;
    }

    public void OnException(ExceptionContext context)
    {
        _logger.LogError(context.Exception, "An exception occurred in action {ActionName}", 
            context.ActionDescriptor.DisplayName);

        var response = context.Exception switch
        {
            ValidationException ex => new ApiErrorResponse
            {
                StatusCode = 400,
                Title = "Validation Error",
                Detail = "One or more validation errors occurred",
                Errors = ex.Errors,
                TraceId = context.HttpContext.TraceIdentifier
            },
            NotFoundException ex => new ApiErrorResponse
            {
                StatusCode = 404,
                Title = "Resource Not Found",
                Detail = ex.Message,
                TraceId = context.HttpContext.TraceIdentifier
            },
            ForbiddenException ex => new ApiErrorResponse
            {
                StatusCode = 403,
                Title = "Access Forbidden",
                Detail = ex.Message,
                TraceId = context.HttpContext.TraceIdentifier
            },
            ConflictException ex => new ApiErrorResponse
            {
                StatusCode = 409,
                Title = "Conflict",
                Detail = ex.Message,
                TraceId = context.HttpContext.TraceIdentifier
            },
            BusinessRuleViolationException ex => new ApiErrorResponse
            {
                StatusCode = 400,
                Title = "Business Rule Violation",
                Detail = ex.Message,
                TraceId = context.HttpContext.TraceIdentifier
            },
            _ => new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Detail = "An internal server error occurred",
                TraceId = context.HttpContext.TraceIdentifier
            }
        };

        context.Result = new ObjectResult(response)
        {
            StatusCode = response.StatusCode
        };

        context.ExceptionHandled = true;
    }
}
