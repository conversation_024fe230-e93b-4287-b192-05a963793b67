# Docker Environment Configuration for HWS Audit Platform

# SQL Server Configuration
MSSQL_SA_PASSWORD=HWSAudit123!
MSSQL_PID=Developer
ACCEPT_EULA=Y

# Application Database User
DB_USER=hwsaudit_user
DB_PASSWORD=HWSAudit123!

# Active Directory Configuration
AD_DOMAIN=HWSAUDIT
AD_DOMAIN_PASS=HWSAudit123!
AD_DNS_FORWARDER=*******
AD_HOST_IP=***********

# Application Configuration
ASPNETCORE_ENVIRONMENT=Docker
DOTNET_ENVIRONMENT=Docker

# JWT Configuration
JWT_KEY=DockerDevelopmentKeyThatIsAtLeast32CharactersLongForTesting!
JWT_ISSUER=HWSAuditPlatform-Docker
JWT_AUDIENCE=HWSAuditPlatformUsers-Docker

# Network Configuration
DOCKER_SUBNET=**********/16
AD_CONTAINER_IP=***********
