using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Tests.Infrastructure.Persistence;

/// <summary>
/// Tests for UnitOfWork implementation focusing on transaction management and disposal
/// </summary>
public class UnitOfWorkTests : BaseDbTestClass
{
    private UnitOfWork _unitOfWork;

    public UnitOfWorkTests()
    {
        _unitOfWork = new UnitOfWork(Context);
    }

    [Fact]
    public async Task SaveChangesAsync_WithValidChanges_ShouldReturnAffectedRowCount()
    {
        // Arrange
        var user = User.Create(
            username: "testuser",
            firstName: "Test",
            lastName: "User", 
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(user);

        // Act
        var result = await _unitOfWork.SaveChangesAsync();

        // Assert
        result.Should().Be(1);
        
        var savedUser = await Context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
        savedUser.Should().NotBeNull();
    }

    [Fact]
    public async Task SaveChangesAsync_WithNoChanges_ShouldReturnZero()
    {
        // Arrange - no changes made to context

        // Act
        var result = await _unitOfWork.SaveChangesAsync();

        // Assert
        result.Should().Be(0);
    }

    [Fact]
    public async Task SaveChangesAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        var user = User.Create(
            username: "testuser",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>", 
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(user);

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() => 
            _unitOfWork.SaveChangesAsync(cts.Token));
    }

    [Fact]
    public async Task BeginTransactionAsync_ShouldReturnValidTransaction()
    {
        // Act
        using var transaction = await _unitOfWork.BeginTransactionAsync();

        // Assert
        transaction.Should().NotBeNull();
        transaction.Should().BeAssignableTo<IDbTransaction>();
    }

    [Fact]
    public async Task BeginTransactionAsync_WithCommit_ShouldPersistChanges()
    {
        // Arrange
        var user = User.Create(
            username: "transactionuser",
            firstName: "Transaction",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        using var transaction = await _unitOfWork.BeginTransactionAsync();
        
        Context.Users.Add(user);
        await _unitOfWork.SaveChangesAsync();
        await transaction.CommitAsync();

        // Assert
        var savedUser = await Context.Users.FirstOrDefaultAsync(u => u.Username == "transactionuser");
        savedUser.Should().NotBeNull();
    }

    [Fact]
    public async Task BeginTransactionAsync_WithRollback_ShouldNotPersistChanges()
    {
        // Arrange
        var user = User.Create(
            username: "rollbackuser",
            firstName: "Rollback",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        using var transaction = await _unitOfWork.BeginTransactionAsync();
        
        Context.Users.Add(user);
        await _unitOfWork.SaveChangesAsync();
        await transaction.RollbackAsync();

        // Assert
        var savedUser = await Context.Users.FirstOrDefaultAsync(u => u.Username == "rollbackuser");
        savedUser.Should().BeNull();
    }

    [Fact]
    public async Task BeginTransactionAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() => 
            _unitOfWork.BeginTransactionAsync(cts.Token));
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _unitOfWork.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        _unitOfWork.Dispose();
        var act = () => _unitOfWork.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task SaveChangesAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _unitOfWork.Dispose();

        // Act & Assert
        await Assert.ThrowsAsync<ObjectDisposedException>(() => 
            _unitOfWork.SaveChangesAsync());
    }

    public override void Dispose()
    {
        _unitOfWork?.Dispose();
        base.Dispose();
    }
}
