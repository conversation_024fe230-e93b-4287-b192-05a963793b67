namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the status of a finding throughout its lifecycle.
/// Maps to the finding_status enum in the database.
/// </summary>
public enum FindingStatus
{
    /// <summary>
    /// Finding is open and requires attention
    /// </summary>
    Open,

    /// <summary>
    /// Finding is under investigation
    /// </summary>
    UnderInvestigation,

    /// <summary>
    /// Finding is pending corrective action
    /// </summary>
    PendingCorrectiveAction,

    /// <summary>
    /// Finding is pending verification of corrective actions
    /// </summary>
    PendingVerification,

    /// <summary>
    /// Finding has been closed
    /// </summary>
    Closed,

    /// <summary>
    /// Finding has been rejected
    /// </summary>
    Rejected
}
