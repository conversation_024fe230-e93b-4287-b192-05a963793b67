using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateSubArea;

/// <summary>
/// Command to create a new sub-area within an area
/// </summary>
public class CreateSubAreaCommand : BaseCommand<int>
{
    public string SubAreaName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
}
