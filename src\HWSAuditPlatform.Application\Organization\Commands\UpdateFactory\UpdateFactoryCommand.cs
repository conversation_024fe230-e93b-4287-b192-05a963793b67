using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Organization.Commands.UpdateFactory;

/// <summary>
/// Command to update an existing factory
/// </summary>
public class UpdateFactoryCommand : BaseCommand
{
    public int Id { get; set; }
    public string FactoryName { get; set; } = string.Empty;
    public string? FactoryProcess { get; set; }
    public int LocationId { get; set; }
    public string? AddressLine1 { get; set; }
    public string? City { get; set; }
    public string? PostalCode { get; set; }
    public bool IsActive { get; set; } = true;
    public int RecordVersion { get; set; }
}
