﻿@using Microsoft.AspNetCore.Components.Authorization
@inherits LayoutComponentBase

<div class="webapp-container">
    <AuthorizeView>
        <Authorized>
            <div class="page">
                <!-- Navigation Sidebar -->
                <div class="sidebar">
                    <NavMenu />
                </div>

                <!-- Main Content -->
                <main class="webapp-content">
                    <div class="container-fluid">
                        @Body
                    </div>
                </main>
            </div>

            <!-- Footer -->
            <footer class="webapp-footer">
                <div class="container-fluid">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">HWS Audit Platform Management v1.0</small>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3">Welcome, @context.User.FindFirst("FullName")?.Value</span>
                            <button class="btn btn-sm btn-outline-secondary" @onclick="LogoutAsync">
                                <i class="bi bi-box-arrow-right me-1"></i>Logout
                            </button>
                        </div>
                    </div>
                </div>
            </footer>
        </Authorized>
        <NotAuthorized>
            <div class="webapp-container">
                <main class="webapp-content d-flex align-items-center justify-content-center">
                    @Body
                </main>
            </div>
        </NotAuthorized>
    </AuthorizeView>
</div>

@code {
    [Inject] private IAuthenticationService AuthService { get; set; } = default!;
    [Inject] private AuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;

    private async Task LogoutAsync()
    {
        try
        {
            await AuthService.LogoutAsync();

            // Only call MarkUserAsLoggedOut if it's the custom provider
            if (AuthStateProvider is CustomAuthenticationStateProvider customProvider)
            {
                customProvider.MarkUserAsLoggedOut();
            }

            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception)
        {
            // Fallback to page reload if logout fails
            Navigation.NavigateTo("/login", forceLoad: true);
        }
    }
}
