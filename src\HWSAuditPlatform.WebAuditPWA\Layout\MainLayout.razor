﻿@using Microsoft.AspNetCore.Components.Authorization
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime
@inject IAuthenticationService AuthService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="pwa-container">
    <AuthorizeView>
        <Authorized>
                <!-- Navigation -->
                <NavMenu />

                <!-- Main Content -->
                <main class="pwa-content">
                    <div class="container-fluid">
                        @Body
                    </div>
                </main>

                <!-- Sync Status Indicator -->
                <SyncStatusIndicator />

                <!-- Footer -->
                @* <footer class="pwa-footer">
                    <div class="container-fluid">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">HWS Audit Platform PWA v1.0</small>
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-2" id="network-status">
                                    <i class="fas fa-wifi me-1"></i>
                                    Online
                                </span>
                                <div class="loading-spinner d-none" id="sync-spinner"></div>
                            </div>
                        </div>
                    </div>
                </footer> *@
            </Authorized>
            <NotAuthorized>
                <div class="pwa-container">
                    <main class="pwa-content d-flex align-items-center justify-content-center">
                        @Body
                    </main>
                </div>
            </NotAuthorized>
        </AuthorizeView>
</div>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialize PWA features
            await JSRuntime.InvokeVoidAsync("initializePwa");

            // Load tablet gesture controls
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (!document.querySelector('script[src=""js/tablet-gestures.js""]')) {
                    const script = document.createElement('script');
                    script.src = 'js/tablet-gestures.js';
                    script.async = true;
                    document.head.appendChild(script);
                }
            ");
        }
    }

    private async Task LogoutAsync()
    {
        try
        {
            await AuthService.LogoutAsync();
            AuthStateProvider.MarkUserAsLoggedOut();
            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception)
        {
            // Fallback to page reload if logout fails
            await JSRuntime.InvokeVoidAsync("location.reload");
        }
    }
}
