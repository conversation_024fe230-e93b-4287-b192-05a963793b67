using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSettings;

/// <summary>
/// Handler for GetRecurringAuditSettingsQuery
/// </summary>
public class GetRecurringAuditSettingsQueryHandler : BaseQueryHandler<GetRecurringAuditSettingsQuery, PaginatedResult<RecurringAuditSettingSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetRecurringAuditSettingsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<RecurringAuditSettingSummaryDto>> Handle(GetRecurringAuditSettingsQuery request, CancellationToken cancellationToken)
    {
        // Build the base query
        var query = _context.RecurringAuditSettings
            .Include(r => r.AuditTemplate)
            .Include(r => r.RecurrenceRule)
            .Include(r => r.AssignToUser)
            .Include(r => r.AssignToUserGroup)
            .Include(r => r.Factory)
            .Include(r => r.Area)
            .Include(r => r.SubArea)
            .AsQueryable();

        // Apply filters
        if (!request.IncludeInactive)
        {
            query = query.Where(r => r.IsEnabled);
        }

        if (request.IsEnabled.HasValue)
        {
            query = query.Where(r => r.IsEnabled == request.IsEnabled.Value);
        }

        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(r => r.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(r => r.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(r => r.AreaId == request.AreaId.Value);
        }

        if (request.SubAreaId.HasValue)
        {
            query = query.Where(r => r.SubAreaId == request.SubAreaId.Value);
        }

        if (request.AssignmentType.HasValue)
        {
            query = query.Where(r => r.AssignmentType == request.AssignmentType.Value);
        }

        if (!string.IsNullOrEmpty(request.AssignedToUserId))
        {
            query = query.Where(r => r.AssignToUserId == request.AssignedToUserId);
        }

        if (!string.IsNullOrEmpty(request.AssignedToUserGroupId))
        {
            query = query.Where(r => r.AssignToUserGroupId == request.AssignedToUserGroupId);
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(r => r.SettingName.ToLower().Contains(searchTerm) ||
                                    (r.AuditTemplate != null && r.AuditTemplate.TemplateName.ToLower().Contains(searchTerm)));
        }

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDirection);

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var items = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(r => MapToSummaryDto(r))
            .ToListAsync(cancellationToken);

        return PaginatedResult<RecurringAuditSettingSummaryDto>.Create(items, totalCount, request.PageNumber, request.PageSize);
    }

    private static IQueryable<RecurringAuditSetting> ApplySorting(IQueryable<RecurringAuditSetting> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.ToLower() == "desc";

        return sortBy.ToLower() switch
        {
            "settingname" => isDescending ? query.OrderByDescending(r => r.SettingName) : query.OrderBy(r => r.SettingName),
            "templatename" => isDescending ? query.OrderByDescending(r => r.AuditTemplate!.TemplateName) : query.OrderBy(r => r.AuditTemplate!.TemplateName),
            "isenabled" => isDescending ? query.OrderByDescending(r => r.IsEnabled) : query.OrderBy(r => r.IsEnabled),
            "nextgenerationdate" => isDescending ? query.OrderByDescending(r => r.NextGenerationDate) : query.OrderBy(r => r.NextGenerationDate),
            "lastgeneratedat" => isDescending ? query.OrderByDescending(r => r.LastGeneratedAt) : query.OrderBy(r => r.LastGeneratedAt),
            "createdat" => isDescending ? query.OrderByDescending(r => r.CreatedAt) : query.OrderBy(r => r.CreatedAt),
            _ => query.OrderBy(r => r.SettingName)
        };
    }

    private static RecurringAuditSettingSummaryDto MapToSummaryDto(RecurringAuditSetting setting)
    {
        return new RecurringAuditSettingSummaryDto
        {
            Id = setting.Id,
            SettingName = setting.SettingName,
            AuditTemplateName = setting.AuditTemplate?.TemplateName,
            IsEnabled = setting.IsEnabled,
            AssignmentType = setting.AssignmentType,
            AssignToUserName = setting.AssignToUser != null
                ? $"{setting.AssignToUser.FirstName} {setting.AssignToUser.LastName}".Trim()
                : null,
            AssignToUserGroupName = setting.AssignToUserGroup?.GroupName,
            FactoryName = setting.Factory?.FactoryName,
            AreaName = setting.Area?.AreaName,
            SubAreaName = setting.SubArea?.SubAreaName,
            NextGenerationDate = setting.NextGenerationDate,
            LastGeneratedAt = setting.LastGeneratedAt,
            RecurrenceDescription = GenerateRecurrenceDescription(setting.RecurrenceRule),
            IsReadyToGenerate = setting.IsEnabled && 
                               setting.NextGenerationDate.HasValue && 
                               setting.NextGenerationDate.Value <= DateOnly.FromDateTime(DateTime.UtcNow),
            CreatedAt = setting.CreatedAt,
            UpdatedAt = setting.UpdatedAt,
            RecordVersion = setting.RecordVersion
        };
    }

    private static string GenerateRecurrenceDescription(RecurrenceRule? rule)
    {
        if (rule == null)
            return "No recurrence rule";

        return rule.FrequencyType switch
        {
            Domain.Enums.FrequencyType.DAILY => $"Every {rule.Interval} day(s)",
            Domain.Enums.FrequencyType.WEEKLY => GenerateWeeklyDescription(rule),
            Domain.Enums.FrequencyType.MONTHLY => GenerateMonthlyDescription(rule),
            Domain.Enums.FrequencyType.YEARLY => GenerateYearlyDescription(rule),
            _ => "Unknown frequency"
        };
    }

    private static string GenerateWeeklyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} week(s)";
        
        if (rule.WeeklyDaysOfWeekMask.HasValue)
        {
            var days = new List<string>();
            var mask = rule.WeeklyDaysOfWeekMask.Value;
            var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
            
            for (int i = 0; i < 7; i++)
            {
                if ((mask & (1 << i)) != 0)
                {
                    days.Add(dayNames[i]);
                }
            }
            
            if (days.Any())
            {
                description += $" on {string.Join(", ", days)}";
            }
        }
        
        return description;
    }

    private static string GenerateMonthlyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} month(s)";
        
        if (rule.MonthlyDayOfMonth.HasValue)
        {
            description += $" on day {rule.MonthlyDayOfMonth.Value}";
        }
        else if (rule.MonthlyNthWeek.HasValue && rule.MonthlyDayOfWeek.HasValue)
        {
            var weekNames = new[] { "", "1st", "2nd", "3rd", "4th", "5th" };
            var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
            
            if (rule.MonthlyNthWeek.Value <= 5 && rule.MonthlyDayOfWeek.Value <= 6)
            {
                description += $" on the {weekNames[rule.MonthlyNthWeek.Value]} {dayNames[rule.MonthlyDayOfWeek.Value]}";
            }
        }
        
        return description;
    }

    private static string GenerateYearlyDescription(RecurrenceRule rule)
    {
        var description = $"Every {rule.Interval} year(s)";
        
        if (rule.YearlyMonth.HasValue)
        {
            var monthNames = new[] { "", "January", "February", "March", "April", "May", "June",
                                   "July", "August", "September", "October", "November", "December" };
            
            if (rule.YearlyMonth.Value <= 12)
            {
                description += $" in {monthNames[rule.YearlyMonth.Value]}";
                
                if (rule.YearlyDayOfMonth.HasValue)
                {
                    description += $" on day {rule.YearlyDayOfMonth.Value}";
                }
                else if (rule.YearlyNthWeek.HasValue && rule.YearlyDayOfWeek.HasValue)
                {
                    var weekNames = new[] { "", "1st", "2nd", "3rd", "4th", "5th" };
                    var dayNames = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
                    
                    if (rule.YearlyNthWeek.Value <= 5 && rule.YearlyDayOfWeek.Value <= 6)
                    {
                        description += $" on the {weekNames[rule.YearlyNthWeek.Value]} {dayNames[rule.YearlyDayOfWeek.Value]}";
                    }
                }
            }
        }
        
        return description;
    }
}
