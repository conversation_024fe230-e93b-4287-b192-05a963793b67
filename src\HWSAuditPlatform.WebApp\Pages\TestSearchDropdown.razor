@page "/test/search-dropdown"
@using Microsoft.AspNetCore.Authorization
@inject IOrganizationApiService OrganizationApiService
@inject ILogger<TestSearchDropdown> Logger

<PageTitle>Search Dropdown Test</PageTitle>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-search me-2"></i>
                        Search Dropdown Component Test
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>User Search Example</h5>
                            <div class="mb-3">
                                <label class="form-label">Search Users</label>
                                <SearchDropdown TItem="UserSummary"
                                              Placeholder="Type to search users..."
                                              Required="false"
                                              MaxResults="5"
                                              DebounceMs="300"
                                              SearchFunction="SearchUsersAsync"
                                              GetPrimaryText="@(user => user.FullName)"
                                              GetSecondaryText="@(user => user.Username)"
                                              GetAdditionalInfo="@(user => user.FactoryName ?? string.Empty)"
                                              GetDisplayText="@(user => $"{user.FullName} ({user.Username})")"
                                              @bind-SelectedItem="selectedUser"
                                              OnItemSelected="OnUserSelected"
                                              OnCleared="OnUserCleared" />
                            </div>
                            
                            @if (selectedUser != null)
                            {
                                <div class="alert alert-info">
                                    <h6>Selected User:</h6>
                                    <p><strong>Name:</strong> @selectedUser.FullName</p>
                                    <p><strong>Username:</strong> @selectedUser.Username</p>
                                    <p><strong>Email:</strong> @(selectedUser.Email ?? "N/A")</p>
                                    <p><strong>Role:</strong> @selectedUser.Role</p>
                                    <p><strong>Factory:</strong> @(selectedUser.FactoryName ?? "N/A")</p>
                                    <p><strong>AD GUID:</strong> @selectedUser.AdObjectGuid</p>
                                </div>
                            }
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Component Features</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Generic type support (TItem)
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Real-time search with debouncing
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Server-side filtering via API
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Customizable display properties
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Event callbacks for selection
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Two-way binding support
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Loading states and error handling
                                </li>
                                <li class="list-group-item">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Industrial theme styling
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Usage Instructions</h5>
                            <ol>
                                <li>Start typing in the search box above</li>
                                <li>Results will appear after a 300ms delay (debouncing)</li>
                                <li>Click on a result to select it</li>
                                <li>The selected item details will appear below</li>
                                <li>Clear the search box to reset the selection</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private UserSummary? selectedUser = null;

    // Search function that calls the API service
    private async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int maxResults)
    {
        try
        {
            return await OrganizationApiService.SearchUsersAsync(searchTerm, maxResults);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error searching users with term: {SearchTerm}", searchTerm);
            return Enumerable.Empty<UserSummary>();
        }
    }

    // Handle item selection
    private void OnUserSelected(UserSummary user)
    {
        selectedUser = user;
        Logger.LogInformation("User selected: {UserName} ({Username})", user.FullName, user.Username);
        StateHasChanged();
    }

    // Handle clearing selection
    private void OnUserCleared()
    {
        selectedUser = null;
        Logger.LogInformation("User selection cleared");
        StateHasChanged();
    }
}
