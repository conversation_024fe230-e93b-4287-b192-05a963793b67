using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Workflow.Commands.CreateCorrectionRequest;

/// <summary>
/// Command to create a new correction request
/// </summary>
public class CreateCorrectionRequestCommand : BaseCommand<string>
{
    /// <summary>
    /// The audit ID to request correction for
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Justification for the correction request
    /// </summary>
    public string RequestReason { get; set; } = string.Empty;
}
