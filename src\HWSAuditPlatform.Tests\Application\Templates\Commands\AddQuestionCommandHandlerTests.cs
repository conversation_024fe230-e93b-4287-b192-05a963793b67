using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestion;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class AddQuestionCommandHandlerTests : BaseDbTestClass
{
    private readonly AddQuestionCommandHandler _handler;

    public AddQuestionCommandHandlerTests()
    {
        _handler = new AddQuestionCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldAddQuestion()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "Is the safety equipment properly maintained?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            Weight = 10.0m,
            HelpText = "Check all safety equipment in the area"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeGreaterThan(0);
        
        var question = await Context.Questions.FirstOrDefaultAsync(q => q.Id == result);
        question.Should().NotBeNull();
        question!.AuditTemplateId.Should().Be(command.AuditTemplateId);
        question.QuestionText.Should().Be(command.QuestionText);
        question.QuestionType.Should().Be(command.QuestionType);
        question.DisplayOrder.Should().Be(command.DisplayOrder);
        question.IsRequired.Should().Be(command.IsRequired);
        question.Weight.Should().Be(command.Weight);
        question.HelpText.Should().Be(command.HelpText);
        question.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithNonExistentTemplate_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = 999, // Non-existent template
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync(isPublished: true);
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithQuestionOptions_ShouldAddOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "What is the condition of the equipment?",
            QuestionType = QuestionType.SingleSelect,
            Options = new List<AddQuestionOptionRequest>
            {
                new() { OptionText = "Excellent", OptionValue = "excellent", DisplayOrder = 1 },
                new() { OptionText = "Good", OptionValue = "good", DisplayOrder = 2 },
                new() { OptionText = "Poor", OptionValue = "poor", DisplayOrder = 3 }
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var options = await Context.QuestionOptions
            .Where(o => o.QuestionId == result)
            .OrderBy(o => o.DisplayOrder)
            .ToListAsync();
            
        options.Should().HaveCount(3);
        options[0].OptionText.Should().Be("Excellent");
        options[0].OptionValue.Should().Be("excellent");
        options[1].OptionText.Should().Be("Good");
        options[2].OptionText.Should().Be("Poor");
    }

    [Fact]
    public async Task Handle_WithConditionalQuestion_ShouldSetParentRelationship()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        // Create parent question first
        var parentQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Is equipment present?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        
        Context.Questions.Add(parentQuestion);
        await Context.SaveChangesAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "What is the equipment condition?",
            QuestionType = QuestionType.ShortText,
            ParentQuestionId = parentQuestion.Id,
            TriggerAnswerValue = "Yes"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var question = await Context.Questions.FirstOrDefaultAsync(q => q.Id == result);
        question.Should().NotBeNull();
        question!.ParentQuestionId.Should().Be(parentQuestion.Id);
        question.TriggerAnswerValue.Should().Be("Yes");
    }

    [Fact]
    public async Task Handle_WithEvidenceRequired_ShouldSetEvidenceFields()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "Provide evidence of compliance",
            QuestionType = QuestionType.LongText,
            EvidenceRequired = true,
            AllowedEvidenceTypes = new List<AddEvidenceTypeRequest>
            {
                new() { EvidenceType = EvidenceType.Image, DisplayOrder = 0 },
                new() { EvidenceType = EvidenceType.Document, DisplayOrder = 1 }
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var question = await Context.Questions.FirstOrDefaultAsync(q => q.Id == result);
        question.Should().NotBeNull();
        question!.EvidenceRequired.Should().BeTrue();
        question.AllowedEvidenceTypes.Should().HaveCount(2);
        question.AllowedEvidenceTypes.Should().Contain(et => et.EvidenceType == EvidenceType.Image);
        question.AllowedEvidenceTypes.Should().Contain(et => et.EvidenceType == EvidenceType.Document);
    }

    [Fact]
    public async Task Handle_WithSeverityLevel_ShouldSetSeverity()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "Critical safety check",
            QuestionType = QuestionType.YesNo,
            SeverityLevel = SeverityLevel.Critical
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var question = await Context.Questions.FirstOrDefaultAsync(q => q.Id == result);
        question.Should().NotBeNull();
        question!.SeverityLevel.Should().Be(SeverityLevel.Critical);
    }

    [Fact]
    public async Task Handle_ShouldUpdateTemplateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var originalUpdateTime = template.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedAt.Should().BeAfter(originalUpdateTime);
    }

    [Fact]
    public async Task Handle_WithQuestionGroup_ShouldAssignToGroup()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var questionGroup = new QuestionGroup
        {
            AuditTemplateId = template.Id,
            GroupName = "Safety Questions",
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        
        Context.QuestionGroups.Add(questionGroup);
        await Context.SaveChangesAsync();
        
        var command = new AddQuestionCommand
        {
            AuditTemplateId = template.Id,
            QuestionGroupId = questionGroup.Id,
            QuestionText = "Safety question in group",
            QuestionType = QuestionType.YesNo
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var question = await Context.Questions.FirstOrDefaultAsync(q => q.Id == result);
        question.Should().NotBeNull();
        question!.QuestionGroupId.Should().Be(questionGroup.Id);
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isPublished = false)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
