using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for TemplateAccessAssignment entity
/// </summary>
public class TemplateAccessAssignmentDto : AuditableDto<int>
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserFullName { get; set; } = string.Empty;
    
    public int AuditTemplateId { get; set; }
    public string AuditTemplateName { get; set; } = string.Empty;
    public string AuditTemplateFullName { get; set; } = string.Empty;
    
    public string AssignedByUserId { get; set; } = string.Empty;
    public string AssignedByUserName { get; set; } = string.Empty;
    public string AssignedByFullName { get; set; } = string.Empty;
    
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsCurrentlyValid { get; set; }
}

/// <summary>
/// Data Transfer Object for creating TemplateAccessAssignment
/// </summary>
public class CreateTemplateAccessAssignmentDto
{
    public string UserId { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public string? Description { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Data Transfer Object for updating TemplateAccessAssignment
/// </summary>
public class UpdateTemplateAccessAssignmentDto
{
    public int Id { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Data Transfer Object for bulk template access assignment
/// </summary>
public class BulkTemplateAccessAssignmentDto
{
    public List<string> UserIds { get; set; } = new();
    public List<int> TemplateIds { get; set; } = new();
    public string? Description { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
