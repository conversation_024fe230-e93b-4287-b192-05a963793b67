using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Services;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFindingCategory;

/// <summary>
/// Handler for creating finding categories
/// </summary>
public class CreateFindingCategoryCommandHandler : BaseCommandHandler<CreateFindingCategoryCommand, FindingCategoryDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IFindingCategorizationService _categorizationService;

    public CreateFindingCategoryCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IFindingCategorizationService categorizationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _categorizationService = categorizationService;
    }

    public override async Task<FindingCategoryDto> Handle(CreateFindingCategoryCommand request, CancellationToken cancellationToken)
    {
        // Validate that the audit template exists and has categorization enabled
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId && t.IsActive, cancellationToken);

        if (template == null)
        {
            throw new NotFoundException("AuditTemplate", request.AuditTemplateId);
        }

        if (!template.EnableFindingCategorization)
        {
            throw new ValidationException("AuditTemplateId", "Finding categorization is not enabled for this audit template.");
        }

        // Check for duplicate category name within the template
        var existingCategory = await _context.FindingCategories
            .FirstOrDefaultAsync(fc => fc.AuditTemplateId == request.AuditTemplateId && 
                                      fc.CategoryName.ToLower() == request.CategoryName.ToLower() &&
                                      fc.IsActive, cancellationToken);

        if (existingCategory != null)
        {
            throw new ValidationException("CategoryName", $"A category with the name '{request.CategoryName}' already exists for this template.");
        }

        // Create the finding category
        var findingCategory = new FindingCategory
        {
            CategoryName = request.CategoryName,
            Description = request.Description,
            AuditTemplateId = request.AuditTemplateId,
            DisplayOrder = request.DisplayOrder,
            IsActive = request.IsActive,
            ColorCode = request.ColorCode,
            IconName = request.IconName,
            RequiresDocumentation = request.RequiresDocumentation
        };

        _context.FindingCategories.Add(findingCategory);
        await _context.SaveChangesAsync(cancellationToken);

        // Return the created category as DTO
        return new FindingCategoryDto
        {
            Id = findingCategory.Id,
            CategoryName = findingCategory.CategoryName,
            Description = findingCategory.Description,
            AuditTemplateId = findingCategory.AuditTemplateId,
            AuditTemplateName = template.TemplateName,
            DisplayOrder = findingCategory.DisplayOrder,
            IsActive = findingCategory.IsActive,
            ColorCode = findingCategory.ColorCode,
            IconName = findingCategory.IconName,
            RequiresDocumentation = findingCategory.RequiresDocumentation,
            FindingCount = 0,
            OpenFindingCount = 0,
            IsUsable = findingCategory.IsUsable,
            CreatedAt = findingCategory.CreatedAt,
            UpdatedAt = findingCategory.UpdatedAt,
            RecordVersion = findingCategory.RecordVersion,
            CreatedByUserId = findingCategory.CreatedByUserId,
            UpdatedByUserId = findingCategory.UpdatedByUserId
        };
    }
}
