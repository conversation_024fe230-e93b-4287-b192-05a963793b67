# HWS Audit Platform - Application Layer Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [CQRS Implementation](#cqrs-implementation)
4. [Functional Areas](#functional-areas)
5. [Validation Framework](#validation-framework)
6. [Error Handling](#error-handling)
7. [API Examples](#api-examples)
8. [Testing Strategy](#testing-strategy)
9. [Configuration](#configuration)
10. [Best Practices](#best-practices)

## Overview

The Application layer serves as the orchestration layer between the presentation layer (API/UI) and the domain layer. It implements the CQRS (Command Query Responsibility Segregation) pattern using MediatR and provides a clean separation between commands (write operations) and queries (read operations).

### Key Responsibilities
- **Use Case Orchestration**: Coordinates business workflows
- **Data Transformation**: Maps between domain entities and DTOs
- **Validation**: Input validation and business rule enforcement
- **Transaction Management**: Ensures data consistency
- **Event Handling**: Publishes and handles domain events

### Design Principles
- **Single Responsibility**: Each handler has one specific purpose
- **Dependency Inversion**: Depends on abstractions, not implementations
- **Open/Closed**: Extensible without modification
- **Interface Segregation**: Small, focused interfaces
- **DRY (Don't Repeat Yourself)**: Shared base classes and utilities

## Architecture

### Layered Architecture
```
┌─────────────────────────────────────┐
│           Presentation              │
│        (API Controllers)            │
├─────────────────────────────────────┤
│           Application               │
│    (Commands, Queries, Handlers)    │
├─────────────────────────────────────┤
│             Domain                  │
│      (Entities, Business Logic)     │
├─────────────────────────────────────┤
│          Infrastructure             │
│    (Database, External Services)    │
└─────────────────────────────────────┘
```

### Key Patterns
- **CQRS**: Separate read and write operations
- **Mediator**: Decoupled request/response handling
- **Repository**: Data access abstraction
- **Unit of Work**: Transaction boundary management
- **Pipeline Behaviors**: Cross-cutting concerns

### Technology Stack
- **.NET 10**: Latest framework features
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Rich validation rules
- **Entity Framework Core**: Data access (via Infrastructure)

## CQRS Implementation

### Commands (Write Operations)
Commands modify application state and trigger business logic.

**Structure:**
```csharp
public class CreateUserCommand : BaseCommand<string>
{
    public string Username { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public UserRole Role { get; set; }
}
```

**Handler:**
```csharp
public class CreateUserCommandHandler : BaseCommandHandler<CreateUserCommand, string>
{
    public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // 1. Create domain entity
        var user = User.Create(request.Username, request.FirstName, ...);
        
        // 2. Persist to database
        await _context.Users.AddAsync(user, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        
        // 3. Return result
        return user.Id;
    }
}
```

### Queries (Read Operations)
Queries return data without side effects.

**Structure:**
```csharp
public class GetUsersQuery : BaseQuery<PaginatedResult<UserSummaryDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public UserRole? Role { get; set; }
}
```

**Handler:**
```csharp
public class GetUsersQueryHandler : BaseQueryHandler<GetUsersQuery, PaginatedResult<UserSummaryDto>>
{
    public override async Task<PaginatedResult<UserSummaryDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Users.AsQueryable();
        
        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
            query = query.Where(u => u.Username.Contains(request.SearchTerm));
            
        // Apply pagination
        var totalCount = await query.CountAsync(cancellationToken);
        var users = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(u => new UserSummaryDto { ... })
            .ToListAsync(cancellationToken);
            
        return PaginatedResult<UserSummaryDto>.Create(users, totalCount, request.PageNumber, request.PageSize);
    }
}
```

### Pipeline Behaviors
Cross-cutting concerns are handled through MediatR pipeline behaviors:

1. **ValidationBehavior**: Validates requests using FluentValidation
2. **LoggingBehavior**: Logs request execution time and details
3. **DomainEventBehavior**: Publishes domain events after successful operations

## Functional Areas

### 1. User Management (`Users/`)

**Purpose**: Manage users, roles, and groups with Active Directory synchronization.

**Key Features:**
- User CRUD operations with validation
- Role-based access control
- User group management
- Active Directory synchronization
- Audit trail for all user changes

**Commands:**
- `CreateUserCommand`: Create new user with role assignment
- `UpdateUserCommand`: Update user information with concurrency control
- `DeleteUserCommand`: Soft delete user (sets IsActive = false)
- `SyncUserFromAdCommand`: Synchronize user from Active Directory

**Queries:**
- `GetUsersQuery`: Paginated user list with filtering and sorting
- `GetUserQuery`: Single user details with relationships
- `GetUserGroupsQuery`: User groups with member counts
- `GetRolesQuery`: Available system roles

**DTOs:**
- `UserDto`: Complete user information
- `UserSummaryDto`: Lightweight user info for lists
- `UserGroupDto`: Group information with members
- `RoleDto`: Role definition

### 2. Organization Management (`Organization/`)

**Purpose**: Manage organizational hierarchy (Location → Factory → Area → SubArea).

**Key Features:**
- Hierarchical structure management
- Address information handling
- Active/inactive status tracking
- Audit trail for organizational changes

**Commands:**
- `CreateFactoryCommand`: Create new factory with location
- `UpdateFactoryCommand`: Update factory information
- `CreateAreaCommand`: Create area within factory
- `CreateSubAreaCommand`: Create sub-area within area

**Queries:**
- `GetFactoriesQuery`: Paginated factory list with location info
- `GetAreasQuery`: Areas within specific factory
- `GetSubAreasQuery`: Sub-areas within specific area
- `GetOrganizationHierarchyQuery`: Complete hierarchy tree

### 3. Audit Execution (`Audits/`)

**Purpose**: Manage audit lifecycle from creation to completion.

**Key Features:**
- Audit creation and assignment
- Status workflow management
- Answer and evidence capture
- Offline synchronization support
- Review and approval process

**Commands:**
- `CreateAuditCommand`: Create new audit instance
- `StartAuditCommand`: Begin audit execution
- `SubmitAuditAnswerCommand`: Record answer to question
- `AttachEvidenceCommand`: Upload evidence files
- `SubmitAuditCommand`: Complete and submit audit
- `ReviewAuditCommand`: Manager review and approval

**Queries:**
- `GetAuditsQuery`: Paginated audit list with filtering
- `GetAuditQuery`: Complete audit details with answers
- `GetAuditAnswersQuery`: Answers for specific audit
- `GetMyAuditsQuery`: Audits assigned to current user

### 4. Template Management (`Templates/`)

**Purpose**: Create and manage audit templates with questions and logic.

**Key Features:**
- Template versioning
- Question type support (YesNo, Numeric, SingleSelect, etc.)
- Conditional question logic
- Evidence requirements
- Template publishing workflow

**Commands:**
- `CreateAuditTemplateCommand`: Create new template
- `AddQuestionCommand`: Add question to template
- `UpdateQuestionCommand`: Modify question details
- `PublishTemplateCommand`: Make template available for use

**Queries:**
- `GetAuditTemplatesQuery`: Available templates
- `GetAuditTemplateQuery`: Template with questions
- `GetQuestionsQuery`: Questions for specific template

## Validation Framework

### FluentValidation Integration
All commands are validated using FluentValidation rules:

```csharp
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator(IApplicationDbContext context)
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("Username is required")
            .MaximumLength(256).WithMessage("Username must not exceed 256 characters")
            .MustAsync(BeUniqueUsername).WithMessage("Username already exists");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be a valid email address")
            .MustAsync(BeUniqueEmail).WithMessage("Email already exists");
    }

    private async Task<bool> BeUniqueUsername(string username, CancellationToken cancellationToken)
    {
        return !await _context.Users.AnyAsync(u => u.Username == username, cancellationToken);
    }
}
```

### Validation Features
- **Synchronous Rules**: Basic property validation
- **Asynchronous Rules**: Database lookups and external service calls
- **Cross-field Validation**: Rules that depend on multiple properties
- **Conditional Validation**: Rules that apply only in certain conditions
- **Custom Error Messages**: Localization-ready error messages

## Error Handling

### Exception Hierarchy
```csharp
ApplicationException (abstract)
├── NotFoundException
├── BusinessRuleViolationException
├── ForbiddenException
├── ConflictException
└── ValidationException
```

### Exception Usage
- **NotFoundException**: Entity not found by ID
- **BusinessRuleViolationException**: Domain rule violations
- **ForbiddenException**: Authorization failures
- **ConflictException**: Concurrency conflicts
- **ValidationException**: Input validation failures

### Global Error Handling
Exceptions are caught and transformed into appropriate HTTP responses by the API layer.

## API Examples

### Creating a User
```http
POST /api/users
Content-Type: application/json

{
  "username": "john.doe",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "Auditor",
  "factoryId": 1
}
```

### Querying Users
```http
GET /api/users?pageNumber=1&pageSize=10&searchTerm=john&role=Auditor&isActive=true
```

### Creating an Audit
```http
POST /api/audits
Content-Type: application/json

{
  "auditTemplateId": 1,
  "assignmentType": "Individual",
  "assignedToUserId": "user123",
  "scheduledDate": "2024-01-15T09:00:00Z",
  "dueDate": "2024-01-22T17:00:00Z",
  "factoryId": 1,
  "areaId": 1
}
```

## Testing Strategy

### Unit Testing
Test individual handlers in isolation:

```csharp
[Fact]
public async Task CreateUser_WithValidData_ShouldReturnUserId()
{
    // Arrange
    var command = new CreateUserCommand
    {
        Username = "testuser",
        FirstName = "Test",
        LastName = "User",
        Email = "<EMAIL>",
        Role = UserRole.Auditor
    };

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    Assert.NotNull(result);
    Assert.NotEmpty(result);
}
```

### Integration Testing
Test complete request/response flows:

```csharp
[Fact]
public async Task CreateUser_EndToEnd_ShouldPersistToDatabase()
{
    // Arrange
    var command = new CreateUserCommand { ... };

    // Act
    var userId = await _mediator.Send(command);

    // Assert
    var user = await _context.Users.FindAsync(userId);
    Assert.NotNull(user);
    Assert.Equal(command.Username, user.Username);
}
```

### Validation Testing
Test validation rules:

```csharp
[Fact]
public async Task CreateUser_WithDuplicateUsername_ShouldFailValidation()
{
    // Arrange
    var command = new CreateUserCommand { Username = "existing-user" };

    // Act & Assert
    await Assert.ThrowsAsync<ValidationException>(() => _mediator.Send(command));
}
```

## Configuration

### Service Registration
```csharp
public void ConfigureServices(IServiceCollection services)
{
    services.AddApplication(); // Registers all Application layer services
}
```

### MediatR Configuration
```csharp
services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(assembly));
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
```

### AutoMapper Configuration
```csharp
services.AddAutoMapper(assembly); // Registers all mapping profiles
```

## Best Practices

### Command Design
1. **Single Purpose**: Each command should have one clear responsibility
2. **Immutable**: Commands should be immutable after creation
3. **Validation**: Always validate input at the command level
4. **Return Minimal Data**: Return only essential information (IDs, success status)

### Query Design
1. **Read-Only**: Queries should never modify state
2. **Optimized DTOs**: Return only required data
3. **Pagination**: Always support pagination for lists
4. **Filtering**: Provide flexible filtering options

### Handler Design
1. **Async/Await**: Use async patterns throughout
2. **Error Handling**: Handle exceptions appropriately
3. **Logging**: Log important operations and errors
4. **Transaction Scope**: Use appropriate transaction boundaries

### DTO Design
1. **Purpose-Specific**: Different DTOs for different use cases
2. **Flat Structure**: Avoid deep nesting
3. **Computed Properties**: Include calculated fields when useful
4. **Versioning**: Consider API versioning for breaking changes

### Validation Rules
1. **Fail Fast**: Validate early and often
2. **Clear Messages**: Provide helpful error messages
3. **Async When Needed**: Use async validation for database checks
4. **Reusable**: Create reusable validation components

This documentation provides a comprehensive guide to understanding and working with the Application layer of the HWS Audit Platform.
