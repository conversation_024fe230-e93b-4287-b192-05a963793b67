@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models

<div class="audit-card card @GetCardClass()" @onclick="OnCardClick">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <i class="@GetStatusIcon() me-2"></i>
            <h6 class="mb-0 fw-bold">@AuditSummary.AuditTemplateName</h6>
        </div>
        <span class="badge industrial-badge-@GetStatusBadgeClass() ms-2">
            @AuditSummary.OverallStatus
        </span>
    </div>
    
    <div class="card-body">
        <!-- Location Information -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex align-items-center text-muted">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>
                        @AuditSummary.FactoryName
                        @if (!string.IsNullOrEmpty(AuditSummary.AreaName))
                        {
                            <span> → @AuditSummary.AreaName</span>
                        }
                        @if (!string.IsNullOrEmpty(AuditSummary.SubAreaName))
                        {
                            <span> → @AuditSummary.SubAreaName</span>
                        }
                    </span>
                </div>
            </div>
        </div>

        <!-- Date Information -->
        <div class="row mb-3">
            <div class="col-6">
                <small class="text-muted d-block">Scheduled</small>
                <span class="fw-semibold">@AuditSummary.ScheduledDate.ToString("MMM dd, yyyy")</span>
                <br>
                <small class="text-muted">@AuditSummary.ScheduledDate.ToString("HH:mm")</small>
            </div>
            @if (AuditSummary.DueDate.HasValue)
            {
                <div class="col-6">
                    <small class="text-muted d-block">Due Date</small>
                    <span class="fw-semibold @(AuditSummary.IsOverdue ? "text-danger" : "")">
                        @AuditSummary.DueDate.Value.ToString("MMM dd, yyyy")
                    </span>
                    <br>
                    <small class="text-muted">@AuditSummary.DueDate.Value.ToString("HH:mm")</small>
                    @if (AuditSummary.IsOverdue)
                    {
                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="Overdue"></i>
                    }
                </div>
            }
        </div>

        <!-- Progress Information -->
        @if (AuditSummary.TotalQuestions > 0)
        {
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Progress</small>
                        <small class="text-muted">@AuditSummary.AnsweredQuestions / @AuditSummary.TotalQuestions</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar industrial-progress-@GetProgressBarClass()" role="progressbar"
                             style="width: @AuditSummary.ProgressPercentage.ToString("F1")%"
                             aria-valuenow="@AuditSummary.ProgressPercentage" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                    <small class="text-muted">@AuditSummary.ProgressPercentage.ToString("F1")% Complete</small>
                </div>
            </div>
        }

        <!-- Score Information -->
        @if (AuditSummary.OverallScore.HasValue)
        {
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Overall Score</small>
                        <span class="fw-bold text-@GetScoreClass()">
                            @AuditSummary.OverallScore.Value.ToString("F1")%
                        </span>
                    </div>
                </div>
            </div>
        }

        <!-- Assigned User -->
        @if (!string.IsNullOrEmpty(AuditSummary.AssignedToUserName))
        {
            <div class="row mb-2">
                <div class="col-12">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-user me-2"></i>
                        <small>Assigned to: @AuditSummary.AssignedToUserName</small>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="card-footer d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            @if (AuditSummary.StartedAt.HasValue)
            {
                <small class="text-muted me-3">
                    <i class="fas fa-play me-1"></i>
                    Started @AuditSummary.StartedAt.Value.ToString("MMM dd, HH:mm")
                </small>
            }
            @if (AuditSummary.CompletedAt.HasValue)
            {
                <small class="text-muted">
                    <i class="fas fa-check me-1"></i>
                    Completed @AuditSummary.CompletedAt.Value.ToString("MMM dd, HH:mm")
                </small>
            }
        </div>
        
        <div class="d-flex align-items-center">
            @if (ShowActions)
            {
                @if (CanStart())
                {
                    <button class="btn btn-primary btn-sm me-2" @onclick="OnStartClick" @onclick:stopPropagation="true">
                        <i class="fas fa-play me-1"></i>
                        Start
                    </button>
                }
                @if (CanContinue())
                {
                    <button class="btn btn-info btn-sm me-2" @onclick="OnContinueClick" @onclick:stopPropagation="true">
                        <i class="fas fa-edit me-1"></i>
                        Continue
                    </button>
                }
                @if (CanView())
                {
                    <button class="btn btn-outline-secondary btn-sm" @onclick="OnViewClick" @onclick:stopPropagation="true">
                        <i class="fas fa-eye me-1"></i>
                        View
                    </button>
                }
            }
            
            <i class="fas fa-chevron-right text-muted ms-2"></i>
        </div>
    </div>
</div>

@code {
    [Parameter] public AuditSummaryDto AuditSummary { get; set; } = new();
    [Parameter] public bool ShowActions { get; set; } = true;
    [Parameter] public EventCallback<AuditSummaryDto> OnCardClicked { get; set; }
    [Parameter] public EventCallback<AuditSummaryDto> OnStartClicked { get; set; }
    [Parameter] public EventCallback<AuditSummaryDto> OnContinueClicked { get; set; }
    [Parameter] public EventCallback<AuditSummaryDto> OnViewClicked { get; set; }

    private async Task OnCardClick()
    {
        if (OnCardClicked.HasDelegate)
        {
            await OnCardClicked.InvokeAsync(AuditSummary);
        }
    }

    private async Task OnStartClick()
    {
        if (OnStartClicked.HasDelegate)
        {
            await OnStartClicked.InvokeAsync(AuditSummary);
        }
    }

    private async Task OnContinueClick()
    {
        if (OnContinueClicked.HasDelegate)
        {
            await OnContinueClicked.InvokeAsync(AuditSummary);
        }
    }

    private async Task OnViewClick()
    {
        if (OnViewClicked.HasDelegate)
        {
            await OnViewClicked.InvokeAsync(AuditSummary);
        }
    }

    private bool CanStart()
    {
        return AuditSummary.OverallStatus == AuditOverallStatus.Scheduled;
    }

    private bool CanContinue()
    {
        return AuditSummary.OverallStatus == AuditOverallStatus.InProgress;
    }

    private bool CanView()
    {
        return AuditSummary.OverallStatus == AuditOverallStatus.Submitted || 
               AuditSummary.OverallStatus == AuditOverallStatus.Closed;
    }

    private string GetCardClass()
    {
        var baseClass = "audit-card-clickable";

        return AuditSummary.OverallStatus switch
        {
            AuditOverallStatus.Scheduled when AuditSummary.IsOverdue => $"{baseClass} industrial-border-danger",
            AuditOverallStatus.InProgress => $"{baseClass} industrial-border-info",
            AuditOverallStatus.Submitted => $"{baseClass} industrial-border-primary",
            AuditOverallStatus.Closed => $"{baseClass} industrial-border-success",
            AuditOverallStatus.Cancelled => $"{baseClass} industrial-border-secondary",
            _ => baseClass
        };
    }

    private string GetStatusIcon()
    {
        return AuditSummary.OverallStatus switch
        {
            AuditOverallStatus.Scheduled => "fas fa-calendar-alt text-warning",
            AuditOverallStatus.InProgress => "fas fa-play-circle text-info",
            AuditOverallStatus.Submitted => "fas fa-paper-plane text-primary",
            AuditOverallStatus.Closed => "fas fa-check-circle text-success",
            AuditOverallStatus.Cancelled => "fas fa-times-circle text-secondary",
            _ => "fas fa-question-circle text-muted"
        };
    }

    private string GetStatusBadgeClass()
    {
        return AuditSummary.OverallStatus switch
        {
            AuditOverallStatus.Scheduled => AuditSummary.IsOverdue ? "danger" : "warning",
            AuditOverallStatus.InProgress => "info",
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.Cancelled => "secondary",
            _ => "secondary"
        };
    }

    private string GetProgressBarClass()
    {
        return AuditSummary.ProgressPercentage switch
        {
            >= 80 => "success",
            >= 50 => "info",
            >= 25 => "warning",
            _ => "danger"
        };
    }

    private string GetScoreClass()
    {
        if (!AuditSummary.OverallScore.HasValue) return "muted";
        
        return AuditSummary.OverallScore.Value switch
        {
            >= 90 => "success",
            >= 80 => "info",
            >= 70 => "warning",
            _ => "danger"
        };
    }
}

<style>
    .audit-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .audit-card-clickable {
        cursor: pointer;
    }

    .audit-card-clickable:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--industrial-shadow-heavy);
        border-color: var(--industrial-teal) !important;
    }

    .audit-card .card-header {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 2px solid var(--industrial-teal);
        padding: 1rem 1.25rem;
    }

    .audit-card .card-body {
        padding: 1.25rem;
    }

    .audit-card .card-footer {
        background-color: var(--industrial-gray);
        border-top: 1px solid var(--industrial-border);
        padding: 0.75rem 1.25rem;
    }

    .progress {
        background-color: var(--industrial-light-gray);
        border-radius: 4px;
    }

    .progress-bar {
        transition: width 0.6s ease;
    }

    /* Industrial border classes */
    .industrial-border-danger { border-color: var(--industrial-red) !important; }
    .industrial-border-info { border-color: var(--industrial-teal) !important; }
    .industrial-border-primary { border-color: var(--industrial-blue) !important; }
    .industrial-border-success { border-color: var(--industrial-green) !important; }
    .industrial-border-secondary { border-color: var(--industrial-steel) !important; }

    /* Industrial badge classes */
    .industrial-badge-danger {
        background-color: var(--industrial-red) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-info {
        background-color: var(--industrial-teal) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-primary {
        background-color: var(--industrial-blue) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-success {
        background-color: var(--industrial-green) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-warning {
        background-color: var(--industrial-orange) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-secondary {
        background-color: var(--industrial-steel) !important;
        color: var(--industrial-text-light);
    }

    /* Industrial progress bar classes */
    .industrial-progress-success {
        background: linear-gradient(90deg, var(--industrial-green) 0%, #48cc6c 100%);
    }
    .industrial-progress-info {
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }
    .industrial-progress-warning {
        background: linear-gradient(90deg, var(--industrial-orange) 0%, #fbbf24 100%);
    }
    .industrial-progress-danger {
        background: linear-gradient(90deg, var(--industrial-red) 0%, #f87171 100%);
    }

    /* Mobile optimizations */
    @@media (max-width: 767px) {
        .audit-card .card-body {
            padding: 1rem;
        }
        
        .audit-card .card-footer {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .audit-card .card-footer > div:first-child {
            margin-bottom: 0.5rem;
        }
    }

    /* Tablet optimizations */
    @@media (min-width: 768px) and (max-width: 1024px) {
        .audit-card {
            margin-bottom: 1.5rem;
        }
        
        .audit-card .card-header {
            padding: 1.25rem 1.5rem;
        }
        
        .audit-card .card-body {
            padding: 1.5rem;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    }
</style>
