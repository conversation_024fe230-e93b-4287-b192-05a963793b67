using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Organization.DTOs;

/// <summary>
/// Data Transfer Object for AreaResponsibility entity
/// </summary>
public class AreaResponsibilityDto : AuditableDto<string>
{
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public ResponsibilityType ResponsibilityType { get; set; }
    public string ResponsibilityTypeName { get; set; } = string.Empty;
    public string ResponsibleUserId { get; set; } = string.Empty;
    public string? ResponsibleUserName { get; set; }
    public string? ResponsibleUserFullName { get; set; }
    public int? AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public int Priority { get; set; }
    public bool IsTemplateSpecific { get; set; }
    public bool IsEffective { get; set; }
}
