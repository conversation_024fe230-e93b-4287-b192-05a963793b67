using System.ComponentModel.DataAnnotations;

namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Model for finding category data
/// </summary>
public class FindingCategoryModel
{
    public int Id { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool RequiresDocumentation { get; set; }
    public int FindingCount { get; set; }
    public int OpenFindingCount { get; set; }
    public bool IsUsable { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int RecordVersion { get; set; }
    public string? CreatedByUserId { get; set; }
    public string? UpdatedByUserId { get; set; }
}

/// <summary>
/// Model for creating finding categories
/// </summary>
public class CreateFindingCategoryModel
{
    [Required(ErrorMessage = "Category name is required")]
    [MaxLength(100, ErrorMessage = "Category name cannot exceed 100 characters")]
    public string CategoryName { get; set; } = string.Empty;

    [MaxLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Audit template is required")]
    public int AuditTemplateId { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be 0 or greater")]
    public int DisplayOrder { get; set; } = 0;

    public bool IsActive { get; set; } = true;

    [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "Color code must be a valid hex color (e.g., #FF5733)")]
    public string? ColorCode { get; set; }

    [MaxLength(50, ErrorMessage = "Icon name cannot exceed 50 characters")]
    public string? IconName { get; set; }

    public bool RequiresDocumentation { get; set; } = false;
}

/// <summary>
/// Model for finding category filters
/// </summary>
public class FindingCategoryFilterModel
{
    public int? AuditTemplateId { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool IncludeUsageStatistics { get; set; } = false;
}

/// <summary>
/// Model for finding category assignment
/// </summary>
public class AssignFindingCategoryModel
{
    public int? FindingCategoryId { get; set; }
}

/// <summary>
/// Model for finding category summary (for dropdowns and lists)
/// </summary>
public class FindingCategorySummaryModel
{
    public int Id { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool IsActive { get; set; }
    public int FindingCount { get; set; }
}

/// <summary>
/// Model for category statistics
/// </summary>
public class CategoryStatisticsModel
{
    public int TotalCategories { get; set; }
    public int ActiveCategories { get; set; }
    public int TotalFindings { get; set; }
    public int CategorizedFindings { get; set; }
    public int UncategorizedFindings { get; set; }
    public Dictionary<int, CategoryUsageModel> CategoryUsage { get; set; } = new();
}

/// <summary>
/// Model for individual category usage statistics
/// </summary>
public class CategoryUsageModel
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public int FindingCount { get; set; }
    public int OpenFindingCount { get; set; }
    public int ClosedFindingCount { get; set; }
    public double UsagePercentage { get; set; }
}

/// <summary>
/// Model for predefined color options
/// </summary>
public class ColorOptionModel
{
    public string ColorCode { get; set; } = string.Empty;
    public string ColorName { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
}

/// <summary>
/// Model for predefined icon options
/// </summary>
public class IconOptionModel
{
    public string IconName { get; set; } = string.Empty;
    public string IconDisplayName { get; set; } = string.Empty;
    public string IconClass { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
}
