using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents a user role in the audit platform.
/// Maps to the Roles table in the database.
/// </summary>
public class Role : BaseEntity<int>
{
    /// <summary>
    /// Name of the user role (e.g., Admin, Manager, Auditor)
    /// </summary>
    [Required]
    public UserRole RoleName { get; set; }

    /// <summary>
    /// Brief description of the role and its responsibilities
    /// </summary>
    [MaxLength(255)]
    public string? Description { get; set; }

    /// <summary>
    /// Navigation property for users with this role
    /// </summary>
    public virtual ICollection<User> Users { get; set; } = new List<User>();

    /// <summary>
    /// Navigation property for AD group role mappings
    /// </summary>
    public virtual ICollection<AdGroupRoleMapping> AdGroupRoleMappings { get; set; } = new List<AdGroupRoleMapping>();
}
