using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetFactory;

/// <summary>
/// Handler for GetFactoryQuery
/// </summary>
public class GetFactoryQueryHandler : BaseQueryHandler<GetFactoryQuery, FactoryDto>
{
    private readonly IApplicationDbContext _context;

    public GetFactoryQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<FactoryDto> Handle(GetFactoryQuery request, CancellationToken cancellationToken)
    {
        var factory = await _context.Factories
            .Include(f => f.Location)
            .Include(f => f.Areas.Where(a => a.IsActive))
                .ThenInclude(a => a.SubAreas.Where(sa => sa.IsActive))
            .FirstOrDefaultAsync(f => f.Id == request.FactoryId, cancellationToken);

        if (factory == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Organization.Factory), request.FactoryId);
        }

        return new FactoryDto
        {
            Id = factory.Id,
            FactoryName = factory.FactoryName,
            FactoryProcess = factory.FactoryProcess,
            LocationId = factory.LocationId,
            LocationName = factory.Location?.LocationName,
            LocationCountry = factory.Location?.LocationCountry,
            Address = factory.Address,
            IsActive = factory.IsActive,
            CreatedAt = factory.CreatedAt,
            UpdatedAt = factory.UpdatedAt,
            RecordVersion = factory.RecordVersion,
            CreatedByUserId = factory.CreatedByUserId,
            UpdatedByUserId = factory.UpdatedByUserId,
            Areas = factory.Areas
                .Where(a => a.IsActive)
                .Select(a => new AreaSummaryDto
                {
                    Id = a.Id,
                    AreaName = a.AreaName,
                    FactoryId = a.FactoryId,
                    FactoryName = factory.FactoryName,
                    Description = a.Description,
                    IsActive = a.IsActive,
                    SubAreaCount = a.SubAreas.Count(sa => sa.IsActive)
                })
                .OrderBy(a => a.AreaName)
                .ToList()
        };
    }
}
