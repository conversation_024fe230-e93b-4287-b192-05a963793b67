using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Audits;

/// <summary>
/// Represents a specific reason provided for a failed/negative audit answer.
/// Maps to the AuditAnswerFailureReasons table in the database.
/// </summary>
public class AuditAnswerFailureReason : AuditableEntity<string>
{
    /// <summary>
    /// Links to the specific audit answer that failed (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit answer
    /// </summary>
    public virtual AuditAnswer AuditAnswer { get; set; } = null!;



    /// <summary>
    /// Specific reason provided for the failed/negative answer. Candidate for multi-language support.
    /// </summary>
    [Required]
    public string ReasonText { get; set; } = string.Empty;

    /// <summary>
    /// Order for displaying multiple reasons for a single answer
    /// </summary>
    public int DisplayOrder { get; set; } = 0;
}
