# 🚨 Debug Mode Instructions

## Overview

I've created a debug mode that allows you to skip authentication and use mock data for testing the dashboard functionality. This helps isolate whether issues are with the dashboard itself or the authentication/API connectivity.

## How to Enable Debug Mode

### Step 1: Enable Debug Mode
The debug mode is **already enabled** in `src\HWSAuditPlatform.Web\appsettings.Development.json`:

```json
{
  "Debug": {
    "SkipAuthentication": true,
    "MockUser": {
      "UserId": "debug-user-001",
      "Username": "debug.user",
      "Email": "<EMAIL>",
      "FullName": "Debug User",
      "Role": "Admin",
      "FactoryId": "1"
    }
  }
}
```

### Step 2: Start the Application
Since you're using Aspire, start the application using:

```bash
dotnet run --project src\HWSAuditPlatform.AppHost
```

## What Debug Mode Does

When `Debug:SkipAuthentication` is `true`:

1. **🔓 Bypasses Authentication**: No login required, automatically authenticated as mock user
2. **📊 Mock Dashboard Data**: Returns fake dashboard statistics instead of calling the real API
3. **⚡ No API Dependency**: Works without the API service running
4. **🎭 Mock User**: Uses the configured mock user with Admin role

## Testing the Dashboard (Step by Step)

### Step 1: Health Check Page (Start Here)
1. Navigate to `/health`
2. **Expected Result**: Shows system status and configuration
3. **Action**: Click the test buttons to diagnose what's working
4. **Look for**: Debug mode status, API client type, authentication status

### Step 2: Test Page (Basic Functionality)
1. Navigate to `/test`
2. **Expected Result**: Shows basic system info
3. **Action**: Click "Load Dashboard Component" to test dashboard rendering
4. **Action**: Click "Test Dashboard API" to verify mock data

### Step 3: Debug Page (Advanced Diagnostics)
1. Navigate to `/debug`
2. **Expected Result**: Shows detailed debug information
3. **Action**: Run test buttons for comprehensive diagnostics

### Step 4: Home Dashboard (Final Test)
1. Navigate to `/`
2. **Expected Result**: Should show dashboard immediately with mock data (no login required)
3. **If it fails**: The issue is likely with authorization logic, not the dashboard itself

## Debug Page Features

The debug page (`/debug`) provides:

- **Configuration Status**: Shows if debug mode is enabled
- **Mock User Info**: Displays the configured mock user details
- **Authentication State**: Shows current authentication status
- **Test Buttons**: 
  - "Test Dashboard API" - Tests the dashboard data loading
  - "Test Current User API" - Tests the current user endpoint
- **Quick Navigation**: Links to login, home, and health pages

## Mock Dashboard Data

When debug mode is enabled, the dashboard will show:

- **Total Audits**: 25
- **Pending Audits**: 8  
- **Overdue Audits**: 3
- **Completed This Month**: 12
- **Average Score**: 87.5%
- **Recent Audits**: 5 mock audits
- **Overdue Audits**: 3 mock overdue audits

## How to Disable Debug Mode

To return to normal authentication mode:

1. Edit `src\HWSAuditPlatform.Web\appsettings.Development.json`
2. Change `"SkipAuthentication": true` to `"SkipAuthentication": false`
3. Restart the web application

## Troubleshooting

### If Debug Mode Isn't Working

1. **Check Configuration**: Ensure `Debug:SkipAuthentication` is `true` in `appsettings.Development.json`
2. **Check Environment**: Make sure `ASPNETCORE_ENVIRONMENT` is set to `Development`
3. **Check Logs**: Look for debug mode warning messages in the console:
   ```
   🚨 DEBUG MODE ENABLED: Skipping authentication and using mock services
   🚨 DEBUG MODE: Mock API client and authentication services configured
   ```

### If You Still See Authentication Issues

1. **Clear Browser Cache**: Clear cookies and local storage
2. **Check URL**: Make sure you're accessing the correct URL
3. **Check Console**: Look for JavaScript errors in browser developer tools

## Log Messages

When debug mode is active, you'll see warning messages like:

```
🚨 DEBUG MODE ENABLED: Skipping authentication and using mock services
🚨 DEBUG MODE: Using mock authentication for user: debug.user with role: Admin
🚨 DEBUG MODE: GetDashboardStatsAsync called - returning mock data
```

## Security Warning

⚠️ **IMPORTANT**: Debug mode should **NEVER** be enabled in production environments. It completely bypasses security and authentication.

## Next Steps

1. **Test Dashboard**: Navigate to `/` and verify the dashboard loads with mock data
2. **Test Debug Page**: Navigate to `/debug` and run the test buttons
3. **Compare Results**: If dashboard works in debug mode but not in normal mode, the issue is with authentication/API connectivity
4. **Report Results**: Let me know what you see so we can proceed with the appropriate fix

## Files Modified

- `src\HWSAuditPlatform.Web\appsettings.Development.json` - Added debug configuration
- `src\HWSAuditPlatform.Web\Services\DebugAuthenticationStateService.cs` - Mock authentication
- `src\HWSAuditPlatform.Web\Services\DebugApiClient.cs` - Mock API client
- `src\HWSAuditPlatform.Web\Components\Pages\Debug.razor` - Debug information page
- `src\HWSAuditPlatform.Web\Program.cs` - Conditional service registration
