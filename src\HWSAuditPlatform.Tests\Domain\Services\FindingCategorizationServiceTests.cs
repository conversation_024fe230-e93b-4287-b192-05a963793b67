using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Domain.Enums;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Services;

public class FindingCategorizationServiceTests
{
    private readonly FindingCategorizationService _service;

    public FindingCategorizationServiceTests()
    {
        _service = new FindingCategorizationService();
    }

    [Fact]
    public void ValidateFindingCategories_ShouldReturnValid_WhenCategorizationDisabled()
    {
        // Arrange
        var template = new AuditTemplate { EnableFindingCategorization = false };

        // Act
        var result = _service.ValidateFindingCategories(template);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.ValidationErrors);
        Assert.Empty(result.ValidationWarnings);
    }

    [Fact]
    public void ValidateFindingCategories_ShouldWarn_WhenNoActiveCategories()
    {
        // Arrange
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { CategoryName = "Test", IsActive = false }
            }
        };

        // Act
        var result = _service.ValidateFindingCategories(template);

        // Assert
        Assert.True(result.IsValid);
        Assert.Single(result.ValidationWarnings);
        Assert.Contains("no active categories are defined", result.ValidationWarnings[0]);
    }

    [Fact]
    public void ValidateFindingCategories_ShouldError_OnDuplicateNames()
    {
        // Arrange
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { CategoryName = "Safety", IsActive = true },
                new() { CategoryName = "SAFETY", IsActive = true } // Case insensitive duplicate
            }
        };

        // Act
        var result = _service.ValidateFindingCategories(template);

        // Assert
        Assert.False(result.IsValid);
        Assert.Single(result.ValidationErrors);
        Assert.Contains("Duplicate category name found: 'safety'", result.ValidationErrors[0]);
    }

    [Fact]
    public void ValidateFindingCategories_ShouldWarn_OnDuplicateDisplayOrders()
    {
        // Arrange
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { CategoryName = "Safety", DisplayOrder = 1, IsActive = true },
                new() { CategoryName = "Quality", DisplayOrder = 1, IsActive = true }
            }
        };

        // Act
        var result = _service.ValidateFindingCategories(template);

        // Assert
        Assert.True(result.IsValid);
        Assert.Single(result.ValidationWarnings);
        Assert.Contains("Multiple categories have the same display order: 1", result.ValidationWarnings[0]);
    }

    [Fact]
    public void GetAvailableCategories_ShouldReturnEmpty_WhenCategorizationDisabled()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate { EnableFindingCategorization = false };

        // Act
        var result = _service.GetAvailableCategories(finding, template);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void GetAvailableCategories_ShouldReturnActiveCategories_OrderedCorrectly()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { Id = 1, CategoryName = "Quality", DisplayOrder = 2, IsActive = true },
                new() { Id = 2, CategoryName = "Safety", DisplayOrder = 1, IsActive = true },
                new() { Id = 3, CategoryName = "Inactive", DisplayOrder = 0, IsActive = false }
            }
        };

        // Act
        var result = _service.GetAvailableCategories(finding, template).ToList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal("Safety", result[0].CategoryName); // DisplayOrder 1
        Assert.Equal("Quality", result[1].CategoryName); // DisplayOrder 2
    }

    [Fact]
    public void AssignCategory_ShouldReturnFalse_WhenCategorizationDisabled()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate { EnableFindingCategorization = false };

        // Act
        var result = _service.AssignCategory(finding, 1, template);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void AssignCategory_ShouldReturnFalse_WhenCategoryNotFound()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>()
        };

        // Act
        var result = _service.AssignCategory(finding, 999, template);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void AssignCategory_ShouldReturnFalse_WhenCategoryInactive()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { Id = 1, CategoryName = "Test", IsActive = false }
            }
        };

        // Act
        var result = _service.AssignCategory(finding, 1, template);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void AssignCategory_ShouldReturnTrue_WhenValidCategory()
    {
        // Arrange
        var finding = new Finding();
        var template = new AuditTemplate 
        { 
            EnableFindingCategorization = true,
            FindingCategories = new List<FindingCategory>
            {
                new() { Id = 1, CategoryName = "Test", IsActive = true }
            }
        };

        // Act
        var result = _service.AssignCategory(finding, 1, template);

        // Assert
        Assert.True(result);
        Assert.Equal(1, finding.FindingCategoryId);
    }

    [Fact]
    public void RemoveCategory_ShouldSetCategoryIdToNull()
    {
        // Arrange
        var finding = new Finding { FindingCategoryId = 1 };

        // Act
        _service.RemoveCategory(finding);

        // Assert
        Assert.Null(finding.FindingCategoryId);
    }

    [Fact]
    public void GetCategoryStatistics_ShouldCalculateCorrectStatistics()
    {
        // Arrange
        var template = new AuditTemplate 
        { 
            FindingCategories = new List<FindingCategory>
            {
                new() { Id = 1, CategoryName = "Safety", IsActive = true },
                new() { Id = 2, CategoryName = "Quality", IsActive = true },
                new() { Id = 3, CategoryName = "Inactive", IsActive = false }
            }
        };

        var findings = new List<Finding>
        {
            new() { Id = "f1", FindingCategoryId = 1, Status = FindingStatus.Open },
            new() { Id = "f2", FindingCategoryId = 1, Status = FindingStatus.Closed },
            new() { Id = "f3", FindingCategoryId = 2, Status = FindingStatus.Open },
            new() { Id = "f4", FindingCategoryId = null, Status = FindingStatus.Open } // Uncategorized
        };

        // Act
        var result = _service.GetCategoryStatistics(template, findings);

        // Assert
        Assert.Equal(3, result.TotalCategories);
        Assert.Equal(2, result.ActiveCategories);
        Assert.Equal(4, result.TotalFindings);
        Assert.Equal(3, result.CategorizedFindings);
        Assert.Equal(1, result.UncategorizedFindings);

        // Check category 1 (Safety) statistics
        Assert.True(result.CategoryUsage.ContainsKey(1));
        var safetyStats = result.CategoryUsage[1];
        Assert.Equal(2, safetyStats.FindingCount);
        Assert.Equal(1, safetyStats.OpenFindingCount);
        Assert.Equal(1, safetyStats.ClosedFindingCount);
        Assert.Equal(50.0, safetyStats.UsagePercentage);

        // Check category 2 (Quality) statistics
        Assert.True(result.CategoryUsage.ContainsKey(2));
        var qualityStats = result.CategoryUsage[2];
        Assert.Equal(1, qualityStats.FindingCount);
        Assert.Equal(1, qualityStats.OpenFindingCount);
        Assert.Equal(0, qualityStats.ClosedFindingCount);
        Assert.Equal(25.0, qualityStats.UsagePercentage);

        // Inactive category should not be included
        Assert.False(result.CategoryUsage.ContainsKey(3));
    }
}
