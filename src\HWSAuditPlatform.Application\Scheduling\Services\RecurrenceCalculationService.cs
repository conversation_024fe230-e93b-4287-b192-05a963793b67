using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Scheduling.Services;

/// <summary>
/// Service for calculating recurrence dates and patterns
/// </summary>
public class RecurrenceCalculationService : IRecurrenceCalculationService
{
    public DateOnly CalculateNextGenerationDate(RecurrenceRule rule, DateOnly? fromDate = null)
    {
        var today = fromDate ?? DateOnly.FromDateTime(DateTime.UtcNow);
        var startDate = rule.StartDate;

        // If start date is in the future, use it as the first generation date
        if (startDate > today)
        {
            return startDate;
        }

        // If there's an end date and we've passed it, return a far future date to disable
        if (rule.EndDate.HasValue && today > rule.EndDate.Value)
        {
            return DateOnly.MaxValue;
        }

        // Calculate the next occurrence based on the frequency type
        return rule.FrequencyType switch
        {
            FrequencyType.DAILY => CalculateNextDailyOccurrence(rule, today),
            FrequencyType.WEEKLY => CalculateNextWeeklyOccurrence(rule, today),
            FrequencyType.MONTHLY => CalculateNextMonthlyOccurrence(rule, today),
            FrequencyType.YEARLY => CalculateNextYearlyOccurrence(rule, today),
            FrequencyType.COSTUM => CalculateCustomOccurrence(rule, today),
            _ => today.AddDays(1) // Default fallback
        };
    }

    public DateOnly CalculateNextOccurrence(RecurrenceRule rule, DateOnly afterDate)
    {
        return CalculateNextGenerationDate(rule, afterDate.AddDays(1));
    }

    public bool IsValidRecurrenceRule(RecurrenceRule rule)
    {
        return rule.IsValid();
    }

    public List<DateOnly> GetOccurrencesInRange(RecurrenceRule rule, DateOnly startDate, DateOnly endDate, int maxOccurrences = 100)
    {
        var occurrences = new List<DateOnly>();
        var currentDate = startDate;
        var count = 0;

        while (currentDate <= endDate && count < maxOccurrences)
        {
            var nextOccurrence = CalculateNextGenerationDate(rule, currentDate);
            
            if (nextOccurrence > endDate || nextOccurrence == DateOnly.MaxValue)
                break;

            if (nextOccurrence >= startDate)
            {
                occurrences.Add(nextOccurrence);
                count++;
            }

            currentDate = nextOccurrence.AddDays(1);
        }

        return occurrences;
    }

    private static DateOnly CalculateNextDailyOccurrence(RecurrenceRule rule, DateOnly today)
    {
        var daysSinceStart = today.DayNumber - rule.StartDate.DayNumber;
        var intervalDays = rule.Interval;
        
        if (daysSinceStart % intervalDays == 0)
        {
            return today.AddDays(intervalDays);
        }
        
        var daysUntilNext = intervalDays - (daysSinceStart % intervalDays);
        return today.AddDays(daysUntilNext);
    }

    private static DateOnly CalculateNextWeeklyOccurrence(RecurrenceRule rule, DateOnly today)
    {
        if (!rule.WeeklyDaysOfWeekMask.HasValue)
        {
            return today.AddDays(7 * rule.Interval);
        }

        var mask = rule.WeeklyDaysOfWeekMask.Value;
        var currentDayOfWeek = (int)today.DayOfWeek;
        
        // Find the next day in the current week
        for (int i = currentDayOfWeek + 1; i <= 6; i++)
        {
            if ((mask & (1 << i)) != 0)
            {
                return today.AddDays(i - currentDayOfWeek);
            }
        }
        
        // No more days this week, find the first day in the next interval
        var weeksToAdd = rule.Interval;
        var nextWeekStart = today.AddDays(7 * weeksToAdd - currentDayOfWeek);
        
        for (int i = 0; i <= 6; i++)
        {
            if ((mask & (1 << i)) != 0)
            {
                return nextWeekStart.AddDays(i);
            }
        }
        
        return today.AddDays(7 * rule.Interval);
    }

    private static DateOnly CalculateNextMonthlyOccurrence(RecurrenceRule rule, DateOnly today)
    {
        var nextMonth = today.AddMonths(rule.Interval);
        
        if (rule.MonthlyDayOfMonth.HasValue)
        {
            var targetDay = Math.Min(rule.MonthlyDayOfMonth.Value, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
            return new DateOnly(nextMonth.Year, nextMonth.Month, targetDay);
        }
        
        if (rule.MonthlyNthWeek.HasValue && rule.MonthlyDayOfWeek.HasValue)
        {
            return CalculateNthWeekdayOfMonth(nextMonth.Year, nextMonth.Month, rule.MonthlyNthWeek.Value, rule.MonthlyDayOfWeek.Value);
        }
        
        return nextMonth;
    }

    private static DateOnly CalculateNextYearlyOccurrence(RecurrenceRule rule, DateOnly today)
    {
        var nextYear = today.AddYears(rule.Interval);
        
        if (rule.YearlyMonth.HasValue)
        {
            if (rule.YearlyDayOfMonth.HasValue)
            {
                var targetDay = Math.Min(rule.YearlyDayOfMonth.Value, DateTime.DaysInMonth(nextYear.Year, rule.YearlyMonth.Value));
                return new DateOnly(nextYear.Year, rule.YearlyMonth.Value, targetDay);
            }
            
            if (rule.YearlyNthWeek.HasValue && rule.YearlyDayOfWeek.HasValue)
            {
                return CalculateNthWeekdayOfMonth(nextYear.Year, rule.YearlyMonth.Value, rule.YearlyNthWeek.Value, rule.YearlyDayOfWeek.Value);
            }
        }
        
        return nextYear;
    }

    private static DateOnly CalculateCustomOccurrence(RecurrenceRule rule, DateOnly today)
    {
        // Custom logic would be implemented here based on business requirements
        // For now, default to daily
        return today.AddDays(1);
    }

    private static DateOnly CalculateNthWeekdayOfMonth(int year, int month, int nthWeek, int dayOfWeek)
    {
        var firstDayOfMonth = new DateOnly(year, month, 1);
        var firstWeekday = (int)firstDayOfMonth.DayOfWeek;
        
        // Handle special cases for dayOfWeek values > 7 (AnyDay, Weekday, WeekendDay)
        if (dayOfWeek > 7)
        {
            // For now, default to Monday for special cases
            dayOfWeek = 1; // Monday
        }
        
        // Calculate the first occurrence of the target day of week
        var daysToAdd = (dayOfWeek - firstWeekday + 7) % 7;
        var firstOccurrence = firstDayOfMonth.AddDays(daysToAdd);
        
        // Handle "last" occurrence (nthWeek = 5)
        if (nthWeek == 5)
        {
            var lastDayOfMonth = new DateOnly(year, month, DateTime.DaysInMonth(year, month));
            var lastWeekday = (int)lastDayOfMonth.DayOfWeek;
            var daysBack = (lastWeekday - dayOfWeek + 7) % 7;
            return lastDayOfMonth.AddDays(-daysBack);
        }
        
        // Calculate the nth occurrence
        var targetDate = firstOccurrence.AddDays(7 * (nthWeek - 1));
        
        // Ensure we don't go beyond the month
        if (targetDate.Month != month)
        {
            return firstOccurrence; // Fall back to first occurrence
        }
        
        return targetDate;
    }
}
