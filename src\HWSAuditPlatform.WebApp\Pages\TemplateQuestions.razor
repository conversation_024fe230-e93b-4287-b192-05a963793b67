@page "/templates/{id:int}/questions"
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "SystemManager,DevAdmin")]
@inject ITemplateApiService TemplateService
@inject NavigationManager Navigation
@inject ILogger<TemplateQuestions> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Manage Questions - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading template questions...</p>
        </div>
    }
    else if (template == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Template Not Found</h4>
            <p>The requested template could not be found or you don't have permission to manage its questions.</p>
            <hr>
            <button class="btn btn-outline-danger" @onclick="BackToTemplate">
                <i class="bi bi-arrow-left me-2"></i>Back to Template
            </button>
        </div>
    }
    else if (template.IsPublished)
    {
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading">Published Template</h4>
            <p>This template is published and its questions cannot be modified. You can create a new version to make changes.</p>
            <hr>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" @onclick="CreateNewVersion">
                    <i class="bi bi-plus-circle me-2"></i>Create New Version
                </button>
                <button class="btn btn-outline-secondary" @onclick="BackToTemplate">
                    <i class="bi bi-arrow-left me-2"></i>Back to Template
                </button>
            </div>
        </div>
    }
    else
    {
        <!-- Header -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="/templates" class="text-decoration-none">Templates</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="/templates/@Id" class="text-decoration-none">@template.TemplateName</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Questions</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="h2 mb-2">Manage Questions</h1>
                        <p class="text-muted">@template.TemplateName (Version @template.Version)</p>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary" @onclick="ShowAddQuestionModal">
                            <i class="bi bi-plus-circle me-2"></i>Add Question
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="BackToTemplate">
                            <i class="bi bi-arrow-left me-2"></i>Back to Template
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @errorMessage
                <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                @successMessage
                <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
            </div>
        }

        <!-- Questions List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle me-2"></i>Questions (@GetTotalQuestionCount())
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="viewMode" id="listView" checked @onchange="@(() => SetViewMode("list"))">
                    <label class="btn btn-outline-secondary" for="listView">
                        <i class="bi bi-list"></i> List
                    </label>
                    <input type="radio" class="btn-check" name="viewMode" id="groupView" @onchange="@(() => SetViewMode("group"))">
                    <label class="btn btn-outline-secondary" for="groupView">
                        <i class="bi bi-collection"></i> Groups
                    </label>
                </div>
            </div>
            <div class="card-body">
                @if (GetTotalQuestionCount() > 0)
                {
                    @if (viewMode == "group" && template.QuestionGroups.Any(g => g.IsActive))
                    {
                        <!-- Grouped View -->
                        @foreach (var group in template.QuestionGroups.Where(g => g.IsActive).OrderBy(g => g.DisplayOrder))
                        {
                            <div class="question-group mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">
                                        <i class="bi bi-folder me-2"></i>@group.GroupName
                                        @if (!string.IsNullOrEmpty(group.Description))
                                        {
                                            <small class="text-muted ms-2">- @group.Description</small>
                                        }
                                    </h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" @onclick="() => EditGroup(group)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" @onclick="() => DeleteGroup(group)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                @foreach (var question in group.Questions.Where(q => q.IsActive).OrderBy(q => q.DisplayOrder))
                                {
                                    @RenderQuestionItem(question)
                                }
                                
                                @if (!group.Questions.Any(q => q.IsActive))
                                {
                                    <div class="text-center py-3 border rounded bg-light">
                                        <small class="text-muted">No questions in this group</small>
                                    </div>
                                }
                            </div>
                        }
                    }
                    
                    <!-- List View or Ungrouped Questions -->
                    @if (viewMode == "list" || template.Questions.Any(q => q.IsActive && !q.QuestionGroupId.HasValue))
                    {
                        <div class="question-list">
                            @if (viewMode == "list")
                            {
                                @foreach (var question in template.Questions.Where(q => q.IsActive).OrderBy(q => q.DisplayOrder))
                                {
                                    @RenderQuestionItem(question)
                                }
                            }
                            else
                            {
                                <h6 class="text-secondary mb-3">
                                    <i class="bi bi-list me-2"></i>Ungrouped Questions
                                </h6>
                                @foreach (var question in template.Questions.Where(q => q.IsActive && !q.QuestionGroupId.HasValue).OrderBy(q => q.DisplayOrder))
                                {
                                    @RenderQuestionItem(question)
                                }
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="bi bi-question-circle display-1 text-muted mb-3"></i>
                        <h5>No Questions</h5>
                        <p class="text-muted">This template doesn't have any questions yet.</p>
                        <button class="btn btn-primary" @onclick="ShowAddQuestionModal">
                            <i class="bi bi-plus-circle me-2"></i>Add First Question
                        </button>
                    </div>
                }
            </div>
        </div>
    }
</div>

@* Add Question Modal *@
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi @(editingQuestion != null ? "bi-pencil-square" : "bi-plus-circle") me-2"></i>
                    @(editingQuestion != null ? "Edit Question" : "Add New Question")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger d-flex align-items-center mb-3" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <div>@errorMessage</div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success d-flex align-items-center mb-3" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <div>@successMessage</div>
                    </div>
                }

                <EditForm Model="@questionModel" OnValidSubmit="@SaveQuestion">
                    <DataAnnotationsValidator />
                    
                    <div class="mb-3">
                        <label for="questionText" class="form-label">Question Text <span class="text-danger">*</span></label>
                        <InputTextArea id="questionText" class="form-control" rows="3" @bind-Value="questionModel.QuestionText" />
                        <ValidationMessage For="@(() => questionModel.QuestionText)" />
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="questionType" class="form-label">Question Type <span class="text-danger">*</span></label>
                                <InputSelect id="questionType" class="form-select" @bind-Value="questionModel.QuestionType">
                                    <option value="">Select type...</option>
                                    <option value="YesNo">Yes/No</option>
                                    <option value="ShortText">Short Text</option>
                                    <option value="LongText">Long Text</option>
                                    <option value="Numeric">Numeric</option>
                                    <option value="SingleSelect">Single Select</option>
                                    <option value="MultiSelect">Multi Select</option>
                                    <option value="Date">Date</option>
                                    <option value="DateTime">Date & Time</option>
                                </InputSelect>
                                <ValidationMessage For="@(() => questionModel.QuestionType)" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="displayOrder" class="form-label">Display Order</label>
                                <InputNumber id="displayOrder" class="form-control" @bind-Value="questionModel.DisplayOrder" />
                                <ValidationMessage For="@(() => questionModel.DisplayOrder)" />
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="helpText" class="form-label">Help Text</label>
                        <InputTextArea id="helpText" class="form-control" rows="2" @bind-Value="questionModel.HelpText" />
                        <ValidationMessage For="@(() => questionModel.HelpText)" />
                        <div class="form-text">Optional guidance for answering this question</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <InputCheckbox id="isRequired" class="form-check-input" @bind-Value="questionModel.IsRequired" />
                                <label class="form-check-label" for="isRequired">
                                    Required Question
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <InputCheckbox id="evidenceRequired" class="form-check-input" @bind-Value="questionModel.EvidenceRequired" />
                                <label class="form-check-label" for="evidenceRequired">
                                    Evidence Required
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    @if (questionModel.QuestionType == "SingleSelect" || questionModel.QuestionType == "MultiSelect")
                    {
                        <div class="mb-3">
                            <label class="form-label">Answer Options</label>
                            @for (int i = 0; i < questionModel.Options.Count; i++)
                            {
                                var index = i; // Capture for closure
                                <div class="input-group mb-2">
                                    <InputText class="form-control" @bind-Value="questionModel.Options[index].OptionText" placeholder="Option text" />
                                    <InputText class="form-control" @bind-Value="questionModel.Options[index].OptionValue" placeholder="Value (optional)" />
                                    <button type="button" class="btn btn-outline-danger" @onclick="() => RemoveOption(index)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            }
                            <button type="button" class="btn btn-outline-primary btn-sm" @onclick="AddOption">
                                <i class="bi bi-plus me-1"></i>Add Option
                            </button>
                        </div>
                    }
                </EditForm>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" @onclick="SaveQuestion" disabled="@isSaving">
                    @if (isSaving)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    }
                    @(editingQuestion != null ? "Update Question" : "Add Question")
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int Id { get; set; }

    private AuditTemplate? template;
    private CreateQuestionRequest questionModel = new();
    private Question? editingQuestion = null;
    private bool isLoading = true;
    private bool isSaving = false;
    private string? errorMessage = null;
    private string? successMessage = null;
    private string viewMode = "list";

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplate();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id > 0)
        {
            await LoadTemplate();
        }
    }

    private async Task LoadTemplate()
    {
        try
        {
            isLoading = true;
            errorMessage = null;
            template = await TemplateService.GetTemplateByIdAsync(Id);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading template {TemplateId}", Id);
            errorMessage = "Failed to load template. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private int GetTotalQuestionCount()
    {
        if (template?.QuestionGroups?.Any() == true)
        {
            return template.QuestionGroups.Sum(g => g.Questions?.Count(q => q.IsActive) ?? 0) +
                   template.Questions.Count(q => q.IsActive && !q.QuestionGroupId.HasValue);
        }
        return template?.Questions?.Count(q => q.IsActive) ?? 0;
    }

    private RenderFragment RenderQuestionItem(Question question) => __builder =>
    {
        <div class="question-item mb-3 p-3 border rounded">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-1">@question.QuestionText</h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" @onclick="() => EditQuestion(question)">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="() => MoveQuestionUp(question)">
                                <i class="bi bi-arrow-up"></i>
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="() => MoveQuestionDown(question)">
                                <i class="bi bi-arrow-down"></i>
                            </button>
                            <button class="btn btn-outline-danger" @onclick="() => DeleteQuestion(question)">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="d-flex align-items-center gap-3 mb-2">
                        <span class="badge bg-light text-dark">@question.QuestionType</span>
                        @if (question.IsRequired)
                        {
                            <span class="badge bg-danger">Required</span>
                        }
                        @if (question.EvidenceRequired)
                        {
                            <span class="badge bg-warning">Evidence Required</span>
                        }
                        @if (question.SeverityLevel.HasValue)
                        {
                            <span class="badge bg-info">@question.SeverityLevel</span>
                        }
                        <small class="text-muted">Order: @question.DisplayOrder</small>
                    </div>

                    @if (!string.IsNullOrEmpty(question.HelpText))
                    {
                        <p class="text-muted small mb-2">
                            <i class="bi bi-info-circle me-1"></i>@question.HelpText
                        </p>
                    }

                    @if (question.Options.Any(o => o.IsActive))
                    {
                        <div class="mt-2">
                            <small class="text-muted d-block mb-1">Options:</small>
                            <ul class="list-unstyled ms-3">
                                @foreach (var option in question.Options.Where(o => o.IsActive).OrderBy(o => o.DisplayOrder))
                                {
                                    <li class="mb-1">
                                        <i class="bi bi-dot me-1"></i>@option.OptionText
                                        @if (!string.IsNullOrEmpty(option.OptionValue))
                                        {
                                            <small class="text-muted">(@option.OptionValue)</small>
                                        }
                                    </li>
                                }
                            </ul>
                        </div>
                    }
                </div>
            </div>
        </div>
    };

    private void BackToTemplate()
    {
        Navigation.NavigateTo($"/templates/{Id}");
    }

    private void SetViewMode(string mode)
    {
        viewMode = mode;
    }

    private async Task CreateNewVersion()
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Create a new version of this template? This will copy all questions and settings to a new draft version.");

            if (confirmed)
            {
                var newTemplateId = await TemplateService.CreateTemplateVersionAsync(Id);
                if (newTemplateId.HasValue)
                {
                    Navigation.NavigateTo($"/templates/{newTemplateId.Value}/questions");
                }
                else
                {
                    errorMessage = "Failed to create template version. Please try again.";
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating template version for template {TemplateId}", Id);
            errorMessage = "An error occurred while creating the template version.";
        }
    }

    private async Task ShowAddQuestionModal()
    {
        editingQuestion = null;
        questionModel = new CreateQuestionRequest
        {
            DisplayOrder = GetTotalQuestionCount() + 1,
            IsRequired = true,
            IsActive = true,
            Options = new List<CreateQuestionOptionRequest>()
        };

        await JSRuntime.InvokeVoidAsync("bootstrapModal.show", "addQuestionModal");
    }

    private async Task EditQuestion(Question question)
    {
        editingQuestion = question;
        questionModel = new CreateQuestionRequest
        {
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType.ToString(),
            DisplayOrder = question.DisplayOrder,
            IsRequired = question.IsRequired,
            HelpText = question.HelpText,
            EvidenceRequired = question.EvidenceRequired,
            AllowedEvidenceTypes = question.GetActiveEvidenceTypes().Select(et => et.ToString()).ToList(),
            IsActive = question.IsActive,
            Options = question.Options.Where(o => o.IsActive).OrderBy(o => o.DisplayOrder)
                .Select(o => new CreateQuestionOptionRequest
                {
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                }).ToList()
        };

        await JSRuntime.InvokeVoidAsync("bootstrapModal.show", "addQuestionModal");
    }

    private async Task SaveQuestion()
    {
        try
        {
            isSaving = true;
            errorMessage = null;
            successMessage = null;

            if (editingQuestion != null)
            {
                // Update existing question
                var updateRequest = new UpdateQuestionRequest
                {
                    QuestionText = questionModel.QuestionText,
                    QuestionType = questionModel.QuestionType,
                    DisplayOrder = questionModel.DisplayOrder,
                    IsRequired = questionModel.IsRequired,
                    HelpText = questionModel.HelpText,
                    EvidenceRequired = questionModel.EvidenceRequired,
                    IsActive = questionModel.IsActive,
                    Options = questionModel.Options
                };

                var success = await TemplateService.UpdateQuestionAsync(editingQuestion.Id, updateRequest);
                if (success)
                {
                    successMessage = "Question updated successfully!";
                    await LoadTemplate();
                    await JSRuntime.InvokeVoidAsync("bootstrapModal.hide", "addQuestionModal");
                }
                else
                {
                    errorMessage = "Failed to update question. Please try again.";
                }
            }
            else
            {
                // Add new question
                var questionId = await TemplateService.AddQuestionToTemplateAsync(Id, questionModel);
                if (questionId.HasValue)
                {
                    successMessage = "Question added successfully!";
                    await LoadTemplate();
                    await JSRuntime.InvokeVoidAsync("bootstrapModal.hide", "addQuestionModal");
                }
                else
                {
                    errorMessage = "Failed to add question. Please try again.";
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving question for template {TemplateId}", Id);
            errorMessage = "An error occurred while saving the question.";
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteQuestion(Question question)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                $"Are you sure you want to delete the question '{question.QuestionText}'?");

            if (confirmed)
            {
                var success = await TemplateService.DeleteQuestionAsync(question.Id);
                if (success)
                {
                    successMessage = "Question deleted successfully!";
                    await LoadTemplate();
                }
                else
                {
                    errorMessage = "Failed to delete question. Please try again.";
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting question {QuestionId}", question.Id);
            errorMessage = "An error occurred while deleting the question.";
        }
    }

    private void AddOption()
    {
        questionModel.Options.Add(new CreateQuestionOptionRequest
        {
            DisplayOrder = questionModel.Options.Count + 1,
            IsActive = true
        });
    }

    private void RemoveOption(int index)
    {
        if (index >= 0 && index < questionModel.Options.Count)
        {
            questionModel.Options.RemoveAt(index);
        }
    }

    private async Task MoveQuestionUp(Question question)
    {
        // TODO: Implement question reordering
        await JSRuntime.InvokeVoidAsync("alert", "Question reordering will be implemented in a future update.");
    }

    private async Task MoveQuestionDown(Question question)
    {
        // TODO: Implement question reordering
        await JSRuntime.InvokeVoidAsync("alert", "Question reordering will be implemented in a future update.");
    }

    private async Task EditGroup(QuestionGroup group)
    {
        // TODO: Implement group editing
        await JSRuntime.InvokeVoidAsync("alert", "Group editing will be implemented in a future update.");
    }

    private async Task DeleteGroup(QuestionGroup group)
    {
        // TODO: Implement group deletion
        await JSRuntime.InvokeVoidAsync("alert", "Group deletion will be implemented in a future update.");
    }
}

<style>
    /* Industrial Modal Styling */
    .modal {
        z-index: 1055;
    }

    .modal-backdrop {
        z-index: 1050;
        background-color: rgba(0, 0, 0, 0.7);
    }

    .modal-content {
        background-color: var(--industrial-gray);
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }

    .modal-header {
        background-color: var(--industrial-darker);
        border-bottom: 2px solid var(--industrial-orange);
        border-radius: 6px 6px 0 0;
        padding: 1.25rem;
    }

    .modal-title {
        color: var(--industrial-text);
        font-family: 'Roboto Condensed', sans-serif;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 1.25rem;
        margin: 0;
    }

    .modal-body {
        background-color: var(--industrial-light-gray);
        padding: 1.5rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-body::-webkit-scrollbar {
        width: 8px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: var(--industrial-darker);
        border-radius: 4px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background: var(--industrial-steel);
        border-radius: 4px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
        background: var(--industrial-orange);
    }

    .modal-footer {
        background-color: var(--industrial-darker);
        border-top: 2px solid var(--industrial-border);
        border-radius: 0 0 6px 6px;
        padding: 1.25rem;
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
    }

    /* Form Styling within Modal */
    .modal .form-label {
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    .modal .form-control,
    .modal .form-select {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 4px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .modal .form-control:focus,
    .modal .form-select:focus {
        background-color: var(--industrial-darker);
        border-color: var(--industrial-orange);
        color: var(--industrial-text);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }

    .modal .form-control::placeholder {
        color: var(--industrial-text-muted);
    }

    .modal .form-text {
        color: var(--industrial-text-muted);
        font-size: 0.875rem;
        font-style: italic;
    }

    /* Checkbox Styling */
    .modal .form-check-input {
        background-color: var(--industrial-gray);
        border: 2px solid var(--industrial-border);
        border-radius: 4px;
        width: 1.25rem;
        height: 1.25rem;
    }

    .modal .form-check-input:checked {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
    }

    .modal .form-check-input:focus {
        border-color: var(--industrial-orange);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }

    .modal .form-check-label {
        color: var(--industrial-text);
        font-weight: 500;
        margin-left: 0.5rem;
    }

    /* Button Styling */
    .modal .btn-primary {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .modal .btn-primary:hover {
        background-color: #fbb040;
        border-color: #fbb040;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .modal .btn-primary:disabled {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-steel);
        opacity: 0.7;
        transform: none;
        box-shadow: none;
    }

    .modal .btn-secondary {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-steel);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .modal .btn-secondary:hover {
        background-color: #5a6578;
        border-color: #5a6578;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .modal .btn-outline-primary {
        border-color: var(--industrial-orange);
        color: var(--industrial-orange);
        background-color: transparent;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .modal .btn-outline-primary:hover {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .modal .btn-outline-danger {
        border-color: var(--industrial-red);
        color: var(--industrial-red);
        background-color: transparent;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .modal .btn-outline-danger:hover {
        background-color: var(--industrial-red);
        border-color: var(--industrial-red);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    /* Input Group Styling */
    .modal .input-group {
        margin-bottom: 0.75rem;
    }

    .modal .input-group .form-control {
        border-right: none;
    }

    .modal .input-group .form-control:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .modal .input-group .form-control:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-left: none;
    }

    .modal .input-group .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* Validation Styling */
    .modal .validation-message {
        color: var(--industrial-red);
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    .modal .text-danger {
        color: var(--industrial-red) !important;
    }

    /* Spinner Styling */
    .modal .spinner-border {
        width: 1rem;
        height: 1rem;
        border-width: 0.125rem;
    }

    /* Alert Styling */
    .modal .alert {
        border-radius: 6px;
        border: none;
        padding: 1rem;
        margin-bottom: 1rem;
        font-weight: 500;
    }

    .modal .alert-danger {
        background-color: rgba(229, 62, 62, 0.15);
        color: #ff6b6b;
        border-left: 4px solid var(--industrial-red);
    }

    .modal .alert-success {
        background-color: rgba(56, 161, 105, 0.15);
        color: #68d391;
        border-left: 4px solid var(--industrial-green);
    }

    .modal .alert i {
        font-size: 1.1rem;
    }

    /* Close Button Styling */
    .modal .btn-close {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: var(--industrial-text-muted);
        opacity: 0.8;
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 4px;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal .btn-close:hover {
        color: var(--industrial-orange);
        opacity: 1;
        background-color: rgba(237, 137, 54, 0.1);
        transform: scale(1.1);
    }

    .modal .btn-close i {
        font-size: 1rem;
    }

    /* Enhanced Visual Elements */
    .modal .mb-3:last-of-type {
        margin-bottom: 1rem !important;
    }

    .modal .row .col-md-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .modal .input-group .form-control:first-child {
        flex: 2;
    }

    .modal .input-group .form-control:nth-child(2) {
        flex: 1;
        border-left: 1px solid var(--industrial-border);
    }

    /* Option Management Styling */
    .modal .input-group .form-control[placeholder*="Option text"] {
        background-color: var(--industrial-gray);
        border-color: var(--industrial-border);
    }

    .modal .input-group .form-control[placeholder*="Value"] {
        background-color: var(--industrial-darker);
        border-color: var(--industrial-border);
        font-style: italic;
    }

    /* Enhanced Focus States */
    .modal .form-control:focus,
    .modal .form-select:focus {
        outline: none;
        border-color: var(--industrial-orange);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25),
                    inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Loading State */
    .modal .btn:disabled .spinner-border {
        animation: spinner-border 0.75s linear infinite;
    }

    /* Modal Animation Enhancement */
    .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out, opacity 0.3s ease-out;
        transform: translate(0, -50px) scale(0.95);
        opacity: 0;
    }

    .modal.show .modal-dialog {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }

    /* Responsive Design - Mobile Styles */
    .modal-dialog {
        margin: 1rem;
    }

    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
</style>
