using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents the many-to-many relationship between Questions and allowed EvidenceTypes.
/// Maps to the QuestionAllowedEvidenceTypes table in the database.
/// </summary>
public class QuestionAllowedEvidenceType : BaseEntity<int>
{
    /// <summary>
    /// Links to the question
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// Navigation property for the question
    /// </summary>
    public virtual Question Question { get; set; } = null!;

    /// <summary>
    /// The allowed evidence type for this question
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Optional display order for UI presentation
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// Indicates if this evidence type is currently active for the question
    /// </summary>
    public bool IsActive { get; set; } = true;
}
