using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Scheduling.Commands.DeleteRecurringAuditSetting;

/// <summary>
/// Handler for DeleteRecurringAuditSettingCommand
/// </summary>
public class DeleteRecurringAuditSettingCommandHandler : BaseCommandHandler<DeleteRecurringAuditSettingCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteRecurringAuditSettingCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(DeleteRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        // Get the existing recurring audit setting
        var recurringAuditSetting = await _context.RecurringAuditSettings
            .Include(r => r.RecurrenceRule)
            .FirstOrDefaultAsync(r => r.Id == request.Id && r.IsEnabled, cancellationToken);

        if (recurringAuditSetting == null)
        {
            throw new NotFoundException(nameof(RecurringAuditSetting), request.Id);
        }

        // Check for optimistic concurrency
        if (recurringAuditSetting.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The recurring audit setting has been modified by another user. Please refresh and try again.");
        }

        // Check if there are any pending audits generated by this setting
        var hasPendingAudits = await _context.Audits
            .AnyAsync(a => a.RecurringAuditSettingId == request.Id &&
                          a.OverallStatus != AuditOverallStatus.Closed &&
                          a.OverallStatus != AuditOverallStatus.Cancelled,
                      cancellationToken);

        if (hasPendingAudits)
        {
            throw new InvalidOperationException("Cannot delete a recurring audit setting that has pending audits. Complete or cancel all pending audits first.");
        }

        // Soft delete the recurring audit setting
        recurringAuditSetting.IsEnabled = false;
        recurringAuditSetting.UpdatedAt = DateTime.UtcNow;
        recurringAuditSetting.UpdatedByUserId = _currentUserService.UserId;

        // Note: RecurrenceRule doesn't have IsEnabled property, it will be effectively disabled
        // when the parent RecurringAuditSetting is disabled

        await _context.SaveChangesAsync(cancellationToken);
    }
}
