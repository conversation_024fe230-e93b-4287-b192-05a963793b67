namespace HWSAuditPlatform.WebAuditPWA.Models;

/// <summary>
/// Model for finding category data in PWA
/// </summary>
public class FindingCategoryModel
{
    public int Id { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool RequiresDocumentation { get; set; }
    public int FindingCount { get; set; }
    public int OpenFindingCount { get; set; }
    public bool IsUsable { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int RecordVersion { get; set; }
    public string? CreatedByUserId { get; set; }
    public string? UpdatedByUserId { get; set; }
}

/// <summary>
/// Model for finding category summary (for dropdowns and lists)
/// </summary>
public class FindingCategorySummaryModel
{
    public int Id { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ColorCode { get; set; }
    public string? IconName { get; set; }
    public bool IsActive { get; set; }
    public int FindingCount { get; set; }
}

/// <summary>
/// Model for finding category assignment
/// </summary>
public class AssignFindingCategoryModel
{
    public int? FindingCategoryId { get; set; }
}

/// <summary>
/// Model for finding category filters
/// </summary>
public class FindingCategoryFilterModel
{
    public int? AuditTemplateId { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool IncludeUsageStatistics { get; set; } = false;
}

/// <summary>
/// Model for audit finding display in PWA
/// </summary>
public class AuditFindingModel
{
    public string Id { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public HWSAuditPlatform.Domain.Enums.SeverityLevel SeverityLevel { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }
    public string? CategoryColorCode { get; set; }
    public string? CategoryIconName { get; set; }
    public string? ResponsibleUserName { get; set; }
    public string? RetrospectiveAnalystUserName { get; set; }
    public DateTime? DueDate { get; set; }
    public bool IsOverdue { get; set; }
    public DateTime CreatedAt { get; set; }
}
