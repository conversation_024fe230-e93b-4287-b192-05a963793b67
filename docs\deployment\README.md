# HWS Audit Platform - Deployment Documentation

## Overview

This section contains comprehensive documentation for deploying and configuring the HWS Audit Platform in various environments.

## Deployment Options

### Development Environment
- Local SQL Server or LocalDB
- Local file storage
- Optional Active Directory integration
- Visual Studio debugging support

### Docker Environment
- Containerized SQL Server and Active Directory
- Isolated network configuration
- Development and testing focused
- Easy setup and teardown

### Production Environment
- Enterprise SQL Server
- Network file storage with backup
- Production Active Directory
- IIS or Kestrel deployment
- Monitoring and logging

## Deployment Documentation

### [Docker Setup](docker-setup.md)
Complete guide for setting up the Docker development environment.
- Docker container configuration
- SQL Server and Active Directory containers
- Network setup and port configuration
- Test users and data seeding
- Troubleshooting common Docker issues

### [Local Development Setup](local-development.md)
Guide for setting up local development environment.
- Prerequisites and system requirements
- Database configuration and migrations
- Development tools setup
- Configuration management
- Local testing procedures

### [External File Storage](external-storage.md)
Configuration guide for external file storage setup.
- External drive configuration (D:\uploads)
- Automated setup scripts
- Permission configuration
- Docker volume mounting
- Backup and maintenance procedures

### [Production Deployment](production-deployment.md)
Comprehensive guide for production deployment.
- Production environment requirements
- IIS configuration and deployment
- Database setup and migration
- Security configuration
- Monitoring and logging setup
- Backup and disaster recovery

### [Troubleshooting](troubleshooting.md)
Common deployment issues and solutions.
- Database connection issues
- Active Directory authentication problems
- File storage permission issues
- Performance troubleshooting
- Log analysis and debugging

## Quick Start

### Docker Environment (Recommended for Development)
```powershell
# Start Docker environment
./docker-start.ps1

# Run the application
$env:ASPNETCORE_ENVIRONMENT="Docker"
dotnet run --project src/HWSAuditPlatform.ApiService
```

### Local Development
```bash
# Restore packages
dotnet restore

# Update database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Run application
dotnet run --project src/HWSAuditPlatform.ApiService
```

## Environment Configuration

### Development
- **Database**: LocalDB or SQL Server Express
- **File Storage**: Local wwwroot/uploads or D:\uploads
- **Authentication**: Optional AD or test users
- **Logging**: Console and file logging

### Docker
- **Database**: SQL Server 2022 container
- **File Storage**: Docker volume mounted to host
- **Authentication**: Samba AD container with test users
- **Logging**: Container logs and application logs

### Production
- **Database**: Enterprise SQL Server with backup
- **File Storage**: Network storage with redundancy
- **Authentication**: Production Active Directory
- **Logging**: Centralized logging with monitoring

## Security Considerations

### Development
- Default passwords for convenience
- HTTP allowed for local testing
- Minimal security restrictions
- Debug information enabled

### Production
- Strong, unique passwords
- HTTPS required for all communications
- Proper network security and firewalls
- Audit logging and monitoring
- Regular security updates

## Monitoring and Health Checks

### Health Check Endpoints
- `/health` - Basic system health
- `/health/info` - API information
- `/health/ping` - Connectivity test

### Key Metrics
- Database connectivity and performance
- Active Directory authentication success rate
- File storage availability and capacity
- API response times and error rates

## Backup and Recovery

### Database Backup
- Regular automated backups
- Point-in-time recovery capability
- Backup verification procedures
- Disaster recovery testing

### File Storage Backup
- Regular file system backups
- Version control for critical files
- Offsite backup storage
- Recovery procedures and testing

## Support and Maintenance

### Regular Maintenance Tasks
- Database maintenance and optimization
- Log file cleanup and archival
- Security updates and patches
- Performance monitoring and tuning

### Troubleshooting Resources
- Application logs and error tracking
- Performance counters and metrics
- Health check monitoring
- Support contact information

For detailed deployment procedures, refer to the specific deployment documentation files in this section.
