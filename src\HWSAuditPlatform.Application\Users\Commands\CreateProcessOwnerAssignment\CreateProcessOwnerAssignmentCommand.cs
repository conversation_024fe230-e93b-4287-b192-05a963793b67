using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Users.Commands.CreateProcessOwnerAssignment;

/// <summary>
/// Command to create a new process owner assignment
/// </summary>
public class CreateProcessOwnerAssignmentCommand : BaseCommand<int>
{
    public string ProcessOwnerUserId { get; set; } = string.Empty;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public string? Description { get; set; }
}
