# HWS Audit Platform - File Management & Health API

## Overview

This document provides comprehensive documentation for the File Management and Health/Monitoring endpoints of the HWS Audit Platform API. These endpoints handle file upload/download operations and system health monitoring.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **Authentication**: JWT Bearer <PERSON>ken required (except health endpoints)
- **Content-Type**: `application/json` or `multipart/form-data` for file uploads
- **API Version**: `1.0`

## File Management Endpoints

### POST /files/upload

Upload a file to the system with validation and security checks.

**Authorization**: Auditor role or above required

**Content-Type**: `multipart/form-data`

**Request Parameters**:
- `file` (file): The file to upload

**File Restrictions**:
- **Maximum Size**: 10 MB (10,485,760 bytes)
- **Allowed Types**: 
  - Images: `image/jpeg`, `image/jpg`, `image/png`, `image/gif`, `image/bmp`
  - Documents: `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - Spreadsheets: `application/vnd.ms-excel`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
  - Text: `text/plain`, `text/csv`

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "fileId": "cf1234567890abcdef",
    "fileName": "safety_inspection_photo.jpg",
    "filePath": "uploads/2024/01/15/cf1234567890abcdef_safety_inspection_photo.jpg",
    "fileSize": 2048576,
    "contentType": "image/jpeg",
    "uploadedAt": "2024-01-15T10:30:00Z",
    "uploadedBy": "john.doe"
  },
  "message": "File uploaded successfully"
}
```

**Error Responses**:
- `400 Bad Request`: No file provided, file too large, or invalid file type
- `413 Payload Too Large`: File exceeds maximum size limit
- `415 Unsupported Media Type`: File type not allowed

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/files/upload" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/your/document.pdf"
```

### GET /files/download/{*filePath}

Download a file from the system.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `filePath` (string): Full path to the file (URL encoded)

**Response**: File content with appropriate headers
- `Content-Type`: Original file content type
- `Content-Disposition`: `attachment; filename="original_filename.ext"`
- `Content-Length`: File size in bytes

**Error Responses**:
- `400 Bad Request`: File path is required
- `404 Not Found`: File not found
- `403 Forbidden`: Access denied to file

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/files/download/uploads/2024/01/15/cf1234567890abcdef_safety_inspection_photo.jpg" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -o "downloaded_file.jpg"
```

### GET /files/download-url/{*filePath}

Generate a temporary download URL for a file.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `filePath` (string): Full path to the file (URL encoded)

**Query Parameters**:
- `expiryHours` (int, optional): URL expiry time in hours (default: 1, max: 24)

**Response** (200 OK):
```json
{
  "success": true,
  "data": "https://localhost:7490/api/v1/files/temp-download/abc123def456?expires=1642248000&signature=xyz789",
  "message": "Download URL generated successfully"
}
```

**Error Responses**:
- `400 Bad Request`: File path required or invalid expiry hours
- `404 Not Found`: File not found

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/files/download-url/uploads/2024/01/15/cf1234567890abcdef_safety_inspection_photo.jpg?expiryHours=2" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### DELETE /files/{*filePath}

Delete a file from the system.

**Authorization**: Manager or Admin role required

**Path Parameters**:
- `filePath` (string): Full path to the file (URL encoded)

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: File path is required
- `404 Not Found`: File not found
- `403 Forbidden`: Insufficient permissions

**Example**:
```bash
curl -X DELETE "https://localhost:7490/api/v1/files/uploads/2024/01/15/cf1234567890abcdef_safety_inspection_photo.jpg" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Health & Monitoring Endpoints

### GET /health

Comprehensive health check endpoint for system monitoring.

**Authorization**: None (Anonymous)

**Response** (200 OK - Healthy):
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.1234567",
  "entries": {
    "database": {
      "status": "Healthy",
      "duration": "00:00:00.0456789",
      "description": "Database connection is healthy"
    },
    "activedirectory": {
      "status": "Healthy",
      "duration": "00:00:00.0234567",
      "description": "Active Directory connection is healthy"
    },
    "filestorage": {
      "status": "Healthy",
      "duration": "00:00:00.0123456",
      "description": "File storage is accessible"
    }
  }
}
```

**Response** (503 Service Unavailable - Unhealthy):
```json
{
  "status": "Unhealthy",
  "totalDuration": "00:00:05.1234567",
  "entries": {
    "database": {
      "status": "Unhealthy",
      "duration": "00:00:05.0000000",
      "description": "Database connection timeout",
      "exception": "Connection timeout after 5 seconds"
    }
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/health"
```

### GET /health/info

Get API information and available endpoints.

**Authorization**: None (Anonymous)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "name": "HWS Audit Platform API",
    "version": "1.0.0",
    "description": "Manufacturing Quality Audit Management System API",
    "documentation": "/swagger",
    "healthCheck": "/health",
    "endpoints": {
      "authentication": "/api/v1/auth",
      "users": "/api/v1/users",
      "audits": "/api/v1/audits",
      "templates": "/api/v1/templates",
      "organization": "/api/v1/organization",
      "files": "/api/v1/files"
    },
    "features": [
      "JWT Authentication",
      "Active Directory Integration",
      "Role-based Authorization",
      "File Upload/Download",
      "Audit Management",
      "Template Management",
      "Organization Hierarchy",
      "Health Monitoring"
    ]
  },
  "message": "API information retrieved successfully"
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/health/info"
```

### GET /health/ping

Simple connectivity test endpoint.

**Authorization**: None (Anonymous)

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "message": "Pong",
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "0HMVP8H8R7G4K:00000001"
  },
  "message": "Ping successful"
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/health/ping"
```

### GET /health/auth-test

Test authenticated endpoint to verify JWT token validity.

**Authorization**: Bearer Token Required

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "message": "Authentication successful",
    "user": {
      "id": "c1234567890abcdef",
      "username": "john.doe",
      "role": "Manager",
      "factoryId": 1
    },
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "message": "Authentication test successful"
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing token

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/health/auth-test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## File Storage Information

- **Storage Location**: Files are stored on external drive `D:\uploads`
- **File Organization**: Files are organized by upload date: `uploads/YYYY/MM/DD/`
- **File Naming**: Files are renamed with CUID prefix: `{CUID}_{original_filename}`
- **Security**: All file operations are logged and require authentication
- **Cleanup**: Temporary download URLs expire automatically

## Error Handling

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "file",
      "message": "File is required"
    }
  ]
}
```

## Security Notes

- File uploads are scanned for malicious content
- File types are strictly validated by content, not just extension
- All file operations are logged with user information
- Temporary download URLs are signed and time-limited
- File deletion requires elevated permissions (Manager/Admin)
