using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteQuestion;

/// <summary>
/// Command to soft delete a question from an audit template
/// </summary>
public class DeleteQuestionCommand : BaseCommand
{
    /// <summary>
    /// The ID of the question to delete
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// The ID of the audit template (for validation)
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }

    public DeleteQuestionCommand(int questionId, int templateId, int recordVersion)
    {
        QuestionId = questionId;
        TemplateId = templateId;
        RecordVersion = recordVersion;
    }
}
