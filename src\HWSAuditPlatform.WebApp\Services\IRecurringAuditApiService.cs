using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for recurring audit API operations
/// </summary>
public interface IRecurringAuditApiService
{
    /// <summary>
    /// Gets a paginated list of recurring audit settings
    /// </summary>
    Task<PaginatedResult<RecurringAuditSettingSummaryDto>> GetRecurringAuditSettingsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        bool? isEnabled = null,
        int? auditTemplateId = null,
        int? factoryId = null,
        int? areaId = null,
        int? subAreaId = null);

    /// <summary>
    /// Gets a specific recurring audit setting by ID
    /// </summary>
    Task<RecurringAuditSettingDto?> GetRecurringAuditSettingByIdAsync(string id);

    /// <summary>
    /// Creates a new recurring audit setting
    /// </summary>
    Task<string> CreateRecurringAuditSettingAsync(CreateRecurringAuditSettingRequest request);

    /// <summary>
    /// Updates an existing recurring audit setting
    /// </summary>
    Task<bool> UpdateRecurringAuditSettingAsync(string id, UpdateRecurringAuditSettingRequest request);

    /// <summary>
    /// Deletes a recurring audit setting
    /// </summary>
    Task<bool> DeleteRecurringAuditSettingAsync(string id);

    /// <summary>
    /// Toggles the enabled status of a recurring audit setting
    /// </summary>
    Task<bool> ToggleRecurringAuditSettingAsync(string id);

    /// <summary>
    /// Manually generates audits from recurring settings
    /// </summary>
    Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> GenerateRecurringAuditsAsync(
        string? settingId = null,
        bool dryRun = false,
        int maxAuditsToGenerate = 100);

    /// <summary>
    /// Previews what audits would be generated from a specific setting
    /// </summary>
    Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> PreviewRecurringAuditGenerationAsync(string settingId);
}

/// <summary>
/// Request model for creating recurring audit settings
/// </summary>
public class CreateRecurringAuditSettingRequest
{
    public string SettingName { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public int FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public int DeadlineDays { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignToUserId { get; set; }
    public string? AssignToUserGroupId { get; set; }
    public CreateRecurrenceRuleRequest RecurrenceRule { get; set; } = new();
}

/// <summary>
/// Request model for updating recurring audit settings
/// </summary>
public class UpdateRecurringAuditSettingRequest
{
    public string SettingName { get; set; } = string.Empty;
    public int DeadlineDays { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignToUserId { get; set; }
    public string? AssignToUserGroupId { get; set; }
    public UpdateRecurrenceRuleRequest RecurrenceRule { get; set; } = new();
}

/// <summary>
/// Request model for creating recurrence rules
/// </summary>
public class CreateRecurrenceRuleRequest
{
    public FrequencyType FrequencyType { get; set; }
    public int Interval { get; set; } = 1;
    public DateOnly StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public int? DayOfMonth { get; set; }
    public DayOfWeek? DayOfWeek { get; set; }
    public int? WeekOfMonth { get; set; }
    public int? MonthOfYear { get; set; }
    public string? CustomPattern { get; set; }
}

/// <summary>
/// Request model for updating recurrence rules
/// </summary>
public class UpdateRecurrenceRuleRequest
{
    public FrequencyType FrequencyType { get; set; }
    public int Interval { get; set; } = 1;
    public DateOnly? EndDate { get; set; }
    public int? DayOfMonth { get; set; }
    public DayOfWeek? DayOfWeek { get; set; }
    public int? WeekOfMonth { get; set; }
    public int? MonthOfYear { get; set; }
    public string? CustomPattern { get; set; }
}




