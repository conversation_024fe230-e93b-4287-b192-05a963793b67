services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: hwsaudit-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=HWSAudit123!
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./docker/sql-init:/docker-entrypoint-initdb.d
    networks:
      - hwsaudit-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P HWSAudit123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  sqlserver_data:
    driver: local

networks:
  hwsaudit-network:
    driver: bridge
