namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the overall status of an audit throughout its lifecycle.
/// Maps to the audit_overall_status enum in the database.
/// </summary>
public enum AuditOverallStatus
{
    /// <summary>
    /// Audit is planned but not yet started
    /// </summary>
    Scheduled,

    /// <summary>
    /// Audit is actively being worked on by the assignee; can be paused and resumed
    /// </summary>
    InProgress,

    /// <summary>
    /// Auditor has completed and submitted the audit for review
    /// </summary>
    Submitted,

    /// <summary>
    /// Audit is awaiting review by a manager
    /// </summary>
    PendingManagerReview,

    /// <summary>
    /// Audit has been reviewed and requires corrections by the auditor
    /// </summary>
    PendingCorrection,

    /// <summary>
    /// Manager has completed their review; may lead to Closed or further actions
    /// </summary>
    ManagerReviewed,

    /// <summary>
    /// Audit lifecycle is complete, all actions finalized
    /// </summary>
    Closed,

    /// <summary>
    /// Audit has been cancelled and will not be performed/completed
    /// </summary>
    Cancelled
}
