#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Stops the HWS Audit Platform Docker environment
.DESCRIPTION
    This script stops and optionally removes the Docker containers for the HWS Audit Platform.
#>

param(
    [switch]$RemoveVolumes,
    [switch]$RemoveImages,
    [switch]$Clean
)

# Colors for output
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")

    switch ($Color) {
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        default { Write-Host $Message }
    }
}

try {
    Write-ColorOutput "Stopping HWS Audit Platform Docker Environment" "Blue"
    Write-ColorOutput "=================================================" "Blue"

    # Stop containers
    Write-ColorOutput "Stopping Docker containers..." "Yellow"
    docker-compose down

    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "Some containers may have already been stopped" "Yellow"
    } else {
        Write-ColorOutput "Containers stopped successfully" "Green"
    }

    # Remove volumes if requested
    if ($RemoveVolumes -or $Clean) {
        Write-ColorOutput "Removing Docker volumes..." "Yellow"
        docker-compose down -v
        Write-ColorOutput "Volumes removed" "Green"
    }

    # Remove images if requested
    if ($RemoveImages -or $Clean) {
        Write-ColorOutput "Removing Docker images..." "Yellow"
        docker rmi mcr.microsoft.com/mssql/server:2022-latest nowsci/samba-domain 2>$null
        Write-ColorOutput "Images removed" "Green"
    }

    # Clean up system if requested
    if ($Clean) {
        Write-ColorOutput "Cleaning up Docker system..." "Yellow"
        docker system prune -f
        Write-ColorOutput "System cleaned" "Green"
    }

    Write-Host ""
    Write-ColorOutput "HWS Audit Platform Docker Environment Stopped!" "Green"
    Write-Host ""
    Write-ColorOutput "To restart:" "Blue"
    Write-Host "  ./docker-start.ps1"
    Write-Host ""
    Write-ColorOutput "To clean restart:" "Blue"
    Write-Host "  ./docker-start.ps1 -Clean"

}
catch {
    Write-ColorOutput "Error: $($_.Exception.Message)" "Red"
    exit 1
}
