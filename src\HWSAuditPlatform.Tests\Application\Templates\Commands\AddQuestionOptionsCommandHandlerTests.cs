using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestionOptions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class AddQuestionOptionsCommandHandlerTests : BaseDbTestClass
{
    private readonly AddQuestionOptionsCommandHandler _handler;

    public AddQuestionOptionsCommandHandlerTests()
    {
        _handler = new AddQuestionOptionsCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldAddOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 },
                new() { OptionText = "Option 2", OptionValue = "opt2", DisplayOrder = 2 },
                new() { OptionText = "Option 3", OptionValue = "opt3", DisplayOrder = 3, IsCorrectOption = true }
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        
        var addedOptions = await Context.QuestionOptions
            .Where(o => o.QuestionId == question.Id)
            .OrderBy(o => o.DisplayOrder)
            .ToListAsync();
        
        addedOptions.Should().HaveCount(3);
        addedOptions[0].OptionText.Should().Be("Option 1");
        addedOptions[0].OptionValue.Should().Be("opt1");
        addedOptions[0].DisplayOrder.Should().Be(1);
        addedOptions[0].IsCorrectOption.Should().BeNull();
        
        addedOptions[2].OptionText.Should().Be("Option 3");
        addedOptions[2].IsCorrectOption.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithInvalidQuestionId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = 999,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<NotFoundException>()
            .WithMessage("*Question*999*");
    }

    [Fact]
    public async Task Handle_WithMismatchedTemplateId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template1 = await CreateTestTemplateAsync();
        var template2 = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template1.Id, QuestionType.SingleSelect);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template2.Id, // Different template
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*does not belong to the specified template*");
    }

    [Fact]
    public async Task Handle_WithPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        template.IsPublished = true;
        await Context.SaveChangesAsync();
        
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("*published template*");
    }

    [Theory]
    [InlineData(QuestionType.YesNo)]
    [InlineData(QuestionType.Numeric)]
    [InlineData(QuestionType.ShortText)]
    [InlineData(QuestionType.LongText)]
    [InlineData(QuestionType.Date)]
    public async Task Handle_WithUnsupportedQuestionType_ShouldThrowInvalidOperationException(QuestionType questionType)
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, questionType);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage($"*{questionType}*question type*");
    }

    [Theory]
    [InlineData(QuestionType.SingleSelect)]
    [InlineData(QuestionType.MultiSelect)]
    public async Task Handle_WithSupportedQuestionType_ShouldSucceed(QuestionType questionType)
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, questionType);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => _handler.Handle(command, CancellationToken.None))
            .Should().NotThrowAsync();
        
        var addedOptions = await Context.QuestionOptions
            .Where(o => o.QuestionId == question.Id)
            .ToListAsync();
        
        addedOptions.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ShouldUpdateQuestionTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        var originalQuestionUpdateTime = question.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedQuestion = await Context.Questions.FirstOrDefaultAsync(q => q.Id == question.Id);
        updatedQuestion.Should().NotBeNull();
        updatedQuestion!.UpdatedAt.Should().BeAfter(originalQuestionUpdateTime);
    }

    [Fact]
    public async Task Handle_ShouldUpdateTemplateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        var originalTemplateUpdateTime = template.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 }
            }
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedAt.Should().BeAfter(originalTemplateUpdateTime);
    }

    [Fact]
    public async Task Handle_ShouldReturnCreatedOptionIds()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var question = await CreateTestQuestionAsync(template.Id, QuestionType.SingleSelect);
        
        var command = new AddQuestionOptionsCommand
        {
            QuestionId = question.Id,
            TemplateId = template.Id,
            Options = new List<AddQuestionOptionDto>
            {
                new() { OptionText = "Option 1", OptionValue = "opt1", DisplayOrder = 1 },
                new() { OptionText = "Option 2", OptionValue = "opt2", DisplayOrder = 2 }
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().OnlyContain(id => id > 0);
        
        var addedOptions = await Context.QuestionOptions
            .Where(o => result.Contains(o.Id))
            .ToListAsync();
        
        addedOptions.Should().HaveCount(2);
    }

    private async Task<Question> CreateTestQuestionAsync(int templateId, QuestionType questionType)
    {
        var question = new Question
        {
            AuditTemplateId = templateId,
            QuestionText = $"Test Question {Guid.NewGuid()}",
            QuestionType = questionType,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();
        return question;
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isPublished = false)
    {
        var template = new AuditTemplate
        {
            TemplateName = $"Test Template {Guid.NewGuid()}",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
