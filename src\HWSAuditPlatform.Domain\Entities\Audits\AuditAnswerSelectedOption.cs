using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Domain.Entities.Audits;

/// <summary>
/// Join table for MultiSelect question types, linking an answer to multiple selected options.
/// Maps to the AuditAnswerSelectedOptions table in the database.
/// </summary>
public class AuditAnswerSelectedOption : BaseEntity<string>
{
    /// <summary>
    /// Links to the audit answer (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit answer
    /// </summary>
    public virtual AuditAnswer AuditAnswer { get; set; } = null!;

    /// <summary>
    /// Links to the selected question option
    /// </summary>
    public int QuestionOptionId { get; set; }

    /// <summary>
    /// Navigation property for the question option
    /// </summary>
    public virtual QuestionOption QuestionOption { get; set; } = null!;
}
