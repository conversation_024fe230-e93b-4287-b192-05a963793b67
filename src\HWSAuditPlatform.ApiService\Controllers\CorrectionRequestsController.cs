using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Workflow.Commands.CreateCorrectionRequest;
using HWSAuditPlatform.Application.Workflow.Queries.GetMyCorrectionRequests;
using HWSAuditPlatform.Application.Workflow.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing audit correction requests
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/correction-requests")]
public class CorrectionRequestsController : BaseController
{
    public CorrectionRequestsController(IMediator mediator, ILogger<CorrectionRequestsController> logger)
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Create a new correction request for an audit
    /// </summary>
    /// <param name="request">Correction request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created correction request ID</returns>
    [HttpPost]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(string), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<string>> CreateCorrectionRequest(
        [FromBody] CreateCorrectionRequestDto request,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Creating correction request for audit: {AuditId}", request.AuditId);

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = request.AuditId,
            RequestReason = request.RequestReason
        };

        var correctionRequestId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetCorrectionRequest), new { id = correctionRequestId }, correctionRequestId, "Correction request created successfully");
    }

    /// <summary>
    /// Get correction requests for the current user
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page (max 50)</param>
    /// <param name="status">Filter by correction request status</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of correction requests</returns>
    [HttpGet("my-requests")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<CorrectionRequestSummaryDto>), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<PaginatedResult<CorrectionRequestSummaryDto>>> GetMyCorrectionRequests(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? status = null,
        [FromQuery] string sortBy = "RequestedAt",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting correction requests for current user - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // Parse status enum if provided
        CorrectionRequestStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<CorrectionRequestStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var query = new GetMyCorrectionRequestsQuery
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            Status = statusEnum,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific correction request by ID
    /// </summary>
    /// <param name="id">Correction request ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Correction request details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(CorrectionRequestDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CorrectionRequestDto>> GetCorrectionRequest(
        string id,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting correction request: {CorrectionRequestId}", id);

        // TODO: Implement GetCorrectionRequestQuery and handler
        // For now, return a placeholder response
        throw new NotImplementedException("GetCorrectionRequest endpoint not yet implemented");
    }
}
