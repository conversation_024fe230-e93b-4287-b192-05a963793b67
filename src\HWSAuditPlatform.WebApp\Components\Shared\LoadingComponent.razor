@if (IsLoading)
{
    <div class="loading-container @(FullScreen ? "loading-fullscreen" : "")">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            @if (!string.IsNullOrEmpty(Message))
            {
                <p class="loading-message">@Message</p>
            }
        </div>
    </div>
}

<style>
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem;
    }

    .loading-fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 9999;
    }

    .loading-content {
        text-align: center;
    }

    .loading-message {
        margin-top: 1rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
    }
</style>

@code {
    [Parameter] public bool IsLoading { get; set; }
    [Parameter] public string? Message { get; set; }
    [Parameter] public bool FullScreen { get; set; } = false;
}
