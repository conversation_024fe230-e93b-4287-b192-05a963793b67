using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Service for publishing domain events
/// </summary>
public interface IDomainEventService
{
    /// <summary>
    /// Publishes a domain event
    /// </summary>
    /// <param name="domainEvent">The domain event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publishes multiple domain events
    /// </summary>
    /// <param name="domainEvents">The domain events to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default);
}
