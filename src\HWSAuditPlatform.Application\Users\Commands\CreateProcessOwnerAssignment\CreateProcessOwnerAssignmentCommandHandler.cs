using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.CreateProcessOwnerAssignment;

/// <summary>
/// Handler for CreateProcessOwnerAssignmentCommand
/// </summary>
public class CreateProcessOwnerAssignmentCommandHandler : BaseCommandHandler<CreateProcessOwnerAssignmentCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateProcessOwnerAssignmentCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateProcessOwnerAssignmentCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Verify current user has permission (SystemManager or DevAdmin)
        var currentUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == currentUserId, cancellationToken);

        if (currentUser?.Role.RoleName != UserRole.SystemManager && currentUser?.Role.RoleName != UserRole.DevAdmin)
        {
            throw new UnauthorizedAccessException("Only SystemManager or DevAdmin can create process owner assignments");
        }

        // Verify the target user exists and is a ProcessOwner
        var processOwnerUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == request.ProcessOwnerUserId, cancellationToken);

        if (processOwnerUser == null)
        {
            throw new InvalidOperationException($"User with ID {request.ProcessOwnerUserId} not found");
        }

        if (processOwnerUser.Role.RoleName != UserRole.ProcessOwner)
        {
            throw new InvalidOperationException($"User {request.ProcessOwnerUserId} is not a ProcessOwner");
        }

        // Verify organizational hierarchy if specified
        if (request.AreaId.HasValue && !request.FactoryId.HasValue)
        {
            throw new InvalidOperationException("FactoryId is required when AreaId is specified");
        }

        if (request.SubAreaId.HasValue && !request.AreaId.HasValue)
        {
            throw new InvalidOperationException("AreaId is required when SubAreaId is specified");
        }

        // Check if assignment already exists
        var existingAssignment = await _context.ProcessOwnerAssignments
            .FirstOrDefaultAsync(poa => poa.ProcessOwnerUserId == request.ProcessOwnerUserId
                                     && poa.FactoryId == request.FactoryId
                                     && poa.AreaId == request.AreaId
                                     && poa.SubAreaId == request.SubAreaId, cancellationToken);

        if (existingAssignment != null)
        {
            if (existingAssignment.IsActive)
            {
                throw new InvalidOperationException("Process owner assignment already exists for this scope");
            }

            // Reactivate existing assignment
            existingAssignment.IsActive = true;
            existingAssignment.Description = request.Description;
            existingAssignment.UpdatedByUserId = currentUserId;
            existingAssignment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);
            return existingAssignment.Id;
        }

        // Create new assignment
        var assignment = ProcessOwnerAssignment.Create(
            request.ProcessOwnerUserId,
            currentUserId,
            request.FactoryId,
            request.AreaId,
            request.SubAreaId,
            request.Description);

        await _context.ProcessOwnerAssignments.AddAsync(assignment, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return assignment.Id;
    }
}
