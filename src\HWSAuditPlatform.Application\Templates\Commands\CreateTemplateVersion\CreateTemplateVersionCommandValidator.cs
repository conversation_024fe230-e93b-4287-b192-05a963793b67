using FluentValidation;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateTemplateVersion;

/// <summary>
/// Validator for CreateTemplateVersionCommand
/// </summary>
public class CreateTemplateVersionCommandValidator : AbstractValidator<CreateTemplateVersionCommand>
{
    public CreateTemplateVersionCommandValidator()
    {
        RuleFor(x => x.OriginalTemplateId)
            .GreaterThan(0)
            .WithMessage("Original template ID must be greater than 0");

        RuleFor(x => x.NewTemplateName)
            .MaximumLength(255)
            .WithMessage("Template name cannot exceed 255 characters")
            .When(x => !string.IsNullOrEmpty(x.NewTemplateName));

        RuleFor(x => x.NewDescription)
            .MaximumLength(2000)
            .WithMessage("Description cannot exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.NewDescription));
    }
}
