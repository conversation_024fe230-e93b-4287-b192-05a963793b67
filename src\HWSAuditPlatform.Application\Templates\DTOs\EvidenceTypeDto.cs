using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.DTOs;

/// <summary>
/// Data Transfer Object for evidence type information
/// </summary>
public class EvidenceTypeDto
{
    /// <summary>
    /// The evidence type enum value
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Display order for UI presentation
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Indicates if this evidence type is active for the question
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request DTO for adding evidence types to questions
/// </summary>
public class AddEvidenceTypeRequest
{
    /// <summary>
    /// The evidence type to add
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Display order for UI presentation
    /// </summary>
    public int DisplayOrder { get; set; } = 0;
}
