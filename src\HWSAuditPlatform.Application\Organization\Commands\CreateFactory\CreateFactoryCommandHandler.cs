using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.ValueObjects;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateFactory;

/// <summary>
/// Handler for CreateFactoryCommand
/// </summary>
public class CreateFactoryCommandHandler : BaseCommandHandler<CreateFactoryCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateFactoryCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateFactoryCommand request, CancellationToken cancellationToken)
    {
        // Create address value object if address details are provided
        Address? address = null;
        if (!string.IsNullOrEmpty(request.AddressLine1) || !string.IsNullOrEmpty(request.City) || !string.IsNullOrEmpty(request.PostalCode))
        {
            address = new Address(request.AddressLine1, request.City, request.PostalCode);
        }

        // Create the factory entity
        var factory = new Factory
        {
            FactoryName = request.FactoryName,
            FactoryProcess = request.FactoryProcess,
            LocationId = request.LocationId,
            Address = address,
            IsActive = request.IsActive,
            CreatedByUserId = _currentUserService.UserId
        };

        // Add to context
        await _context.Factories.AddAsync(factory, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return factory.Id;
    }
}
