using AutoMapper;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Application.Mappings;

/// <summary>
/// AutoMapper profile for User-related mappings
/// </summary>
public class UserMappingProfile : Profile
{
    public UserMappingProfile()
    {
        // User mappings
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.Role.RoleName))
            .ForMember(dest => dest.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.FactoryName : null));

        CreateMap<User, UserSummaryDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => $"{src.FirstName} {src.LastName}".Trim()))
            .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.Role.RoleName))
            .ForMember(dest => dest.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.FactoryName : null));

        // Role mappings
        CreateMap<Role, RoleDto>();

        // UserGroup mappings
        CreateMap<UserGroup, UserGroupDto>()
            .ForMember(dest => dest.CreatedByUserName, opt => opt.MapFrom(src => src.CreatedByUser != null ? $"{src.CreatedByUser.FirstName} {src.CreatedByUser.LastName}".Trim() : null))
            .ForMember(dest => dest.Members, opt => opt.MapFrom(src => src.UserGroupMembers.Select(ugm => ugm.User)));

        CreateMap<UserGroup, UserGroupSummaryDto>()
            .ForMember(dest => dest.MemberCount, opt => opt.MapFrom(src => src.UserGroupMembers.Count));

        // AdGroupRoleMapping mappings
        CreateMap<AdGroupRoleMapping, AdGroupRoleMappingDto>()
            .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role.RoleName));
    }
}
