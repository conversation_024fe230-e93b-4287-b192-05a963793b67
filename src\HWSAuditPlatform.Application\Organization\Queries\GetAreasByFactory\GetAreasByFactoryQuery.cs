using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Organization.Queries.GetAreasByFactory;

/// <summary>
/// Query to get areas for a specific factory
/// </summary>
public class GetAreasByFactoryQuery : BaseQuery<List<AreaSummaryDto>>
{
    public int FactoryId { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
}
