// Tablet Gesture Controls for HWS Audit Platform PWA
// Optimized for tablet usage with comprehensive touch gesture support

class TabletGestureManager {
    constructor() {
        this.isEnabled = this.isTabletDevice();
        this.swipeThreshold = 50;
        this.swipeTimeout = 300;
        this.pullToRefreshThreshold = 80;
        this.longPressTimeout = 500;
        
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchStartTime = 0;
        this.isScrolling = false;
        this.isPullToRefresh = false;
        this.longPressTimer = null;
        
        if (this.isEnabled) {
            this.init();
        }
    }

    isTabletDevice() {
        // Detect tablet devices
        const userAgent = navigator.userAgent.toLowerCase();
        const isTablet = /tablet|ipad|playbook|silk|(android(?!.*mobile))/i.test(userAgent);
        const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        const screenSize = window.innerWidth >= 768 && window.innerWidth <= 1024;
        
        return isTablet || (hasTouch && screenSize);
    }

    init() {
        console.log('Initializing tablet gesture controls...');
        
        // Initialize gesture handlers
        this.initSwipeGestures();
        this.initPullToRefresh();
        this.initPinchZoom();
        this.initLongPress();
        this.initDragDrop();
        
        // Add visual feedback for touch interactions
        this.addTouchFeedback();
        
        // Optimize scroll performance
        this.optimizeScrolling();
        
        console.log('Tablet gesture controls initialized successfully');
    }

    initSwipeGestures() {
        const auditContainer = document.querySelector('.audit-execution-container');
        if (!auditContainer) return;

        let startX = 0;
        let startY = 0;
        let startTime = 0;

        auditContainer.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                startTime = Date.now();
                this.isScrolling = false;
            }
        }, { passive: true });

        auditContainer.addEventListener('touchmove', (e) => {
            if (e.touches.length === 1 && !this.isScrolling) {
                const deltaX = Math.abs(e.touches[0].clientX - startX);
                const deltaY = Math.abs(e.touches[0].clientY - startY);
                
                // Determine if this is a horizontal swipe
                if (deltaX > deltaY && deltaX > 10) {
                    this.isScrolling = false;
                } else if (deltaY > deltaX && deltaY > 10) {
                    this.isScrolling = true;
                }
            }
        }, { passive: true });

        auditContainer.addEventListener('touchend', (e) => {
            if (e.changedTouches.length === 1 && !this.isScrolling) {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const endTime = Date.now();
                
                const deltaX = endX - startX;
                const deltaY = Math.abs(endY - startY);
                const deltaTime = endTime - startTime;
                
                // Check for horizontal swipe
                if (Math.abs(deltaX) > this.swipeThreshold && 
                    deltaY < this.swipeThreshold && 
                    deltaTime < this.swipeTimeout) {
                    
                    if (deltaX > 0) {
                        this.handleSwipeRight();
                    } else {
                        this.handleSwipeLeft();
                    }
                }
            }
        }, { passive: true });
    }

    handleSwipeLeft() {
        // Navigate to next question
        const nextButton = document.querySelector('[data-action="next-question"]');
        if (nextButton && !nextButton.disabled) {
            this.addSwipeAnimation('left');
            nextButton.click();
        }
    }

    handleSwipeRight() {
        // Navigate to previous question
        const prevButton = document.querySelector('[data-action="prev-question"]');
        if (prevButton && !prevButton.disabled) {
            this.addSwipeAnimation('right');
            prevButton.click();
        }
    }

    addSwipeAnimation(direction) {
        const questionCard = document.querySelector('.question-card');
        if (questionCard) {
            questionCard.style.transform = direction === 'left' ? 'translateX(-20px)' : 'translateX(20px)';
            questionCard.style.opacity = '0.8';
            
            setTimeout(() => {
                questionCard.style.transform = 'translateX(0)';
                questionCard.style.opacity = '1';
            }, 150);
        }
    }

    initPullToRefresh() {
        const scrollContainers = document.querySelectorAll('.pwa-content, .question-content');
        
        scrollContainers.forEach(container => {
            let startY = 0;
            let pullDistance = 0;
            let isPulling = false;
            
            container.addEventListener('touchstart', (e) => {
                if (container.scrollTop === 0) {
                    startY = e.touches[0].clientY;
                    isPulling = true;
                }
            }, { passive: true });
            
            container.addEventListener('touchmove', (e) => {
                if (isPulling && container.scrollTop === 0) {
                    pullDistance = e.touches[0].clientY - startY;
                    
                    if (pullDistance > 0) {
                        e.preventDefault();
                        this.updatePullToRefreshIndicator(pullDistance);
                    }
                }
            });
            
            container.addEventListener('touchend', () => {
                if (isPulling && pullDistance > this.pullToRefreshThreshold) {
                    this.triggerRefresh();
                }
                
                isPulling = false;
                pullDistance = 0;
                this.hidePullToRefreshIndicator();
            }, { passive: true });
        });
    }

    updatePullToRefreshIndicator(distance) {
        let indicator = document.querySelector('.pull-to-refresh-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'pull-to-refresh-indicator';
            indicator.innerHTML = `
                <div class="pull-to-refresh-content">
                    <i class="fas fa-arrow-down"></i>
                    <span>Pull to refresh</span>
                </div>
            `;
            document.body.appendChild(indicator);
        }
        
        const progress = Math.min(distance / this.pullToRefreshThreshold, 1);
        indicator.style.transform = `translateY(${Math.min(distance * 0.5, 40)}px)`;
        indicator.style.opacity = progress;
        
        if (progress >= 1) {
            indicator.querySelector('span').textContent = 'Release to refresh';
            indicator.querySelector('i').style.transform = 'rotate(180deg)';
        }
    }

    hidePullToRefreshIndicator() {
        const indicator = document.querySelector('.pull-to-refresh-indicator');
        if (indicator) {
            indicator.style.transform = 'translateY(-100px)';
            indicator.style.opacity = '0';
            
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }
    }

    triggerRefresh() {
        console.log('Pull to refresh triggered');
        
        // Show refresh animation
        this.showRefreshAnimation();
        
        // Trigger page refresh or data reload
        if (window.location.pathname.includes('/audits')) {
            // Reload audit data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    }

    showRefreshAnimation() {
        const indicator = document.querySelector('.pull-to-refresh-indicator');
        if (indicator) {
            indicator.innerHTML = `
                <div class="pull-to-refresh-content">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span>Refreshing...</span>
                </div>
            `;
        }
    }

    initPinchZoom() {
        // Enable pinch-to-zoom for images and attachments
        const images = document.querySelectorAll('img, .attachment-preview');
        
        images.forEach(element => {
            element.style.touchAction = 'pinch-zoom';
            
            let scale = 1;
            let initialDistance = 0;
            
            element.addEventListener('touchstart', (e) => {
                if (e.touches.length === 2) {
                    initialDistance = this.getDistance(e.touches[0], e.touches[1]);
                }
            }, { passive: true });
            
            element.addEventListener('touchmove', (e) => {
                if (e.touches.length === 2) {
                    e.preventDefault();
                    
                    const currentDistance = this.getDistance(e.touches[0], e.touches[1]);
                    const scaleChange = currentDistance / initialDistance;
                    scale = Math.min(Math.max(0.5, scale * scaleChange), 3);
                    
                    element.style.transform = `scale(${scale})`;
                    initialDistance = currentDistance;
                }
            });
            
            element.addEventListener('touchend', (e) => {
                if (e.touches.length < 2) {
                    // Reset zoom on double tap
                    if (scale !== 1) {
                        element.style.transition = 'transform 0.3s ease';
                        element.style.transform = 'scale(1)';
                        scale = 1;
                        
                        setTimeout(() => {
                            element.style.transition = '';
                        }, 300);
                    }
                }
            }, { passive: true });
        });
    }

    getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    initLongPress() {
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                const target = e.target.closest('.enhanced-option-label, .question-card, .list-group-item');
                
                if (target) {
                    this.longPressTimer = setTimeout(() => {
                        this.handleLongPress(target, e.touches[0]);
                    }, this.longPressTimeout);
                }
            }
        }, { passive: true });
        
        document.addEventListener('touchend', () => {
            if (this.longPressTimer) {
                clearTimeout(this.longPressTimer);
                this.longPressTimer = null;
            }
        }, { passive: true });
        
        document.addEventListener('touchmove', () => {
            if (this.longPressTimer) {
                clearTimeout(this.longPressTimer);
                this.longPressTimer = null;
            }
        }, { passive: true });
    }

    handleLongPress(element, touch) {
        // Add haptic feedback if available
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
        
        // Show context menu
        this.showContextMenu(element, touch.clientX, touch.clientY);
    }

    showContextMenu(element, x, y) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.tablet-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        
        const menu = document.createElement('div');
        menu.className = 'tablet-context-menu';
        menu.style.left = `${x}px`;
        menu.style.top = `${y}px`;
        
        // Add context menu items based on element type
        const menuItems = this.getContextMenuItems(element);
        menu.innerHTML = menuItems.map(item => 
            `<div class="context-menu-item" data-action="${item.action}">
                <i class="${item.icon}"></i>
                <span>${item.label}</span>
            </div>`
        ).join('');
        
        document.body.appendChild(menu);
        
        // Add click handlers
        menu.addEventListener('click', (e) => {
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            if (action) {
                this.handleContextMenuAction(action, element);
            }
            menu.remove();
        });
        
        // Remove menu on outside click
        setTimeout(() => {
            document.addEventListener('click', () => menu.remove(), { once: true });
        }, 100);
    }

    getContextMenuItems(element) {
        const items = [];
        
        if (element.classList.contains('question-card')) {
            items.push(
                { action: 'flag', icon: 'fas fa-flag', label: 'Flag Question' },
                { action: 'note', icon: 'fas fa-sticky-note', label: 'Add Note' }
            );
        }
        
        if (element.classList.contains('enhanced-option-label')) {
            items.push(
                { action: 'info', icon: 'fas fa-info-circle', label: 'More Info' }
            );
        }
        
        return items;
    }

    handleContextMenuAction(action, element) {
        console.log(`Context menu action: ${action}`, element);
        
        switch (action) {
            case 'flag':
                this.flagQuestion(element);
                break;
            case 'note':
                this.addNote(element);
                break;
            case 'info':
                this.showMoreInfo(element);
                break;
        }
    }

    flagQuestion(element) {
        // Add visual flag indicator
        element.classList.add('flagged');
        
        // Show toast notification
        this.showToast('Question flagged for review', 'success');
    }

    addNote(element) {
        // This would integrate with the existing note functionality
        console.log('Add note functionality would be implemented here');
    }

    showMoreInfo(element) {
        // Show additional information about the option
        console.log('Show more info functionality would be implemented here');
    }

    initDragDrop() {
        // Enable drag and drop for reorderable lists
        const draggableItems = document.querySelectorAll('[draggable="true"]');
        
        draggableItems.forEach(item => {
            item.addEventListener('touchstart', this.handleDragStart.bind(this), { passive: false });
            item.addEventListener('touchmove', this.handleDragMove.bind(this), { passive: false });
            item.addEventListener('touchend', this.handleDragEnd.bind(this), { passive: false });
        });
    }

    handleDragStart(e) {
        // Implement drag start logic
        console.log('Drag start');
    }

    handleDragMove(e) {
        // Implement drag move logic
        console.log('Drag move');
    }

    handleDragEnd(e) {
        // Implement drag end logic
        console.log('Drag end');
    }

    addTouchFeedback() {
        // Add CSS for touch feedback
        const style = document.createElement('style');
        style.textContent = `
            .pull-to-refresh-indicator {
                position: fixed;
                top: -100px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--industrial-teal);
                color: white;
                padding: 1rem 2rem;
                border-radius: 0 0 12px 12px;
                box-shadow: 0 4px 16px var(--industrial-shadow);
                z-index: 1000;
                transition: all 0.3s ease;
            }
            
            .pull-to-refresh-content {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 600;
            }
            
            .pull-to-refresh-content i {
                transition: transform 0.3s ease;
            }
            
            .tablet-context-menu {
                position: fixed;
                background: var(--industrial-gray);
                border: 2px solid var(--industrial-teal);
                border-radius: 12px;
                box-shadow: 0 8px 32px var(--industrial-shadow-heavy);
                z-index: 2000;
                min-width: 200px;
                overflow: hidden;
                animation: contextMenuAppear 0.2s ease-out;
            }
            
            @keyframes contextMenuAppear {
                from {
                    opacity: 0;
                    transform: scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }
            
            .context-menu-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 1rem 1.5rem;
                cursor: pointer;
                transition: background-color 0.2s ease;
                color: var(--industrial-text);
            }
            
            .context-menu-item:hover {
                background-color: var(--industrial-teal);
                color: white;
            }
            
            .context-menu-item i {
                width: 16px;
                text-align: center;
            }
            
            .flagged {
                position: relative;
            }
            
            .flagged::after {
                content: '🚩';
                position: absolute;
                top: 10px;
                right: 10px;
                font-size: 1.2rem;
            }
        `;
        document.head.appendChild(style);
    }

    optimizeScrolling() {
        // Add momentum scrolling optimization
        const scrollElements = document.querySelectorAll('.pwa-content, .question-content, .audit-execution-container');
        
        scrollElements.forEach(element => {
            element.style.webkitOverflowScrolling = 'touch';
            element.style.scrollBehavior = 'smooth';
        });
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `tablet-toast tablet-toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize tablet gesture manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.tabletGestureManager = new TabletGestureManager();
});

// Export for use in other modules
window.TabletGestureManager = TabletGestureManager;
