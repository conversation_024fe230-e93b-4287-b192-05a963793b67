using System.Text.Json;
using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Service for area responsibility API operations
/// </summary>
public class AreaResponsibilityApiService : IAreaResponsibilityApiService
{
    private readonly AuthenticatedHttpClientService _httpClientService;
    private readonly ILogger<AreaResponsibilityApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public AreaResponsibilityApiService(
        AuthenticatedHttpClientService httpClientService,
        ILogger<AreaResponsibilityApiService> logger)
    {
        _httpClientService = httpClientService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesAsync(AreaResponsibilityFilterModel? filter = null)
    {
        try
        {
            var queryParams = new List<string>();

            if (filter != null)
            {
                if (filter.AreaId.HasValue)
                    queryParams.Add($"areaId={filter.AreaId}");
                if (filter.AuditTemplateId.HasValue)
                    queryParams.Add($"auditTemplateId={filter.AuditTemplateId}");
                if (filter.ResponsibilityType.HasValue)
                    queryParams.Add($"responsibilityType={filter.ResponsibilityType}");
                if (!string.IsNullOrEmpty(filter.ResponsibleUserId))
                    queryParams.Add($"responsibleUserId={filter.ResponsibleUserId}");
                if (filter.IsActive.HasValue)
                    queryParams.Add($"isActive={filter.IsActive}");
                queryParams.Add($"includeTemplateSpecific={filter.IncludeTemplateSpecific}");
                queryParams.Add($"includeGeneral={filter.IncludeGeneral}");
            }

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var response = await _httpClientService.GetAsync($"api/v1/organization/area-responsibilities{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<AreaResponsibilityModel>>>(content, _jsonOptions);
                return apiResponse?.Data ?? new List<AreaResponsibilityModel>();
            }

            _logger.LogWarning("Failed to get area responsibilities. Status: {StatusCode}", response.StatusCode);
            return new List<AreaResponsibilityModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting area responsibilities");
            return new List<AreaResponsibilityModel>();
        }
    }

    public async Task<AreaResponsibilityModel?> GetAreaResponsibilityAsync(string id)
    {
        try
        {
            var response = await _httpClientService.GetAsync($"api/v1/organization/area-responsibilities/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AreaResponsibilityModel>>(content, _jsonOptions);
                return apiResponse?.Data;
            }

            _logger.LogWarning("Failed to get area responsibility {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting area responsibility {Id}", id);
            return null;
        }
    }

    public async Task<AreaResponsibilityModel> CreateAreaResponsibilityAsync(CreateAreaResponsibilityModel model)
    {
        try
        {
            var response = await _httpClientService.PostAsJsonAsync("api/v1/organization/area-responsibilities", model);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AreaResponsibilityModel>>(content, _jsonOptions);
                return apiResponse?.Data ?? throw new InvalidOperationException("Failed to create area responsibility");
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to create area responsibility. Status: {StatusCode}, Error: {Error}", 
                response.StatusCode, errorContent);
            throw new InvalidOperationException($"Failed to create area responsibility: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating area responsibility");
            throw;
        }
    }

    public async Task<AreaResponsibilityModel> UpdateAreaResponsibilityAsync(string id, CreateAreaResponsibilityModel model)
    {
        try
        {
            var response = await _httpClientService.PutAsJsonAsync($"api/v1/organization/area-responsibilities/{id}", model);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AreaResponsibilityModel>>(content, _jsonOptions);
                return apiResponse?.Data ?? throw new InvalidOperationException("Failed to update area responsibility");
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to update area responsibility {Id}. Status: {StatusCode}, Error: {Error}", 
                id, response.StatusCode, errorContent);
            throw new InvalidOperationException($"Failed to update area responsibility: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating area responsibility {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteAreaResponsibilityAsync(string id)
    {
        try
        {
            var response = await _httpClientService.DeleteAsync($"api/v1/organization/area-responsibilities/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting area responsibility {Id}", id);
            return false;
        }
    }

    public async Task<List<ResponsibilityTypeModel>> GetResponsibilityTypesAsync()
    {
        try
        {
            var response = await _httpClientService.GetAsync("api/v1/organization/area-responsibilities/responsibility-types");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<ResponsibilityTypeModel>>>(content, _jsonOptions);
                return apiResponse?.Data ?? new List<ResponsibilityTypeModel>();
            }

            _logger.LogWarning("Failed to get responsibility types. Status: {StatusCode}", response.StatusCode);
            return new List<ResponsibilityTypeModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting responsibility types");
            return new List<ResponsibilityTypeModel>();
        }
    }

    public async Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesForAreaAsync(int areaId, int? auditTemplateId = null)
    {
        var filter = new AreaResponsibilityFilterModel
        {
            AreaId = areaId,
            AuditTemplateId = auditTemplateId,
            IsActive = true
        };

        return await GetAreaResponsibilitiesAsync(filter);
    }

    public async Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesForUserAsync(string userId)
    {
        var filter = new AreaResponsibilityFilterModel
        {
            ResponsibleUserId = userId,
            IsActive = true
        };

        return await GetAreaResponsibilitiesAsync(filter);
    }
}
