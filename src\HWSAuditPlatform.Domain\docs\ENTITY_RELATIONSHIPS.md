# Entity Relationships Documentation

This document provides detailed information about the relationships between domain entities in the HWS Audit Platform.

## Relationship Diagram Overview

```mermaid
erDiagram
    %% Core Access & Users
    Role ||--o{ User : "has"
    User ||--o{ UserGroupMember : "belongs to"
    UserGroup ||--o{ UserGroupMember : "contains"
    Role ||--o{ AdGroupRoleMapping : "maps to"
    
    %% Organizational Structure
    Location ||--o{ Factory : "contains"
    Factory ||--o{ Area : "contains"
    Area ||--o{ SubArea : "contains"
    User }o--|| Factory : "primary factory"
    
    %% Audit Templates
    AuditTemplate ||--o{ QuestionGroup : "contains"
    AuditTemplate ||--o{ Question : "contains"
    QuestionGroup ||--o{ Question : "organizes"
    Question ||--o{ QuestionOption : "has options"
    Question ||--o{ Question : "parent-child"
    
    %% Audit Execution
    AuditTemplate ||--o{ Audit : "based on"
    Factory ||--o{ Audit : "conducted at"
    Area ||--o{ Audit : "conducted in"
    SubArea }o--o{ Audit : "conducted in"
    User }o--o{ Audit : "assigned to"
    UserGroup }o--o{ Audit : "assigned to"
    Audit ||--o{ AuditAnswer : "contains"
    Question ||--o{ AuditAnswer : "answered"
    AuditAnswer ||--o{ AuditAnswerSelectedOption : "multi-select"
    AuditAnswer ||--o{ AuditAnswerFailureReason : "failure reasons"
    AuditAnswer ||--o{ AuditAttachment : "evidence"
    
    %% Findings & Corrective Actions
    AuditAnswer ||--o{ Finding : "generates"
    Finding ||--o{ CorrectiveAction : "requires"
    User ||--o{ CorrectiveAction : "assigned to"

    %% Area-Based Responsibility (NEW)
    Area ||--o{ AreaResponsibility : "has responsibilities"
    User ||--o{ AreaResponsibility : "responsible for"
    AreaResponsibility ||--o{ Finding : "manages"

    %% Finding Categorization (NEW)
    AuditTemplate ||--o{ FindingCategory : "defines categories"
    FindingCategory ||--o{ Finding : "categorizes"
    
    %% Scheduling
    AuditTemplate ||--o{ RecurringAuditSetting : "uses"
    User }o--o{ RecurringAuditSetting : "assigned to"
    UserGroup }o--o{ RecurringAuditSetting : "assigned to"
    RecurringAuditSetting ||--|| RecurrenceRule : "defines pattern"
    RecurringAuditSetting ||--o{ Audit : "generates"
    
    %% Workflow
    Audit ||--o{ AuditCorrectionRequest : "requests correction"
    User ||--o{ AuditCorrectionRequest : "requests"
    User }o--o{ AuditCorrectionRequest : "reviews"
    User }o--o{ AuditLog : "performs action"
```

## Detailed Relationship Descriptions

### Core Access & Users Relationships

#### Role ↔ User (One-to-Many)
- **Relationship**: Each user has exactly one primary role
- **Foreign Key**: `User.RoleId` → `Role.RoleId`
- **Business Rule**: Users must have a valid role assignment
- **Cascade**: Restrict delete (cannot delete role with active users)

#### User ↔ UserGroup (Many-to-Many via UserGroupMember)
- **Relationship**: Users can belong to multiple groups, groups can contain multiple users
- **Junction Table**: `UserGroupMember`
- **Foreign Keys**: 
  - `UserGroupMember.UserId` → `User.UserId`
  - `UserGroupMember.UserGroupId` → `UserGroup.UserGroupId`
- **Business Rule**: Group membership enables audit assignment flexibility

#### Role ↔ AdGroupRoleMapping (One-to-Many)
- **Relationship**: Each AD group mapping references one application role
- **Foreign Key**: `AdGroupRoleMapping.RoleId` → `Role.RoleId`
- **Business Rule**: Enables automatic role assignment from AD group membership

### Organizational Structure Relationships

#### Location → Factory → Area → SubArea (Hierarchical)
- **Relationship**: Strict hierarchical containment
- **Foreign Keys**:
  - `Factory.LocationId` → `Location.LocationId`
  - `Area.FactoryId` → `Factory.FactoryId`
  - `SubArea.AreaId` → `Area.AreaId`
- **Business Rules**:
  - Factories must belong to a location
  - Areas must belong to a factory
  - SubAreas are optional but must belong to an area
  - Unique names within parent scope (e.g., area names unique within factory)

#### User ↔ Factory (Many-to-One, Optional)
- **Relationship**: Users can have a primary factory association
- **Foreign Key**: `User.FactoryId` → `Factory.FactoryId` (nullable)
- **Business Rule**: Provides default factory context for user operations

### Audit Template Relationships

#### AuditTemplate ↔ QuestionGroup (One-to-Many)
- **Relationship**: Templates can contain multiple question groups for organization
- **Foreign Key**: `QuestionGroup.AuditTemplateId` → `AuditTemplate.AuditTemplateId`
- **Business Rule**: Groups provide logical organization but are optional

#### AuditTemplate ↔ Question (One-to-Many)
- **Relationship**: Templates contain multiple questions
- **Foreign Key**: `Question.AuditTemplateId` → `AuditTemplate.AuditTemplateId`
- **Business Rule**: Questions define the audit content and logic

#### QuestionGroup ↔ Question (One-to-Many, Optional)
- **Relationship**: Questions can optionally belong to a group
- **Foreign Key**: `Question.QuestionGroupId` → `QuestionGroup.QuestionGroupId` (nullable)
- **Business Rule**: Grouping is for UI organization, not business logic

#### Question ↔ QuestionOption (One-to-Many)
- **Relationship**: Questions can have predefined answer options
- **Foreign Key**: `QuestionOption.QuestionId` → `Question.QuestionId`
- **Business Rule**: Only applicable for SingleSelect and MultiSelect question types

#### Question ↔ Question (Self-Referencing, Optional)
- **Relationship**: Questions can have parent-child relationships for conditional logic
- **Foreign Key**: `Question.ParentQuestionId` → `Question.QuestionId` (nullable)
- **Business Rule**: Enables conditional question display based on parent answers

### Audit Execution Relationships

#### AuditTemplate ↔ Audit (One-to-Many)
- **Relationship**: Audits are based on specific template versions
- **Foreign Key**: `Audit.AuditTemplateId` → `AuditTemplate.AuditTemplateId`
- **Business Rule**: Template version is locked when audit is created

#### Organizational Entities ↔ Audit
- **Relationships**: Audits are conducted at specific locations
- **Foreign Keys**:
  - `Audit.FactoryId` → `Factory.FactoryId` (required)
  - `Audit.AreaId` → `Area.AreaId` (required)
  - `Audit.SubAreaId` → `SubArea.SubAreaId` (optional)
- **Business Rule**: Defines the physical scope of the audit

#### User/UserGroup ↔ Audit (Assignment)
- **Relationships**: Audits can be assigned to users or groups
- **Foreign Keys**:
  - `Audit.AssignedToUserId` → `User.UserId` (nullable)
  - `Audit.AssignedToUserGroupId` → `UserGroup.UserGroupId` (nullable)
- **Business Rules**:
  - Assignment type determines which field is populated
  - Individual: `AssignedToUserId` populated
  - GroupAny: `AssignedToUserGroupId` populated, `AssignedToUserId` set when claimed
  - GroupAllScheduled: Creates multiple individual audits

#### Audit ↔ AuditAnswer (One-to-Many)
- **Relationship**: Audits contain answers to template questions
- **Foreign Key**: `AuditAnswer.AuditId` → `Audit.AuditId`
- **Business Rule**: One answer per question per audit

#### Question ↔ AuditAnswer (One-to-Many)
- **Relationship**: Answers reference the template question
- **Foreign Key**: `AuditAnswer.QuestionId` → `Question.QuestionId`
- **Business Rule**: Answer type must match question type

#### AuditAnswer ↔ Related Entities
- **AuditAnswerSelectedOption**: Multi-select answer choices
- **AuditAnswerFailureReason**: Multiple failure reasons for negative answers
- **AuditAttachment**: Evidence files attached to answers

### Findings & Corrective Actions Relationships

#### AuditAnswer ↔ Finding (One-to-Many)
- **Relationship**: Findings are generated from audit answers
- **Foreign Key**: `Finding.AuditAnswerId` → `AuditAnswer.AuditAnswerId`
- **Business Rule**: Findings represent non-conformities identified during audits

#### Finding ↔ CorrectiveAction (One-to-Many)
- **Relationship**: Findings can have multiple corrective actions
- **Foreign Key**: `CorrectiveAction.FindingId` → `Finding.FindingId`
- **Business Rule**: Actions address the root causes of findings

#### User ↔ CorrectiveAction (Many-to-One)
- **Relationship**: Corrective actions are assigned to specific users
- **Foreign Key**: `CorrectiveAction.AssignedToUserId` → `User.UserId`
- **Business Rule**: Clear accountability for action completion

### Scheduling Relationships

#### AuditTemplate ↔ RecurringAuditSetting (One-to-Many)
- **Relationship**: Recurring settings use specific templates
- **Foreign Key**: `RecurringAuditSetting.AuditTemplateId` → `AuditTemplate.AuditTemplateId`
- **Business Rule**: Template version is locked for consistency

#### User/UserGroup ↔ RecurringAuditSetting (Assignment)
- **Relationships**: Recurring settings define default assignments
- **Foreign Keys**:
  - `RecurringAuditSetting.AssignToUserId` → `User.UserId` (nullable)
  - `RecurringAuditSetting.AssignToUserGroupId` → `UserGroup.UserGroupId` (nullable)
- **Business Rule**: Assignment type determines which field is used

#### RecurringAuditSetting ↔ RecurrenceRule (One-to-One)
- **Relationship**: Each setting has exactly one recurrence pattern
- **Foreign Key**: `RecurrenceRule.RecurringAuditSettingId` → `RecurringAuditSetting.RecurringAuditSettingId`
- **Business Rule**: Defines the detailed scheduling pattern

#### RecurringAuditSetting ↔ Audit (One-to-Many)
- **Relationship**: Settings generate multiple audit instances
- **Foreign Key**: `Audit.RecurringAuditSettingId` → `RecurringAuditSetting.RecurringAuditSettingId` (nullable)
- **Business Rule**: Links generated audits back to their source setting

### Workflow Relationships

#### Audit ↔ AuditCorrectionRequest (One-to-Many)
- **Relationship**: Audits can have correction requests
- **Foreign Key**: `AuditCorrectionRequest.AuditId` → `Audit.AuditId`
- **Business Rule**: Only one active correction request per audit

#### User ↔ AuditCorrectionRequest (Multiple Roles)
- **Relationships**: Users can request and review corrections
- **Foreign Keys**:
  - `AuditCorrectionRequest.RequestedByUserId` → `User.UserId`
  - `AuditCorrectionRequest.ReviewedByUserId` → `User.UserId` (nullable)
- **Business Rule**: Separation of requester and reviewer roles

#### User ↔ AuditLog (Many-to-One, Optional)
- **Relationship**: Logs can be associated with user actions
- **Foreign Key**: `AuditLog.UserId` → `User.UserId` (nullable)
- **Business Rule**: System events may not have associated users

## Relationship Constraints and Business Rules

### Referential Integrity
- All foreign key relationships enforce referential integrity
- Cascade delete is carefully controlled to prevent data loss
- Soft deletes (IsActive flags) are used where appropriate

### Unique Constraints
- Template name + version combinations must be unique
- Factory + Area name combinations must be unique
- Area + SubArea name combinations must be unique
- AD group names must be unique in mappings

### Business Logic Constraints
- Audit status transitions must follow defined workflows
- Assignment types must match populated assignment fields
- Recurrence rules must be valid for their frequency types
- Evidence requirements must be satisfied for marked questions

### Concurrency Control
- RecordVersion fields enable optimistic concurrency control
- Critical for offline synchronization scenarios
- Prevents lost updates in multi-user environments
