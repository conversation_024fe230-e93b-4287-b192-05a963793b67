# HWSAuditPlatform Domain Tests

This directory contains comprehensive unit tests for the HWSAuditPlatform.Domain project, ensuring the reliability and correctness of the core business logic.

## Test Structure

The tests are organized to mirror the domain project structure:

```
Domain/
├── Common/                 # Tests for base classes and interfaces
│   ├── BaseEntityTests.cs
│   └── AuditableEntityTests.cs
├── Entities/              # Tests for domain entities
│   ├── Users/
│   │   └── UserTests.cs
│   ├── Audits/
│   │   ├── AuditTests.cs
│   │   └── AuditAnswerTests.cs
│   ├── Templates/
│   │   └── QuestionTests.cs
│   ├── Findings/
│   │   └── FindingTests.cs
│   └── Scheduling/
│       └── RecurrenceRuleTests.cs
├── ValueObjects/          # Tests for value objects
│   ├── AddressTests.cs
│   └── AuditScoreTests.cs
├── Events/                # Tests for domain events
│   └── AuditEventsTests.cs
├── Exceptions/            # Tests for domain exceptions
│   └── DomainExceptionTests.cs
├── DomainTestsRunner.cs   # Test discovery and configuration verification
└── README.md              # This file
```

## Testing Framework and Tools

### Core Testing Stack
- **xUnit**: Primary testing framework
- **FluentAssertions**: Fluent assertion library for readable tests
- **Moq**: Mocking framework (for future integration tests)

### Test Categories

#### **1. Base Classes Tests (`Common/`)**
Tests for foundational classes that provide common functionality:
- **BaseEntity**: Entity identity, equality, domain events
- **AuditableEntity**: Optimistic concurrency, audit tracking

#### **2. Entity Tests (`Entities/`)**
Tests for domain entities covering:
- **Property validation and business rules**
- **Calculated properties and derived values**
- **Entity relationships and navigation properties**
- **Business logic methods**
- **Aggregate root behavior**

Key entities tested:
- **User**: AD synchronization, full name calculation, active status
- **Audit**: Status workflow, assignment logic, overdue calculation
- **AuditAnswer**: Answer type validation, display value formatting
- **Question**: Conditional logic, evidence requirements, validation
- **Finding**: Status management, overdue tracking, corrective actions
- **RecurrenceRule**: Complex validation logic, description generation

#### **3. Value Objects Tests (`ValueObjects/`)**
Tests for immutable value objects:
- **Address**: Equality semantics, string formatting, empty state
- **AuditScore**: Validation, percentage calculation, business rules

#### **4. Domain Events Tests (`Events/`)**
Tests for domain events ensuring:
- **Proper property initialization**
- **Timestamp accuracy**
- **Interface implementation**
- **Event data integrity**

#### **5. Exception Tests (`Exceptions/`)**
Tests for domain-specific exceptions:
- **Exception hierarchy**
- **Message formatting**
- **Error collection handling**
- **Inner exception preservation**

## Test Patterns and Conventions

### **Naming Conventions**
- Test classes: `{EntityName}Tests.cs`
- Test methods: `{MethodName}_{Scenario}_{ExpectedResult}`
- Example: `IsOverdue_WithDueDateInPast_ShouldReturnTrue`

### **Test Structure (AAA Pattern)**
```csharp
[Fact]
public void Method_Scenario_ExpectedResult()
{
    // Arrange
    var entity = new Entity();
    var expectedValue = "expected";

    // Act
    var result = entity.Method();

    // Assert
    result.Should().Be(expectedValue);
}
```

### **FluentAssertions Usage**
```csharp
// Basic assertions
result.Should().Be(expected);
result.Should().NotBeNull();
result.Should().BeTrue();

// Collection assertions
collection.Should().HaveCount(3);
collection.Should().Contain(item);
collection.Should().BeEmpty();

// DateTime assertions
timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));

// Exception assertions
var act = () => entity.Method();
act.Should().Throw<ValidationException>()
   .WithMessage("Expected message");

// Type assertions
entity.Should().BeAssignableTo<IAggregateRoot>();
```

### **Test Data Patterns**
```csharp
// Use constants for test data
const string testUserId = "clh7ckb0x0000qh08w5t6h5zx";
const string testEmail = "<EMAIL>";

// Use realistic CUID values for entity IDs
// Use meaningful test data that reflects real scenarios
```

## Business Logic Coverage

### **Critical Business Rules Tested**
1. **User Management**
   - All users are AD-synced (no local users)
   - Full name calculation with null handling
   - Active status management

2. **Audit Workflow**
   - Status transition validation
   - Assignment type logic (Individual, GroupAny, GroupAllScheduled)
   - Overdue calculation based on due date and status
   - Location description formatting

3. **Question Logic**
   - Conditional question relationships
   - Evidence requirement validation
   - Answer type matching
   - Display value formatting for different question types

4. **Finding Management**
   - Status workflow enforcement
   - Overdue tracking
   - Corrective action counting
   - Severity level assignment

5. **Scheduling Rules**
   - Recurrence pattern validation
   - Frequency-specific parameter requirements
   - Human-readable description generation

### **Value Object Validation**
1. **Address**
   - Equality semantics
   - String representation
   - Empty state detection

2. **AuditScore**
   - Range validation (0 ≤ value ≤ maxValue)
   - Percentage calculation
   - Perfect/failing score detection

### **Domain Events**
1. **Event Creation**
   - Property initialization
   - Timestamp accuracy
   - Interface compliance

2. **Event Types**
   - Audit lifecycle events
   - Finding management events
   - Status change events

## Running Tests

### **Command Line**
```bash
# Run all domain tests
dotnet test --filter "FullyQualifiedName~HWSAuditPlatform.Tests.Domain"

# Run specific test class
dotnet test --filter "FullyQualifiedName~UserTests"

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### **Visual Studio**
- Use Test Explorer to run individual tests or test categories
- Right-click on test methods/classes to run specific tests
- Use Live Unit Testing for continuous test execution

### **Test Categories**
Tests can be filtered by namespace:
```bash
# Base classes
dotnet test --filter "FullyQualifiedName~Domain.Common"

# Entities
dotnet test --filter "FullyQualifiedName~Domain.Entities"

# Value objects
dotnet test --filter "FullyQualifiedName~Domain.ValueObjects"

# Events
dotnet test --filter "FullyQualifiedName~Domain.Events"

# Exceptions
dotnet test --filter "FullyQualifiedName~Domain.Exceptions"
```

## Test Coverage Goals

### **Target Coverage**
- **Entities**: 90%+ coverage of business logic
- **Value Objects**: 100% coverage (simple, critical logic)
- **Domain Events**: 100% coverage (data integrity)
- **Exceptions**: 100% coverage (error handling)

### **Coverage Exclusions**
- Auto-generated properties (getters/setters)
- Navigation properties
- Simple constructors without logic
- Framework-required methods (ToString, GetHashCode for entities)

## Best Practices

### **Test Independence**
- Each test should be independent and not rely on other tests
- Use fresh instances for each test
- Avoid shared state between tests

### **Readable Tests**
- Use descriptive test names that explain the scenario
- Keep tests focused on a single behavior
- Use FluentAssertions for readable assertions

### **Maintainable Tests**
- Follow the same patterns across all test classes
- Extract common test data to constants
- Keep tests simple and focused

### **Performance**
- Domain tests should run quickly (< 1ms per test)
- Avoid external dependencies in unit tests
- Use mocks only when necessary (prefer real objects for domain tests)

## Future Enhancements

### **Planned Additions**
1. **Property-based testing** for value objects
2. **Mutation testing** to verify test quality
3. **Performance benchmarks** for critical business logic
4. **Integration tests** for cross-aggregate scenarios

### **Test Data Builders**
Consider implementing test data builders for complex entities:
```csharp
public class AuditBuilder
{
    public static Audit CreateValid() => new Audit { /* valid defaults */ };
    public AuditBuilder WithStatus(AuditOverallStatus status) { /* ... */ }
    public AuditBuilder WithDueDate(DateTime dueDate) { /* ... */ }
}
```

This comprehensive test suite ensures the domain layer maintains its integrity and business rules are properly enforced throughout the application lifecycle.
