using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Domain.Entities.Scheduling;

namespace HWSAuditPlatform.Application.Scheduling.Commands.UpdateRecurringAuditSetting;

/// <summary>
/// Handler for UpdateRecurringAuditSettingCommand
/// </summary>
public class UpdateRecurringAuditSettingCommandHandler : BaseCommandHandler<UpdateRecurringAuditSettingCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IRecurrenceCalculationService _recurrenceCalculationService;

    public UpdateRecurringAuditSettingCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IRecurrenceCalculationService recurrenceCalculationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _recurrenceCalculationService = recurrenceCalculationService;
    }

    public override async Task Handle(UpdateRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        // Get the existing recurring audit setting with its recurrence rule
        var recurringAuditSetting = await _context.RecurringAuditSettings
            .Include(r => r.RecurrenceRule)
            .FirstOrDefaultAsync(r => r.Id == request.Id && r.IsEnabled, cancellationToken);

        if (recurringAuditSetting == null)
        {
            throw new NotFoundException(nameof(RecurringAuditSetting), request.Id);
        }

        // Check for optimistic concurrency
        if (recurringAuditSetting.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The recurring audit setting has been modified by another user. Please refresh and try again.");
        }

        // Verify the audit template exists and is published
        var auditTemplateExists = await _context.AuditTemplates
            .AnyAsync(t => t.Id == request.AuditTemplateId && t.IsPublished && t.IsActive, cancellationToken);

        if (!auditTemplateExists)
        {
            throw new NotFoundException("AuditTemplate", request.AuditTemplateId);
        }

        // Verify factory exists
        var factoryExists = await _context.Factories
            .AnyAsync(f => f.Id == request.FactoryId && f.IsActive, cancellationToken);

        if (!factoryExists)
        {
            throw new NotFoundException("Factory", request.FactoryId);
        }

        // Verify area exists if specified
        if (request.AreaId.HasValue)
        {
            var areaExists = await _context.Areas
                .AnyAsync(a => a.Id == request.AreaId.Value && a.FactoryId == request.FactoryId && a.IsActive, 
                    cancellationToken);

            if (!areaExists)
            {
                throw new NotFoundException("Area", request.AreaId.Value);
            }
        }

        // Verify subarea exists if specified
        if (request.SubAreaId.HasValue)
        {
            var subAreaExists = await _context.SubAreas
                .AnyAsync(sa => sa.Id == request.SubAreaId.Value && 
                               (request.AreaId == null || sa.AreaId == request.AreaId.Value) && 
                               sa.IsActive, cancellationToken);

            if (!subAreaExists)
            {
                throw new NotFoundException("SubArea", request.SubAreaId.Value);
            }
        }

        // Verify assignment is valid
        await ValidateAssignmentAsync(request, cancellationToken);

        // Update the recurring audit setting
        recurringAuditSetting.SettingName = request.SettingName;
        recurringAuditSetting.AuditTemplateId = request.AuditTemplateId;
        recurringAuditSetting.DeadlineDays = request.DeadlineDays;
        recurringAuditSetting.IsEnabled = request.IsEnabled;
        recurringAuditSetting.AssignmentType = request.AssignmentType;
        recurringAuditSetting.AssignToUserId = request.AssignToUserId;
        recurringAuditSetting.AssignToUserGroupId = request.AssignToUserGroupId;
        recurringAuditSetting.FactoryId = request.FactoryId;
        recurringAuditSetting.AreaId = request.AreaId;
        recurringAuditSetting.SubAreaId = request.SubAreaId;
        recurringAuditSetting.UpdatedAt = DateTime.UtcNow;
        recurringAuditSetting.UpdatedByUserId = _currentUserService.UserId;

        // Update the recurrence rule
        if (recurringAuditSetting.RecurrenceRule != null)
        {
            var recurrenceRule = recurringAuditSetting.RecurrenceRule;
            recurrenceRule.FrequencyType = request.RecurrenceRule.FrequencyType;
            recurrenceRule.Interval = request.RecurrenceRule.Interval;
            recurrenceRule.StartDate = request.RecurrenceRule.StartDate;
            recurrenceRule.EndDate = request.RecurrenceRule.EndDate;
            recurrenceRule.WeeklyDaysOfWeekMask = request.RecurrenceRule.WeeklyDaysOfWeekMask;
            recurrenceRule.MonthlyDayOfMonth = request.RecurrenceRule.MonthlyDayOfMonth;
            recurrenceRule.MonthlyNthWeek = request.RecurrenceRule.MonthlyNthWeek;
            recurrenceRule.MonthlyDayOfWeek = request.RecurrenceRule.MonthlyDayOfWeek;
            recurrenceRule.YearlyMonth = request.RecurrenceRule.YearlyMonth;
            recurrenceRule.YearlyDayOfMonth = request.RecurrenceRule.YearlyDayOfMonth;
            recurrenceRule.YearlyNthWeek = request.RecurrenceRule.YearlyNthWeek;
            recurrenceRule.YearlyDayOfWeek = request.RecurrenceRule.YearlyDayOfWeek;
            recurrenceRule.UpdatedAt = DateTime.UtcNow;

            // Recalculate the next generation date if the recurrence rule changed
            recurringAuditSetting.NextGenerationDate = _recurrenceCalculationService.CalculateNextGenerationDate(recurrenceRule);
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    private async Task ValidateAssignmentAsync(UpdateRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        switch (request.AssignmentType)
        {
            case Domain.Enums.AssignmentType.Individual:
                if (string.IsNullOrEmpty(request.AssignToUserId))
                {
                    throw new ValidationException("AssignToUserId", "User ID is required for Individual assignment type");
                }

                var userExists = await _context.Users
                    .AnyAsync(u => u.AdObjectGuid == request.AssignToUserId && u.IsActive, cancellationToken);

                if (!userExists)
                {
                    throw new NotFoundException("User", request.AssignToUserId);
                }
                break;

            case Domain.Enums.AssignmentType.GroupAny:
            case Domain.Enums.AssignmentType.GroupAllScheduled:
                if (string.IsNullOrEmpty(request.AssignToUserGroupId))
                {
                    throw new ValidationException("AssignToUserGroupId", "User Group ID is required for Group assignment types");
                }

                var groupExists = await _context.UserGroups
                    .AnyAsync(g => g.Id == request.AssignToUserGroupId, cancellationToken);

                if (!groupExists)
                {
                    throw new NotFoundException("UserGroup", request.AssignToUserGroupId);
                }
                break;
        }
    }


}
