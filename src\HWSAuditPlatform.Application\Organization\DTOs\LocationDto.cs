using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Organization.DTOs;

/// <summary>
/// Data Transfer Object for Location entity
/// </summary>
public class LocationDto : BaseDto<int>
{
    public string LocationName { get; set; } = string.Empty;
    public string LocationCountry { get; set; } = string.Empty;
    public string? LocationCountryCode { get; set; }
    public string OwnerGroupId { get; set; } = string.Empty;
    
    public List<FactorySummaryDto> Factories { get; set; } = new();
    public int FactoryCount => Factories.Count;
}

/// <summary>
/// Simplified Location DTO for lists and lookups
/// </summary>
public class LocationSummaryDto
{
    public int Id { get; set; }
    public string LocationName { get; set; } = string.Empty;
    public string LocationCountry { get; set; } = string.Empty;
    public string? LocationCountryCode { get; set; }
    public int FactoryCount { get; set; }
}
