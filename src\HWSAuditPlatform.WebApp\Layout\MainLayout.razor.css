.webapp-container {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #1a1a1a;
}

.page {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: #1a1a1a;
    flex: 1;
}

.webapp-content {
    flex: 1;
    background-color: #1a1a1a;
    padding: 0;
}

.webapp-footer {
    background: linear-gradient(135deg, #4a5568 0%, #404040 100%);
    border-top: 2px solid #ed8936;
    padding: 1rem 0;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.3);
    position: relative;
}

.webapp-footer::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        #ed8936 20%,
        #ed8936 80%,
        transparent 100%);
    box-shadow: 0 0 10px #ed8936;
}

.webapp-footer .text-muted {
    color: #a0a0a0 !important;
}

.webapp-footer .btn {
    background: linear-gradient(135deg, #ed8936 0%, #d69e2e 100%);
    border: 1px solid #ed8936;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.webapp-footer .btn:hover {
    background: linear-gradient(135deg, #d69e2e 0%, #ed8936 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    color: white;
}

/* Sidebar styling for navigation */
.sidebar {
    background: linear-gradient(180deg,
        #0f0f0f 0%,
        #2d2d2d 30%,
        #4a5568 70%,
        #1a1a1a 100%);
    position: relative;
    box-shadow: 2px 0 10px rgba(0,0,0,0.5);
    width: 250px;
    height: 100vh;
    position: sticky;
    top: 0;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 2px,
            rgba(255,255,255,0.03) 2px,
            rgba(255,255,255,0.03) 4px
        );
    pointer-events: none;
}

/* Main content area */
@media (min-width: 641px) {
    .page {
        flex-direction: row;
    }

    .webapp-content {
        flex: 1;
        padding-left: 2rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 640.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .webapp-content {
        padding: 1rem;
    }
}

/* Error UI styling */
#blazor-error-ui {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    bottom: 0;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.6);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    border-top: 3px solid #ed8936;
    color: white;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
        transition: color 0.3s ease;
    }

    #blazor-error-ui .dismiss:hover {
        color: #ed8936;
    }

/* Container styling */
.container-fluid {
    background-color: #1a1a1a;
    color: #e0e0e0;
    //min-height: calc(100vh - 120px);
    padding: 2rem;
}

/* General text styling */
h1, h2, h3, h4, h5, h6 {
    color: #e0e0e0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Button styling */
.btn-primary {
    background: linear-gradient(135deg, #ed8936 0%, #d69e2e 100%);
    border: 1px solid #ed8936;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #d69e2e 0%, #ed8936 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    border-color: #ed8936;
    color: white;
}

.btn-outline-secondary {
    border-color: #404040;
    color: #e0e0e0;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: #ed8936;
    border-color: #ed8936;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}
