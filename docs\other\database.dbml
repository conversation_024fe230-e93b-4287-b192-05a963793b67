//// Database: AuditFlowDB
//// Project: Internal Audit Management System
//// Version: 1.7
//// Last Updated: 2025-06-26

// Note on CUIDs: Represented as varchar(25) as DBML may not have a native CUID type.
// CUIDs are collision-resistant, k-sortable unique identifiers, excellent for client-side generation.
// Integer PKs are used for entities primarily managed server-side or whose identity is stable and less prone to client-side creation conflicts.

// ENUMS
Enum user_role {
  DevAdmin
  SystemManager
  ProcessOwner
  Auditor
  Viewer
}

Enum question_type {
  YesNo
  Numeric
  SingleSelect
  MultiSelect
  ShortText
  LongText
  Date
}

Enum audit_overall_status {
  Scheduled [note: 'Audit is planned but not yet started.']
  InProgress [note: 'Audit is actively being worked on by the assignee; can be paused and resumed.']
  Submitted [note: 'Auditor has completed and submitted the audit for review.']
  PendingManagerReview [note: 'Audit is awaiting review by a manager.']
  PendingCorrection [note: 'Audit has been reviewed and requires corrections by the auditor.']
  ManagerReviewed [note: 'Manager has completed their review; may lead to Closed or further actions.']
  Closed [note: 'Audit lifecycle is complete, all actions finalized.']
  Cancelled [note: 'Audit has been cancelled and will not be performed/completed.']
  // Consider: UnableToComplete [note: 'Assigned auditor cannot complete (e.g., unavailable); requires manager intervention/reassignment.']
}

Enum severity_level {
  Critical
  Major
  Minor
  Observation
}

Enum frequency_type {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  COSTUM
}

Enum correction_request_status {
  PendingApproval
  Approved
  Denied
  ChangesSubmitted [note: 'Auditor has submitted the requested changes.']
}

Enum assignment_type { // For Audits and RecurringAuditSettings
  Individual [note: 'Assigned to a specific user.']
  GroupAny [note: 'Assigned to a group; any one member can claim and complete the audit.']
  GroupAllScheduled [note: 'Scheduler creates individual audits for each member of the specified group.']
}

Enum finding_status {
  Open
  UnderInvestigation
  PendingCorrectiveAction
  PendingVerification
  Closed
  Rejected
}

Enum corrective_action_status {
  Assigned
  InProgress
  CompletedPendingVerification
  VerifiedClosed
  Cancelled
  Ineffective [note: 'Action taken did not resolve the finding.']
}

Enum evidence_timing_hint {
  OnSitePreferred [note: 'Evidence (e.g., photos) should ideally be captured live during the audit.']
  LaterAllowed [note: 'Evidence (e.g., documents) can be uploaded after the on-site portion of the audit.']
  AnyTime [note: 'No specific timing preference for evidence submission.']
}

Enum evidence_type {
  Image [note: 'Image files (JPEG, PNG, GIF, BMP)']
  Document [note: 'Document files (PDF, Word, Excel, Text)']
  Video [note: 'Video files (MP4, AVI, MOV)']
  Audio [note: 'Audio files (MP3, WAV, AAC)']
  Spreadsheet [note: 'Spreadsheet files (Excel, CSV)']
  Any [note: 'Any file type (no restrictions)']
}

Enum responsibility_type {
  CorrectiveActionResponsible [note: 'Responsible for corrective actions in this area']
  RetrospectiveAnalyst [note: 'Responsible for retrospective analysis in this area']
}


//// Table Definitions

// Core User and Access Control Tables

// AD roles are transformed into auditor/manager/admin roles
Table Roles {
  RoleId integer [pk, increment, note: 'Unique identifier for the role.']
  RoleName user_role [not null, unique, note: 'Name of the user role (e.g., Admin, Manager, Auditor).']
  Description varchar(255) [note: 'Brief description of the role and its responsibilities.']
}

Table Users {
  UserId varchar(25) [pk, note: 'Unique identifier for the user (server-generated CUID).']
  Username varchar(256) [not null, unique, note: 'Login username for the user.']
  FirstName varchar(100)
  LastName varchar(100)
  Email varchar(256) [note: 'User_s email address, used for notifications (optional, non-unique).']
  RoleId integer [not null, ref: > Roles.RoleId, note: 'Foreign key linking to the user_s primary role. (AD roles are translated to auditor/admin/manager)']
  FactoryId integer [ref: > Factories.FactoryId, note: 'Optional: Primary factory association for the user.']
  IsActive boolean [not null, default: true, note: 'Indicates if the user account is active or disabled.']
  LastLoginDate timestamp [note: 'Timestamp of the user_s last successful login.']
  CreatedAt timestamp [not null, default: `now()`, note: 'Timestamp of when the user record was created.']
  UpdatedAt timestamp [not null, default: `now()`, note: 'Timestamp of the last update to the user record.']
  RecordVersion integer [not null, default: 1, note: 'Version number for optimistic concurrency control during PWA sync.']
  AdObjectGuid varchar(36) [not null, unique, note:'AD objectGUID for synced users (36 char), used as FK reference']
  AdDistinguishedName text [note:'Full AD distinguished name (optional, for debugging/tracing)']
  AdSyncLastDate datetime [note:'Last time this user was synced from AD']
}

Table UserGroups {
  UserGroupId varchar(25) [pk, note: 'CUID, unique identifier for the user group. Client-side generatable.']
  GroupName varchar(100) [not null, unique, note: 'Display name of the user group.']
  Description text [note: 'Optional description of the group_s purpose.']
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who created this group (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`, note: 'Timestamp of when the group was created.']
  UpdatedAt timestamp [not null, default: `now()`, note: 'Timestamp of the last update to the group.']
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
  AdObjectGuid varchar(36) [note:'AD objectGUID for synced groups (36 char), or NULL for local']
  IsAdSynced boolean [note: 'Indicates if the group is synced from AD']
}

Table UserGroupMembers {
  UserGroupId varchar(25) [not null, ref: > UserGroups.UserGroupId, note: 'Part of composite PK; links to UserGroups (CUID FK).']
  UserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'Part of composite PK; links to Users (36 char FK to AdObjectGuid).']
  // Consider adding: RoleInGroup user_role_in_group_enum (e.g., Member, Lead) if group-specific roles are needed.
  primary key (UserGroupId, UserId)
  Note: 'Join table mapping users to user groups (many-to-many).'
}

Table AdGroupRoleMapping{
  AdGroupRoleMappingId integer [PK, increment]
  AdGroupName varchar(256) [not null, unique, note: 'Name of the Active Directory group']
  RoleId integer [not null,ref: > Roles.RoleId, note: "Maps to the application's RoleId"]
  Description varchar(255) [note: "Description of this mapping"]
  IsActive boolean [not null, default: true, note: "Toggle for enabling/disabling mapping"]
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
}

Table ProcessOwnerAssignments {
  ProcessOwnerAssignmentId integer [pk, increment, note: 'Unique identifier for process owner assignment.']
  ProcessOwnerUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User assigned as process owner (36 char FK to AdObjectGuid).']
  FactoryId integer [ref: > Factories.FactoryId, note: 'Optional: Factory scope for the assignment.']
  AreaId integer [ref: > Areas.AreaId, note: 'Optional: Area scope for the assignment.']
  SubAreaId integer [ref: > SubAreas.SubAreaId, note: 'Optional: SubArea scope for the assignment.']
  AssignedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who made this assignment (36 char FK to AdObjectGuid).']
  IsActive boolean [not null, default: true, note: 'Indicates if the assignment is currently active.']
  Description varchar(500) [note: 'Optional description of the assignment scope.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created this assignment (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this assignment (36 char FK to AdObjectGuid).']

  indexes {
    (ProcessOwnerUserId, FactoryId, AreaId, SubAreaId) [unique, name: 'uidx_process_owner_scope']
  }
}

Table TemplateAccessAssignments {
  TemplateAccessAssignmentId integer [pk, increment, note: 'Unique identifier for template access assignment.']
  UserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User granted access (36 char FK to AdObjectGuid).']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'Template the user has access to.']
  AssignedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who granted access (36 char FK to AdObjectGuid).']
  IsActive boolean [not null, default: true, note: 'Indicates if the access is currently active.']
  Description varchar(500) [note: 'Optional description of the access grant.']
  ExpiresAt timestamp [note: 'Optional expiration date for the access.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created this assignment (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this assignment (36 char FK to AdObjectGuid).']

  indexes {
    (UserId, AuditTemplateId) [unique, name: 'uidx_user_template_access']
  }
}


// Organizational Structure Tables
Table Location{
  LocationId integer [pk, increment, note: 'Unique identifier for a geographical location.']
  LocationName varchar(100) [not null, unique, note: 'Name of the location (e.g., country, region).']
  LocationCountry varchar(100) [not null, note: 'Country where the location is situated.']
  LocationCountryCode varchar(10) [unique, note: 'Standard country code (e.g., ISO 3166-1 alpha-2).']
  OwnerGroupId varchar(100) [not null, note: 'Identifier for an external owning group or business unit, context-specific.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
}

Table Factories {
  FactoryId integer [pk, increment, note: 'Unique identifier for a factory/site.']
  FactoryName varchar(150) [not null, note: 'Name of the factory.']
  // TODO: Normalize FactoryProcess. This is a critical denormalization to address.
  FactoryProcess varchar(2000) [note:'Denormalized list of processes. Strongly consider normalizing to Processes and FactoryProcesses tables for querying and integrity.']
  LocationId integer [not null, ref: > Location.LocationId, note: 'Links to the geographical location of the factory.']
  AddressLine1 varchar(255)
  City varchar(100)
  PostalCode varchar(20)
  IsActive boolean [not null, default: true, note: 'Indicates if the factory is currently operational/relevant for audits.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who added this factory record (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this factory record (36 char FK to AdObjectGuid).']
}

Table Areas {
  AreaId integer [pk, increment, note: 'Unique identifier for an area within a factory.']
  AreaName varchar(150) [not null, note: 'Name of the area (e.g., Production Line A, Warehouse).']
  FactoryId integer [not null, ref: > Factories.FactoryId, note: 'Links to the parent factory.']
  Description varchar(500) [note: 'Optional description of the area.']
  IsActive boolean [not null, default: true, note: 'Indicates if the area is currently active/relevant.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who created this area (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this area (36 char FK to AdObjectGuid).']

  indexes {
    (FactoryId, AreaName) [unique, name: 'uidx_factory_area_name']
  }
}

Table SubAreas {
  SubAreaId integer [pk, increment, note: 'Unique identifier for a sub-area within an area.']
  SubAreaName varchar(150) [not null, note: 'Name of the sub-area (e.g., Welding Station 1).']
  AreaId integer [not null, ref: > Areas.AreaId, note: 'Links to the parent area.']
  Description varchar(500) [note: 'Optional description of the sub-area.']
  IsActive boolean [not null, default: true, note: 'Indicates if the sub-area is currently active/relevant.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who created this sub-area (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this sub-area (36 char FK to AdObjectGuid).']

  indexes {
    (AreaId, SubAreaName) [unique, name: 'uidx_area_subarea_name']
  }
}

Table AreaResponsibilities {
  AreaResponsibilityId varchar(25) [pk, note: 'CUID, unique identifier for area responsibility assignment.']
  AreaId integer [not null, ref: > Areas.AreaId, note: 'Area this responsibility applies to.']
  ResponsibilityType responsibility_type [not null, note: 'Type of responsibility (CorrectiveActionResponsible, RetrospectiveAnalyst).']
  ResponsibleUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User responsible for this area (36 char FK to AdObjectGuid).']
  AuditTemplateId integer [ref: > AuditTemplates.AuditTemplateId, note: 'Optional: Template-specific responsibility.']
  IsActive boolean [not null, default: true, note: 'Indicates if the responsibility is currently active.']
  Description varchar(500) [note: 'Optional description of the responsibility.']
  Priority integer [not null, default: 0, note: 'Priority level for this responsibility.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who created this responsibility (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this responsibility (36 char FK to AdObjectGuid).']

  indexes {
    (AreaId, ResponsibilityType, AuditTemplateId) [name: 'idx_area_responsibility_type_template']
  }
}

// Audit Template and Question Definition Tables
Table AuditTemplates {
  AuditTemplateId integer [pk, increment, note: 'Unique identifier for an audit template. Integer PK as templates are centrally managed.']
  TemplateName varchar(255) [not null, note: 'Name of the audit template.']
  Description text [note: 'Detailed description of the template_s purpose and scope.']
  Version integer [not null, default: 1, note: 'Design version of the template, not for concurrency control. Incremented for significant changes to questions/structure.']
  IsPublished boolean [not null, default: false, note: 'Indicates if this template version is finalized and ready for use in new audits.']
  IsActive boolean [not null, default: true, note: 'Indicates if this template version can be used (for soft deletion or retiring old versions).']
  EnableAreaBasedResponsibility boolean [not null, default: false, note: 'Enables area-based responsibility assignment for findings.']
  EnableFindingCategorization boolean [not null, default: false, note: 'Enables flexible finding categorization system.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created this template version (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this template version (36 char FK to AdObjectGuid).']
  UpdatedAt timestamp [not null, default: `now()`]

  indexes {
    (TemplateName, Version) [unique, name: 'uidx_template_name_version']
  }
}

Table QuestionGroups {
  QuestionGroupId integer [pk, increment, note: 'Unique identifier for a group of questions within a template.']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'Links to the audit template this group belongs to.']
  GroupName varchar(255) [not null, note: 'Name of the question group (e.g., "Safety Procedures", "Documentation").']
  DisplayOrder integer [not null, default: 0, note: 'Order in which this group appears within the template.']
  Description text [note: 'Optional description for the question group.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
}

Table Questions {
  QuestionId integer [pk, increment, note: 'Unique identifier for a question within a template version.']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'Links to the audit template this question belongs to.']
  QuestionGroupId integer [ref: > QuestionGroups.QuestionGroupId, note: 'Optional: Links to a question group for organization.']
  QuestionText text [not null, note: 'The actual text of the question. Candidate for multi-language support.']
  QuestionType question_type [not null, note: 'The type of answer expected (e.g., YesNo, Numeric).']
  DisplayOrder integer [not null, default: 0, note: 'Order in which this question appears within its group/template.']
  IsRequired boolean [not null, default: true, note: 'If true, the auditor must provide an answer or mark as Not Applicable.']
  Weight decimal(5,2) [note: 'Optional weight for this question if the audit uses weighted scoring.']
  HelpText text [note: 'Additional guidance or explanation for answering the question. Candidate for multi-language support.']
  ParentQuestionId integer [ref: > Questions.QuestionId, note: 'For creating conditional follow-up questions; links to the parent question.']
  TriggerAnswerValue varchar(255) [note: 'Specific answer value from ParentQuestionId that triggers this conditional question.']
  SeverityLevel severity_level [note: 'Default severity level to be applied if this question results in a failure or negative answer.']

  EvidenceRequired boolean [not null, default: false, note: 'Indicates if evidence must be provided for this question.']
  EvidenceInstructions text [note: 'Specific instructions for the required evidence. Candidate for multi-language support.']
  EvidenceTimingHint evidence_timing_hint [note: 'Guidance on when the evidence should ideally be collected.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
}

Table QuestionOptions {
  QuestionOptionId integer [pk, increment, note: 'Unique identifier for a predefined answer option.']
  QuestionId integer [not null, ref: > Questions.QuestionId, note: 'Links to the question these options belong to (for SingleSelect/MultiSelect types).']
  OptionText varchar(500) [not null, note: 'Display text for the answer option. Candidate for multi-language support.']
  OptionValue varchar(100) [note: 'Internal value of the option, can be used for scoring or as TriggerAnswerValue for conditional logic.']
  IsCorrectOption boolean [note: 'Indicates if this is the "correct" option, if applicable (e.g., for quiz-like audits).']
  DisplayOrder integer [not null, default: 0, note: 'Order in which this option appears for the question.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
}

Table QuestionAllowedEvidenceTypes {
  QuestionAllowedEvidenceTypeId integer [pk, increment, note: 'Unique identifier for the question-evidence type relationship.']
  QuestionId integer [not null, ref: > Questions.QuestionId, note: 'Links to the question.']
  EvidenceType evidence_type [not null, note: 'The allowed evidence type for this question.']
  DisplayOrder integer [not null, default: 0, note: 'Order in which this evidence type appears for the question.']
  IsActive boolean [not null, default: true, note: 'Indicates if this evidence type is currently active for the question.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]

  indexes {
    (QuestionId, EvidenceType) [unique, name: 'uidx_question_evidence_type']
  }
}

// Audit Instance, Execution, and Evidence Tables
Table Audits {
  AuditId varchar(25) [pk, note: 'CUID, unique identifier for this specific audit instance. Client-side generatable.']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'The template version used for this audit.']

  AssignmentType assignment_type [not null, default: 'Individual', note: 'Defines how the audit is assigned (Individual, GroupAny). GroupAllScheduled results in individual audits.']
  AssignedToUserGroupId varchar(25) [ref: > UserGroups.UserGroupId, note: 'Target group if AssignmentType is GroupAny (CUID FK). Null otherwise.']
  AssignedToUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User specifically assigned (Individual type) OR user who claimed a GroupAny audit (36 char FK to AdObjectGuid). Null initially for GroupAny.']

  ScheduledDate timestamp [not null, note: 'The date and time the audit is scheduled to be performed.']
  DueDate timestamp [note: 'The date and time by which the audit should be completed.']
  StartedAt timestamp [note: 'Timestamp when the auditor began working on this audit (first interaction).']
  CompletedAt timestamp [note: 'Timestamp when the auditor submitted the completed audit.']
  OverallStatus audit_overall_status [not null, note: 'Current stage of the audit in its lifecycle.']
  FactoryId integer [not null, ref: > Factories.FactoryId, note: 'The factory where the audit is conducted.']
  AreaId integer [not null, ref: > Areas.AreaId, note: 'The specific area within the factory being audited.']
  SubAreaId integer [ref: > SubAreas.SubAreaId, note: 'Optional: The specific sub-area being audited.']
  OverallScore decimal(7,2) [note: 'Calculated overall score for the audit, if applicable.']
  ManagerComments text [note: 'Comments provided by the manager during the review process.']
  ReviewedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'Manager who performed the final review or closed the audit (36 char FK to AdObjectGuid).']
  ReviewedAt timestamp [note: 'Timestamp of when the manager_s review was completed.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created/scheduled this audit instance (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecurringAuditSettingId varchar(25) [ref: > RecurringAuditSettings.RecurringAuditSettingId, note: 'Link to the recurring setting if this audit was generated automatically (CUID FK).']
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control, especially for PWA sync.']

  // Note on Assignment Logic:
  // - For 'Individual': AssignedToUserId is NOT NULL, AssignedToUserGroupId is NULL.
  // - For 'GroupAny': AssignedToUserGroupId is NOT NULL, AssignedToUserId is NULL until an auditor claims it.
  // - 'GroupAllScheduled' type in RecurringAuditSettings results in multiple 'Individual' audits being created by the scheduler.
}

Table AuditAnswers {
  AuditAnswerId varchar(25) [pk, note: 'CUID, unique identifier for this specific answer. Client-side generatable.']
  AuditId varchar(25) [not null, ref: > Audits.AuditId, note: 'Links to the audit this answer belongs to (CUID FK).']
  QuestionId integer [not null, ref: > Questions.QuestionId, note: 'Links to the question being answered.']
  AnswerBoolean boolean [note: 'For Yes/No question types.']
  AnswerText text [note: 'For ShortText, LongText question types. Numeric answers might also be stored here if not strictly numeric type.']
  AnswerNumeric decimal(18,4) [note: 'For Numeric question types.']
  AnswerDate timestamp [note: 'For Date question types.']
  SelectedOptionId integer [ note: 'For SingleSelect questions, links to the chosen QuestionOption.']
  IsNotApplicable boolean [not null, default: false, note: 'If true, the question is skipped; Comments field should provide justification.']
  Comments text [note: 'Auditor_s comments specific to this answer, or justification if NotApplicable.']
  AnsweredAt timestamp [not null, default: `now()`, note: 'Timestamp when the user provided/last modified this answer (client-side).']
  CreatedAt timestamp [not null, default: `now()`, note: 'Timestamp when this answer record was first created in the system.']
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

Table AuditAnswerSelectedOptions {
  AuditAnswerId varchar(25) [not null, ref: > AuditAnswers.AuditAnswerId, note: 'Part of composite PK; links to the audit answer (CUID FK).']
  QuestionOptionId integer [not null, ref: > QuestionOptions.QuestionOptionId, note: 'Part of composite PK; links to the selected question option.']
  indexes {
    (AuditAnswerId, QuestionOptionId) [pk, name: 'pk_auditanswer_selectedoptions']
  }
  Note: 'Join table for MultiSelect question types, linking an answer to multiple selected options.'
}

Table AuditAnswerFailureReasons {
  AuditAnswerFailureReasonId varchar(25) [pk, note: 'CUID, unique identifier for this failure reason entry. Client-side generatable.']
  AuditAnswerId varchar(25) [not null, ref: > AuditAnswers.AuditAnswerId, note: 'Links to the specific audit answer that failed (CUID FK).']
  ReasonText text [not null, note: 'Specific reason provided for the failed/negative answer. Candidate for multi-language support.']
  DisplayOrder integer [not null, default: 0, note: 'Order for displaying multiple reasons for a single answer.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

Table AuditAttachments {
  AuditAttachmentId varchar(25) [pk, note: 'CUID, unique identifier for this attachment. Client-side generatable.']
  AuditAnswerId varchar(25) [not null, ref: > AuditAnswers.AuditAnswerId, note: 'Links to the audit answer this attachment provides evidence for (CUID FK).']
  // Consider: Polymorphic link if attachments can also belong to Findings or CorrectiveActions directly.
  FileName varchar(255) [not null, note: 'Original name of the uploaded file.']
  FilePath text [not null, note: 'Relative path or identifier for the file in blob storage/file system.']
  ContentType varchar(100) [not null, note: 'MIME type of the file (e.g., image/jpeg, application/pdf).']
  FileSize long [note: 'Size of the file in bytes.']
  UploadedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who uploaded this attachment (36 char FK to AdObjectGuid).']
  UploadedAt timestamp [not null, default: `now()`, note: 'Timestamp when the user uploaded this attachment (client-side).']
  Description varchar(500) [note: 'Optional description of the attachment.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

// Findings and Corrective Actions
Table Findings {
  FindingId varchar(25) [pk, note: 'CUID, unique identifier for this finding/non-conformity. Client-side generatable.']
  AuditAnswerId varchar(25) [not null, ref: > AuditAnswers.AuditAnswerId, note: 'Links to the specific audit answer that primarily generated this finding (CUID FK).']
  FindingCode varchar(50) [unique, note: 'A human-readable unique identifier for the finding (e.g., FND-2025-001), potentially system-generated.']
  FindingDescription text [not null, note: 'Detailed description of the non-conformity. Candidate for multi-language support.']
  FindingSeverityLevel severity_level [not null, note: 'Severity level assigned to this finding.']
  RootCauseAnalysis text [note: 'Analysis of the root cause of the finding.']
  ImmediateActionTaken text [note: 'Description of any immediate actions taken upon discovering the finding.']
  Status finding_status [not null, default: 'Open', note: 'Current status of the finding in its lifecycle.']
  FindingCategoryId integer [ref: > FindingCategories.FindingCategoryId, note: 'Optional: Category for flexible finding classification.']
  ResponsibleUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'Optional: User responsible for corrective actions (36 char FK to AdObjectGuid).']
  RetrospectiveAnalystUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'Optional: User responsible for retrospective analysis (36 char FK to AdObjectGuid).']
  ReportedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who reported or created this finding (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this finding (36 char FK to AdObjectGuid).']
  UpdatedAt timestamp [not null, default: `now()`]
  DueDate date [note: 'Target date for resolving this finding and its corrective actions.']
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

Table FindingCategories {
  FindingCategoryId integer [pk, increment, note: 'Unique identifier for finding category.']
  CategoryName varchar(100) [not null, note: 'Name of the finding category.']
  Description varchar(500) [note: 'Optional description of the category.']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'Template this category belongs to.']
  DisplayOrder integer [not null, default: 0, note: 'Order for displaying categories.']
  IsActive boolean [not null, default: true, note: 'Indicates if the category is currently active.']
  ColorCode varchar(7) [note: 'Optional hex color code for UI display.']
  IconName varchar(50) [note: 'Optional icon name for UI display.']
  RequiresDocumentation boolean [not null, default: false, note: 'Indicates if findings in this category require additional documentation.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
  CreatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who created this category (36 char FK to AdObjectGuid).']
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this category (36 char FK to AdObjectGuid).']

  indexes {
    (AuditTemplateId, CategoryName) [unique, name: 'uidx_template_category_name']
    (AuditTemplateId, DisplayOrder) [name: 'idx_template_category_order']
  }
}

Table CorrectiveActions {
  CorrectiveActionId varchar(25) [pk, note: 'CUID, unique identifier for this corrective action. Client-side generatable.']
  FindingId varchar(25) [not null, ref: > Findings.FindingId, note: 'Links to the finding this action addresses (CUID FK).']
  ActionDescription text [not null, note: 'Description of the corrective or preventive action to be taken. Candidate for multi-language support.']
  AssignedToUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User responsible for implementing this action (36 char FK to AdObjectGuid).']
  DueDate date [not null, note: 'Target date for completing this corrective action.']
  CompletionDate date [note: 'Actual date when this corrective action was completed.']
  EvidenceNotes text [note: 'Notes regarding verification or evidence of completion for this action.']
  // Consider: A join table CorrectiveActionAttachments if actions need their own dedicated attachments.
  Status corrective_action_status [not null, default: 'Assigned', note: 'Current status of the corrective action.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created this corrective action entry (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this corrective action (36 char FK to AdObjectGuid).']
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

// Recurring Audit Scheduling Tables
Table RecurringAuditSettings {
  RecurringAuditSettingId varchar(25) [pk, note: 'CUID, unique identifier for this recurring audit schedule. Client-side generatable.']
  SettingName varchar(255) [not null, note: 'User-defined name for this recurring schedule.']
  AuditTemplateId integer [not null, ref: > AuditTemplates.AuditTemplateId, note: 'The audit template to be used for generated audits.']
  // RecurrenceRule details are in the RecurrenceRules extension table (1-to-1).
  DeadlineDays integer [not null, default: 7, note: 'Number of days from the generated ScheduledDate to the audit_s DueDate.']
  IsEnabled boolean [not null, default: true, note: 'If false, the scheduler will not generate audits for this setting.']

  AssignmentType assignment_type [not null, default: 'Individual', note: 'Determines how the scheduler generates/assigns audits from this setting.']
  AssignToUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'Default user if AssignmentType is Individual (36 char FK to AdObjectGuid).']
  AssignToUserGroupId varchar(25) [ref: > UserGroups.UserGroupId, note: 'Default group if AssignmentType is GroupAny or GroupAllScheduled (CUID FK).']

  NextGenerationDate date [note: 'Calculated date for when the NEXT audit instance(s) should be generated by the scheduler.']
  LastGeneratedAt timestamp [note: 'Timestamp of when the last audit instance(s) were actually generated by the scheduler for this setting.']
  CreatedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'User who created this recurring setting (36 char FK to AdObjectGuid).']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who last updated this recurring setting (36 char FK to AdObjectGuid).']
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

Table RecurrenceRules { // This is an extension of RecurringAuditSettings (1-to-1 relationship)
  RecurringAuditSettingId varchar(25) [pk, ref:  > RecurringAuditSettings.RecurringAuditSettingId, note: 'CUID, links to the parent RecurringAuditSetting (PK/FK for 1-to-1).']

  // Core Recurrence Parameters
  FrequencyType frequency_type [not null, note: 'The base frequency of recurrence (e.g., DAILY, WEEKLY, MONTHLY, YEARLY).']
  Interval integer [not null, default: 1, note: 'Frequency interval (e.g., for WEEKLY with Interval=2, it means every 2 weeks).']
  StartDate date [not null, note: 'The date when this recurrence rule becomes active.']
  EndDate date [note: 'Optional date when this recurrence rule stops generating audits.']

  // Conditional Parameters for WEEKLY frequency
  //Bitmask: 1=Sun, 2=Mon, 4=Tue, 8=Wed, 16=Thu, 32=Fri, 64=Sat. 
  // E.g., Monday and Wednesday = 2 | 8 = 10.
  WeeklyDaysOfWeekMask integer [note: 'Bitmask for specific days of the week (e.g., 1=Sun, 2=Mon, ... 64=Sat). Required for WEEKLY frequency.']

  //Conditional Parameters for MONTHLY frequency
  //If RecurrenceType = MONTHLY:
  //   Either (MonthlyDayOfMonth IS NOT NULL) OR (MonthlyNthWeek AND MonthlyDayOfWeek IS NOT NULL)
  MonthlyDayOfMonth integer [note: 'Specific day of the month (1-31). Used for "On day X of the month" monthly recurrence.']
  MonthlyNthWeek integer [note: 'Which occurrence of the weekday (1=first, 2=second, ... 4=fourth, 5=last). Used for "On the Nth [Weekday] of the month" monthly recurrence.']
  MonthlyDayOfWeek integer [note: 'Day of week (1=Sun, 2=Mon, ..., 7=Sat). Used with MonthlyNthWeek. Also supports 8=AnyDay, 9=Weekday, 10=WeekendDay as per UI.']


  //Conditional Parameters for YEARLY frequency
  // If RecurrenceType = YEARLY:
  //   Either (YearlyMonth IS NOT NULL AND YearlyDayOfMonth IS NOT NULL) 
  //   OR (YearlyMonth IS NOT NULL AND YearlyNthWeek AND YearlyDayOfWeek IS NOT NULL)
  YearlyMonth integer [note: 'Specific month of the year (1-12). Required for YEARLY frequency.']
  YearlyDayOfMonth integer [note: 'Specific day of the month (1-31). Used for "On [Month] X" yearly recurrence.']
  YearlyNthWeek integer [note: 'Which occurrence of the weekday (1=first, 2=second, ... 4=fourth, 5=last). Used for "On the Nth [Weekday] of [Month]" yearly recurrence.']
  YearlyDayOfWeek integer [note: 'Day of week (1=Sun, 2=Mon, ..., 7=Sat). Used with YearlyNthWeek. Also supports 8=AnyDay, 9=Weekday, 10=WeekendDay as per UI.']

  CreatedAt timestamp [not null, default: `now()`, note: 'Timestamp for rule record creation.']
  UpdatedAt timestamp [not null, default: `now()`, note: 'Timestamp for rule record update.']
  // Note: Changes to these rules effectively mean the RecurringAuditSetting is updated.
}


// Audit Correction Workflow and System Logging Tables
Table AuditCorrectionRequests {
  AuditCorrectionRequestId varchar(25) [pk, note: 'CUID, unique identifier for this correction request. Client-side generatable.']
  AuditId varchar(25) [not null, unique, ref: > Audits.AuditId, note: 'The audit being requested for correction (CUID FK). Unique constraint ensures only one open request per audit at a time.']
  RequestedByUserId varchar(36) [not null, ref: > Users.AdObjectGuid, note: 'Auditor who submitted the request for correction (36 char FK to AdObjectGuid).']
  RequestReason text [not null, note: 'Justification provided by the auditor for needing to correct the audit.']
  RequestedAt timestamp [not null, default: `now()`, note: 'Timestamp when the correction request was submitted.']
  Status correction_request_status [not null, note: 'Current status of the correction request.']
  ReviewedByUserId varchar(36) [ref: > Users.AdObjectGuid, note: 'Manager who reviewed the correction request (36 char FK to AdObjectGuid).']
  ReviewedAt timestamp [note: 'Timestamp when the manager_s review was completed.']
  ManagerComments text [note: 'Comments provided by the manager regarding the correction request.']
  CreatedAt timestamp [not null, default: `now()`]
  UpdatedAt timestamp [not null, default: `now()`]
  RecordVersion integer [not null, default: 1, note: 'Version for optimistic concurrency control.']
}

Table AuditLogs {
  AuditLogId long [pk, increment, note: 'Auto-incrementing ID, as logs are server-generated only and high-volume.']
  EventTimestamp timestamp [not null, note: 'Timestamp when the event actually occurred (client-side if available from PWA sync, otherwise server time of event).']
  ServerReceivedAt timestamp [not null, default: `now()`, note: 'Timestamp when the server processed and recorded this log entry.']
  UserId varchar(36) [ref: > Users.AdObjectGuid, note: 'User who performed the action (36 char FK to AdObjectGuid). NULL for system-generated events.']
  EntityType varchar(100) [not null, note: 'The type of entity affected (e.g., "Audit", "User", "Finding").']
  EntityId varchar(255) [not null, note: 'Primary Key (CUID or integer) of the entity that was affected.']
  ActionType varchar(100) [not null, note: 'Type of action performed (e.g., "Create", "Update", "Delete", "StatusChange", "LoginSuccess").']
  OldValues text [note: 'JSON or XML representation of data before the change, if applicable.']
  NewValues text [note: 'JSON or XML representation of data after the change, if applicable.']
  Details text [note: 'Additional context, comments, or system-generated information about the log entry.']
  IPAddress varchar(45) [note: 'IP address from which the action originated, if available.']
  AppVersion varchar(50) [note: 'Version of the application that made the change, if available.']
  // Note: AuditLogs are typically immutable once created; no UpdatedAt field.
}


//// Table Groups
TableGroup "Core Access & Users" [color: #1D6A96] {
  Roles
  Users
  UserGroups
  UserGroupMembers
  AdGroupRoleMapping
  ProcessOwnerAssignments
  TemplateAccessAssignments
  Note: '''
  Core User, Group, and Access Control Tables.
  - Roles: Defines user roles within the system (DevAdmin, SystemManager, ProcessOwner, Auditor, Viewer).
  - Users: Stores individual user information, credentials, and role assignments.
  - UserGroups: Defines collections of users for assignment purposes.
  - UserGroupMembers: Maps users to their respective groups.
  - ProcessOwnerAssignments: Assigns process owners to specific organizational scopes.
  - TemplateAccessAssignments: Grants users access to specific audit templates.
  '''
}

TableGroup "Organizational Structure" [color: #2A9D8F] {
  Location
  Factories
  Areas
  SubAreas
  AreaResponsibilities
  Note: '''
  Defines the physical and logical hierarchy of the organization where audits are conducted.
  - Location: Broad geographical locations (e.g., countries).
  - Factories: Specific plants or sites.
  - Areas: Departments or sections within factories.
  - SubAreas: Granular divisions within areas.
  - AreaResponsibilities: Assigns responsibility for corrective actions and retrospective analysis by area.
  '''
}

TableGroup "Audit Definition & Templates" [color: #E9C46A] {
  AuditTemplates
  QuestionGroups
  Questions
  QuestionOptions
  QuestionAllowedEvidenceTypes
  Note: '''
  Tables for defining the structure, content, and rules of audit templates.
  - AuditTemplates: Reusable blueprints for conducting specific types of audits with area-based responsibility and finding categorization features.
  - QuestionGroups: Logical sections to organize questions within a template.
  - Questions: Individual questions, their types, and associated logic (e.g., conditional, evidence).
  - QuestionOptions: Predefined answer choices for select-type questions.
  - QuestionAllowedEvidenceTypes: Defines which evidence types are allowed for each question.
  '''
}

TableGroup "Audit Execution & Evidence" [color: #F4A261] {
  Audits
  AuditAnswers
  AuditAnswerSelectedOptions
  AuditAnswerFailureReasons
  AuditAttachments
  Note: '''
  Tables related to the execution of audit instances, recording answers, and collecting evidence.
  - Audits: Specific instances of an audit being performed.
  - AuditAnswers: Responses provided by auditors to template questions.
  - AuditAnswerSelectedOptions: Links answers to selected options for multi-select questions.
  - AuditAnswerFailureReasons: Captures multiple reasons for a failed/negative answer.
  - AuditAttachments: Stores files (images, documents) attached as evidence to answers.
  '''
}

TableGroup "Findings & Corrective Actions" [color: #D9534F] {
  Findings
  FindingCategories
  CorrectiveActions
  Note: '''
  Manages non-conformities (findings) identified during audits and tracks their subsequent corrective and preventive actions (CAPA).
  - Findings: Records of deviations or non-conformities with area-based responsibility assignment and categorization.
  - FindingCategories: Flexible categorization system for findings within audit templates.
  - CorrectiveActions: Actions planned and taken to address the root causes of findings.
  '''
}

TableGroup "Scheduling & Recurrence" [color: #E76F51] {
  RecurringAuditSettings
  RecurrenceRules
  Note: '''
  Manages the automated scheduling of recurring audits.
  - RecurringAuditSettings: Defines the "what, who, and when (high-level)" for recurring audits.
  - RecurrenceRules: Details the specific frequency and pattern (the "how often") for a recurring setting.
  '''
}

TableGroup "Workflow & Logging" [color: #606C38] {
  AuditCorrectionRequests
  AuditLogs
  Note: '''
  Manages audit correction workflows and provides a comprehensive system-wide audit trail.
  - AuditCorrectionRequests: Handles requests to modify submitted audits.
  - AuditLogs: Records significant events and changes within the application for traceability.
  '''
}

// Note: All foreign key relationships are defined within the table definitions above
// The database uses AdObjectGuid (36 char) as the primary foreign key reference for users
// instead of the original UserId (25 char CUID) to align with Active Directory integration