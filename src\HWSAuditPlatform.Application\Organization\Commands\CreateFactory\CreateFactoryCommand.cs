using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.ValueObjects;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateFactory;

/// <summary>
/// Command to create a new factory
/// </summary>
public class CreateFactoryCommand : BaseCommand<int>
{
    public string FactoryName { get; set; } = string.Empty;
    public string? FactoryProcess { get; set; }
    public int LocationId { get; set; }
    public string? AddressLine1 { get; set; }
    public string? City { get; set; }
    public string? PostalCode { get; set; }
    public bool IsActive { get; set; } = true;
}
