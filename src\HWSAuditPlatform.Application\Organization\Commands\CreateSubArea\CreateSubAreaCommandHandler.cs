using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Application.Organization.Commands.CreateSubArea;

/// <summary>
/// Handler for CreateSubAreaCommand
/// </summary>
public class CreateSubAreaCommandHandler : BaseCommandHandler<CreateSubAreaCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateSubAreaCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateSubAreaCommand request, CancellationToken cancellationToken)
    {
        // Create the sub-area entity
        var subArea = new SubArea
        {
            SubAreaName = request.SubAreaName,
            AreaId = request.AreaId,
            Description = request.Description,
            IsActive = request.IsActive,
            CreatedByUserId = _currentUserService.UserId
        };

        // Add to context
        await _context.SubAreas.AddAsync(subArea, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return subArea.Id;
    }
}
