#!/bin/bash

# Wait for Samba AD to be ready
echo "Waiting for Samba AD to be ready..."
# A shorter sleep is fine now that we know the service is up.
sleep 15

# Create Organizational Units
echo "Creating Organizational Units..."
samba-tool ou create "OU=HWSAudit" || echo "OU=HWSAudit already exists"
samba-tool ou create "OU=Users,OU=HWSAudit" || echo "OU=Users already exists"
samba-tool ou create "OU=Groups,OU=HWSAudit" || echo "OU=Groups already exists"
samba-tool ou create "OU=ServiceAccounts,OU=HWSAudit" || echo "OU=ServiceAccounts already exists"

# Create Groups
echo "Creating Groups..."
# FIXED: Provide the relative OU path, not the full DN
samba-tool group add "HWSAudit-Administrators" --groupou="OU=Groups,OU=HWSAudit" || echo "HWSAudit-Administrators group already exists"
samba-tool group add "HWSAudit-Auditors" --groupou="OU=Groups,OU=HWSAudit" || echo "HWSAudit-Auditors group already exists"
samba-tool group add "HWSAudit-Managers" --groupou="OU=Groups,OU=HWSAudit" || echo "HWSAudit-Managers group already exists"
samba-tool group add "HWSAudit-Viewers" --groupou="OU=Groups,OU=HWSAudit" || echo "HWSAudit-Viewers group already exists"

# Create Service Account
echo "Creating Service Account..."
# FIXED: Provide the relative OU path
samba-tool user create hwsaudit-service "HWSAudit123!" \
    --userou="OU=ServiceAccounts,OU=HWSAudit" \
    --given-name="HWS Audit" \
    --surname="Service Account" \
    --mail-address="<EMAIL>" \
    --description="Service account for HWS Audit Platform" || echo "Service account already exists"

# Set service account to never expire
samba-tool user setexpiry hwsaudit-service --noexpiry || echo "Failed to set service account expiry"

# Create Test Users
echo "Creating Test Users..."

# Administrator
# FIXED: Provide the relative OU path
samba-tool user create admin.user "HWSAudit123!" \
    --userou="OU=Users,OU=HWSAudit" \
    --given-name="Admin" \
    --surname="User" \
    --mail-address="<EMAIL>" \
    --description="Test Administrator User" || echo "admin.user already exists"

# Auditor
# FIXED: Provide the relative OU path
samba-tool user create auditor.user "HWSAudit123!" \
    --userou="OU=Users,OU=HWSAudit" \
    --given-name="Auditor" \
    --surname="User" \
    --mail-address="<EMAIL>" \
    --description="Test Auditor User" || echo "auditor.user already exists"

# Manager
# FIXED: Provide the relative OU path
samba-tool user create manager.user "HWSAudit123!" \
    --userou="OU=Users,OU=HWSAudit" \
    --given-name="Manager" \
    --surname="User" \
    --mail-address="<EMAIL>" \
    --description="Test Manager User" || echo "manager.user already exists"

# Viewer
# FIXED: Provide the relative OU path
samba-tool user create viewer.user "HWSAudit123!" \
    --userou="OU=Users,OU=HWSAudit" \
    --given-name="Viewer" \
    --surname="User" \
    --mail-address="<EMAIL>" \
    --description="Test Viewer User" || echo "viewer.user already exists"

# Add users to groups
echo "Adding users to groups..."
samba-tool group addmembers "HWSAudit-Administrators" admin.user || echo "admin.user already in HWSAudit-Administrators"
samba-tool group addmembers "HWSAudit-Auditors" auditor.user || echo "auditor.user already in HWSAudit-Auditors"
samba-tool group addmembers "HWSAudit-Managers" manager.user || echo "manager.user already in HWSAudit-Managers"
samba-tool group addmembers "HWSAudit-Viewers" viewer.user || echo "viewer.user already in HWSAudit-Viewers"

echo "Active Directory setup completed successfully!"
echo ""
echo "Test Users Created:"
echo "  Administrator: <EMAIL> (Password: HWSAudit123!)"
echo "  Auditor: <EMAIL> (Password: HWSAudit123!)"
echo "  Manager: <EMAIL> (Password: HWSAudit123!)"
echo "  Viewer: <EMAIL> (Password: HWSAudit123!)"
echo ""
echo "Service Account: <EMAIL> (Password: HWSAudit123!)"