﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HWSAuditPlatform.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddLocationColumnsToRecurringAuditSettings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameIndex(
                name: "IX_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType",
                table: "hwsap_tpl_question_allowed_evidence_types",
                newName: "UQ_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType");

            migrationBuilder.AlterColumn<string>(
                name: "EvidenceTimingHint",
                table: "hwsap_tpl_questions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EvidenceInstructions",
                table: "hwsap_tpl_questions",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AreaId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FactoryId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SubAreaId",
                table: "hwsap_sch_recurring_audit_settings",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_QuestionAllowedEvidenceTypes_QuestionId_DisplayOrder",
                table: "hwsap_tpl_question_allowed_evidence_types",
                columns: new[] { "QuestionId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_AreaId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AreaId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_FactoryId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "FactoryId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringAuditSettings_SubAreaId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "SubAreaId");

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_areas_AreaId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "AreaId",
                principalTable: "hwsap_org_areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_factories_FactoryId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "FactoryId",
                principalTable: "hwsap_org_factories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_sub_areas_SubAreaId",
                table: "hwsap_sch_recurring_audit_settings",
                column: "SubAreaId",
                principalTable: "hwsap_org_sub_areas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_areas_AreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_factories_FactoryId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropForeignKey(
                name: "FK_hwsap_sch_recurring_audit_settings_hwsap_org_sub_areas_SubAreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropIndex(
                name: "IX_QuestionAllowedEvidenceTypes_QuestionId_DisplayOrder",
                table: "hwsap_tpl_question_allowed_evidence_types");

            migrationBuilder.DropIndex(
                name: "IX_RecurringAuditSettings_AreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropIndex(
                name: "IX_RecurringAuditSettings_FactoryId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropIndex(
                name: "IX_RecurringAuditSettings_SubAreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropColumn(
                name: "AreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropColumn(
                name: "FactoryId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.DropColumn(
                name: "SubAreaId",
                table: "hwsap_sch_recurring_audit_settings");

            migrationBuilder.RenameIndex(
                name: "UQ_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType",
                table: "hwsap_tpl_question_allowed_evidence_types",
                newName: "IX_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType");

            migrationBuilder.AlterColumn<int>(
                name: "EvidenceTimingHint",
                table: "hwsap_tpl_questions",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EvidenceInstructions",
                table: "hwsap_tpl_questions",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
