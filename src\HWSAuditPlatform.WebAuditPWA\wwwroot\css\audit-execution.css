/* Audit Execution Components - Industrial Theme CSS Module */

/* ===== SHARED VARIABLES AND UTILITIES ===== */
:root {
    /* Audit Execution Specific Variables */
    --audit-header-height: 120px;
    --audit-nav-height: 80px;
    --audit-footer-height: 100px;
    --audit-content-padding: 2rem;
    --audit-card-max-width: 900px;
    --audit-border-radius: 12px;
    --audit-border-radius-lg: 16px;
    --audit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --audit-shadow-depth: 0 8px 24px var(--industrial-shadow);
    --audit-shadow-hover: 0 12px 32px var(--industrial-shadow-heavy);
}

/* ===== AUDIT EXECUTION LAYOUT ===== */
.audit-execution-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--industrial-dark) 0%, #1a1a1a 100%);
    position: relative;
}

.audit-execution-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(20, 184, 166, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(20, 184, 166, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.audit-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

.audit-question-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--audit-content-padding);
    /* Smooth scrolling */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--industrial-teal) var(--industrial-gray);
}

/* Custom scrollbar for webkit browsers */
.audit-question-content::-webkit-scrollbar {
    width: 8px;
}

.audit-question-content::-webkit-scrollbar-track {
    background: var(--industrial-gray);
    border-radius: 4px;
}

.audit-question-content::-webkit-scrollbar-thumb {
    background: var(--industrial-teal);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.audit-question-content::-webkit-scrollbar-thumb:hover {
    background: var(--industrial-teal-light);
}

/* ===== INDUSTRIAL THEME ENHANCEMENTS ===== */

/* Enhanced Card Styling */
.audit-card-enhanced {
    background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
    border: 1px solid var(--industrial-border-light);
    border-radius: var(--audit-border-radius-lg);
    box-shadow: var(--audit-shadow-depth);
    position: relative;
    overflow: hidden;
    transition: var(--audit-transition);
}

.audit-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    z-index: 1;
}

.audit-card-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--audit-shadow-hover);
    border-color: var(--industrial-teal);
}

/* Enhanced Button Styling */
.btn-audit-primary {
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
    border: 2px solid var(--industrial-teal);
    color: white;
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 1rem 2rem;
    border-radius: var(--audit-border-radius);
    transition: var(--audit-transition);
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px var(--industrial-glow);
    position: relative;
    overflow: hidden;
}

.btn-audit-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-audit-primary:hover {
    background: linear-gradient(135deg, var(--industrial-teal-dark) 0%, var(--industrial-teal) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--industrial-glow-heavy);
}

.btn-audit-primary:hover::before {
    left: 100%;
}

.btn-audit-secondary {
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    border: 2px solid var(--industrial-border-light);
    color: var(--industrial-text);
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 1rem 2rem;
    border-radius: var(--audit-border-radius);
    transition: var(--audit-transition);
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-audit-secondary:hover {
    background: var(--industrial-teal);
    border-color: var(--industrial-teal);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--industrial-glow);
}

/* Enhanced Form Controls */
.form-control-audit {
    background-color: var(--industrial-gray);
    border: 2px solid var(--industrial-border);
    color: var(--industrial-text);
    border-radius: var(--audit-border-radius);
    padding: 1rem;
    font-size: 1rem;
    font-family: 'Roboto', sans-serif;
    transition: var(--audit-transition);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-control-audit:focus {
    background-color: var(--industrial-light-gray);
    border-color: var(--industrial-teal);
    color: var(--industrial-text);
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 0 0 0.2rem rgba(20, 184, 166, 0.25);
    outline: none;
}

.form-control-audit::placeholder {
    color: var(--industrial-text-muted);
    font-style: italic;
}

/* Enhanced Progress Indicators */
.progress-audit {
    height: 12px;
    background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-gray) 100%);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px var(--industrial-shadow);
    border: 1px solid var(--industrial-border);
}

.progress-bar-audit {
    background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    height: 100%;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
    box-shadow: 0 2px 4px var(--industrial-glow);
    position: relative;
    overflow: hidden;
}

.progress-bar-audit::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 25%, 
        rgba(255, 255, 255, 0.1) 25%, 
        rgba(255, 255, 255, 0.1) 50%, 
        transparent 50%, 
        transparent 75%, 
        rgba(255, 255, 255, 0.1) 75%);
    background-size: 20px 20px;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* Enhanced Typography */
.audit-title-primary {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--industrial-text-light);
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin: 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px var(--industrial-shadow);
    background: linear-gradient(135deg, var(--industrial-text-light) 0%, var(--industrial-teal-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.audit-subtitle {
    font-size: 1rem;
    color: var(--industrial-text-muted);
    font-weight: 500;
    letter-spacing: 0.5px;
}

.audit-section-title {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--industrial-text);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.audit-section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--industrial-teal);
    border-radius: 2px;
}

/* Enhanced Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.required {
    background: linear-gradient(135deg, var(--industrial-red) 0%, #c53030 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.status-indicator.optional {
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    color: var(--industrial-text);
    border: 1px solid var(--industrial-border);
}

.status-indicator.completed {
    background: linear-gradient(135deg, var(--industrial-green) 0%, #2f855a 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.3);
}

/* Enhanced Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner-audit {
    width: 48px;
    height: 48px;
    border: 4px solid var(--industrial-border);
    border-top: 4px solid var(--industrial-teal);
    border-radius: 50%;
    animation: spin-audit 1s linear infinite;
}

@keyframes spin-audit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1199px) {
    :root {
        --audit-content-padding: 1.5rem;
        --audit-card-max-width: 100%;
    }

    .audit-question-content {
        padding: 1.5rem 1rem;
    }
}

@media (max-width: 767px) {
    :root {
        --audit-content-padding: 1rem;
        --audit-header-height: 80px;
        --audit-nav-height: 60px;
        --audit-footer-height: 70px;
    }

    .audit-title-primary {
        font-size: 1.5rem;
        letter-spacing: 1px;
    }

    .btn-audit-primary,
    .btn-audit-secondary {
        padding: 1.25rem 2rem;
        font-size: 1rem;
        min-height: 64px;
    }

    /* Ensure button text is always visible on mobile */
    .btn-audit-primary .btn-text,
    .btn-audit-secondary .btn-text {
        display: inline !important;
    }

    .btn-audit-primary i,
    .btn-audit-secondary i {
        display: inline !important;
    }

    .form-control-audit {
        padding: 1.25rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .audit-question-content {
        padding: 1rem 0.75rem;
        /* Ensure proper scrolling on mobile */
        height: auto;
        min-height: 0;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Extra small devices - even more compact */
@media (max-width: 480px) {
    :root {
        --audit-content-padding: 0.75rem;
        --audit-header-height: 70px;
        --audit-nav-height: 50px;
        --audit-footer-height: 60px;
    }

    .audit-question-content {
        padding: 0.75rem 0.5rem;
    }

    /* Ensure all buttons maintain visibility on extra small screens */
    .btn-audit-primary,
    .btn-audit-secondary {
        min-height: 52px !important;
        padding: 1rem 1.5rem !important;
        font-size: 0.9rem !important;
    }

    /* Force button text and icons to be visible */
    .btn-audit-primary .btn-text,
    .btn-audit-secondary .btn-text,
    .btn .btn-text {
        display: inline !important;
        white-space: nowrap;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    .btn-audit-primary i,
    .btn-audit-secondary i,
    .btn i {
        display: inline !important;
        margin-right: 0.5rem;
    }
}

/* ===== TOUCH-FRIENDLY IMPROVEMENTS ===== */
@media (hover: none) and (pointer: coarse) {
    .btn-audit-primary,
    .btn-audit-secondary {
        min-height: 64px;
        padding: 1.5rem 2.5rem;
        font-size: 1.1rem;
    }

    .form-control-audit {
        min-height: 64px;
        padding: 1.5rem;
        font-size: 16px;
    }

    /* Remove hover effects on touch devices */
    .audit-card-enhanced:hover,
    .btn-audit-primary:hover,
    .btn-audit-secondary:hover {
        transform: none;
    }

    /* Improve touch scrolling */
    .audit-question-content {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .audit-card-enhanced {
        border-width: 2px;
        border-color: var(--industrial-text);
    }

    .btn-audit-primary,
    .btn-audit-secondary {
        border-width: 3px;
    }

    .form-control-audit {
        border-width: 2px;
    }
}
