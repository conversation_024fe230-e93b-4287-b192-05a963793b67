using Moq;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Services;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using MediatR;
using HWSAuditPlatform.Infrastructure.Persistence;


namespace HWSAuditPlatform.Tests.Common;

/// <summary>
/// Factory for creating mock services used in tests
/// </summary>
public static class MockServices
{
    /// <summary>
    /// Creates a mock current user service
    /// </summary>
    /// <param name="userId">User ID to return</param>
    /// <param name="username">Username to return</param>
    /// <param name="isAuthenticated">Whether user is authenticated</param>
    /// <returns>Mock ICurrentUserService</returns>
    public static Mock<ICurrentUserService> CreateCurrentUserService(
        string userId = "test-user-id",
        string username = "testuser",
        bool isAuthenticated = true)
    {
        var mock = new Mock<ICurrentUserService>();
        mock.Setup(x => x.UserId).Returns(userId);
        mock.Setup(x => x.Username).Returns(username);
        mock.Setup(x => x.IsAuthenticated).Returns(isAuthenticated);
        return mock;
    }

    /// <summary>
    /// Creates a mock domain event service
    /// </summary>
    /// <returns>Mock IDomainEventService</returns>
    public static Mock<IDomainEventService> CreateDomainEventService()
    {
        var mock = new Mock<IDomainEventService>();
        mock.Setup(x => x.PublishAsync(It.IsAny<IDomainEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        return mock;
    }

    /// <summary>
    /// Creates a mock unit of work
    /// </summary>
    /// <param name="saveChangesResult">Result to return from SaveChangesAsync</param>
    /// <returns>Mock IUnitOfWork</returns>
    public static Mock<IUnitOfWork> CreateUnitOfWork(int saveChangesResult = 1)
    {
        var mock = new Mock<IUnitOfWork>();
        mock.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(saveChangesResult);
        return mock;
    }

    /// <summary>
    /// Creates a mock repository
    /// </summary>
    /// <typeparam name="TEntity">Entity type</typeparam>
    /// <typeparam name="TKey">Key type</typeparam>
    /// <returns>Mock IRepository</returns>
    public static Mock<IRepository<TEntity, TKey>> CreateRepository<TEntity, TKey>()
        where TEntity : BaseEntity<TKey>
        where TKey : IEquatable<TKey>
    {
        var mock = new Mock<IRepository<TEntity, TKey>>();
        
        // Setup common repository methods
        mock.Setup(x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((TEntity entity, CancellationToken _) => entity);
            
        mock.Setup(x => x.Update(It.IsAny<TEntity>()));

        mock.Setup(x => x.Remove(It.IsAny<TEntity>()));
        
        return mock;
    }

    /// <summary>
    /// Creates a mock template access service
    /// </summary>
    /// <param name="hasAccess">Default access result to return</param>
    /// <returns>Mock ITemplateAccessService</returns>
    public static Mock<ITemplateAccessService> CreateTemplateAccessService(bool hasAccess = true)
    {
        var mock = new Mock<ITemplateAccessService>();
        mock.Setup(x => x.HasTemplateAccessAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(hasAccess);
        return mock;
    }

    /// <summary>
    /// Creates a mock mediator
    /// </summary>
    /// <returns>Mock IMediator</returns>
    public static Mock<IMediator> CreateMediator()
    {
        var mock = new Mock<IMediator>();

        // Setup default responses for common patterns
        mock.Setup(x => x.Send(It.IsAny<IRequest<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-id");

        mock.Setup(x => x.Send(It.IsAny<IRequest<int>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        mock.Setup(x => x.Send(It.IsAny<IRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        return mock;
    }

    /// <summary>
    /// Creates a mock area responsibility service
    /// </summary>
    /// <returns>Mock IAreaResponsibilityService</returns>
    public static Mock<IAreaResponsibilityService> CreateAreaResponsibilityService()
    {
        var mock = new Mock<IAreaResponsibilityService>();

        // Setup default behavior
        mock.Setup(x => x.AssignResponsibilitiesAsync(
                It.IsAny<Finding>(),
                It.IsAny<Audit>(),
                It.IsAny<IEnumerable<AreaResponsibility>>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mock.Setup(x => x.GetResponsibleUser(
                It.IsAny<int>(),
                It.IsAny<ResponsibilityType>(),
                It.IsAny<int?>(),
                It.IsAny<IEnumerable<AreaResponsibility>>()))
            .Returns("test-user-id");

        return mock;
    }

    /// <summary>
    /// Creates a mock finding categorization service
    /// </summary>
    /// <returns>Mock IFindingCategorizationService</returns>
    public static Mock<IFindingCategorizationService> CreateFindingCategorizationService()
    {
        var mock = new Mock<IFindingCategorizationService>();

        // Setup default behavior
        mock.Setup(x => x.AssignCategory(
                It.IsAny<Finding>(),
                It.IsAny<int>(),
                It.IsAny<AuditTemplate>()))
            .Returns(true);

        mock.Setup(x => x.ValidateFindingCategories(It.IsAny<AuditTemplate>()))
            .Returns(new FindingCategorizationValidationResult { IsValid = true });

        return mock;
    }
}

/// <summary>
/// Base test class with common setup
/// </summary>
public abstract class BaseTestClass : IDisposable
{
    protected readonly Mock<ICurrentUserService> MockCurrentUserService;
    protected readonly Mock<IDomainEventService> MockDomainEventService;
    protected readonly Mock<IUnitOfWork> MockUnitOfWork;
    protected readonly Mock<ITemplateAccessService> MockTemplateAccessService;

    protected BaseTestClass()
    {
        MockCurrentUserService = MockServices.CreateCurrentUserService();
        MockDomainEventService = MockServices.CreateDomainEventService();
        MockUnitOfWork = MockServices.CreateUnitOfWork();
        MockTemplateAccessService = MockServices.CreateTemplateAccessService();
    }

    public virtual void Dispose()
    {
        // Override in derived classes if needed
    }
}

/// <summary>
/// Base test class with database context
/// </summary>
public abstract class BaseDbTestClass : BaseTestClass
{
    protected readonly ApplicationDbContext Context;

    protected BaseDbTestClass()
    {
        Context = TestDbContextFactory.CreateInMemoryContext(currentUserService: MockCurrentUserService.Object);
    }

    protected async Task SeedTestDataAsync()
    {
        await TestDbContextFactory.SeedTestDataAsync(Context);
    }

    public override void Dispose()
    {
        Context?.Dispose();
        base.Dispose();
    }
}
