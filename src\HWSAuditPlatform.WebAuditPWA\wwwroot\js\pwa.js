// PWA functionality for HWS Audit Platform
window.initializePwa = () => {
    console.log('Initializing PWA features...');

    // Initialize network status monitoring
    initializeNetworkStatus();

    // Initialize install prompt handling
    initializeInstallPrompt();

    // Initialize service worker messaging
    initializeServiceWorkerMessaging();

    // Initialize PWA detection
    initializePwaDetection();

    // Initialize background sync
    initializeBackgroundSync();

    // Initialize cache management
    initializeCacheManagement();

    // Initialize enhanced navigation
    initializeEnhancedNavigation();

    // Initialize touch enhancements
    initializeTouchEnhancements();
};

// PWA detection
let isPwaMode = false;
let networkStatusCallback = null;

function initializePwaDetection() {
    // Check if running as PWA
    isPwaMode = window.matchMedia('(display-mode: standalone)').matches ||
                window.navigator.standalone === true ||
                document.referrer.includes('android-app://');

    console.log('PWA mode detected:', isPwaMode);
}

// PWA status functions
window.isPwaMode = () => {
    return isPwaMode;
};

window.canInstallPwa = () => {
    return deferredPrompt !== null;
};

window.isOnline = () => {
    return navigator.onLine;
};

window.registerNetworkStatusCallback = (dotNetRef) => {
    networkStatusCallback = dotNetRef;
    console.log('Network status callback registered');
};

// Network status monitoring
function initializeNetworkStatus() {
    const networkStatus = document.getElementById('network-status');
    const networkStatusNav = document.getElementById('network-status-nav');

    function updateNetworkStatus() {
        const isOnline = navigator.onLine;

        // Update main network status
        if (networkStatus) {
            if (isOnline) {
                networkStatus.innerHTML = '<i class="fas fa-wifi me-1"></i>Online';
                networkStatus.className = 'text-success me-2';
            } else {
                networkStatus.innerHTML = '<i class="fas fa-wifi-slash me-1"></i>Offline';
                networkStatus.className = 'text-warning me-2';
            }
        }

        // Update navigation network status
        if (networkStatusNav) {
            if (isOnline) {
                networkStatusNav.innerHTML = '<i class="fas fa-wifi me-1"></i>Online';
                networkStatusNav.className = 'text-success me-2';
            } else {
                networkStatusNav.innerHTML = '<i class="fas fa-wifi-slash me-1"></i>Offline';
                networkStatusNav.className = 'text-warning me-2';
            }
        }

        // Notify .NET callback if registered
        if (networkStatusCallback) {
            try {
                networkStatusCallback.invokeMethodAsync('OnNetworkStatusChanged', isOnline);
            } catch (error) {
                console.error('Error calling network status callback:', error);
            }
        }
    }

    // Initial status
    updateNetworkStatus();

    // Listen for network changes
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Periodic check for network status (some devices don't fire events reliably)
    setInterval(() => {
        updateNetworkStatus();
    }, 30000); // Check every 30 seconds
}

// Install prompt handling
let deferredPrompt;

function initializeInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('PWA install prompt available');
        e.preventDefault();
        deferredPrompt = e;
        
        // Show install button if needed
        showInstallButton();
    });
    
    window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        hideInstallButton();
        deferredPrompt = null;
    });
}

function showInstallButton() {
    // TODO: Show install button in UI
    console.log('PWA can be installed');
}

function hideInstallButton() {
    // TODO: Hide install button in UI
    console.log('PWA install button hidden');
}

window.promptInstall = async () => {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        console.log(`User response to install prompt: ${outcome}`);
        deferredPrompt = null;
    }
};

// Service worker messaging
function initializeServiceWorkerMessaging() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', (event) => {
            console.log('Message from service worker:', event.data);
            
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'SYNC_COMPLETE':
                        handleSyncComplete(event.data);
                        break;
                    case 'SYNC_ERROR':
                        handleSyncError(event.data);
                        break;
                    case 'UPDATE_AVAILABLE':
                        handleUpdateAvailable(event.data);
                        break;
                }
            }
        });
    }
}

function handleSyncComplete(data) {
    console.log('Sync completed:', data);
    hideSyncSpinner();
    // TODO: Show sync success notification
}

function handleSyncError(data) {
    console.log('Sync error:', data);
    hideSyncSpinner();
    // TODO: Show sync error notification
}

function handleUpdateAvailable(data) {
    console.log('App update available:', data);
    // TODO: Show update notification
}

function showSyncSpinner() {
    const spinner = document.getElementById('sync-spinner');
    if (spinner) {
        spinner.classList.remove('d-none');
    }
}

function hideSyncSpinner() {
    const spinner = document.getElementById('sync-spinner');
    if (spinner) {
        spinner.classList.add('d-none');
    }
}

// Camera functionality
window.isCameraAvailable = async () => {
    try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            return false;
        }

        // Check if we can enumerate devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        return devices.some(device => device.kind === 'videoinput');
    } catch (error) {
        console.error('Error checking camera availability:', error);
        return false;
    }
};

window.capturePhoto = async () => {
    try {
        if (!await window.isCameraAvailable()) {
            throw new Error('Camera not available');
        }

        return await window.capturePhotoWithModal();
    } catch (error) {
        console.error('Error capturing photo:', error);
        throw error;
    }
};

window.capturePhotoWithModal = async () => {
    return new Promise((resolve, reject) => {
        // Create modal for camera interface
        const modal = document.createElement('div');
        modal.className = 'camera-modal';
        modal.innerHTML = `
            <div class="camera-modal-content">
                <div class="camera-header">
                    <h5>Take Photo</h5>
                    <button class="btn-close" onclick="this.closest('.camera-modal').remove()">&times;</button>
                </div>
                <div class="camera-body">
                    <video id="camera-video" autoplay playsinline></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                </div>
                <div class="camera-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.camera-modal').remove()">Cancel</button>
                    <button class="btn btn-primary" id="capture-btn">
                        <i class="fas fa-camera"></i> Capture
                    </button>
                    <button class="btn btn-outline-secondary" id="switch-camera-btn" style="display: none;">
                        <i class="fas fa-sync-alt"></i> Switch
                    </button>
                </div>
            </div>
        `;

        // Add modal styles
        const style = document.createElement('style');
        style.textContent = `
            .camera-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }
            .camera-modal-content {
                background: var(--industrial-gray);
                border-radius: 12px;
                max-width: 90vw;
                max-height: 90vh;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            .camera-header {
                padding: 1rem;
                border-bottom: 1px solid var(--industrial-border);
                display: flex;
                justify-content: between;
                align-items: center;
                background: var(--industrial-steel);
            }
            .camera-header h5 {
                margin: 0;
                color: var(--industrial-text);
                flex: 1;
            }
            .btn-close {
                background: none;
                border: none;
                color: var(--industrial-text);
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0.25rem;
            }
            .camera-body {
                padding: 1rem;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 300px;
                background: #000;
            }
            #camera-video {
                max-width: 100%;
                max-height: 60vh;
                border-radius: 8px;
            }
            .camera-footer {
                padding: 1rem;
                border-top: 1px solid var(--industrial-border);
                display: flex;
                gap: 1rem;
                justify-content: center;
                background: var(--industrial-steel);
            }
            @media (max-width: 768px) {
                .camera-modal-content {
                    width: 100vw;
                    height: 100vh;
                    max-width: none;
                    max-height: none;
                    border-radius: 0;
                }
                #camera-video {
                    width: 100%;
                    height: auto;
                    max-height: 70vh;
                }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(modal);

        let stream = null;
        let currentFacingMode = 'environment';

        const video = modal.querySelector('#camera-video');
        const canvas = modal.querySelector('#camera-canvas');
        const captureBtn = modal.querySelector('#capture-btn');
        const switchBtn = modal.querySelector('#switch-camera-btn');

        const startCamera = async (facingMode = 'environment') => {
            try {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }

                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: facingMode,
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });

                video.srcObject = stream;

                // Check if we have multiple cameras
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                if (videoDevices.length > 1) {
                    switchBtn.style.display = 'inline-block';
                }
            } catch (error) {
                console.error('Error starting camera:', error);
                const errorMessage = window.handleCameraError(error);
                modal.querySelector('.camera-body').innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>${errorMessage}</p>
                        <button class="btn btn-outline-primary mt-2" onclick="location.reload()">
                            <i class="fas fa-refresh me-1"></i>Retry
                        </button>
                    </div>
                `;
            }
        };

        captureBtn.addEventListener('click', () => {
            if (video.videoWidth && video.videoHeight) {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                const context = canvas.getContext('2d');
                context.drawImage(video, 0, 0);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            // Stop camera
                            if (stream) {
                                stream.getTracks().forEach(track => track.stop());
                            }
                            modal.remove();
                            style.remove();
                            resolve(new Uint8Array(reader.result));
                        };
                        reader.onerror = () => {
                            modal.remove();
                            style.remove();
                            reject(new Error('Failed to process captured image'));
                        };
                        reader.readAsArrayBuffer(blob);
                    } else {
                        modal.remove();
                        style.remove();
                        reject(new Error('Failed to capture photo'));
                    }
                }, 'image/jpeg', 0.8);
            }
        });

        switchBtn.addEventListener('click', () => {
            currentFacingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
            startCamera(currentFacingMode);
        });

        // Handle modal close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
                modal.remove();
                style.remove();
                reject(new Error('User cancelled photo capture'));
            }
        });

        // Start camera
        startCamera(currentFacingMode);
    });
};

window.selectPhoto = async () => {
    return new Promise((resolve, reject) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.multiple = false;

        input.onchange = async (event) => {
            const file = event.target.files[0];
            if (file) {
                try {
                    // Validate file size (10MB max)
                    if (file.size > 10 * 1024 * 1024) {
                        reject(new Error('File size too large. Maximum 10MB allowed.'));
                        return;
                    }

                    // Validate file type
                    if (!file.type.startsWith('image/')) {
                        reject(new Error('Please select a valid image file.'));
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = () => resolve(new Uint8Array(reader.result));
                    reader.onerror = () => reject(new Error('Failed to read selected file'));
                    reader.readAsArrayBuffer(file);
                } catch (error) {
                    reject(error);
                }
            } else {
                reject(new Error('No file selected'));
            }
        };

        // Trigger file selection
        input.click();
    });
};

// Enhanced camera capabilities
window.getCameraCapabilities = async () => {
    try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
            return {
                hasCamera: false,
                hasFrontCamera: false,
                hasBackCamera: false,
                supportsFlash: false,
                supportsZoom: false,
                supportedFormats: [],
                supportedResolutions: []
            };
        }

        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        const capabilities = {
            hasCamera: videoDevices.length > 0,
            hasFrontCamera: false,
            hasBackCamera: false,
            supportsFlash: false,
            supportsZoom: false,
            supportedFormats: ['image/jpeg', 'image/png', 'image/webp'],
            supportedResolutions: [
                { width: 1920, height: 1080 },
                { width: 1280, height: 720 },
                { width: 640, height: 480 }
            ]
        };

        // Try to determine camera types
        for (const device of videoDevices) {
            if (device.label.toLowerCase().includes('front') || device.label.toLowerCase().includes('user')) {
                capabilities.hasFrontCamera = true;
            } else if (device.label.toLowerCase().includes('back') || device.label.toLowerCase().includes('environment')) {
                capabilities.hasBackCamera = true;
            }
        }

        // If we can't determine from labels, assume we have both if multiple cameras
        if (videoDevices.length > 1 && !capabilities.hasFrontCamera && !capabilities.hasBackCamera) {
            capabilities.hasFrontCamera = true;
            capabilities.hasBackCamera = true;
        } else if (videoDevices.length === 1) {
            capabilities.hasBackCamera = true; // Assume single camera is back camera
        }

        return capabilities;
    } catch (error) {
        console.error('Error getting camera capabilities:', error);
        return {
            hasCamera: false,
            hasFrontCamera: false,
            hasBackCamera: false,
            supportsFlash: false,
            supportsZoom: false,
            supportedFormats: [],
            supportedResolutions: []
        };
    }
};

// Image validation
window.validateImageFormat = async (imageData) => {
    try {
        if (!imageData || imageData.length === 0) {
            return false;
        }

        // Check for common image file signatures
        const uint8Array = new Uint8Array(imageData);

        // JPEG signature
        if (uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF) {
            return true;
        }

        // PNG signature
        if (uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47) {
            return true;
        }

        // WebP signature
        if (uint8Array[8] === 0x57 && uint8Array[9] === 0x45 && uint8Array[10] === 0x42 && uint8Array[11] === 0x50) {
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error validating image format:', error);
        return false;
    }
};

// Utility functions
window.resizeImage = async (imageData, maxWidth, maxHeight, quality = 0.8) => {
    return new Promise((resolve) => {
        const blob = new Blob([imageData], { type: 'image/jpeg' });
        const img = new Image();
        
        img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Calculate new dimensions
            let { width, height } = img;
            
            if (width > height) {
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
            } else {
                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }
            }
            
            canvas.width = width;
            canvas.height = height;
            
            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);
            
            canvas.toBlob((blob) => {
                const reader = new FileReader();
                reader.onload = () => resolve(new Uint8Array(reader.result));
                reader.readAsArrayBuffer(blob);
            }, 'image/jpeg', quality);
        };
        
        img.src = URL.createObjectURL(blob);
    });
};

// Enhanced camera capture with options
window.capturePhotoWithOptions = async (options = {}) => {
    try {
        const defaultOptions = {
            maxWidth: 1920,
            maxHeight: 1080,
            quality: 0.8,
            facingMode: 'environment',
            allowGallerySelection: true
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Use the existing capturePhoto function but with enhanced processing
        const imageData = await window.capturePhoto();

        // Resize if needed
        const resizedData = await window.resizeImage(imageData, finalOptions.maxWidth, finalOptions.maxHeight, finalOptions.quality);

        // Get metadata
        const metadata = await window.getImageMetadata(resizedData);

        return {
            data: resizedData,
            fileName: `photo_${Date.now()}.jpg`,
            contentType: 'image/jpeg',
            size: resizedData.length,
            width: metadata.width,
            height: metadata.height,
            capturedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error('Error capturing photo with options:', error);
        throw error;
    }
};

window.selectPhotoWithOptions = async (options = {}) => {
    try {
        const defaultOptions = {
            maxWidth: 1920,
            maxHeight: 1080,
            quality: 0.8,
            allowGallerySelection: true
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Use the existing selectPhoto function but with enhanced processing
        const imageData = await window.selectPhoto();

        // Resize if needed
        const resizedData = await window.resizeImage(imageData, finalOptions.maxWidth, finalOptions.maxHeight, finalOptions.quality);

        // Get metadata
        const metadata = await window.getImageMetadata(resizedData);

        return {
            data: resizedData,
            fileName: `selected_${Date.now()}.jpg`,
            contentType: 'image/jpeg',
            size: resizedData.length,
            width: metadata.width,
            height: metadata.height,
            capturedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error('Error selecting photo with options:', error);
        throw error;
    }
};

// Enhanced image capture with metadata
window.capturePhotoWithMetadata = async () => {
    try {
        const imageData = await window.capturePhoto();
        const metadata = await window.getImageMetadata(imageData);

        return {
            data: imageData,
            metadata: metadata,
            timestamp: new Date().toISOString(),
            source: 'camera'
        };
    } catch (error) {
        console.error('Error capturing photo with metadata:', error);
        throw error;
    }
};

window.selectPhotoWithMetadata = async () => {
    try {
        const imageData = await window.selectPhoto();
        const metadata = await window.getImageMetadata(imageData);

        return {
            data: imageData,
            metadata: metadata,
            timestamp: new Date().toISOString(),
            source: 'gallery'
        };
    } catch (error) {
        console.error('Error selecting photo with metadata:', error);
        throw error;
    }
};

window.getImageMetadata = async (imageData) => {
    return new Promise((resolve) => {
        const blob = new Blob([imageData], { type: 'image/jpeg' });
        const img = new Image();

        img.onload = () => {
            resolve({
                width: img.width,
                height: img.height,
                size: imageData.length,
                aspectRatio: img.width / img.height,
                format: 'jpeg'
            });
        };

        img.onerror = () => {
            resolve({
                width: 0,
                height: 0,
                size: imageData.length,
                aspectRatio: 1,
                format: 'unknown'
            });
        };

        img.src = URL.createObjectURL(blob);
    });
};

// Enhanced error handling for camera operations
window.handleCameraError = (error) => {
    console.error('Camera error:', error);

    let userMessage = 'Camera error occurred';

    if (error.name === 'NotAllowedError') {
        userMessage = 'Camera access denied. Please allow camera permissions and try again.';
    } else if (error.name === 'NotFoundError') {
        userMessage = 'No camera found on this device.';
    } else if (error.name === 'NotSupportedError') {
        userMessage = 'Camera is not supported on this device.';
    } else if (error.name === 'NotReadableError') {
        userMessage = 'Camera is already in use by another application.';
    } else if (error.name === 'OverconstrainedError') {
        userMessage = 'Camera settings are not supported by this device.';
    } else if (error.name === 'SecurityError') {
        userMessage = 'Camera access blocked due to security restrictions.';
    }

    return userMessage;
};

// Background sync functionality
let backgroundSyncSupported = false;

function initializeBackgroundSync() {
    // Check if background sync is supported
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        backgroundSyncSupported = true;
        console.log('Background sync is supported');
    } else {
        console.log('Background sync is not supported');
    }
}

// Register background sync for data synchronization
window.registerBackgroundSync = async (tag) => {
    if (!backgroundSyncSupported) {
        console.log('Background sync not supported, falling back to immediate sync');
        return false;
    }

    try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register(tag);
        console.log(`Background sync registered: ${tag}`);
        return true;
    } catch (error) {
        console.error('Error registering background sync:', error);
        return false;
    }
};

// Queue data for background sync
window.queueForBackgroundSync = async (data, syncType = 'audit-data') => {
    try {
        // Store data in IndexedDB for background sync
        await storeForBackgroundSync(data, syncType);

        // Register background sync
        const registered = await window.registerBackgroundSync(syncType);

        if (!registered) {
            // Fallback: attempt immediate sync if background sync failed
            console.log('Attempting immediate sync as fallback');
            return await attemptImmediateSync(data, syncType);
        }

        return true;
    } catch (error) {
        console.error('Error queuing for background sync:', error);
        return false;
    }
};

// Store data for background sync in IndexedDB
async function storeForBackgroundSync(data, syncType) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('syncQueue')) {
                const store = db.createObjectStore('syncQueue', { keyPath: 'id', autoIncrement: true });
                store.createIndex('syncType', 'syncType', { unique: false });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readwrite');
            const store = transaction.objectStore('syncQueue');

            const syncItem = {
                data: data,
                syncType: syncType,
                timestamp: Date.now(),
                attempts: 0,
                maxAttempts: 3
            };

            const addRequest = store.add(syncItem);
            addRequest.onsuccess = () => resolve();
            addRequest.onerror = () => reject(addRequest.error);
        };
    });
}

// Attempt immediate sync as fallback
async function attemptImmediateSync(data, syncType) {
    try {
        // This would call the appropriate sync method based on syncType
        console.log(`Attempting immediate sync for type: ${syncType}`);

        // For now, just return true - this would be implemented based on specific sync needs
        return true;
    } catch (error) {
        console.error('Immediate sync failed:', error);
        return false;
    }
}

// Get pending sync items
window.getPendingSyncItems = async (syncType = null) => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readonly');
            const store = transaction.objectStore('syncQueue');

            let getRequest;
            if (syncType) {
                const index = store.index('syncType');
                getRequest = index.getAll(syncType);
            } else {
                getRequest = store.getAll();
            }

            getRequest.onsuccess = () => resolve(getRequest.result);
            getRequest.onerror = () => reject(getRequest.error);
        };
    });
};

// Remove sync item after successful sync
window.removeSyncItem = async (itemId) => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('HWSAuditPWA_BackgroundSync', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['syncQueue'], 'readwrite');
            const store = transaction.objectStore('syncQueue');

            const deleteRequest = store.delete(itemId);
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
        };
    });
};

// Check if background sync is supported
window.isBackgroundSyncSupported = () => backgroundSyncSupported;

// Request manual sync via service worker message
window.requestManualSync = async (syncType) => {
    try {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
                type: 'SYNC_REQUEST',
                syncType: syncType
            });
            console.log(`Manual sync requested: ${syncType}`);
        } else {
            console.log('Service worker not available for manual sync');
        }
    } catch (error) {
        console.error('Error requesting manual sync:', error);
    }
};

// Cache management functionality
let cacheManagementSupported = false;

function initializeCacheManagement() {
    // Check if cache API is supported
    if ('caches' in window) {
        cacheManagementSupported = true;
        console.log('Cache management is supported');
    } else {
        console.log('Cache management is not supported');
    }
}

// Clear old caches
window.clearOldCaches = async () => {
    if (!cacheManagementSupported) {
        console.log('Cache management not supported');
        return false;
    }

    try {
        const cacheNames = await caches.keys();
        const currentCacheName = 'offline-cache-' + (window.assetsManifest?.version || 'current');

        const oldCaches = cacheNames.filter(name =>
            name.startsWith('offline-cache-') && name !== currentCacheName
        );

        if (oldCaches.length > 0) {
            console.log('Clearing old caches:', oldCaches);

            const deletePromises = oldCaches.map(cacheName => caches.delete(cacheName));
            await Promise.all(deletePromises);

            console.log('Successfully cleared old caches');
            return true;
        } else {
            console.log('No old caches to clear');
            return true;
        }
    } catch (error) {
        console.error('Error clearing old caches:', error);
        return false;
    }
};

// Get cache storage usage
window.getCacheStorageUsage = async () => {
    if (!cacheManagementSupported) {
        return { supported: false, usage: 0, quota: 0 };
    }

    try {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            return {
                supported: true,
                usage: estimate.usage || 0,
                quota: estimate.quota || 0,
                usageDetails: estimate.usageDetails || {}
            };
        } else {
            return { supported: false, usage: 0, quota: 0 };
        }
    } catch (error) {
        console.error('Error getting cache storage usage:', error);
        return { supported: false, usage: 0, quota: 0 };
    }
};

// Preload critical resources
window.preloadCriticalResources = async (resources) => {
    if (!cacheManagementSupported || !Array.isArray(resources)) {
        return false;
    }

    try {
        const cache = await caches.open('critical-resources');

        const preloadPromises = resources.map(async (resource) => {
            try {
                const response = await fetch(resource);
                if (response.ok) {
                    await cache.put(resource, response);
                    console.log('Preloaded resource:', resource);
                }
            } catch (error) {
                console.warn('Failed to preload resource:', resource, error);
            }
        });

        await Promise.all(preloadPromises);
        console.log('Critical resources preloading completed');
        return true;
    } catch (error) {
        console.error('Error preloading critical resources:', error);
        return false;
    }
};

// Check if resource is cached
window.isResourceCached = async (url) => {
    if (!cacheManagementSupported) {
        return false;
    }

    try {
        const cacheNames = await caches.keys();

        for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName);
            const response = await cache.match(url);
            if (response) {
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error checking if resource is cached:', error);
        return false;
    }
};

// Update cache with fresh data
window.updateCache = async (url, data) => {
    if (!cacheManagementSupported) {
        return false;
    }

    try {
        const cache = await caches.open('dynamic-cache');
        const response = new Response(JSON.stringify(data), {
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'max-age=3600'
            }
        });

        await cache.put(url, response);
        console.log('Updated cache for:', url);
        return true;
    } catch (error) {
        console.error('Error updating cache:', error);
        return false;
    }
};

// Enhanced Navigation Functions
function initializeEnhancedNavigation() {
    console.log('Initializing enhanced navigation...');

    // Close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
        const dropdowns = document.querySelectorAll('.dropdown-menu.show');
        dropdowns.forEach(dropdown => {
            const parent = dropdown.closest('.dropdown');
            if (parent && !parent.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
    });

    // Handle escape key to close mobile menu and dropdowns
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            // Close mobile menu
            const mobileOverlay = document.querySelector('.mobile-nav-overlay.show');
            if (mobileOverlay) {
                mobileOverlay.classList.remove('show');
            }

            // Close dropdowns
            const dropdowns = document.querySelectorAll('.dropdown-menu.show');
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });

    // Enhanced network status updates for new navigation
    const originalUpdateNetworkStatus = window.updateNetworkStatus;
    window.updateNetworkStatus = () => {
        const networkStatus = document.getElementById('network-status');
        const isOnline = navigator.onLine;

        if (networkStatus) {
            if (isOnline) {
                networkStatus.innerHTML = '<i class="fas fa-wifi status-icon online"></i><span class="status-text d-none d-lg-inline">Online</span>';
                networkStatus.className = 'status-item';
            } else {
                networkStatus.innerHTML = '<i class="fas fa-wifi-slash status-icon offline"></i><span class="status-text d-none d-lg-inline">Offline</span>';
                networkStatus.className = 'status-item';
            }
        }

        // Call original function for backward compatibility
        if (originalUpdateNetworkStatus) {
            originalUpdateNetworkStatus();
        }
    };
}

function initializeTouchEnhancements() {
    console.log('Initializing touch enhancements...');

    // Add touch feedback for interactive elements
    const interactiveElements = document.querySelectorAll('.btn, .nav-item, .dropdown-item, .card, .mobile-nav-item');

    interactiveElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
            this.style.transition = 'transform 0.1s ease';
        }, { passive: true });

        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
                this.style.transition = '';
            }, 150);
        }, { passive: true });

        element.addEventListener('touchcancel', function() {
            this.style.transform = '';
            this.style.transition = '';
        }, { passive: true });
    });

    // Improve scroll performance on mobile
    if ('scrollBehavior' in document.documentElement.style) {
        document.documentElement.style.scrollBehavior = 'smooth';
    }

    // Add momentum scrolling for iOS
    document.body.style.webkitOverflowScrolling = 'touch';
}

// Enhanced sync status functions
window.showSyncSpinner = () => {
    const syncStatus = document.getElementById('sync-status');
    const syncSpinner = document.getElementById('sync-spinner');
    const syncSpinnerNav = document.getElementById('sync-spinner-nav');

    if (syncStatus) {
        syncStatus.innerHTML = '<i class="fas fa-sync-alt status-icon syncing"></i><span class="status-text d-none d-lg-inline">Syncing</span>';
        syncStatus.className = 'status-item';
    }

    if (syncSpinner) syncSpinner.classList.remove('d-none');
    if (syncSpinnerNav) syncSpinnerNav.classList.remove('d-none');

    console.log('Sync spinner shown');
};

window.hideSyncSpinner = () => {
    const syncStatus = document.getElementById('sync-status');
    const syncSpinner = document.getElementById('sync-spinner');
    const syncSpinnerNav = document.getElementById('sync-spinner-nav');

    if (syncStatus) {
        syncStatus.innerHTML = '<i class="fas fa-sync-alt status-icon synced"></i><span class="status-text d-none d-lg-inline">Synced</span>';
        syncStatus.className = 'status-item';
    }

    if (syncSpinner) syncSpinner.classList.add('d-none');
    if (syncSpinnerNav) syncSpinnerNav.classList.add('d-none');

    console.log('Sync spinner hidden');
};

// Toast notification system
window.showToast = (message, type = 'info', duration = 3000) => {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => toast.classList.add('show'), 100);

    // Auto remove
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
};

// Add toast notification styles if not already present
if (!document.querySelector('#toast-styles')) {
    const toastStyles = document.createElement('style');
    toastStyles.id = 'toast-styles';
    toastStyles.textContent = `
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--industrial-gray);
            border: 1px solid var(--industrial-border);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 8px 24px var(--industrial-shadow);
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--industrial-text);
        }

        .toast-success {
            border-left: 4px solid var(--industrial-green);
        }

        .toast-error {
            border-left: 4px solid var(--industrial-red);
        }

        .toast-info {
            border-left: 4px solid var(--industrial-teal);
        }

        @media (max-width: 768px) {
            .toast-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
    `;
    document.head.appendChild(toastStyles);
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePwa);
} else {
    initializePwa();
}
