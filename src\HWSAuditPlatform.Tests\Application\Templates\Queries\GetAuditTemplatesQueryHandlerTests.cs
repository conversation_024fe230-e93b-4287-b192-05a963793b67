using FluentAssertions;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Queries;

public class GetAuditTemplatesQueryHandlerTests : BaseDbTestClass
{
    private readonly GetAuditTemplatesQueryHandler _handler;

    public GetAuditTemplatesQueryHandlerTests()
    {
        _handler = new GetAuditTemplatesQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidQuery_ShouldReturnPaginatedTemplates()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().NotBeEmpty();
        result.TotalCount.Should().BeGreaterThan(0);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(10);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_ShouldFilterTemplates()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            SearchTerm = "Safety",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(t => 
            t.TemplateName.Contains("Safety", StringComparison.OrdinalIgnoreCase) ||
            (t.Description != null && t.Description.Contains("Safety", StringComparison.OrdinalIgnoreCase)));
    }

    [Fact]
    public async Task Handle_WithPublishedFilter_ShouldFilterByPublishedStatus()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            IsPublished = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(t => t.IsPublished);
    }

    [Fact]
    public async Task Handle_WithActiveFilter_ShouldFilterByActiveStatus()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            IsActive = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(t => t.IsActive);
    }

    [Fact]
    public async Task Handle_WithCanBeUsedFilter_ShouldFilterUsableTemplates()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            CanBeUsed = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().OnlyContain(t => t.CanBeUsed);
        result.Items.Should().OnlyContain(t => t.IsPublished && t.IsActive);
    }

    [Fact]
    public async Task Handle_WithSorting_ShouldSortCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            SortBy = "TemplateName",
            SortDirection = "asc",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeInAscendingOrder(t => t.TemplateName);
    }

    [Fact]
    public async Task Handle_WithDescendingSorting_ShouldSortDescending()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateTestTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            SortBy = "CreatedAt",
            SortDirection = "desc",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeInDescendingOrder(t => t.CreatedAt);
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateManyTestTemplatesAsync(15); // Create more than one page
        
        var query = new GetAuditTemplatesQuery
        {
            PageNumber = 2,
            PageSize = 5
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(5);
        result.Items.Should().HaveCount(5);
        result.TotalCount.Should().Be(15);
        result.TotalPages.Should().Be(3);
    }

    [Fact]
    public async Task Handle_ShouldReturnCorrectTemplateProperties()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        var templateDto = result.Items.First(t => t.Id == template.Id);
        
        templateDto.TemplateName.Should().Be(template.TemplateName);
        templateDto.Description.Should().Be(template.Description);
        templateDto.Version.Should().Be(template.Version);
        templateDto.IsPublished.Should().Be(template.IsPublished);
        templateDto.IsActive.Should().Be(template.IsActive);
        templateDto.FullName.Should().Be($"{template.TemplateName} v{template.Version}");
        templateDto.CanBeUsed.Should().Be(template.IsPublished && template.IsActive);
        templateDto.QuestionCount.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_WithVersionSorting_ShouldSortByVersion()
    {
        // Arrange
        await SeedTestDataAsync();
        await CreateVersionedTemplatesAsync();
        
        var query = new GetAuditTemplatesQuery
        {
            SortBy = "Version",
            SortDirection = "desc",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeInDescendingOrder(t => t.Version);
    }

    private async Task CreateTestTemplatesAsync()
    {
        var templates = new[]
        {
            new AuditTemplate
            {
                TemplateName = "Safety Audit Template",
                Description = "Safety audit for manufacturing",
                Version = 1,
                IsPublished = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuditTemplate
            {
                TemplateName = "Quality Audit Template",
                Description = "Quality control audit",
                Version = 1,
                IsPublished = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuditTemplate
            {
                TemplateName = "Environmental Audit Template",
                Description = "Environmental compliance audit",
                Version = 1,
                IsPublished = true,
                IsActive = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.AuditTemplates.AddRange(templates);
        await Context.SaveChangesAsync();
    }

    private async Task CreateManyTestTemplatesAsync(int count)
    {
        var templates = new List<AuditTemplate>();
        for (int i = 0; i < count; i++)
        {
            templates.Add(new AuditTemplate
            {
                TemplateName = $"Template {i + 1}",
                Description = $"Description {i + 1}",
                Version = 1,
                IsPublished = i % 2 == 0,
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddMinutes(-i),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-i)
            });
        }

        Context.AuditTemplates.AddRange(templates);
        await Context.SaveChangesAsync();
    }

    private async Task CreateVersionedTemplatesAsync()
    {
        var templates = new[]
        {
            new AuditTemplate
            {
                TemplateName = "Versioned Template",
                Version = 1,
                IsPublished = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuditTemplate
            {
                TemplateName = "Versioned Template",
                Version = 2,
                IsPublished = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new AuditTemplate
            {
                TemplateName = "Versioned Template",
                Version = 3,
                IsPublished = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.AuditTemplates.AddRange(templates);
        await Context.SaveChangesAsync();
    }

    private async Task<AuditTemplate> CreateTestTemplateWithQuestionsAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Template with Questions",
            Description = "Template for testing question count",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var questions = new[]
        {
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Question 1",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Question 2",
                QuestionType = QuestionType.ShortText,
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        Context.Questions.AddRange(questions);
        await Context.SaveChangesAsync();

        return template;
    }
}
