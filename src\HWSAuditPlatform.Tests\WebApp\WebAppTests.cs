using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using HWSAuditPlatform.WebApp.Services;
using HWSAuditPlatform.WebApp.Models;
using Blazored.LocalStorage;

namespace HWSAuditPlatform.Tests.WebApp;

/// <summary>
/// Tests for the new Blazor WebAssembly WebApp
/// </summary>
public class WebAppTests
{
    [Fact]
    public void AuthenticationService_Interface_ShouldExist()
    {
        // Arrange & Act
        var interfaceType = typeof(IAuthenticationService);
        var implementationType = typeof(AuthenticationService);

        // Assert
        Assert.NotNull(interfaceType);
        Assert.NotNull(implementationType);
        Assert.True(interfaceType.IsInterface);
        Assert.True(implementationType.IsAssignableTo(interfaceType));
    }

    [Fact]
    public void AuthenticationResult_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var result = new AuthenticationResult
        {
            Token = "test-token",
            Username = "test.user",
            Email = "<EMAIL>",
            FullName = "Test User",
            Role = "Admin",
            FactoryId = "1",
            ExpiresAt = DateTime.UtcNow.AddHours(8),
            IsSuccess = true
        };

        // Assert
        Assert.Equal("test-token", result.Token);
        Assert.Equal("test.user", result.Username);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.Equal("Test User", result.FullName);
        Assert.Equal("Admin", result.Role);
        Assert.Equal("1", result.FactoryId);
        Assert.True(result.IsSuccess);
        Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public void UserInfo_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var userInfo = new UserInfo
        {
            UserId = "user-123",
            Username = "test.user",
            Email = "<EMAIL>",
            FullName = "Test User",
            Role = "Admin",
            FactoryId = "1",
            IsActive = true
        };

        // Assert
        Assert.Equal("user-123", userInfo.UserId);
        Assert.Equal("test.user", userInfo.Username);
        Assert.Equal("<EMAIL>", userInfo.Email);
        Assert.Equal("Test User", userInfo.FullName);
        Assert.Equal("Admin", userInfo.Role);
        Assert.Equal("1", userInfo.FactoryId);
        Assert.True(userInfo.IsActive);
    }

    [Fact]
    public void LoginRequest_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var loginRequest = new LoginRequest
        {
            Username = "test.user",
            Password = "password123"
        };

        // Assert
        Assert.Equal("test.user", loginRequest.Username);
        Assert.Equal("password123", loginRequest.Password);
    }

    [Fact]
    public void ApiResponse_ShouldHaveCorrectStructure()
    {
        // Arrange & Act
        var response = new ApiResponse<string>
        {
            Success = true,
            Data = "test data",
            Message = "Success",
            StatusCode = 200,
            Timestamp = DateTime.UtcNow
        };

        // Assert
        Assert.True(response.Success);
        Assert.Equal("test data", response.Data);
        Assert.Equal("Success", response.Message);
        Assert.Equal(200, response.StatusCode);
        Assert.True(response.Timestamp <= DateTime.UtcNow);
    }

    [Fact]
    public void ApiErrorResponse_ShouldHaveCorrectStructure()
    {
        // Arrange & Act
        var errorResponse = new ApiErrorResponse
        {
            StatusCode = 400,
            Title = "Bad Request",
            Detail = "Invalid input",
            Timestamp = DateTime.UtcNow
        };

        // Assert
        Assert.Equal(400, errorResponse.StatusCode);
        Assert.Equal("Bad Request", errorResponse.Title);
        Assert.Equal("Invalid input", errorResponse.Detail);
        Assert.True(errorResponse.Timestamp <= DateTime.UtcNow);
    }
}
