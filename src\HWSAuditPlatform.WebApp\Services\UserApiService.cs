using System.Net.Http.Json;
using System.Text.Json;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.WebApp.Models;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of user API service
/// </summary>
public class UserApiService : IUserApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<UserApiService> _logger;

    public UserApiService(
        AuthenticatedHttpClientService httpClient,
        ILogger<UserApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<IEnumerable<User>> GetUsersAsync()
    {
        try
        {
            _logger.LogInformation("Fetching users from API");
            var response = await _httpClient.GetAsync("api/v1/users?pageSize=100"); // Get more items for now

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PaginatedResult<UserSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    // Convert UserSummaryDto to User entities
                    var users = apiResponse.Data.Items.Select(dto => new User
                    {
                        Id = dto.Id,
                        Username = dto.Username,
                        Email = dto.Email,
                        FirstName = dto.FullName?.Split(' ').FirstOrDefault() ?? "",
                        LastName = dto.FullName?.Split(' ').Skip(1).FirstOrDefault() ?? "",
                        IsActive = dto.IsActive,
                        AdObjectGuid = dto.AdObjectGuid,
                        Factory = dto.FactoryName != null ? new Factory { FactoryName = dto.FactoryName } : null
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} users", users.Count);
                    return users;
                }
            }

            _logger.LogWarning("Failed to fetch users. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<User>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching users");
            return Enumerable.Empty<User>();
        }
    }

    public async Task<User?> GetUserByIdAsync(string id)
    {
        try
        {
            _logger.LogInformation("Fetching user with ID {Id}", id);
            var response = await _httpClient.GetAsync($"api/v1/users/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<UserDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    // Convert UserDto to User entity
                    var dto = apiResponse.Data;
                    var user = new User
                    {
                        Id = dto.Id,
                        Username = dto.Username,
                        Email = dto.Email,
                        FirstName = dto.FirstName,
                        LastName = dto.LastName,
                        IsActive = dto.IsActive,
                        AdObjectGuid = dto.AdObjectGuid ?? string.Empty,
                        CreatedAt = dto.CreatedAt,
                        UpdatedAt = dto.UpdatedAt,
                        CreatedByUserId = dto.CreatedByUserId,
                        UpdatedByUserId = dto.UpdatedByUserId,
                        Factory = dto.FactoryName != null ? new Factory { FactoryName = dto.FactoryName } : null
                    };

                    _logger.LogInformation("Successfully fetched user with ID {Id}", id);
                    return user;
                }
            }

            _logger.LogWarning("Failed to fetch user with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching user with ID {Id}", id);
            return null;
        }
    }

    public async Task<User> CreateUserAsync(User user)
    {
        try
        {
            _logger.LogInformation("Creating new user: {Username}", user.Username);
            var response = await _httpClient.PostAsJsonAsync("api/v1/users", user);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<User>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully created user with ID {Id}", apiResponse.Data.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to create user. Status: {StatusCode}", response.StatusCode);
            throw new InvalidOperationException("Failed to create user");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            throw;
        }
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        try
        {
            _logger.LogInformation("Updating user with ID {Id}", user.Id);
            var response = await _httpClient.PutAsJsonAsync($"api/v1/users/{user.Id}", user);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<User>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully updated user with ID {Id}", user.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to update user with ID {Id}. Status: {StatusCode}", user.Id, response.StatusCode);
            throw new InvalidOperationException("Failed to update user");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user with ID {Id}", user.Id);
            throw;
        }
    }

    public async Task<bool> DeleteUserAsync(string id)
    {
        try
        {
            _logger.LogInformation("Deleting user with ID {Id}", id);
            var response = await _httpClient.DeleteAsync($"api/v1/users/{id}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted user with ID {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to delete user with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user with ID {Id}", id);
            return false;
        }
    }
}
