@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.WebAuditPWA.Components.Audit

<div class="question-card-container">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-8">
            <div class="question-card">
                <!-- Question Header -->
                <div class="question-header">
                    <div class="header-content">
                        <div class="question-meta">
                            <h5 class="question-number">Question @QuestionNumber</h5>
                            @if (Question?.IsRequired == true)
                            {
                                <span class="required-badge">
                                    <i class="fas fa-asterisk me-1"></i>
                                    Required
                                </span>
                            }
                        </div>
                        @if (Question?.Weight.HasValue == true)
                        {
                            <div class="weight-indicator">
                                <i class="fas fa-weight-hanging me-1"></i>
                                <span class="weight-value">@Question.Weight.Value</span>
                            </div>
                        }
                    </div>
                </div>

                <!-- Question Body -->
                <div class="question-body">
                    <!-- Question Text Section -->
                    <div class="question-text-section">
                        <h6 class="question-title">@Question?.QuestionText</h6>
                        @if (!string.IsNullOrEmpty(Question?.HelpText))
                        {
                            <div class="help-text">
                                <i class="fas fa-info-circle me-2"></i>
                                <span class="help-content">@Question.HelpText</span>
                            </div>
                        }
                    </div>

                    <!-- Answer Input Section -->
                    <div class="answer-section">
                        <QuestionAnswerInput Question="Question"
                                           Answer="Answer"
                                           ShowPreviousAnswer="ShowPreviousAnswer"
                                           OnAnswerChanged="HandleAnswerChanged"
                                           OnValidationStateChanged="HandleValidationStateChanged" />
                    </div>

                    <!-- Comments Section -->
                    <div class="comments-section">
                        <label class="section-label">
                            <i class="fas fa-comment-alt me-2"></i>
                            Comments (Optional)
                        </label>
                        <textarea class="form-control comments-textarea" 
                                 rows="3" 
                                 placeholder="Add any additional comments..."
                                 value="@(Answer?.Comments ?? "")"
                                 @onchange="@((e) => HandleCommentsChanged(e.Value?.ToString() ?? ""))">
                        </textarea>
                    </div>

                    <!-- Attachments Section -->
                    <div class="attachments-section">
                        <label class="section-label">
                            <i class="fas fa-paperclip me-2"></i>
                            Evidence & Attachments
                        </label>
                        <div class="attachment-actions">
                            <button class="btn btn-attachment-action" @onclick="HandleCapturePhoto">
                                <i class="fas fa-camera me-2"></i>
                                <span class="btn-text">Take Photo</span>
                            </button>
                            <button class="btn btn-attachment-action" @onclick="HandleSelectPhoto">
                                <i class="fas fa-image me-2"></i>
                                <span class="btn-text">Select Photo</span>
                            </button>
                        </div>

                        @if (AttachmentIds?.Any() == true)
                        {
                            <AttachmentDisplay AttachmentIds="AttachmentIds"
                                             AllowRemove="true"
                                             OnRemoveAttachment="HandleRemoveAttachment" />
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public Question? Question { get; set; }
    [Parameter] public AuditAnswer? Answer { get; set; }
    [Parameter] public int QuestionNumber { get; set; }
    [Parameter] public List<string>? AttachmentIds { get; set; }
    [Parameter] public bool ShowPreviousAnswer { get; set; } = false;
    [Parameter] public EventCallback<AuditAnswer> OnAnswerChanged { get; set; }
    [Parameter] public EventCallback<string> OnCommentsChanged { get; set; }
    [Parameter] public EventCallback<bool> OnValidationStateChanged { get; set; }
    [Parameter] public EventCallback OnCapturePhoto { get; set; }
    [Parameter] public EventCallback OnSelectPhoto { get; set; }
    [Parameter] public EventCallback<string> OnRemoveAttachment { get; set; }

    private async Task HandleAnswerChanged(AuditAnswer answer)
    {
        if (OnAnswerChanged.HasDelegate)
        {
            await OnAnswerChanged.InvokeAsync(answer);
        }
    }

    private async Task HandleCommentsChanged(string comments)
    {
        if (OnCommentsChanged.HasDelegate)
        {
            await OnCommentsChanged.InvokeAsync(comments);
        }
    }

    private async Task HandleCapturePhoto()
    {
        if (OnCapturePhoto.HasDelegate)
        {
            await OnCapturePhoto.InvokeAsync();
        }
    }

    private async Task HandleSelectPhoto()
    {
        if (OnSelectPhoto.HasDelegate)
        {
            await OnSelectPhoto.InvokeAsync();
        }
    }

    private async Task HandleRemoveAttachment(string attachmentId)
    {
        if (OnRemoveAttachment.HasDelegate)
        {
            await OnRemoveAttachment.InvokeAsync(attachmentId);
        }
    }

    private async Task HandleValidationStateChanged(bool isValid)
    {
        if (OnValidationStateChanged.HasDelegate)
        {
            await OnValidationStateChanged.InvokeAsync(isValid);
        }
    }
}

<style>
    .question-card-container {
        padding: 1rem 0;
        /* Ensure proper scrolling behavior */
        width: 100%;
        max-width: 100%;
        overflow: visible;
    }

    .question-card {
        background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
        border: 1px solid var(--industrial-border-light);
        border-radius: 16px;
        box-shadow: 0 8px 24px var(--industrial-shadow), 0 2px 8px var(--industrial-shadow-light);
        overflow: hidden;
        position: relative;
    }

    .question-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    }

    /* Question Header */
    .question-header {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border-bottom: 2px solid var(--industrial-teal);
        padding: 1.5rem 2rem;
        position: relative;
        overflow: hidden;
    }

    .question-header::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(20, 184, 166, 0.1));
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        z-index: 1;
    }

    .question-meta {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .question-number {
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--industrial-text-light);
        text-transform: uppercase;
        letter-spacing: 1px;
        margin: 0;
    }

    .required-badge {
        display: inline-flex;
        align-items: center;
        background: var(--industrial-red);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .weight-indicator {
        display: flex;
        align-items: center;
        background: var(--industrial-teal);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .weight-value {
        margin-left: 0.25rem;
    }

    /* Question Body */
    .question-body {
        padding: 2rem;
        background: rgba(45, 45, 45, 0.3);
    }

    .question-text-section {
        margin-bottom: 2rem;
    }

    .question-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--industrial-text-light);
        line-height: 1.4;
        margin-bottom: 1rem;
    }

    .help-text {
        background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-gray) 100%);
        border: 1px solid var(--industrial-border);
        border-left: 4px solid var(--industrial-teal);
        border-radius: 8px;
        padding: 1rem;
        color: var(--industrial-text-muted);
        font-size: 0.9rem;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .help-text i {
        color: var(--industrial-teal);
        margin-top: 0.1rem;
        flex-shrink: 0;
    }

    .help-content {
        line-height: 1.5;
    }

    /* Sections */
    .answer-section {
        margin-bottom: 2rem;
    }

    .comments-section,
    .attachments-section {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--industrial-border);
    }

    .section-label {
        font-size: 1rem;
        font-weight: 600;
        color: var(--industrial-text);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .section-label i {
        color: var(--industrial-teal);
    }

    .comments-textarea {
        background-color: var(--industrial-gray);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        resize: vertical;
    }

    .comments-textarea:focus {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-teal);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
        outline: none;
    }

    .comments-textarea::placeholder {
        color: var(--industrial-text-muted);
    }

    /* Attachment Actions */
    .attachment-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }

    .btn-attachment-action {
        background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
        border: 2px solid var(--industrial-border-light);
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        padding: 0.875rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        min-height: 48px;
    }

    .btn-attachment-action:hover {
        background: var(--industrial-teal);
        border-color: var(--industrial-teal);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--industrial-glow);
    }

    /* Responsive Design */
    @@media (max-width: 1199px) {
        .question-card-container .col-xl-8 {
            max-width: 100%;
        }

        .question-card-container {
            padding: 0.75rem 0;
        }
    }

    @@media (max-width: 991px) {
        .question-card-container {
            padding: 0.5rem 0;
        }

        .question-header {
            padding: 1.25rem 1.75rem;
        }

        .question-body {
            padding: 1.75rem;
        }

        .header-content {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .weight-indicator {
            padding: 0.375rem 0.875rem;
            font-size: 0.85rem;
        }
    }

    @@media (max-width: 767px) {
        .question-card-container {
            padding: 0.25rem 0;
        }

        .question-header {
            padding: 1rem 1.25rem;
        }

        .question-body {
            padding: 1.25rem;
        }

        .header-content {
            flex-direction: column;
            gap: 0.75rem;
            align-items: flex-start;
        }

        .question-number {
            font-size: 1rem;
        }

        .question-title {
            font-size: 1rem;
            line-height: 1.3;
        }

        .help-text {
            padding: 0.875rem;
            font-size: 0.85rem;
        }

        .attachment-actions {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-attachment-action {
            width: 100%;
            justify-content: center;
            padding: 1rem 1.5rem;
        }

        .required-badge {
            padding: 0.2rem 0.6rem;
            font-size: 0.75rem;
        }

        .weight-indicator {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
        }
    }

    @@media (max-width: 575px) {
        .question-card-container {
            padding: 0;
        }

        .question-card {
            border-radius: 12px;
            margin: 0.5rem;
        }

        .question-header {
            padding: 0.875rem 1rem;
        }

        .question-body {
            padding: 1rem;
        }

        .question-title {
            font-size: 0.95rem;
        }

        .help-text {
            padding: 0.75rem;
            font-size: 0.8rem;
        }

        .section-label {
            font-size: 0.9rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .btn-attachment-action {
            min-height: 56px;
            padding: 1.25rem 2rem;
            font-size: 1rem;
        }

        .comments-textarea {
            padding: 1.25rem;
            font-size: 16px; /* Prevents zoom on iOS */
            min-height: 120px;
        }
    }
</style>
