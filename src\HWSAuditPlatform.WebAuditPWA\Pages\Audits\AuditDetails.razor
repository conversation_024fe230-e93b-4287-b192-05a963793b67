@page "/audits/{AuditId}/view"
@page "/audits/{AuditId}/summary"
@attribute [Authorize]
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@inject IAuditApiService AuditService
@inject NavigationManager Navigation
@inject ILogger<AuditDetails> Logger

<PageTitle>Audit Details - HWS Audit Platform PWA</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <LoadingSpinner Message="Loading audit details..." />
    }
    else if (audit != null)
    {
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/audits" class="text-decoration-none">
                                        <i class="fas fa-clipboard-list me-1"></i>
                                        My Audits
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    @audit.AuditTemplate?.TemplateName
                                </li>
                            </ol>
                        </nav>
                        <h1 class="mb-2">
                            <i class="fas fa-eye me-2" style="color: var(--industrial-teal);"></i>
                            Audit Details
                        </h1>
                        <p class="text-muted mb-0">View audit information and results</p>
                    </div>
                    <div class="d-flex align-items-center">
                        @if (audit.OverallStatus == AuditOverallStatus.InProgress)
                        {
                            <button class="btn btn-primary me-2" @onclick="ContinueAudit">
                                <i class="fas fa-edit me-1"></i>
                                Continue Audit
                            </button>
                        }
                        <button class="btn btn-outline-secondary" @onclick="GoBack">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Information -->
        <div class="row mb-4">
            <div class="col-12 col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Audit Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 col-md-6 mb-3">
                                <label class="form-label text-muted">Template</label>
                                <div class="fw-semibold">@audit.AuditTemplate?.TemplateName</div>
                                @if (!string.IsNullOrEmpty(audit.AuditTemplate?.Description))
                                {
                                    <small class="text-muted">@audit.AuditTemplate.Description</small>
                                }
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <span class="badge industrial-badge-@GetStatusBadgeClass() fs-6">
                                        @audit.OverallStatus
                                    </span>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                                <label class="form-label text-muted">Scheduled Date</label>
                                <div class="fw-semibold">@audit.ScheduledDate.ToString("MMMM dd, yyyy")</div>
                                <small class="text-muted">@audit.ScheduledDate.ToString("HH:mm")</small>
                            </div>
                            @if (audit.DueDate.HasValue)
                            {
                                <div class="col-12 col-md-6 mb-3">
                                    <label class="form-label text-muted">Due Date</label>
                                    <div class="fw-semibold @(IsOverdue() ? "industrial-text-danger" : "")">
                                        @audit.DueDate.Value.ToString("MMMM dd, yyyy")
                                        @if (IsOverdue())
                                        {
                                            <i class="fas fa-exclamation-triangle ms-1" title="Overdue"></i>
                                        }
                                    </div>
                                    <small class="text-muted">@audit.DueDate.Value.ToString("HH:mm")</small>
                                </div>
                            }
                            <div class="col-12 col-md-6 mb-3">
                                <label class="form-label text-muted">Location</label>
                                <div class="fw-semibold">
                                    @audit.Factory?.FactoryName
                                    @if (audit.Area != null)
                                    {
                                        <span> → @audit.Area.AreaName</span>
                                    }
                                    @if (audit.SubArea != null)
                                    {
                                        <span> → @audit.SubArea.SubAreaName</span>
                                    }
                                </div>
                            </div>
                            @if (audit.AssignedToUser != null)
                            {
                                <div class="col-12 col-md-6 mb-3">
                                    <label class="form-label text-muted">Assigned To</label>
                                    <div class="fw-semibold">@audit.AssignedToUser.FullName</div>
                                    <small class="text-muted">@audit.AssignedToUser.Email</small>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Progress and Score -->
            <div class="col-12 col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Progress & Score
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        @if (audit.OverallScore.HasValue)
                        {
                            <div class="mb-4">
                                <div class="display-4 fw-bold text-@GetScoreClass()">
                                    @audit.OverallScore.Value.ToString("F1")%
                                </div>
                                <div class="text-muted">Overall Score</div>
                            </div>
                        }
                        
                        @if (GetTotalQuestions() > 0)
                        {
                            <div class="mb-3">
                                <div class="progress mb-2" style="height: 12px;">
                                    <div class="progress-bar bg-@GetProgressBarClass()" role="progressbar" 
                                         style="width: @GetProgressPercentage()%"
                                         aria-valuenow="@GetProgressPercentage()" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <div class="text-muted">
                                    @GetAnsweredQuestions() of @GetTotalQuestions() questions answered
                                    (@GetProgressPercentage().ToString("F1")%)
                                </div>
                            </div>
                        }
                        
                        @if (audit.StartedAt.HasValue)
                        {
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-play me-1"></i>
                                    Started: @audit.StartedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                </small>
                            </div>
                        }
                        
                        @if (audit.CompletedAt.HasValue)
                        {
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-check me-1"></i>
                                    Completed: @audit.CompletedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                </small>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions and Answers (if available) -->
        @if (audit.Answers?.Any() == true)
        {
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Questions & Answers
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach (var answer in audit.Answers.OrderBy(a => a.Question?.DisplayOrder))
                            {
                                <div class="border-bottom pb-3 mb-3">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <h6 class="fw-semibold">@answer.Question?.QuestionText</h6>
                                            @if (!string.IsNullOrEmpty(answer.Question?.HelpText))
                                            {
                                                <small class="text-muted d-block mb-2">@answer.Question.HelpText</small>
                                            }
                                        </div>
                                        <div class="col-12 col-md-4 text-md-end">
                                            <span class="badge industrial-badge-@(HasAnswer(answer) ? "success" : "secondary")">
                                                @(HasAnswer(answer) ? "Answered" : "Not Answered")
                                            </span>
                                        </div>
                                    </div>
                                    
                                    @if (HasAnswer(answer))
                                    {
                                        <div class="mt-2">
                                            <strong>Answer: </strong>
                                            @GetAnswerText(answer)
                                        </div>
                                        
                                        @if (!string.IsNullOrEmpty(answer.Comments))
                                        {
                                            <div class="mt-2">
                                                <strong>Comments: </strong>
                                                @answer.Comments
                                            </div>
                                        }
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <EmptyState Title="Audit Not Found"
                   Description="The requested audit could not be found. It may have been deleted or you may not have permission to view it."
                   IconClass="fas fa-exclamation-triangle fa-3x industrial-text-warning"
                   ActionText="Back to Audits"
                   ActionIcon="fas fa-arrow-left"
                   OnActionClick="@GoBack" />
    }
</div>

@code {
    [Parameter] public string AuditId { get; set; } = string.Empty;

    private Audit? audit;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadAuditDetails();
    }

    private async Task LoadAuditDetails()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await AuditService.GetAuditAsync(AuditId);
            if (result.IsSuccess)
            {
                audit = result.Data;
            }
            else
            {
                Logger.LogWarning("Failed to load audit details: {AuditId}, Error: {Error}", AuditId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit details: {AuditId}", AuditId);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ContinueAudit()
    {
        Navigation.NavigateTo($"/audits/{AuditId}/execute");
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/audits");
    }

    private bool IsOverdue()
    {
        return audit?.DueDate.HasValue == true && 
               audit.DueDate.Value < DateTime.Now && 
               audit.OverallStatus != AuditOverallStatus.Closed;
    }

    private string GetStatusBadgeClass()
    {
        if (audit == null) return "secondary";
        
        return audit.OverallStatus switch
        {
            AuditOverallStatus.Scheduled => IsOverdue() ? "danger" : "warning",
            AuditOverallStatus.InProgress => "info",
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.Cancelled => "secondary",
            _ => "secondary"
        };
    }

    private string GetScoreClass()
    {
        if (audit?.OverallScore == null) return "muted";
        
        return audit.OverallScore.Value switch
        {
            >= 90 => "success",
            >= 80 => "info",
            >= 70 => "warning",
            _ => "danger"
        };
    }

    private string GetProgressBarClass()
    {
        var percentage = GetProgressPercentage();
        return percentage switch
        {
            >= 80 => "success",
            >= 50 => "info",
            >= 25 => "warning",
            _ => "danger"
        };
    }

    private int GetTotalQuestions()
    {
        return audit?.Answers?.Count ?? 0;
    }

    private int GetAnsweredQuestions()
    {
        return audit?.Answers?.Count(HasAnswer) ?? 0;
    }

    private double GetProgressPercentage()
    {
        var total = GetTotalQuestions();
        if (total == 0) return 0;
        
        var answered = GetAnsweredQuestions();
        return (double)answered / total * 100;
    }

    private bool HasAnswer(AuditAnswer answer)
    {
        return !string.IsNullOrEmpty(answer.AnswerText) ||
               answer.AnswerBoolean.HasValue ||
               answer.AnswerNumeric.HasValue ||
               answer.AnswerDate.HasValue ||
               answer.SelectedOptionId.HasValue ||
               (answer.GetSelectedOptionIds()?.Any() == true);
    }

    private string GetAnswerText(AuditAnswer answer)
    {
        if (!string.IsNullOrEmpty(answer.AnswerText))
            return answer.AnswerText;
        
        if (answer.AnswerBoolean.HasValue)
            return answer.AnswerBoolean.Value ? "Yes" : "No";
        
        if (answer.AnswerNumeric.HasValue)
            return answer.AnswerNumeric.Value.ToString();
        
        if (answer.AnswerDate.HasValue)
            return answer.AnswerDate.Value.ToString("MMMM dd, yyyy");
        
        if (answer.SelectedOptionId.HasValue)
            return $"Option {answer.SelectedOptionId.Value}"; // TODO: Get actual option text
        
        if (answer.GetSelectedOptionIds()?.Any() == true)
            return $"Options: {string.Join(", ", answer.GetSelectedOptionIds())}"; // TODO: Get actual option texts
        
        return "No answer provided";
    }
}

<style>
    /* Industrial badge classes */
    .industrial-badge-danger {
        background-color: var(--industrial-red) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-info {
        background-color: var(--industrial-teal) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-primary {
        background-color: var(--industrial-blue) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-success {
        background-color: var(--industrial-green) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-warning {
        background-color: var(--industrial-orange) !important;
        color: var(--industrial-text-light);
    }
    .industrial-badge-secondary {
        background-color: var(--industrial-steel) !important;
        color: var(--industrial-text-light);
    }

    /* Industrial text color classes */
    .industrial-text-danger { color: var(--industrial-red) !important; }
    .industrial-text-warning { color: var(--industrial-orange) !important; }

    /* Card styling */
    .card {
        background-color: var(--industrial-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 12px;
        box-shadow: 0 2px 8px var(--industrial-shadow-light);
    }

    .card-header {
        background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-steel) 100%);
        border-bottom: 1px solid var(--industrial-border);
        color: var(--industrial-text-light);
        border-radius: 12px 12px 0 0;
    }

    .card-body {
        background-color: var(--industrial-gray);
        color: var(--industrial-text);
    }
</style>
