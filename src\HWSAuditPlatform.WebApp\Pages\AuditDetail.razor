@page "/audits/{auditId}"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditApiService
@inject ILogger<AuditDetail> Logger
@inject NavigationManager Navigation

<PageTitle>Audit Details - HWS Audit Platform</PageTitle>

<div class="audit-detail-container">
    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading audit details..." />

    @if (!isLoading && audit == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Audit Not Found</h4>
            <p>The requested audit could not be found or you don't have permission to view it.</p>
            <hr>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" @onclick="GoBackToAudits">
                    <i class="bi bi-arrow-left me-2"></i>Back to Audits
                </button>
            </div>
        </div>
    }
    else if (!isLoading && audit != null)
    {
        <!-- Page Header -->
        <div class="page-header mb-4">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/audits" class="text-decoration-none">Audit Management</a>
                            </li>
                            <li class="breadcrumb-item active">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</li>
                        </ol>
                    </nav>
                    <h1 class="mb-2">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</h1>
                    <p class="text-muted mb-0">
                        <i class="bi bi-building me-1"></i>@(audit.Factory?.FactoryName ?? "Unknown Factory")
                        @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                        {
                            <span> • @audit.Area.AreaName</span>
                        }
                    </p>
                </div>
                <div class="d-flex gap-2">
                    @if (audit.OverallStatus == AuditOverallStatus.Scheduled)
                    {
                        <button class="btn btn-success" @onclick="StartAudit" disabled="@isProcessingAction">
                            @if (isProcessingAction)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-play me-2"></i>
                            }
                            Start Audit
                        </button>
                    }
                    @if (audit.OverallStatus == AuditOverallStatus.PendingManagerReview)
                    {
                        <button class="btn btn-warning" @onclick="ReviewAudit">
                            <i class="bi bi-check-circle me-2"></i>Review Audit
                        </button>
                    }
                    <button class="btn btn-outline-secondary" @onclick="GoBackToAudits">
                        <i class="bi bi-arrow-left me-2"></i>Back to List
                    </button>
                </div>
            </div>
        </div>

        <!-- User Feedback Messages -->
        <HWSAuditPlatform.WebApp.Components.Shared.ErrorComponent 
            HasError="@(!string.IsNullOrEmpty(errorMessage))"
            Title="Error"
            Message="@errorMessage"
            AlertType="danger"
            OnDismiss="DismissMessages" />

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>@successMessage
                <button type="button" class="btn-close" @onclick="DismissMessages"></button>
            </div>
        }

        <!-- Audit Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">
                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                @audit.OverallStatus
                            </span>
                        </div>
                        <h6 class="card-title">Status</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2">
                            @if (audit.OverallScore.HasValue)
                            {
                                <span class="badge @GetScoreBadgeClass(audit.OverallScore.Value)">
                                    @audit.OverallScore.Value.ToString("F1")%
                                </span>
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </div>
                        <h6 class="card-title">Score</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 text-primary">
                            @audit.ScheduledDate.ToString("MMM dd")
                        </div>
                        <h6 class="card-title">Scheduled</h6>
                        <small class="text-muted">@audit.ScheduledDate.ToString("yyyy")</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="display-6 mb-2 @(IsOverdue(audit) ? "text-danger" : "text-success")">
                            @if (audit.DueDate.HasValue)
                            {
                                @audit.DueDate.Value.ToString("MMM dd")
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </div>
                        <h6 class="card-title">Due Date</h6>
                        @if (audit.DueDate.HasValue)
                        {
                            <small class="text-muted">@audit.DueDate.Value.ToString("yyyy")</small>
                            @if (IsOverdue(audit))
                            {
                                <div><small class="text-danger"><i class="bi bi-exclamation-triangle"></i> Overdue</small></div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Details -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clipboard-data me-2"></i>Audit Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">Template:</dt>
                                    <dd class="col-sm-7">@(audit.AuditTemplate?.TemplateName ?? "Unknown")</dd>
                                    
                                    <dt class="col-sm-5">Assigned To:</dt>
                                    <dd class="col-sm-7">@(audit.AssignedToUserId ?? "Unassigned")</dd>
                                    
                                    <dt class="col-sm-5">Created:</dt>
                                    <dd class="col-sm-7">@audit.CreatedAt.ToString("MMM dd, yyyy HH:mm")</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">Factory:</dt>
                                    <dd class="col-sm-7">@(audit.Factory?.FactoryName ?? "Unknown")</dd>
                                    
                                    <dt class="col-sm-5">Area:</dt>
                                    <dd class="col-sm-7">@(audit.Area?.AreaName ?? "Not specified")</dd>
                                    
                                    <dt class="col-sm-5">Assignment:</dt>
                                    <dd class="col-sm-7">@audit.AssignmentType</dd>
                                </dl>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(audit.ManagerComments))
                        {
                            <hr>
                            <h6>Manager Comments:</h6>
                            <p class="text-muted">@audit.ManagerComments</p>
                        }
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>Timeline
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6>Created</h6>
                                    <small class="text-muted">@audit.CreatedAt.ToString("MMM dd, yyyy HH:mm")</small>
                                </div>
                            </div>
                            
                            @if (audit.StartedAt.HasValue)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6>Started</h6>
                                        <small class="text-muted">@audit.StartedAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                                    </div>
                                </div>
                            }
                            
                            @if (audit.CompletedAt.HasValue)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6>Completed</h6>
                                        <small class="text-muted">@audit.CompletedAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                                    </div>
                                </div>
                            }
                            
                            @if (audit.ReviewedAt.HasValue)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h6>Reviewed</h6>
                                        <small class="text-muted">@audit.ReviewedAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .audit-detail-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }

    .timeline-content h6 {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
        border-radius: 12px 12px 0 0 !important;
    }
</style>

@code {
    [Parameter] public string AuditId { get; set; } = string.Empty;

    private Audit? audit;
    private bool isLoading = true;
    private bool isProcessingAction = false;
    private string? successMessage = null;
    private string? errorMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadAudit();
    }

    private async Task LoadAudit()
    {
        try
        {
            isLoading = true;
            audit = await AuditApiService.GetAuditByIdAsync(AuditId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit {AuditId}", AuditId);
            ShowErrorMessage("Failed to load audit details. Please try again.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private bool IsOverdue(Audit audit)
    {
        return audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now &&
               audit.OverallStatus != AuditOverallStatus.Closed;
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-secondary",
            AuditOverallStatus.InProgress => "bg-primary",
            AuditOverallStatus.PendingManagerReview => "bg-warning",
            AuditOverallStatus.Closed => "bg-success",
            AuditOverallStatus.Cancelled => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private string GetScoreBadgeClass(decimal score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-primary",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private void StartAudit()
    {
        try
        {
            isProcessingAction = true;
            // TODO: Implement start audit functionality when comprehensive API is available
            ShowErrorMessage("Start Audit functionality requires the comprehensive API client to be implemented.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting audit {AuditId}", AuditId);
            ShowErrorMessage("An error occurred while starting the audit. Please try again.");
        }
        finally
        {
            isProcessingAction = false;
        }
    }

    private void ReviewAudit()
    {
        Navigation.NavigateTo($"/audits/{AuditId}/review");
    }

    private void GoBackToAudits()
    {
        Navigation.NavigateTo("/audits");
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();
    }

    private void DismissMessages()
    {
        successMessage = null;
        errorMessage = null;
        StateHasChanged();
    }
}
