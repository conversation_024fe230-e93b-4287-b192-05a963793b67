using HWSAuditPlatform.Domain.Entities.Templates;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for QuestionAllowedEvidenceType entity
/// </summary>
public class QuestionAllowedEvidenceTypeConfiguration : IEntityTypeConfiguration<QuestionAllowedEvidenceType>
{
    public void Configure(EntityTypeBuilder<QuestionAllowedEvidenceType> builder)
    {
        builder.ToTable("hwsap_tpl_question_allowed_evidence_types");

        // Primary Key
        builder.HasKey(qaet => qaet.Id);

        // Properties
        builder.Property(qaet => qaet.QuestionId)
            .IsRequired();

        builder.Property(qaet => qaet.EvidenceType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(qaet => qaet.DisplayOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(qaet => qaet.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Auditable properties
        builder.Property(qaet => qaet.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(qaet => qaet.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(qaet => qaet.QuestionId)
            .HasDatabaseName("IX_QuestionAllowedEvidenceTypes_QuestionId");

        builder.HasIndex(qaet => new { qaet.QuestionId, qaet.EvidenceType })
            .IsUnique()
            .HasDatabaseName("UQ_QuestionAllowedEvidenceTypes_QuestionId_EvidenceType");

        builder.HasIndex(qaet => new { qaet.QuestionId, qaet.DisplayOrder })
            .HasDatabaseName("IX_QuestionAllowedEvidenceTypes_QuestionId_DisplayOrder");

        // Relationships
        builder.HasOne(qaet => qaet.Question)
            .WithMany(q => q.AllowedEvidenceTypes)
            .HasForeignKey(qaet => qaet.QuestionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(qaet => qaet.DomainEvents);
    }
}
