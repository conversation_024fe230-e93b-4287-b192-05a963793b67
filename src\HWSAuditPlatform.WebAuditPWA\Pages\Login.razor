@page "/login"
@using HWSAuditPlatform.WebAuditPWA.Models
@using System.ComponentModel.DataAnnotations
@inject IAuthenticationService AuthService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject ILogger<Login> Logger

<div class="container-fluid h-100 d-flex align-items-center justify-content-center">
    <div class="row w-100">
        <div class="col-12 col-md-6 col-lg-4 mx-auto">
            <div class="card">
                <div class="card-header text-center">
                    <h2 class="mb-0">HWS Audit Platform</h2>
                    <p class="text-muted mb-0">Audit Execution PWA</p>
                </div>
                <div class="card-body">
                    <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                        <DataAnnotationsValidator />
                        
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @errorMessage
                            </div>
                        }

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <InputText id="username" class="form-control" @bind-Value="loginModel.Username" 
                                          placeholder="Enter your username" disabled="@isLoading" />
                            </div>
                            <ValidationMessage For="@(() => loginModel.Username)" />
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <InputText id="password" type="password" class="form-control" @bind-Value="loginModel.Password" 
                                          placeholder="Enter your password" disabled="@isLoading" />
                            </div>
                            <ValidationMessage For="@(() => loginModel.Password)" />
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="loading-spinner me-2"></span>
                                    <span>Signing In...</span>
                                }
                                else
                                {
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    <span>Sign In</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        Secure authentication powered by Active Directory
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginRequest loginModel = new();
    private string? errorMessage;
    private bool isLoading = false;

    [Parameter]
    [SupplyParameterFromQuery]
    public string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var isAuthenticated = await AuthService.IsAuthenticatedAsync();
        if (isAuthenticated)
        {
            var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
            Navigation.NavigateTo(redirectUrl);
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = null;
            StateHasChanged();

            Logger.LogInformation("Attempting login for user: {Username}", loginModel.Username);

            var result = await AuthService.LoginAsync(loginModel.Username, loginModel.Password);

            if (result.IsSuccess)
            {
                var userInfo = new UserInfo
                {
                    UserId = loginModel.Username, // This should be the actual user ID from the response
                    Username = result.Username,
                    Email = result.Email,
                    FullName = result.FullName,
                    Role = result.Role,
                    FactoryId = result.FactoryId,
                    IsActive = true
                };

                AuthStateProvider.MarkUserAsAuthenticated(userInfo);

                Logger.LogInformation("Login successful for user: {Username}", loginModel.Username);
                var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
                Navigation.NavigateTo(redirectUrl);
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "Login failed. Please check your credentials.";
                Logger.LogWarning("Login failed for user: {Username}. Error: {Error}", loginModel.Username, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during login for user: {Username}", loginModel.Username);
            errorMessage = "An unexpected error occurred. Please try again.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}

<style>
    .container-fluid {
        background: linear-gradient(135deg, var(--industrial-dark) 0%, var(--industrial-darker) 100%);
        min-height: 100vh;
    }

    .card {
        box-shadow: 0 8px 32px var(--industrial-shadow-heavy);
        border: 2px solid var(--industrial-border);
        max-width: 400px;
    }

    .input-group-text {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-border);
        color: var(--industrial-teal);
        width: 45px;
        justify-content: center;
    }

    .form-control:focus + .input-group-text,
    .input-group-text + .form-control:focus {
        border-color: var(--industrial-teal);
    }

    .btn-primary {
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .loading-spinner {
        width: 1rem;
        height: 1rem;
        border-width: 2px;
    }

    @@media (max-width: 576px) {
        .card {
            margin: 1rem;
            max-width: none;
        }
    }
</style>
