-- Create the main HWS Audit Platform database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'HWSAuditPlatformDb')
BEGIN
    CREATE DATABASE HWSAuditPlatformDb;
    PRINT 'Created HWSAuditPlatformDb database';
<PERSON>ND
ELSE
BEGIN
    PRINT 'HWSAuditPlatformDb database already exists';
END
GO

-- Create the support database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'HWSAuditPlatformSupport')
BEGIN
    CREATE DATABASE HWSAuditPlatformSupport;
    PRINT 'Created HWSAuditPlatformSupport database';
END
ELSE
BEGIN
    PRINT 'HWSAuditPlatformSupport database already exists';
END
GO

-- Use the main database
USE HWSAuditPlatformDb;
GO

-- Create a dedicated user for the application
IF NOT EXISTS (SELECT name FROM sys.server_principals WHERE name = 'hwsaudit_user')
BEGIN
    CREATE LOGIN hwsaudit_user WITH PASSWORD = 'HWSAudit123!';
    PRINT 'Created hwsaudit_user login';
END
ELSE
BEGIN
    PRINT 'hwsaudit_user login already exists';
END
GO

-- Create user in the main database
IF NOT EXISTS (SELECT name FROM sys.database_principals WHERE name = 'hwsaudit_user')
BEGIN
    CREATE USER hwsaudit_user FOR LOGIN hwsaudit_user;
    ALTER ROLE db_owner ADD MEMBER hwsaudit_user;
    PRINT 'Created hwsaudit_user in HWSAuditPlatformDb and granted db_owner role';
END
ELSE
BEGIN
    PRINT 'hwsaudit_user already exists in HWSAuditPlatformDb';
END
GO

-- Use the support database
USE HWSAuditPlatformSupport;
GO

-- Create user in the support database
IF NOT EXISTS (SELECT name FROM sys.database_principals WHERE name = 'hwsaudit_user')
BEGIN
    CREATE USER hwsaudit_user FOR LOGIN hwsaudit_user;
    ALTER ROLE db_owner ADD MEMBER hwsaudit_user;
    PRINT 'Created hwsaudit_user in HWSAuditPlatformSupport and granted db_owner role';
END
ELSE
BEGIN
    PRINT 'hwsaudit_user already exists in HWSAuditPlatformSupport';
END
GO

PRINT 'Database initialization completed successfully';
