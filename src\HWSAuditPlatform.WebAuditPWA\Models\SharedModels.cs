using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebAuditPWA.Models;

/// <summary>
/// Shared model for audit execution context in PWA
/// </summary>
public class AuditExecutionContextModel
{
    public string AuditId { get; set; } = string.Empty;
    public int AuditTemplateId { get; set; }
    public string AuditTemplateName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public string FactoryName { get; set; } = string.Empty;
    public bool EnableAreaBasedResponsibility { get; set; }
    public bool EnableFindingCategorization { get; set; }
    public List<AreaResponsibilityModel> AreaResponsibilities { get; set; } = new();
    public List<FindingCategorySummaryModel> AvailableCategories { get; set; } = new();
}

/// <summary>
/// Shared model for area responsibility in PWA
/// </summary>
public class AreaResponsibilityModel
{
    public int Id { get; set; }
    public int AreaId { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public ResponsibilityType ResponsibilityType { get; set; }
    public string ResponsibleUserId { get; set; } = string.Empty;
    public string ResponsibleUserFullName { get; set; } = string.Empty;
    public string ResponsibleUserName { get; set; } = string.Empty;
    public int? AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public bool IsTemplateSpecific { get; set; }
    public int Priority { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Shared model for finding assignment in PWA
/// </summary>
public class FindingAssignmentModel
{
    public string FindingId { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }
    public string? ResponsibleUserId { get; set; }
    public string? ResponsibleUserName { get; set; }
    public string? RetrospectiveAnalystUserId { get; set; }
    public string? RetrospectiveAnalystUserName { get; set; }
    public DateTime? DueDate { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
    public string? AssignmentReason { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Shared model for audit completion summary in PWA
/// </summary>
public class AuditCompletionSummaryModel
{
    public string AuditId { get; set; } = string.Empty;
    public int TotalQuestions { get; set; }
    public int AnsweredQuestions { get; set; }
    public int TotalFindings { get; set; }
    public int CategorizedFindings { get; set; }
    public int FindingsWithResponsibleUsers { get; set; }
    public int CriticalFindings { get; set; }
    public int MajorFindings { get; set; }
    public int MinorFindings { get; set; }
    public int ObservationFindings { get; set; }
    public bool IsComplete => AnsweredQuestions == TotalQuestions;
    public double CompletionPercentage => TotalQuestions > 0 ? (double)AnsweredQuestions / TotalQuestions * 100 : 0;
    public double CategorizationPercentage => TotalFindings > 0 ? (double)CategorizedFindings / TotalFindings * 100 : 0;
    public double ResponsibilityAssignmentPercentage => TotalFindings > 0 ? (double)FindingsWithResponsibleUsers / TotalFindings * 100 : 0;
}

/// <summary>
/// Shared model for offline sync status
/// </summary>
public class SyncStatusModel
{
    public bool IsOnline { get; set; }
    public DateTime LastSyncTime { get; set; }
    public int PendingUploads { get; set; }
    public int PendingDownloads { get; set; }
    public List<string> SyncErrors { get; set; } = new();
    public bool HasPendingChanges => PendingUploads > 0 || PendingDownloads > 0;
    public bool HasErrors => SyncErrors.Any();
}

/// <summary>
/// Shared model for PWA notification
/// </summary>
public class PwaNotificationModel
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; } = NotificationType.Info;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsRead { get; set; } = false;
    public string? ActionUrl { get; set; }
    public string? ActionText { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Notification types for PWA
/// </summary>
public enum NotificationType
{
    Info,
    Success,
    Warning,
    Error,
    Assignment,
    Reminder,
    Escalation
}

/// <summary>
/// Shared model for camera capture result
/// </summary>
public class CaptureResultModel
{
    public bool Success { get; set; }
    public string? ImageData { get; set; }
    public string? FileName { get; set; }
    public string? ContentType { get; set; }
    public long FileSize { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CapturedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Shared model for offline audit data
/// </summary>
public class OfflineAuditDataModel
{
    public string AuditId { get; set; } = string.Empty;
    public string AuditTitle { get; set; } = string.Empty;
    public AuditExecutionContextModel Context { get; set; } = new();
    public List<QuestionAnswerModel> Answers { get; set; } = new();
    public List<AttachmentModel> Attachments { get; set; } = new();
    public List<FindingAssignmentModel> FindingAssignments { get; set; } = new();
    public DateTime LastModified { get; set; } = DateTime.UtcNow;
    public bool IsSynced { get; set; } = false;
    public bool IsCompleted { get; set; } = false;
}

/// <summary>
/// Shared model for question answer in offline storage
/// </summary>
public class QuestionAnswerModel
{
    public string QuestionId { get; set; } = string.Empty;
    public string QuestionText { get; set; } = string.Empty;
    public QuestionType QuestionType { get; set; }
    public bool? AnswerBoolean { get; set; }
    public string? AnswerText { get; set; }
    public decimal? AnswerNumeric { get; set; }
    public DateTime? AnswerDate { get; set; }
    public int? SelectedOptionId { get; set; }
    public List<int> SelectedOptionIds { get; set; } = new();
    public List<string> FailureReasons { get; set; } = new();
    public string? Comments { get; set; }
    public DateTime AnsweredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Shared model for attachment in offline storage
/// </summary>
public class AttachmentModel
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string? LocalPath { get; set; }
    public string? Base64Data { get; set; }
    public string QuestionId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsUploaded { get; set; } = false;
    public string? UploadedUrl { get; set; }
}

/// <summary>
/// Shared model for validation result in PWA
/// </summary>
public class ValidationResultModel
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, List<string>> FieldErrors { get; set; } = new();

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    public void AddFieldError(string field, string error)
    {
        if (!FieldErrors.ContainsKey(field))
        {
            FieldErrors[field] = new List<string>();
        }
        FieldErrors[field].Add(error);
        IsValid = false;
    }

    public bool HasErrors => Errors.Any() || FieldErrors.Any();
    public bool HasWarnings => Warnings.Any();
}

/// <summary>
/// Shared model for PWA settings
/// </summary>
public class PwaSettingsModel
{
    public bool OfflineModeEnabled { get; set; } = true;
    public bool AutoSyncEnabled { get; set; } = true;
    public int AutoSyncIntervalMinutes { get; set; } = 15;
    public bool CameraAutoFocusEnabled { get; set; } = true;
    public string CameraQuality { get; set; } = "high"; // "low", "medium", "high"
    public bool NotificationsEnabled { get; set; } = true;
    public bool VibrationEnabled { get; set; } = true;
    public string Theme { get; set; } = "industrial"; // "light", "dark", "industrial"
    public string Language { get; set; } = "en";
    public bool DebugModeEnabled { get; set; } = false;
}
