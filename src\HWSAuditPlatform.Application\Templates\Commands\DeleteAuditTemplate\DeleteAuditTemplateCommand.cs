using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteAuditTemplate;

/// <summary>
/// Command to soft delete an audit template
/// </summary>
public class DeleteAuditTemplateCommand : BaseCommand
{
    /// <summary>
    /// The ID of the audit template to delete
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Record version for optimistic concurrency control
    /// </summary>
    public int RecordVersion { get; set; }

    public DeleteAuditTemplateCommand(int id, int recordVersion)
    {
        Id = id;
        RecordVersion = recordVersion;
    }
}
