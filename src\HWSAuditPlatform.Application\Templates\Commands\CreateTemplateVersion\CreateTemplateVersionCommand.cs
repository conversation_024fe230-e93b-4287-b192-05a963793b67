using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateTemplateVersion;

/// <summary>
/// Command to create a new version of an existing audit template
/// </summary>
public class CreateTemplateVersionCommand : BaseCommand<int>
{
    /// <summary>
    /// The ID of the original template to create a new version from
    /// </summary>
    public int OriginalTemplateId { get; set; }

    /// <summary>
    /// Optional new name for the template version (defaults to original name)
    /// </summary>
    public string? NewTemplateName { get; set; }

    /// <summary>
    /// Optional new description for the template version (defaults to original description)
    /// </summary>
    public string? NewDescription { get; set; }
}
