using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.CreateTemplateVersion;
using HWSAuditPlatform.Application.Templates.Commands.UpdateAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.DeleteAuditTemplate;
using HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestion;
using HWSAuditPlatform.Application.Templates.Commands.UpdateQuestion;
using HWSAuditPlatform.Application.Templates.Commands.DeleteQuestion;
using HWSAuditPlatform.Application.Templates.Commands.AddQuestionOptions;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;
using HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplates;
using HWSAuditPlatform.Application.Templates.Queries.GetTemplateQuestions;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for audit template management operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class TemplatesController : BaseController
{
    public TemplatesController(IMediator mediator, ILogger<TemplatesController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get a paginated list of audit templates
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="searchTerm">Search term to filter templates</param>
    /// <param name="isPublished">Filter by published status</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="canBeUsed">Show only templates that can be used for new audits</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit templates</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditTemplateSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditTemplateSummaryDto>>> GetTemplates(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isPublished = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? canBeUsed = null,
        [FromQuery] string sortBy = "TemplateName",
        [FromQuery] string sortDirection = "asc",
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting audit templates - Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}", 
            pageNumber, pageSize, searchTerm);

        var query = new GetAuditTemplatesQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            SearchTerm = searchTerm,
            IsPublished = isPublished,
            IsActive = isActive,
            CanBeUsed = canBeUsed,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific audit template by ID with full details
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit template details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(AuditTemplateDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditTemplateDto>> GetTemplate(int id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting audit template with ID: {TemplateId}", id);

        var query = new GetAuditTemplateQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new audit template
    /// </summary>
    /// <param name="command">Template creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created template ID</returns>
    [HttpPost]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    public async Task<ActionResult<int>> CreateTemplate(
        CreateAuditTemplateCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating audit template: {TemplateName}", command.TemplateName);

        var templateId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetTemplate), new { id = templateId }, templateId, "Template created successfully");
    }

    /// <summary>
    /// Update an existing audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="command">Template update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> UpdateTemplate(
        int id,
        UpdateAuditTemplateCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating audit template with ID: {TemplateId}", id);

        command.Id = id; // Ensure the ID from the route is used
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Template updated successfully");
    }

    /// <summary>
    /// Soft delete an audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "DevAdminOnly")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> DeleteTemplate(
        int id,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Deleting audit template with ID: {TemplateId}", id);

        var command = new DeleteAuditTemplateCommand(id, recordVersion);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Template deleted successfully");
    }

    /// <summary>
    /// Publish an audit template to make it available for use
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/publish")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> PublishTemplate(int id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Publishing audit template with ID: {TemplateId}", id);

        var command = new PublishTemplateCommand { AuditTemplateId = id };
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Template published successfully");
    }

    /// <summary>
    /// Create a new version of an existing audit template
    /// </summary>
    /// <param name="id">Original template ID</param>
    /// <param name="command">Template version creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created template version ID</returns>
    [HttpPost("{id}/create-version")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)] // Bad request if template is not published
    public async Task<ActionResult<int>> CreateTemplateVersion(
        int id,
        CreateTemplateVersionCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating new version of audit template with ID: {TemplateId}", id);

        command.OriginalTemplateId = id; // Ensure the template ID from the route is used
        var newTemplateId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetTemplate), new { id = newTemplateId }, newTemplateId, "Template version created successfully");
    }

    /// <summary>
    /// Add a question to an audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="command">Question data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created question ID</returns>
    [HttpPost("{id}/questions")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<int>> AddQuestion(
        int id,
        AddQuestionCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Adding question to template with ID: {TemplateId}", id);

        command.AuditTemplateId = id; // Ensure the template ID from the route is used
        var questionId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetTemplateQuestions), new { id }, questionId, "Question added successfully");
    }

    /// <summary>
    /// Get all questions for a specific audit template
    /// </summary>
    /// <param name="id">Template ID</param>
    /// <param name="questionGroupId">Optional filter by question group ID</param>
    /// <param name="includeInactive">Whether to include inactive questions</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of questions</returns>
    [HttpGet("{id}/questions")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<QuestionDto>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<List<QuestionDto>>> GetTemplateQuestions(
        int id,
        [FromQuery] int? questionGroupId = null,
        [FromQuery] bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting questions for template with ID: {TemplateId}", id);

        var query = new GetTemplateQuestionsQuery(id)
        {
            QuestionGroupId = questionGroupId,
            IncludeInactive = includeInactive
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Update an existing question in an audit template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="questionId">Question ID</param>
    /// <param name="command">Question update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPut("{templateId}/questions/{questionId}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> UpdateQuestion(
        int templateId,
        int questionId,
        UpdateQuestionCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating question {QuestionId} in template {TemplateId}", questionId, templateId);

        command.QuestionId = questionId; // Ensure the question ID from the route is used
        command.TemplateId = templateId; // Ensure the template ID from the route is used
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Question updated successfully");
    }

    /// <summary>
    /// Soft delete a question from an audit template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="questionId">Question ID</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpDelete("{templateId}/questions/{questionId}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> DeleteQuestion(
        int templateId,
        int questionId,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Deleting question {QuestionId} from template {TemplateId}", questionId, templateId);

        var command = new DeleteQuestionCommand(questionId, templateId, recordVersion);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Question deleted successfully");
    }

    /// <summary>
    /// Add options to an existing question
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="questionId">Question ID</param>
    /// <param name="command">Question options data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created option IDs</returns>
    [HttpPost("{templateId}/questions/{questionId}/options")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(List<int>), 201)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<List<int>>> AddQuestionOptions(
        int templateId,
        int questionId,
        AddQuestionOptionsCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Adding options to question {QuestionId} in template {TemplateId}", questionId, templateId);

        command.QuestionId = questionId; // Ensure the question ID from the route is used
        command.TemplateId = templateId; // Ensure the template ID from the route is used
        var optionIds = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetTemplateQuestions), new { id = templateId }, optionIds, "Question options added successfully");
    }
}
