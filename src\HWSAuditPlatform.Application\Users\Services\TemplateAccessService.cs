using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Implementation of template access control service
/// </summary>
public class TemplateAccessService : ITemplateAccessService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<TemplateAccessService> _logger;

    public TemplateAccessService(IApplicationDbContext context, ILogger<TemplateAccessService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> HasTemplateAccessAsync(string userId, int templateId, CancellationToken cancellationToken = default)
    {
        // DevAdmin and SystemManager have access to all templates
        var user = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == userId, cancellationToken);

        if (user?.Role.RoleName == UserRole.DevAdmin || user?.Role.RoleName == UserRole.SystemManager)
        {
            return true;
        }

        // Check explicit template access assignment
        var hasAccess = await _context.TemplateAccessAssignments
            .AnyAsync(taa => taa.UserId == userId 
                          && taa.AuditTemplateId == templateId 
                          && taa.IsActive 
                          && (!taa.ExpiresAt.HasValue || taa.ExpiresAt.Value > DateTime.UtcNow), 
                      cancellationToken);

        return hasAccess;
    }

    public async Task<IEnumerable<AuditTemplate>> GetAccessibleTemplatesAsync(string userId, CancellationToken cancellationToken = default)
    {
        // DevAdmin and SystemManager have access to all published templates
        var user = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == userId, cancellationToken);

        if (user?.Role.RoleName == UserRole.DevAdmin || user?.Role.RoleName == UserRole.SystemManager)
        {
            return await _context.AuditTemplates
                .Where(at => at.IsPublished && at.IsActive)
                .ToListAsync(cancellationToken);
        }

        // For other users, get templates they have explicit access to
        var accessibleTemplateIds = await _context.TemplateAccessAssignments
            .Where(taa => taa.UserId == userId 
                       && taa.IsActive 
                       && (!taa.ExpiresAt.HasValue || taa.ExpiresAt.Value > DateTime.UtcNow))
            .Select(taa => taa.AuditTemplateId)
            .ToListAsync(cancellationToken);

        return await _context.AuditTemplates
            .Where(at => accessibleTemplateIds.Contains(at.Id) && at.IsPublished && at.IsActive)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<User>> GetUsersWithTemplateAccessAsync(int templateId, CancellationToken cancellationToken = default)
    {
        // Get users with explicit template access assignments
        var userIds = await _context.TemplateAccessAssignments
            .Where(taa => taa.AuditTemplateId == templateId
                       && taa.IsActive
                       && (!taa.ExpiresAt.HasValue || taa.ExpiresAt.Value > DateTime.UtcNow))
            .Select(taa => taa.UserId)
            .ToListAsync(cancellationToken);

        // Get users with explicit access
        var usersWithExplicitAccess = await _context.Users
            .Include(u => u.Role)
            .Where(u => userIds.Contains(u.AdObjectGuid))
            .ToListAsync(cancellationToken);

        // Get DevAdmin and SystemManager users who have access to all templates
        var adminUsers = await _context.Users
            .Include(u => u.Role)
            .Where(u => u.Role.RoleName == UserRole.DevAdmin || u.Role.RoleName == UserRole.SystemManager)
            .ToListAsync(cancellationToken);

        // Combine and deduplicate users
        var allUsers = usersWithExplicitAccess
            .Concat(adminUsers)
            .GroupBy(u => u.AdObjectGuid)
            .Select(g => g.First())
            .ToList();

        return allUsers;
    }

    public async Task<TemplateAccessAssignment> GrantTemplateAccessAsync(
        string userId, 
        int templateId, 
        string assignedByUserId, 
        string? description = null, 
        DateTime? expiresAt = null,
        CancellationToken cancellationToken = default)
    {
        // Check if access already exists
        var existingAccess = await _context.TemplateAccessAssignments
            .FirstOrDefaultAsync(taa => taa.UserId == userId && taa.AuditTemplateId == templateId, cancellationToken);

        if (existingAccess != null)
        {
            if (existingAccess.IsActive)
            {
                _logger.LogWarning("Template access already exists for user {UserId} and template {TemplateId}", userId, templateId);
                return existingAccess;
            }

            // Reactivate existing access
            existingAccess.IsActive = true;
            existingAccess.ExpiresAt = expiresAt;
            existingAccess.Description = description;
            existingAccess.UpdatedByUserId = assignedByUserId;
            existingAccess.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);
            return existingAccess;
        }

        // Create new access assignment
        var newAccess = TemplateAccessAssignment.Create(userId, templateId, assignedByUserId, description, expiresAt);
        await _context.TemplateAccessAssignments.AddAsync(newAccess, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Granted template access to user {UserId} for template {TemplateId} by {AssignedByUserId}", 
            userId, templateId, assignedByUserId);

        return newAccess;
    }

    public async Task<bool> RevokeTemplateAccessAsync(string userId, int templateId, string revokedByUserId, CancellationToken cancellationToken = default)
    {
        var access = await _context.TemplateAccessAssignments
            .FirstOrDefaultAsync(taa => taa.UserId == userId && taa.AuditTemplateId == templateId && taa.IsActive, cancellationToken);

        if (access == null)
        {
            return false;
        }

        access.Revoke(revokedByUserId);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Revoked template access from user {UserId} for template {TemplateId} by {RevokedByUserId}", 
            userId, templateId, revokedByUserId);

        return true;
    }

    public async Task<bool> HasOrganizationalAccessAsync(
        string userId, 
        int factoryId, 
        int? areaId = null, 
        int? subAreaId = null,
        CancellationToken cancellationToken = default)
    {
        // DevAdmin and SystemManager have access to all organizational scopes
        var user = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == userId, cancellationToken);

        if (user?.Role.RoleName == UserRole.DevAdmin || user?.Role.RoleName == UserRole.SystemManager)
        {
            return true;
        }

        // Check process owner assignments
        var hasAccess = await _context.ProcessOwnerAssignments
            .AnyAsync(poa => poa.ProcessOwnerUserId == userId 
                          && poa.IsActive
                          && (poa.FactoryId == null || poa.FactoryId == factoryId)
                          && (poa.AreaId == null || !areaId.HasValue || poa.AreaId == areaId)
                          && (poa.SubAreaId == null || !subAreaId.HasValue || poa.SubAreaId == subAreaId),
                      cancellationToken);

        return hasAccess;
    }

    public async Task<IEnumerable<ProcessOwnerAssignment>> GetAccessibleScopesAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _context.ProcessOwnerAssignments
            .Include(poa => poa.Factory)
            .Include(poa => poa.Area)
            .Include(poa => poa.SubArea)
            .Where(poa => poa.ProcessOwnerUserId == userId && poa.IsActive)
            .ToListAsync(cancellationToken);
    }
}
