using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAudits;

/// <summary>
/// Query to get a paginated list of audits
/// </summary>
public class GetAuditsQuery : BaseQuery<PaginatedResult<AuditSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term to filter audits
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by audit status
    /// </summary>
    public AuditOverallStatus? Status { get; set; }

    /// <summary>
    /// Filter by assigned user ID
    /// </summary>
    public string? AssignedToUserId { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Filter by audit template ID
    /// </summary>
    public int? AuditTemplateId { get; set; }

    /// <summary>
    /// Filter by scheduled date range - start
    /// </summary>
    public DateTime? ScheduledDateFrom { get; set; }

    /// <summary>
    /// Filter by scheduled date range - end
    /// </summary>
    public DateTime? ScheduledDateTo { get; set; }

    /// <summary>
    /// Filter by due date range - start
    /// </summary>
    public DateTime? DueDateFrom { get; set; }

    /// <summary>
    /// Filter by due date range - end
    /// </summary>
    public DateTime? DueDateTo { get; set; }

    /// <summary>
    /// Show only overdue audits
    /// </summary>
    public bool? IsOverdue { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "ScheduledDate";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
