using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAudit;

/// <summary>
/// Query to get a single audit with full details
/// </summary>
public class GetAuditQuery : BaseQuery<AuditDto>
{
    /// <summary>
    /// The ID of the audit to retrieve
    /// </summary>
    public string Id { get; set; } = string.Empty;

    public GetAuditQuery(string id)
    {
        Id = id;
    }

    public GetAuditQuery() { }
}
