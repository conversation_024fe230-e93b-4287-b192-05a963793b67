using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for ProcessOwnerAssignment entity
/// </summary>
public class ProcessOwnerAssignmentDto : AuditableDto<int>
{
    public string ProcessOwnerUserId { get; set; } = string.Empty;
    public string ProcessOwnerUserName { get; set; } = string.Empty;
    public string ProcessOwnerFullName { get; set; } = string.Empty;
    
    public int? FactoryId { get; set; }
    public string? FactoryName { get; set; }
    
    public int? AreaId { get; set; }
    public string? AreaName { get; set; }
    
    public int? SubAreaId { get; set; }
    public string? SubAreaName { get; set; }
    
    public string AssignedByUserId { get; set; } = string.Empty;
    public string AssignedByUserName { get; set; } = string.Empty;
    public string AssignedByFullName { get; set; } = string.Empty;
    
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public string ScopeDescription { get; set; } = string.Empty;
}

/// <summary>
/// Data Transfer Object for creating ProcessOwnerAssignment
/// </summary>
public class CreateProcessOwnerAssignmentDto
{
    public string ProcessOwnerUserId { get; set; } = string.Empty;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Data Transfer Object for updating ProcessOwnerAssignment
/// </summary>
public class UpdateProcessOwnerAssignmentDto
{
    public int Id { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
}
