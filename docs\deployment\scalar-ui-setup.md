# Scalar UI Setup for HWS Audit Platform API

This document describes the Scalar UI setup for the HWS Audit Platform API documentation.

## Overview

Scalar UI has been configured as the primary API documentation interface, providing a modern and interactive way to explore and test the API endpoints. The traditional Swagger UI is still available as a fallback option.

## Endpoints

### Scalar UI (Primary)
- **URL**: `https://localhost:7490/scalar/v1`
- **Description**: Modern, interactive API documentation with Scalar UI
- **Features**:
  - Purple theme for better visual appeal
  - Built-in authentication support for Bearer tokens
  - Code generation examples for C# HttpClient
  - Search functionality (hotkey: 'k')
  - Download button for OpenAPI spec
  - Clean, modern interface

### Swagger UI (Fallback)
- **URL**: `https://localhost:7490/swagger`
- **Description**: Traditional Swagger UI interface
- **Features**:
  - Request duration display
  - Deep linking support
  - Filtering capabilities
  - Extensions display

### OpenAPI Specification
- **URL**: `https://localhost:7490/swagger/v1/swagger.json`
- **Description**: Raw OpenAPI 3.0 specification in JSON format

## Configuration

The Scalar UI is configured in `Program.cs` with the following features:

```csharp
app.MapScalarApiReference(options =>
{
    options
        .WithTitle("HWS Audit Platform API")
        .WithTheme(ScalarTheme.Purple)
        .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient)
        .WithPreferredScheme("Bearer")
        .WithModels(false)
        .WithDownloadButton(true)
        .WithSearchHotKey("k")
        .WithOpenApiRoutePattern("/swagger/{documentName}/swagger.json");
});
```

## Authentication

The API uses JWT Bearer token authentication. In Scalar UI:

1. Click on the "Authenticate" button
2. Select "Bearer" authentication scheme
3. Enter your JWT token in the format: `Bearer your-jwt-token-here`
4. The token will be automatically included in all API requests

## Features

### Code Generation
Scalar UI provides automatic code generation examples for:
- C# HttpClient (default)
- Other languages available through the interface

### Search
- Use the 'k' hotkey to open the search functionality
- Search through endpoints, parameters, and descriptions

### Download
- Click the download button to get the OpenAPI specification file
- Useful for importing into other tools or generating client SDKs

### Models
- Models are hidden by default for a cleaner interface
- Can be enabled if needed for detailed schema exploration

## Development vs Production

Currently, both Scalar UI and Swagger UI are only available in the Development environment. For production deployments, consider:

1. Enabling documentation for specific environments
2. Adding authentication to the documentation endpoints
3. Customizing the theme and branding

## Customization Options

The Scalar UI can be further customized with:

- **Themes**: Default, Alternate, Purple, Solarized, BluePlanet, Saturn, Kepler, Mars, Moon
- **Authentication**: API Key, OAuth, Basic Auth
- **HTTP Clients**: Various language targets
- **Layout**: Models visibility, sidebar configuration
- **Branding**: Custom logos, colors, and styling

## Troubleshooting

### Common Issues

1. **404 on /scalar**: Ensure you're accessing `/scalar/v1` not just `/scalar`
2. **OpenAPI spec not loading**: Verify that `/swagger/v1/swagger.json` is accessible
3. **Authentication not working**: Check JWT token format and validity

### Logs

Monitor the application logs for Scalar-related requests:
- Scalar UI requests: `GET /scalar/v1`
- OpenAPI spec requests: `GET /swagger/v1/swagger.json`
- Static assets: `GET /scalar/scalar.js`

## Next Steps

Consider these enhancements for the future:

1. **Custom Branding**: Add company logo and custom styling
2. **Environment-specific Configuration**: Different settings for dev/staging/prod
3. **API Versioning**: Support for multiple API versions in the documentation
4. **Interactive Examples**: Pre-filled request examples for common use cases
5. **Rate Limiting Documentation**: Document API rate limits and usage guidelines
