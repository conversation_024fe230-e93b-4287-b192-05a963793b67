using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class PublishTemplateCommandHandlerTests : BaseDbTestClass
{
    private readonly PublishTemplateCommandHandler _handler;

    public PublishTemplateCommandHandlerTests()
    {
        _handler = new PublishTemplateCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidTemplate_ShouldPublishTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync();
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.IsPublished.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithNonExistentTemplate_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = 999 // Non-existent template
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithAlreadyPublishedTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync(isPublished: true);

        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("Template is already published");
    }

    [Fact]
    public async Task Handle_WithTemplateWithoutQuestions_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync(); // No questions

        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("Template must have at least one question to be published");
    }

    [Fact]
    public async Task Handle_ShouldSetUpdatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-current-user";
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var template = await CreateTestTemplateWithQuestionsAsync();
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedByUserId.Should().Be(currentUserId);
    }

    [Fact]
    public async Task Handle_ShouldUpdateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync();
        var originalUpdateTime = template.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.UpdatedAt.Should().BeAfter(originalUpdateTime);
    }

    [Fact]
    public async Task Handle_WithInactiveTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateWithQuestionsAsync(isActive: false);

        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("Template is not active");
    }

    [Fact]
    public async Task Handle_WithTemplateWithInactiveQuestions_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        // Add inactive question
        var inactiveQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Inactive Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = false, // Inactive
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        
        Context.Questions.Add(inactiveQuestion);
        await Context.SaveChangesAsync();
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithMultipleActiveQuestions_ShouldPublishSuccessfully()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        
        // Add multiple active questions
        var questions = new[]
        {
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Question 1",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Question 2",
                QuestionType = QuestionType.ShortText,
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
        
        Context.Questions.AddRange(questions);
        await Context.SaveChangesAsync();
        
        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedTemplate = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == template.Id);
        updatedTemplate.Should().NotBeNull();
        updatedTemplate!.IsPublished.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithMultipleValidationErrors_ShouldThrowInvalidOperationExceptionWithAllErrors()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync(isActive: false); // Inactive template with no questions
        template.IsPublished = true; // Also already published
        Context.AuditTemplates.Update(template);
        await Context.SaveChangesAsync();

        var command = new PublishTemplateCommand
        {
            AuditTemplateId = template.Id
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        // Should contain multiple error messages
        exception.Message.Should().Contain("Cannot publish template:");
        exception.Message.Should().Contain("Template is not active");
        exception.Message.Should().Contain("Template is already published");
        exception.Message.Should().Contain("Template must have at least one question to be published");
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync(bool isActive = true)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = false,
            IsActive = isActive,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }

    private async Task<AuditTemplate> CreateTestTemplateWithQuestionsAsync(
        bool isPublished = false, 
        bool isActive = true)
    {
        var template = await CreateTestTemplateAsync(isActive);
        
        if (!isPublished) // Only add questions if not already published
        {
            var question = new Question
            {
                AuditTemplateId = template.Id,
                QuestionText = "Test Question",
                QuestionType = QuestionType.YesNo,
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            
            Context.Questions.Add(question);
        }
        
        if (isPublished)
        {
            template.IsPublished = true;
            Context.AuditTemplates.Update(template);
        }
        
        await Context.SaveChangesAsync();
        return template;
    }
}
