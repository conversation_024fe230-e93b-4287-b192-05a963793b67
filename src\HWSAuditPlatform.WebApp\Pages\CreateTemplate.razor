@page "/templates/create"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Components.Templates
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject ITemplateApiService TemplateApiService
@inject ILogger<CreateTemplate> Logger
@inject NavigationManager Navigation
@inject IConfiguration Configuration
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Create Protocol - HWS Audit Platform</PageTitle>

@if (skipAuthentication)
{
    <!-- Debug mode: Skip authorization check -->
    <div class="container-fluid mt-4">
        @RenderCreateTemplateContent()
    </div>
}
else
{
    <!-- Normal mode: Require authorization -->
    <AuthorizeView Roles="SystemManager,DevAdmin">
        <Authorized>
            <div class="container-fluid mt-4">
                @RenderCreateTemplateContent()
            </div>
        </Authorized>
        <NotAuthorized>
            <div class="text-center p-4">
                <h3>Access Denied</h3>
                <p>You need System Manager or Dev Admin privileges to create protocols.</p>
                <a href="/templates" class="btn btn-primary">Back to Protocols</a>
            </div>
        </NotAuthorized>
    </AuthorizeView>
}

@code {
    private bool skipAuthentication = false;
    private CreateAuditTemplateRequest templateRequest = new();
    private List<CreateQuestionRequest> questions = new();
    private bool isCreating = false;
    private string? successMessage = null;
    private string? errorMessage = null;
    private int nextQuestionOrder = 1;
    private bool showQuestionForm = false;
    private CreateQuestionRequest currentQuestion = new();
    private int editingQuestionIndex = -1;

    protected override void OnInitialized()
    {
        // Check if we should skip authentication (debug mode)
        skipAuthentication = Configuration.GetValue<bool>("Debug:SkipAuthentication", false);

        // Initialize with default values
        templateRequest.IsActive = true;
    }

    private RenderFragment RenderCreateTemplateContent() => __builder =>
    {
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="mb-1">
                            <i class="bi bi-plus-circle me-2" style="color: var(--industrial-orange);"></i>
                            Create New Protocol
                        </h1>
                        <p class="text-muted mb-0">Design a new audit protocol with custom questions</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary me-2" @onclick="Cancel">
                            <i class="bi bi-arrow-left me-2"></i>Cancel
                        </button>
                        <button class="btn btn-primary" @onclick="SaveTemplate" disabled="@isCreating">
                            @if (isCreating)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-check-circle me-2"></i>
                            }
                            Create Protocol
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>@successMessage
                <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
            </div>
        }

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>@errorMessage
                <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
            </div>
        }

        <div class="row">
            <!-- Template Basic Information -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>Protocol Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="templateName" class="form-label">Protocol Name *</label>
                            <input type="text" class="form-control" id="templateName"
                                   @bind="templateRequest.TemplateName"
                                   placeholder="Enter protocol name..." />
                        </div>

                        <div class="mb-3">
                            <label for="templateDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="templateDescription" rows="4"
                                      @bind="templateRequest.Description"
                                      placeholder="Describe the purpose and scope of this protocol..."></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive"
                                   @bind="templateRequest.IsActive" />
                            <label class="form-check-label" for="isActive">
                                Active Protocol
                            </label>
                            <small class="form-text text-muted d-block">
                                Active protocols can be used to create new audits
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Protocol Stats
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-primary">@questions.Count</div>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-success">@questions.Count(q => q.IsRequired)</div>
                                    <small class="text-muted">Required</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Section -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-question-circle me-2"></i>Questions
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" @onclick="StartAddQuestion">
                            <i class="bi bi-plus me-1"></i>Add Question
                        </button>
                    </div>
                    <div class="card-body">
                        @if (showQuestionForm)
                        {
                            <!-- Question Form -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">@(editingQuestionIndex >= 0 ? $"Edit Question {editingQuestionIndex + 1}" : "Add New Question")</h6>
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="CancelQuestionForm">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>

                                <QuestionFormComponent Question="currentQuestion"
                                                     IsEditMode="@(editingQuestionIndex >= 0)"
                                                     IsSaving="false"
                                                     OnCancel="CancelQuestionForm"
                                                     OnSave="SaveCurrentQuestion" />
                            </div>
                            <hr />
                        }

                        @if (questions.Any())
                        {
                            <div class="questions-list">
                                @for (int i = 0; i < questions.Count; i++)
                                {
                                    var questionIndex = i;
                                    var question = questions[questionIndex];

                                    <div class="question-item mb-3 p-3 border rounded">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Question @(questionIndex + 1)</h6>
                                                <p class="mb-1 text-truncate">@question.QuestionText</p>
                                                <small class="text-muted">Type: @question.QuestionType</small>
                                                @if (question.Options?.Any() == true)
                                                {
                                                    <small class="text-muted"> | @question.Options.Count option(s)</small>
                                                }
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" @onclick="() => EditQuestion(questionIndex)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @onclick="() => RemoveQuestion(questionIndex)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }

                        @if (!showQuestionForm)
                        {
                            <div class="text-center py-4">
                                @if (!questions.Any())
                                {
                                    <i class="bi bi-question-circle display-4 text-muted mb-3"></i>
                                    <h5>No Questions Added</h5>
                                    <p class="text-muted mb-4">Start building your protocol by adding questions.</p>
                                }
                                <button class="btn btn-primary" @onclick="StartAddQuestion">
                                    <i class="bi bi-plus-circle me-2"></i>@(questions.Any() ? "Add Another Question" : "Add First Question")
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    };

    private void StartAddQuestion()
    {
        currentQuestion = new CreateQuestionRequest
        {
            QuestionText = "",
            QuestionType = "ShortText", // Default to ShortText type
            Weight = 1, // Default weight > 0 to pass validation
            SeverityLevel = "", // Optional field
            DisplayOrder = nextQuestionOrder,
            HelpText = "",
            IsRequired = false,
            EvidenceRequired = false,
            IsActive = true,
            AllowedEvidenceTypes = new List<string>(),
            Options = new List<CreateQuestionOptionRequest>()
        };
        editingQuestionIndex = -1;
        showQuestionForm = true;
    }

    private void EditQuestion(int index)
    {
        if (index >= 0 && index < questions.Count)
        {
            // Create a copy of the question for editing
            var originalQuestion = questions[index];
            currentQuestion = new CreateQuestionRequest
            {
                QuestionText = originalQuestion.QuestionText,
                QuestionType = originalQuestion.QuestionType,
                Weight = originalQuestion.Weight,
                SeverityLevel = originalQuestion.SeverityLevel,
                DisplayOrder = originalQuestion.DisplayOrder,
                HelpText = originalQuestion.HelpText,
                IsRequired = originalQuestion.IsRequired,
                EvidenceRequired = originalQuestion.EvidenceRequired,
                IsActive = originalQuestion.IsActive,
                AllowedEvidenceTypes = originalQuestion.AllowedEvidenceTypes,
                Options = originalQuestion.Options?.Select(o => new CreateQuestionOptionRequest
                {
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                }).ToList() ?? new List<CreateQuestionOptionRequest>()
            };
            editingQuestionIndex = index;
            showQuestionForm = true;
        }
    }

    private void RemoveQuestion(int index)
    {
        if (index >= 0 && index < questions.Count)
        {
            questions.RemoveAt(index);

            // Reorder remaining questions
            for (int i = 0; i < questions.Count; i++)
            {
                questions[i].DisplayOrder = i + 1;
            }

            // Update nextQuestionOrder
            nextQuestionOrder = questions.Count + 1;
        }
    }

    private void SaveCurrentQuestion(CreateQuestionRequest questionToSave)
    {
        if (editingQuestionIndex >= 0)
        {
            // Update existing question
            questions[editingQuestionIndex] = questionToSave;
        }
        else
        {
            // Add new question
            questionToSave.DisplayOrder = nextQuestionOrder++;
            questions.Add(questionToSave);
        }

        CancelQuestionForm();
    }

    private void CancelQuestionForm()
    {
        showQuestionForm = false;
        editingQuestionIndex = -1;
        currentQuestion = new CreateQuestionRequest();
    }

    private async Task SaveTemplate()
    {
        if (string.IsNullOrWhiteSpace(templateRequest.TemplateName))
        {
            ShowErrorMessage("Protocol name is required.");
            return;
        }

        if (!questions.Any())
        {
            ShowErrorMessage("At least one question is required.");
            return;
        }

        // Validate questions
        for (int i = 0; i < questions.Count; i++)
        {
            var question = questions[i];
            if (string.IsNullOrWhiteSpace(question.QuestionText))
            {
                ShowErrorMessage($"Question {i + 1} text is required.");
                return;
            }
            if (string.IsNullOrWhiteSpace(question.QuestionType))
            {
                ShowErrorMessage($"Question {i + 1} type is required.");
                return;
            }
        }

        try
        {
            isCreating = true;

            // Create the template
            var templateId = await TemplateApiService.CreateTemplateAsync(templateRequest);
            if (templateId.HasValue)
            {
                // Add questions to the template
                bool allQuestionsAdded = true;
                foreach (var question in questions)
                {
                    var questionId = await TemplateApiService.AddQuestionToTemplateAsync(templateId.Value, question);
                    if (!questionId.HasValue)
                    {
                        allQuestionsAdded = false;
                        break;
                    }
                }

                if (allQuestionsAdded)
                {
                    ShowSuccessMessage("Protocol created successfully!");
                    await Task.Delay(2000); // Show success message briefly
                    Navigation.NavigateTo("/templates");
                }
                else
                {
                    ShowErrorMessage("Protocol was created but some questions failed to save. Please edit the protocol to add missing questions.");
                }
            }
            else
            {
                ShowErrorMessage("Failed to create protocol. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating template");
            ShowErrorMessage("An error occurred while creating the protocol. Please try again.");
        }
        finally
        {
            isCreating = false;
        }
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/templates");
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
        StateHasChanged();
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();
    }
}

<style>
    .question-item {
        background-color: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border) !important;
        transition: all 0.3s ease;
    }

    .question-item:hover {
        border-color: var(--industrial-orange) !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    .questions-list {
        max-height: 600px;
        overflow-y: auto;
        padding-right: 10px;
    }

    .questions-list::-webkit-scrollbar {
        width: 6px;
    }

    .questions-list::-webkit-scrollbar-track {
        background: var(--industrial-darker);
        border-radius: 3px;
    }

    .questions-list::-webkit-scrollbar-thumb {
        background: var(--industrial-steel);
        border-radius: 3px;
    }

    .questions-list::-webkit-scrollbar-thumb:hover {
        background: var(--industrial-orange);
    }

    .form-label {
        color: var(--industrial-text);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .form-check-label {
        color: var(--industrial-text);
        font-weight: 500;
    }

    .btn-link {
        color: var(--industrial-orange);
        text-decoration: none;
        font-weight: 500;
    }

    .btn-link:hover {
        color: #fbb040;
        text-decoration: underline;
    }

    .card {
        border: 1px solid var(--industrial-border);
        background-color: var(--industrial-light-gray);
    }

    .card-header {
        background-color: var(--industrial-darker);
        border-bottom: 1px solid var(--industrial-border);
        color: var(--industrial-text);
    }

    .card-body {
        background-color: var(--industrial-light-gray);
    }

    .btn-primary {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
    }

    .btn-primary:hover {
        background-color: #fbb040;
        border-color: #fbb040;
        color: white;
    }

    .btn-outline-secondary {
        border-color: var(--industrial-steel);
        color: var(--industrial-steel);
    }

    .btn-outline-secondary:hover {
        background-color: var(--industrial-steel);
        border-color: var(--industrial-steel);
        color: white;
    }

    .btn-outline-primary {
        border-color: var(--industrial-orange);
        color: var(--industrial-orange);
    }

    .btn-outline-primary:hover {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
        color: white;
    }

    .alert-success {
        background-color: rgba(40, 167, 69, 0.1);
        border-color: #28a745;
        color: #155724;
    }

    .alert-danger {
        background-color: rgba(220, 53, 69, 0.1);
        border-color: #dc3545;
        color: #721c24;
    }

    .text-muted {
        color: var(--industrial-text-muted) !important;
    }

    h1, h5, h6 {
        color: var(--industrial-text);
    }

    .display-1 {
        color: var(--industrial-steel);
    }

    /* Form Controls Styling */
    .form-control,
    .form-select {
        background-color: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 6px;
    }

    .form-control:focus,
    .form-select:focus {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-orange);
        color: var(--industrial-text);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }

    .form-control::placeholder {
        color: var(--industrial-text-muted);
    }

    .form-select option {
        background-color: var(--industrial-light-gray);
        color: var(--industrial-text);
    }

    .form-text {
        color: var(--industrial-text-muted);
        font-size: 0.875rem;
    }

    .form-check-input {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-border);
    }

    .form-check-input:checked {
        background-color: var(--industrial-orange);
        border-color: var(--industrial-orange);
    }

    .form-check-input:focus {
        border-color: var(--industrial-orange);
        box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
    }
</style>
