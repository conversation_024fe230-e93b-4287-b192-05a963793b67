namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Summary model for Factory entity
/// </summary>
public class FactorySummary
{
    public int Id { get; set; }
    public string FactoryName { get; set; } = string.Empty;
    public string? LocationName { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Summary model for Area entity
/// </summary>
public class AreaSummary
{
    public int Id { get; set; }
    public string AreaName { get; set; } = string.Empty;
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Summary model for SubArea entity
/// </summary>
public class SubAreaSummary
{
    public int Id { get; set; }
    public string SubAreaName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Summary model for User entity
/// </summary>
public class UserSummary
{
    public string Id { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string? FactoryName { get; set; }
    public string AdObjectGuid { get; set; } = string.Empty;
}

/// <summary>
/// Summary model for UserGroup entity
/// </summary>
public class UserGroupSummary
{
    public string Id { get; set; } = string.Empty;
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public int MemberCount { get; set; }
}
