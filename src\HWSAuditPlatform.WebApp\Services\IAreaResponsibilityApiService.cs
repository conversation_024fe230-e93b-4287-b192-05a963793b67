using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for area responsibility API operations
/// </summary>
public interface IAreaResponsibilityApiService
{
    /// <summary>
    /// Gets area responsibilities with optional filtering
    /// </summary>
    /// <param name="filter">Filter criteria</param>
    /// <returns>List of area responsibilities</returns>
    Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesAsync(AreaResponsibilityFilterModel? filter = null);

    /// <summary>
    /// Gets a specific area responsibility by ID
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <returns>Area responsibility details</returns>
    Task<AreaResponsibilityModel?> GetAreaResponsibilityAsync(string id);

    /// <summary>
    /// Creates a new area responsibility assignment
    /// </summary>
    /// <param name="model">Area responsibility creation data</param>
    /// <returns>Created area responsibility</returns>
    Task<AreaResponsibilityModel> CreateAreaResponsibilityAsync(CreateAreaResponsibilityModel model);

    /// <summary>
    /// Updates an existing area responsibility
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <param name="model">Updated area responsibility data</param>
    /// <returns>Updated area responsibility</returns>
    Task<AreaResponsibilityModel> UpdateAreaResponsibilityAsync(string id, CreateAreaResponsibilityModel model);

    /// <summary>
    /// Deletes an area responsibility
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <returns>Success status</returns>
    Task<bool> DeleteAreaResponsibilityAsync(string id);

    /// <summary>
    /// Gets available responsibility types
    /// </summary>
    /// <returns>List of responsibility types</returns>
    Task<List<ResponsibilityTypeModel>> GetResponsibilityTypesAsync();

    /// <summary>
    /// Gets area responsibilities for a specific area
    /// </summary>
    /// <param name="areaId">Area ID</param>
    /// <param name="auditTemplateId">Optional audit template ID</param>
    /// <returns>List of area responsibilities</returns>
    Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesForAreaAsync(int areaId, int? auditTemplateId = null);

    /// <summary>
    /// Gets area responsibilities for a specific user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of area responsibilities</returns>
    Task<List<AreaResponsibilityModel>> GetAreaResponsibilitiesForUserAsync(string userId);
}
