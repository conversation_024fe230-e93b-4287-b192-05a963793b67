using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents the assignment of a Process Owner to organizational areas.
/// Maps to the ProcessOwnerAssignments table in the database.
/// </summary>
public class ProcessOwnerAssignment : AuditableEntity<int>
{
    /// <summary>
    /// The Process Owner user ID (AD ObjectGUID FK)
    /// </summary>
    [Required]
    [MaxLength(36)]
    public string ProcessOwnerUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the Process Owner user
    /// </summary>
    public virtual User ProcessOwnerUser { get; set; } = null!;

    /// <summary>
    /// Optional: Factory assignment (if null, applies to all factories)
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the factory
    /// </summary>
    public virtual Factory? Factory { get; set; }

    /// <summary>
    /// Optional: Area assignment (if null, applies to all areas in factory)
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Navigation property for the area
    /// </summary>
    public virtual Area? Area { get; set; }

    /// <summary>
    /// Optional: SubArea assignment (if null, applies to all subareas in area)
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Navigation property for the subarea
    /// </summary>
    public virtual SubArea? SubArea { get; set; }

    /// <summary>
    /// User who made this assignment (typically a SystemManager or DevAdmin)
    /// </summary>
    [Required]
    [MaxLength(36)]
    public string AssignedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user who made the assignment
    /// </summary>
    public virtual User AssignedByUser { get; set; } = null!;

    /// <summary>
    /// Indicates if this assignment is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Optional description or notes about this assignment
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Creates a new process owner assignment
    /// </summary>
    /// <param name="processOwnerUserId">Process owner user ID</param>
    /// <param name="assignedByUserId">User making the assignment</param>
    /// <param name="factoryId">Optional factory ID</param>
    /// <param name="areaId">Optional area ID</param>
    /// <param name="subAreaId">Optional subarea ID</param>
    /// <param name="description">Optional description</param>
    /// <returns>New ProcessOwnerAssignment instance</returns>
    public static ProcessOwnerAssignment Create(
        string processOwnerUserId,
        string assignedByUserId,
        int? factoryId = null,
        int? areaId = null,
        int? subAreaId = null,
        string? description = null)
    {
        return new ProcessOwnerAssignment
        {
            ProcessOwnerUserId = processOwnerUserId,
            AssignedByUserId = assignedByUserId,
            FactoryId = factoryId,
            AreaId = areaId,
            SubAreaId = subAreaId,
            Description = description,
            IsActive = true,
            CreatedByUserId = assignedByUserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Validates the assignment hierarchy (SubArea requires Area, Area requires Factory)
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValidHierarchy()
    {
        // If SubArea is specified, Area must be specified
        if (SubAreaId.HasValue && !AreaId.HasValue)
            return false;

        // If Area is specified, Factory must be specified
        if (AreaId.HasValue && !FactoryId.HasValue)
            return false;

        return true;
    }

    /// <summary>
    /// Gets the scope description for this assignment
    /// </summary>
    public string GetScopeDescription()
    {
        if (SubAreaId.HasValue)
            return $"SubArea: {SubArea?.SubAreaName ?? "Unknown"}";
        
        if (AreaId.HasValue)
            return $"Area: {Area?.AreaName ?? "Unknown"}";
        
        if (FactoryId.HasValue)
            return $"Factory: {Factory?.FactoryName ?? "Unknown"}";
        
        return "All Factories";
    }
}
