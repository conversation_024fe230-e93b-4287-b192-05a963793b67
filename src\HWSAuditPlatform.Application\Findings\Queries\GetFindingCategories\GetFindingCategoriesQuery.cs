using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFindingCategories;

/// <summary>
/// Query to get finding categories with optional filtering
/// </summary>
public class GetFindingCategoriesQuery : BaseQuery<List<FindingCategoryDto>>
{
    public int? AuditTemplateId { get; set; }
    public bool? IsActive { get; set; } = true;
    public bool IncludeUsageStatistics { get; set; } = false;
}
