@using HWSAuditPlatform.WebAuditPWA.Models
@inject IOfflineStorageService OfflineStorage
@inject IJSRuntime JSRuntime
@inject ILogger<AttachmentDisplay> Logger

<div class="attachment-display">
    @if (AttachmentIds?.Any() == true)
    {
        <div class="attachment-grid">
            @foreach (var attachmentId in AttachmentIds)
            {
                <div class="attachment-item" @key="attachmentId">
                    @if (attachmentPreviews.ContainsKey(attachmentId))
                    {
                        var preview = attachmentPreviews[attachmentId];
                        <div class="attachment-preview">
                            @if (preview.IsImage)
                            {
                                <div class="image-preview" @onclick="() => ShowImageModal(preview)">
                                    <img src="@preview.DataUrl" alt="@preview.FileName" class="preview-image" />
                                    <div class="image-overlay">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="file-preview">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            }
                            
                            <div class="attachment-info">
                                <div class="attachment-name" title="@preview.FileName">
                                    @preview.FileName
                                </div>
                                <div class="attachment-meta">
                                    <small class="text-muted">
                                        @FormatFileSize(preview.Size)
                                        @if (!preview.IsUploaded)
                                        {
                                            <span class="badge bg-warning ms-1">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success ms-1">
                                                <i class="fas fa-check me-1"></i>Uploaded
                                            </span>
                                        }
                                    </small>
                                </div>
                            </div>
                            
                            @if (AllowRemove)
                            {
                                <button class="btn btn-outline-danger btn-sm attachment-remove" 
                                        @onclick="() => RemoveAttachment(attachmentId)"
                                        title="Remove attachment">
                                    <i class="fas fa-trash"></i>
                                </button>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="attachment-loading">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <small class="text-muted ms-2">Loading...</small>
                        </div>
                    }
                </div>
            }
        </div>
    }
    else
    {
        <div class="no-attachments text-muted text-center py-3">
            <i class="fas fa-paperclip fa-2x mb-2"></i>
            <p class="mb-0">No attachments</p>
        </div>
    }
</div>

<!-- Image Modal -->
@if (selectedImagePreview != null)
{
    <div class="image-modal" @onclick="CloseImageModal">
        <div class="image-modal-content" @onclick:stopPropagation="true">
            <div class="image-modal-header">
                <h5 class="modal-title">@selectedImagePreview.FileName</h5>
                <button class="btn-close" @onclick="CloseImageModal">&times;</button>
            </div>
            <div class="image-modal-body">
                <img src="@selectedImagePreview.DataUrl" alt="@selectedImagePreview.FileName" class="modal-image" />
            </div>
            <div class="image-modal-footer">
                <div class="image-info">
                    <small class="text-muted">
                        Size: @FormatFileSize(selectedImagePreview.Size) | 
                        Status: @(selectedImagePreview.IsUploaded ? "Uploaded" : "Pending Upload")
                    </small>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .attachment-display {
        width: 100%;
    }

    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .attachment-item {
        position: relative;
        border: 1px solid var(--industrial-border);
        border-radius: 8px;
        overflow: hidden;
        background: var(--industrial-light-gray);
        transition: all 0.2s ease;
    }

    .attachment-item:hover {
        border-color: var(--industrial-teal);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .attachment-preview {
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .image-preview {
        position: relative;
        height: 150px;
        overflow: hidden;
        cursor: pointer;
        background: #000;
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
    }

    .image-preview:hover .preview-image {
        transform: scale(1.05);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
        color: white;
        font-size: 1.5rem;
    }

    .image-preview:hover .image-overlay {
        opacity: 1;
    }

    .file-preview {
        height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--industrial-steel);
        background: var(--industrial-gray);
    }

    .attachment-info {
        padding: 0.75rem;
        flex: 1;
    }

    .attachment-name {
        font-weight: 600;
        color: var(--industrial-text);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0.25rem;
    }

    .attachment-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .attachment-remove {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--industrial-border);
    }

    .attachment-loading {
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: var(--industrial-steel);
    }

    .no-attachments {
        color: var(--industrial-steel);
    }

    /* Image Modal Styles */
    .image-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 1rem;
    }

    .image-modal-content {
        background: var(--industrial-gray);
        border-radius: 12px;
        max-width: 90vw;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .image-modal-header {
        padding: 1rem;
        border-bottom: 1px solid var(--industrial-border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--industrial-steel);
    }

    .image-modal-header h5 {
        margin: 0;
        color: var(--industrial-text);
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 1rem;
    }

    .btn-close {
        background: none;
        border: none;
        color: var(--industrial-text);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
    }

    .btn-close:hover {
        background: var(--industrial-border);
    }

    .image-modal-body {
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        max-height: 70vh;
        overflow: auto;
    }

    .modal-image {
        max-width: 100%;
        max-height: 100%;
        border-radius: 8px;
    }

    .image-modal-footer {
        padding: 1rem;
        border-top: 1px solid var(--industrial-border);
        background: var(--industrial-steel);
    }

    .image-info {
        text-align: center;
    }

    @@media (max-width: 768px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 0.75rem;
        }

        .image-preview {
            height: 120px;
        }

        .attachment-info {
            padding: 0.5rem;
        }

        .image-modal-content {
            width: 100vw;
            height: 100vh;
            max-width: none;
            max-height: none;
            border-radius: 0;
        }
    }
</style>

@code {
    [Parameter] public List<string>? AttachmentIds { get; set; }
    [Parameter] public bool AllowRemove { get; set; } = true;
    [Parameter] public EventCallback<string> OnRemoveAttachment { get; set; }

    private Dictionary<string, AttachmentPreview> attachmentPreviews = new();
    private AttachmentPreview? selectedImagePreview;

    protected override async Task OnParametersSetAsync()
    {
        if (AttachmentIds?.Any() == true)
        {
            await LoadAttachmentPreviews();
        }
    }

    private async Task LoadAttachmentPreviews()
    {
        if (AttachmentIds == null) return;

        foreach (var attachmentId in AttachmentIds)
        {
            if (!attachmentPreviews.ContainsKey(attachmentId))
            {
                try
                {
                    var attachment = await GetAttachmentById(attachmentId);
                    if (attachment != null)
                    {
                        var preview = new AttachmentPreview
                        {
                            Id = attachment.Id,
                            FileName = attachment.FileName,
                            Size = attachment.Size,
                            IsUploaded = attachment.IsUploaded,
                            IsImage = IsImageFile(attachment.ContentType),
                            DataUrl = attachment.IsImage ? CreateDataUrl(attachment.Data, attachment.ContentType) : null
                        };

                        attachmentPreviews[attachmentId] = preview;
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error loading attachment preview: {AttachmentId}", attachmentId);
                }
            }
        }

        StateHasChanged();
    }

    private async Task<OfflineAttachment?> GetAttachmentById(string attachmentId)
    {
        // First try to get from offline storage
        var allAttachments = await OfflineStorage.GetPendingAttachmentsAsync();
        var offlineAttachment = allAttachments.FirstOrDefault(a => a.Id == attachmentId);
        
        if (offlineAttachment != null)
        {
            return offlineAttachment;
        }

        // TODO: If not found offline, try to get from API (for uploaded attachments)
        // This would require implementing an API endpoint to get attachment metadata
        
        return null;
    }

    private bool IsImageFile(string contentType)
    {
        return contentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    }

    private string CreateDataUrl(byte[] data, string contentType)
    {
        var base64 = Convert.ToBase64String(data);
        return $"data:{contentType};base64,{base64}";
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
        return $"{bytes / (1024 * 1024 * 1024):F1} GB";
    }

    private void ShowImageModal(AttachmentPreview preview)
    {
        if (preview.IsImage)
        {
            selectedImagePreview = preview;
            StateHasChanged();
        }
    }

    private void CloseImageModal()
    {
        selectedImagePreview = null;
        StateHasChanged();
    }

    private async Task RemoveAttachment(string attachmentId)
    {
        if (OnRemoveAttachment.HasDelegate)
        {
            await OnRemoveAttachment.InvokeAsync(attachmentId);
        }

        // Remove from local preview cache
        attachmentPreviews.Remove(attachmentId);
        StateHasChanged();
    }

    private class AttachmentPreview
    {
        public string Id { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long Size { get; set; }
        public bool IsUploaded { get; set; }
        public bool IsImage { get; set; }
        public string? DataUrl { get; set; }
    }
}
