using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Queries.GetUserGroup;

/// <summary>
/// Handler for GetUserGroupQuery
/// </summary>
public class GetUserGroupQueryHandler : BaseQueryHandler<GetUserGroupQuery, UserGroupDto>
{
    private readonly IApplicationDbContext _context;

    public GetUserGroupQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<UserGroupDto> Handle(GetUserGroupQuery request, CancellationToken cancellationToken)
    {
        var userGroup = await _context.UserGroups
            .Include(ug => ug.Members)
                .ThenInclude(ugm => ugm.User)
                    .ThenInclude(u => u.Role)
            .Include(ug => ug.CreatedByUser)
            .FirstOrDefaultAsync(ug => ug.Id == request.Id, cancellationToken);

        if (userGroup == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Users.UserGroup), request.Id);
        }

        return new UserGroupDto
        {
            Id = userGroup.Id,
            GroupName = userGroup.GroupName,
            Description = userGroup.Description,
            AdObjectGuid = userGroup.AdObjectGuid,
            IsAdSynced = userGroup.IsAdSynced,
            CreatedByUserName = userGroup.CreatedByUser?.Username,
            Members = userGroup.Members.Select(ugm => new UserSummaryDto
            {
                Id = ugm.User.Id,
                Username = ugm.User.Username,
                FullName = $"{ugm.User.FirstName} {ugm.User.LastName}".Trim(),
                Email = ugm.User.Email,
                Role = ugm.User.Role.RoleName,
                IsActive = ugm.User.IsActive,
                FactoryName = ugm.User.Factory?.FactoryName,
                AdObjectGuid = ugm.User.AdObjectGuid
            }).ToList(),
            CreatedAt = userGroup.CreatedAt,
            UpdatedAt = userGroup.UpdatedAt,
            RecordVersion = userGroup.RecordVersion,
            CreatedByUserId = userGroup.CreatedByUserId,
            UpdatedByUserId = userGroup.UpdatedByUserId
        };
    }
}
