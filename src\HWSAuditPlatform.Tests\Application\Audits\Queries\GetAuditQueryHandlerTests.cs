using FluentAssertions;
using HWSAuditPlatform.Application.Audits.Queries.GetAudit;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Queries;

public class GetAuditQueryHandlerTests : BaseDbTestClass
{
    private readonly GetAuditQueryHandler _handler;

    public GetAuditQueryHandlerTests()
    {
        _handler = new GetAuditQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(audit.Id);
        result.AuditTemplateId.Should().Be(audit.AuditTemplateId);
        result.AuditTemplateName.Should().Be("Test Template");
        result.AssignmentType.Should().Be(audit.AssignmentType);
        result.AssignedToUserId.Should().Be(audit.AssignedToUserId);
        result.ScheduledDate.Should().Be(audit.ScheduledDate);
        result.DueDate.Should().Be(audit.DueDate);
        result.OverallStatus.Should().Be(audit.OverallStatus);
        result.FactoryId.Should().Be(audit.FactoryId);
        result.FactoryName.Should().Be("Test Factory");
        result.AreaId.Should().Be(audit.AreaId);
        result.AreaName.Should().Be("Test Area");
        result.SubAreaId.Should().Be(audit.SubAreaId);
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetAuditQuery("non-existent-id");

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithGroupAssignment_ShouldReturnGroupInfo()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(assignmentType: AssignmentType.GroupAny);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.AssignmentType.Should().Be(AssignmentType.GroupAny);
        result.AssignedToUserGroupId.Should().Be("test-group-id");
        result.AssignedToUserId.Should().BeNull();
    }

    [Fact]
    public async Task Handle_WithCompletedAudit_ShouldReturnCompletionDetails()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.Closed,
            completedAt: DateTime.UtcNow.AddDays(-1),
            overallScore: 85.5m);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OverallStatus.Should().Be(AuditOverallStatus.Closed);
        result.CompletedAt.Should().NotBeNull();
        result.OverallScore.Should().Be(85.5m);
    }

    [Fact]
    public async Task Handle_WithStartedAudit_ShouldReturnStartTime()
    {
        // Arrange
        await SeedTestDataAsync();
        var startTime = DateTime.UtcNow.AddHours(-2);
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.InProgress,
            startedAt: startTime);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OverallStatus.Should().Be(AuditOverallStatus.InProgress);
        result.StartedAt.Should().Be(startTime);
    }

    [Fact]
    public async Task Handle_WithReviewedAudit_ShouldReturnReviewDetails()
    {
        // Arrange
        await SeedTestDataAsync();
        var reviewTime = DateTime.UtcNow.AddDays(-1);
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.Closed,
            reviewedAt: reviewTime,
            reviewedByUserId: "reviewer-id");
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ReviewedAt.Should().Be(reviewTime);
        result.ReviewedByUserId.Should().Be("reviewer-id");
    }

    [Fact]
    public async Task Handle_WithManagerComments_ShouldReturnComments()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(managerComments: "Excellent work on this audit");
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ManagerComments.Should().Be("Excellent work on this audit");
    }

    [Fact]
    public async Task Handle_ShouldReturnAuditTrailFields()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CreatedAt.Should().Be(audit.CreatedAt);
        result.UpdatedAt.Should().Be(audit.UpdatedAt);
        result.CreatedByUserId.Should().Be(audit.CreatedByUserId);
        result.UpdatedByUserId.Should().Be(audit.UpdatedByUserId);
        result.RecordVersion.Should().Be(audit.RecordVersion);
    }

    [Fact]
    public async Task Handle_WithTemplateQuestions_ShouldReturnQuestionsAndOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithQuestionsAsync();

        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Questions.Should().NotBeEmpty();
        result.Questions.Should().HaveCount(2);

        // Verify question details
        var yesNoQuestion = result.Questions.First(q => q.QuestionType == QuestionType.YesNo);
        yesNoQuestion.QuestionText.Should().Be("Is the equipment in good condition?");
        yesNoQuestion.IsRequired.Should().BeTrue();
        yesNoQuestion.Options.Should().BeEmpty(); // Yes/No questions don't have options

        var singleSelectQuestion = result.Questions.First(q => q.QuestionType == QuestionType.SingleSelect);
        singleSelectQuestion.QuestionText.Should().Be("What is the condition level?");
        singleSelectQuestion.Options.Should().NotBeEmpty();
        singleSelectQuestion.Options.Should().HaveCount(3);
        singleSelectQuestion.Options.Should().BeInAscendingOrder(o => o.DisplayOrder);

        // Verify option details
        var firstOption = singleSelectQuestion.Options.First();
        firstOption.OptionText.Should().Be("Excellent");
        firstOption.OptionValue.Should().Be("excellent");
    }

    [Fact]
    public async Task Handle_WithQuestionGroups_ShouldReturnGroupedQuestions()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithQuestionGroupsAsync();

        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.QuestionGroups.Should().NotBeEmpty();
        result.QuestionGroups.Should().HaveCount(1);

        var group = result.QuestionGroups.First();
        group.GroupName.Should().Be("Safety Questions");
        group.Questions.Should().NotBeEmpty();
        group.Questions.Should().HaveCount(1);

        var groupQuestion = group.Questions.First();
        groupQuestion.QuestionText.Should().Be("Are safety protocols followed?");
    }

    private async Task<Audit> CreateTestAuditAsync(
        AuditOverallStatus status = AuditOverallStatus.Scheduled,
        AssignmentType assignmentType = AssignmentType.Individual,
        DateTime? startedAt = null,
        DateTime? completedAt = null,
        DateTime? reviewedAt = null,
        string? reviewedByUserId = null,
        decimal? overallScore = null,
        string? managerComments = null)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = assignmentType,
            AssignedToUserId = assignmentType == AssignmentType.Individual ? "test-user-id" : null,
            AssignedToUserGroupId = assignmentType == AssignmentType.GroupAny ? "test-group-id" : null,
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            StartedAt = startedAt,
            CompletedAt = completedAt,
            ReviewedAt = reviewedAt,
            ReviewedByUserId = reviewedByUserId,
            OverallStatus = status,
            OverallScore = overallScore,
            ManagerComments = managerComments,
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }

    private async Task<Audit> CreateTestAuditWithQuestionsAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template with Questions",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        // Create questions
        var yesNoQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "Is the equipment in good condition?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var singleSelectQuestion = new Question
        {
            AuditTemplateId = template.Id,
            QuestionText = "What is the condition level?",
            QuestionType = QuestionType.SingleSelect,
            DisplayOrder = 2,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.AddRange(yesNoQuestion, singleSelectQuestion);
        await Context.SaveChangesAsync();

        // Create options for single select question
        var options = new[]
        {
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Excellent",
                OptionValue = "excellent",
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Good",
                OptionValue = "good",
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new QuestionOption
            {
                QuestionId = singleSelectQuestion.Id,
                OptionText = "Poor",
                OptionValue = "poor",
                DisplayOrder = 3,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }
        };

        Context.QuestionOptions.AddRange(options);
        await Context.SaveChangesAsync();

        // Create audit
        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }

    private async Task<Audit> CreateTestAuditWithQuestionGroupsAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template with Groups",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        // Create question group
        var questionGroup = new QuestionGroup
        {
            AuditTemplateId = template.Id,
            GroupName = "Safety Questions",
            Description = "Questions related to safety protocols",
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.QuestionGroups.Add(questionGroup);
        await Context.SaveChangesAsync();

        // Create question in group
        var question = new Question
        {
            AuditTemplateId = template.Id,
            QuestionGroupId = questionGroup.Id,
            QuestionText = "Are safety protocols followed?",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();

        // Create audit
        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }
}
