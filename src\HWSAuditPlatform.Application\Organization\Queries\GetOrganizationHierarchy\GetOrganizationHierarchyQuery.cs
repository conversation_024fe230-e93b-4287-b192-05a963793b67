using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;

namespace HWSAuditPlatform.Application.Organization.Queries.GetOrganizationHierarchy;

/// <summary>
/// Query to get the complete organizational hierarchy
/// </summary>
public class GetOrganizationHierarchyQuery : BaseQuery<List<LocationDto>>
{
    public int? LocationId { get; set; }
    public bool IncludeInactive { get; set; } = false;
}
