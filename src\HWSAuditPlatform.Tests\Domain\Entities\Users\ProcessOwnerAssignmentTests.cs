using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Users;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Entities.Users;

public class ProcessOwnerAssignmentTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateAssignment()
    {
        // Arrange
        var processOwnerUserId = "user-123";
        var assignedByUserId = "admin-456";
        var factoryId = 1;
        var areaId = 2;
        var subAreaId = 3;
        var description = "Test assignment";

        // Act
        var assignment = ProcessOwnerAssignment.Create(
            processOwnerUserId,
            assignedByUserId,
            factoryId,
            areaId,
            subAreaId,
            description);

        // Assert
        assignment.Should().NotBeNull();
        assignment.ProcessOwnerUserId.Should().Be(processOwnerUserId);
        assignment.AssignedByUserId.Should().Be(assignedByUserId);
        assignment.FactoryId.Should().Be(factoryId);
        assignment.AreaId.Should().Be(areaId);
        assignment.SubAreaId.Should().Be(subAreaId);
        assignment.Description.Should().Be(description);
        assignment.IsActive.Should().BeTrue();
        assignment.CreatedByUserId.Should().Be(assignedByUserId);
        assignment.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        assignment.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Create_WithMinimalParameters_ShouldCreateAssignment()
    {
        // Arrange
        var processOwnerUserId = "user-123";
        var assignedByUserId = "admin-456";

        // Act
        var assignment = ProcessOwnerAssignment.Create(processOwnerUserId, assignedByUserId);

        // Assert
        assignment.Should().NotBeNull();
        assignment.ProcessOwnerUserId.Should().Be(processOwnerUserId);
        assignment.AssignedByUserId.Should().Be(assignedByUserId);
        assignment.FactoryId.Should().BeNull();
        assignment.AreaId.Should().BeNull();
        assignment.SubAreaId.Should().BeNull();
        assignment.Description.Should().BeNull();
        assignment.IsActive.Should().BeTrue();
    }

    [Theory]
    [InlineData(null, null, null, true)] // All null - valid
    [InlineData(1, null, null, true)] // Factory only - valid
    [InlineData(1, 2, null, true)] // Factory and Area - valid
    [InlineData(1, 2, 3, true)] // Factory, Area, and SubArea - valid
    [InlineData(null, 2, null, false)] // Area without Factory - invalid
    [InlineData(null, null, 3, false)] // SubArea without Area - invalid
    [InlineData(1, null, 3, false)] // SubArea without Area - invalid
    public void IsValidHierarchy_WithDifferentCombinations_ShouldReturnExpectedResult(
        int? factoryId, int? areaId, int? subAreaId, bool expectedValid)
    {
        // Arrange
        var assignment = ProcessOwnerAssignment.Create("user-123", "admin-456", factoryId, areaId, subAreaId);

        // Act
        var isValid = assignment.IsValidHierarchy();

        // Assert
        isValid.Should().Be(expectedValid);
    }

    [Fact]
    public void GetScopeDescription_WithSubArea_ShouldReturnSubAreaDescription()
    {
        // Arrange
        var assignment = ProcessOwnerAssignment.Create("user-123", "admin-456", 1, 2, 3);
        assignment.SubArea = new HWSAuditPlatform.Domain.Entities.Organization.SubArea { SubAreaName = "Test SubArea" };

        // Act
        var description = assignment.GetScopeDescription();

        // Assert
        description.Should().Be("SubArea: Test SubArea");
    }

    [Fact]
    public void GetScopeDescription_WithAreaOnly_ShouldReturnAreaDescription()
    {
        // Arrange
        var assignment = ProcessOwnerAssignment.Create("user-123", "admin-456", 1, 2);
        assignment.Area = new HWSAuditPlatform.Domain.Entities.Organization.Area { AreaName = "Test Area" };

        // Act
        var description = assignment.GetScopeDescription();

        // Assert
        description.Should().Be("Area: Test Area");
    }

    [Fact]
    public void GetScopeDescription_WithFactoryOnly_ShouldReturnFactoryDescription()
    {
        // Arrange
        var assignment = ProcessOwnerAssignment.Create("user-123", "admin-456", 1);
        assignment.Factory = new HWSAuditPlatform.Domain.Entities.Organization.Factory { FactoryName = "Test Factory" };

        // Act
        var description = assignment.GetScopeDescription();

        // Assert
        description.Should().Be("Factory: Test Factory");
    }

    [Fact]
    public void GetScopeDescription_WithNoScope_ShouldReturnAllFactories()
    {
        // Arrange
        var assignment = ProcessOwnerAssignment.Create("user-123", "admin-456");

        // Act
        var description = assignment.GetScopeDescription();

        // Assert
        description.Should().Be("All Factories");
    }
}
