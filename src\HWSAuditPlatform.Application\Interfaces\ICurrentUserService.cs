using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Service for accessing current user information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user's ID (CUID)
    /// </summary>
    string? UserId { get; }

    /// <summary>
    /// Gets the current user's username
    /// </summary>
    string? Username { get; }

    /// <summary>
    /// Gets the current user's email
    /// </summary>
    string? Email { get; }

    /// <summary>
    /// Gets the current user's role
    /// </summary>
    UserRole? Role { get; }

    /// <summary>
    /// Gets the current user's primary factory ID
    /// </summary>
    int? FactoryId { get; }

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Checks if the current user has the specified role
    /// </summary>
    /// <param name="role">The role to check</param>
    /// <returns>True if the user has the role, false otherwise</returns>
    bool HasRole(UserRole role);

    /// <summary>
    /// Checks if the current user has any of the specified roles
    /// </summary>
    /// <param name="roles">The roles to check</param>
    /// <returns>True if the user has any of the roles, false otherwise</returns>
    bool HasAnyRole(params UserRole[] roles);
}
