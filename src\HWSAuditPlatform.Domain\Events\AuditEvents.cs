using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Events;

/// <summary>
/// Domain event raised when an audit is created
/// </summary>
public class AuditCreatedEvent : IDomainEvent
{
    public string AuditId { get; }
    public int AuditTemplateId { get; }
    public string? AssignedToUserId { get; }
    public string? AssignedToUserGroupId { get; }
    public DateTime ScheduledDate { get; }
    public DateTime OccurredOn { get; }

    public AuditCreatedEvent(string auditId, int auditTemplateId, string? assignedToUserId, string? assignedToUserGroupId, DateTime scheduledDate)
    {
        AuditId = auditId;
        AuditTemplateId = auditTemplateId;
        AssignedToUserId = assignedToUserId;
        AssignedToUserGroupId = assignedToUserGroupId;
        ScheduledDate = scheduledDate;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an audit status changes
/// </summary>
public class AuditStatusChangedEvent : IDomainEvent
{
    public string AuditId { get; }
    public AuditOverallStatus PreviousStatus { get; }
    public AuditOverallStatus NewStatus { get; }
    public string? ChangedByUserId { get; }
    public DateTime OccurredOn { get; }

    public AuditStatusChangedEvent(string auditId, AuditOverallStatus previousStatus, AuditOverallStatus newStatus, string? changedByUserId)
    {
        AuditId = auditId;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        ChangedByUserId = changedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an audit is submitted
/// </summary>
public class AuditSubmittedEvent : IDomainEvent
{
    public string AuditId { get; }
    public string SubmittedByUserId { get; }
    public DateTime SubmittedAt { get; }
    public DateTime OccurredOn { get; }

    public AuditSubmittedEvent(string auditId, string submittedByUserId, DateTime submittedAt)
    {
        AuditId = auditId;
        SubmittedByUserId = submittedByUserId;
        SubmittedAt = submittedAt;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an audit is assigned to a user
/// </summary>
public class AuditAssignedEvent : IDomainEvent
{
    public string AuditId { get; }
    public string AssignedToUserId { get; }
    public string? AssignedByUserId { get; }
    public DateTime OccurredOn { get; }

    public AuditAssignedEvent(string auditId, string assignedToUserId, string? assignedByUserId)
    {
        AuditId = auditId;
        AssignedToUserId = assignedToUserId;
        AssignedByUserId = assignedByUserId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an audit is started
/// </summary>
public class AuditStartedEvent : IDomainEvent
{
    public string AuditId { get; }
    public string StartedByUserId { get; }
    public DateTime StartedAt { get; }
    public DateTime OccurredOn { get; }

    public AuditStartedEvent(string auditId, string startedByUserId, DateTime startedAt)
    {
        AuditId = auditId;
        StartedByUserId = startedByUserId;
        StartedAt = startedAt;
        OccurredOn = DateTime.UtcNow;
    }
}
