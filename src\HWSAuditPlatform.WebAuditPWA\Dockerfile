# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["src/HWSAuditPlatform.WebAuditPWA/HWSAuditPlatform.WebAuditPWA.csproj", "HWSAuditPlatform.WebAuditPWA/"]
COPY ["src/HWSAuditPlatform.Domain/HWSAuditPlatform.Domain.csproj", "HWSAuditPlatform.Domain/"]

# Restore dependencies
RUN dotnet restore "HWSAuditPlatform.WebAuditPWA/HWSAuditPlatform.WebAuditPWA.csproj"

# Copy source code
COPY src/ .

# Build the application
WORKDIR "/src/HWSAuditPlatform.WebAuditPWA"
RUN dotnet build "HWSAuditPlatform.WebAuditPWA.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "HWSAuditPlatform.WebAuditPWA.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html

# Copy published files
COPY --from=publish /app/publish/wwwroot .

# Copy nginx configuration
COPY src/HWSAuditPlatform.WebAuditPWA/nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
