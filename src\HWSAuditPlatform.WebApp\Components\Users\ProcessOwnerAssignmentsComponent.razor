@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Users
@inject IUserApiService UserApiService
@inject ILogger<ProcessOwnerAssignmentsComponent> Logger

<div class="process-owner-assignments">
    <div class="page-header">
        <h1>Process Owner Assignments</h1>
        <p class="text-muted">Manage process owner assignments to plants, areas, and sub-areas</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading process owner assignments..." />

    @if (!isLoading)
    {
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people me-2"></i>Process Owners
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (processOwners?.Any() == true)
                        {
                            <div class="list-group">
                                @foreach (var owner in processOwners)
                                {
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">@owner.FullName</h6>
                                            <small class="text-muted">@owner.Email</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ManageAssignments(owner.Id)">
                                            <i class="bi bi-gear"></i> Manage
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="bi bi-person-x text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No process owners found</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-building me-2"></i>Assignment Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Feature Under Development</strong>
                            <p class="mb-0 mt-2">
                                Process owner assignment functionality will be implemented when the comprehensive API client is available.
                                This will include:
                            </p>
                            <ul class="mt-2 mb-0">
                                <li>Assign process owners to specific plants/factories</li>
                                <li>Assign process owners to areas within plants</li>
                                <li>Assign process owners to sub-areas</li>
                                <li>View and manage assignment hierarchies</li>
                                <li>Bulk assignment operations</li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <h6>Quick Stats</h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="stat-card">
                                        <div class="stat-number">@(processOwners?.Count() ?? 0)</div>
                                        <div class="stat-label">Process Owners</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-card">
                                        <div class="stat-number">0</div>
                                        <div class="stat-label">Assignments</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .process-owner-assignments {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .stat-card {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #007bff;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .list-group-item {
        border: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
        border-radius: 8px;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
    }
</style>

@code {
    private bool isLoading = true;
    private IEnumerable<User>? processOwners;

    protected override async Task OnInitializedAsync()
    {
        await LoadProcessOwners();
    }

    private async Task LoadProcessOwners()
    {
        try
        {
            isLoading = true;
            
            var allUsers = await UserApiService.GetUsersAsync();
            if (allUsers != null)
            {
                // Filter for process owners (assuming role ID 3 is ProcessOwner)
                processOwners = allUsers.Where(u => u.RoleId == 3 && u.IsActive);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading process owners");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ManageAssignments(string userId)
    {
        // TODO: Navigate to assignment management page
        Logger.LogInformation("Manage assignments requested for user {UserId}", userId);
    }
}
