using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAuditAnswer;

/// <summary>
/// Command to submit an answer to an audit question
/// </summary>
public class SubmitAuditAnswerCommand : BaseCommand<string>
{
    /// <summary>
    /// The ID of the audit being answered
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// The ID of the question being answered
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// The answer value provided by the auditor
    /// </summary>
    public string? AnswerValue { get; set; }

    /// <summary>
    /// Whether the question is marked as Not Applicable
    /// </summary>
    public bool IsNotApplicable { get; set; }

    /// <summary>
    /// Optional comments from the auditor
    /// </summary>
    public string? Comments { get; set; }

    /// <summary>
    /// Selected option IDs for multi-select questions
    /// </summary>
    public List<int> SelectedOptionIds { get; set; } = [];



    /// <summary>
    /// Failure reason texts if the answer indicates a failure (for PWA compatibility)
    /// </summary>
    public List<string> FailureReasonTexts { get; set; } = [];

    /// <summary>
    /// Severity level if the answer indicates a failure
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }
}
