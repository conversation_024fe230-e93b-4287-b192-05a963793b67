# Finding Categorization API

This document describes the API endpoints for managing finding categorization in the HWS Audit Platform.

## Overview

Finding categorization allows organizations to classify findings and discrepancies into predefined categories for better reporting and analysis. Categories are defined per audit template and can be customized based on organizational needs.

## Endpoints

### Get Finding Categories

**GET** `/api/findings/categories`

Retrieves finding categories with optional filtering.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `auditTemplateId` | integer | No | Filter by specific audit template ID |
| `isActive` | boolean | No | Filter by active status (default: true) |
| `includeUsageStatistics` | boolean | No | Include finding count statistics (default: false) |

#### Response

```json
[
  {
    "id": 1,
    "categoryName": "Safety Violation",
    "description": "Issues related to safety procedures and equipment",
    "auditTemplateId": 5,
    "auditTemplateName": "Safety Audit v2",
    "displayOrder": 1,
    "isActive": true,
    "colorCode": "#FF5733",
    "iconName": "warning-triangle",
    "requiresDocumentation": true,
    "findingCount": 15,
    "openFindingCount": 8,
    "isUsable": true,
    "createdAt": "2025-01-25T10:00:00Z",
    "updatedAt": "2025-01-25T10:00:00Z",
    "recordVersion": 1,
    "createdByUserId": "12345678-1234-1234-1234-123456789012",
    "updatedByUserId": null
  }
]
```

### Create Finding Category

**POST** `/api/findings/categories`

Creates a new finding category for an audit template.

#### Request Body

```json
{
  "categoryName": "Safety Violation",
  "description": "Issues related to safety procedures and equipment",
  "auditTemplateId": 5,
  "displayOrder": 1,
  "isActive": true,
  "colorCode": "#FF5733",
  "iconName": "warning-triangle",
  "requiresDocumentation": true
}
```

#### Request Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `categoryName` | string | Yes | Name of the category (max 100 characters) |
| `description` | string | No | Optional description (max 500 characters) |
| `auditTemplateId` | integer | Yes | ID of the audit template |
| `displayOrder` | integer | No | Display order for UI (default: 0) |
| `isActive` | boolean | No | Whether the category is active (default: true) |
| `colorCode` | string | No | Hex color code for UI display (e.g., #FF5733) |
| `iconName` | string | No | Icon identifier for UI display (max 50 characters) |
| `requiresDocumentation` | boolean | No | Whether findings in this category require additional documentation (default: false) |

#### Response

Returns the created finding category object (same structure as GET response).

### Update Finding Category

**PUT** `/api/findings/categories/{id}`

Updates an existing finding category.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | ID of the finding category |

#### Request Body

Same structure as Create Finding Category.

### Delete Finding Category

**DELETE** `/api/findings/categories/{id}`

Deletes a finding category. Categories with associated findings cannot be deleted.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | ID of the finding category |

#### Response

```json
{
  "success": true,
  "message": "Finding category deleted successfully"
}
```

### Assign Finding Category

**PUT** `/api/findings/{findingId}/category`

Assigns or removes a category from a finding.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `findingId` | string | Yes | CUID of the finding |

#### Request Body

```json
{
  "findingCategoryId": 1
}
```

To remove a category, set `findingCategoryId` to `null`:

```json
{
  "findingCategoryId": null
}
```

#### Request Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `findingCategoryId` | integer | No | ID of the category to assign (null to remove category) |

#### Response

```json
{
  "success": true,
  "message": "Finding category assigned successfully"
}
```

### Get Category Statistics

**GET** `/api/findings/categories/{auditTemplateId}/statistics`

Gets usage statistics for finding categories within an audit template.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `auditTemplateId` | integer | Yes | ID of the audit template |

#### Response

```json
{
  "totalCategories": 5,
  "activeCategories": 4,
  "totalFindings": 50,
  "categorizedFindings": 45,
  "uncategorizedFindings": 5,
  "categoryUsage": {
    "1": {
      "categoryId": 1,
      "categoryName": "Safety Violation",
      "findingCount": 15,
      "openFindingCount": 8,
      "closedFindingCount": 7,
      "usagePercentage": 30.0
    },
    "2": {
      "categoryId": 2,
      "categoryName": "Quality Issue",
      "findingCount": 20,
      "openFindingCount": 12,
      "closedFindingCount": 8,
      "usagePercentage": 40.0
    }
  }
}
```

## Business Rules

1. **Template Enablement**: Finding categorization must be enabled at the audit template level (`EnableFindingCategorization = true`).

2. **Unique Names**: Category names must be unique within each audit template.

3. **Template Scope**: Categories are scoped to specific audit templates and cannot be shared across templates.

4. **Optional Assignment**: Categorizing findings is optional unless specifically required by business rules.

5. **Color Validation**: Color codes must be valid hex colors (e.g., #FF5733).

6. **Deletion Restrictions**: Categories with associated findings cannot be deleted (must be deactivated instead).

## Error Responses

### 400 Bad Request
```json
{
  "type": "ValidationError",
  "title": "Validation failed",
  "status": 400,
  "errors": {
    "CategoryName": ["Category name is required."],
    "ColorCode": ["Color code must be a valid hex color (e.g., #FF5733)."]
  }
}
```

### 404 Not Found
```json
{
  "type": "NotFound",
  "title": "Resource not found",
  "status": 404,
  "detail": "Finding category with ID 999 was not found."
}
```

### 409 Conflict
```json
{
  "type": "ValidationError",
  "title": "Business rule violation",
  "status": 409,
  "detail": "A category with the name 'Safety Violation' already exists for this template."
}
```
