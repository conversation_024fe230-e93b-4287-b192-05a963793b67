# Database-Driven Role Assignment

## Overview

The HWS Audit Platform authentication system has been enhanced to prioritize database-stored user roles over Active Directory group mappings. This provides more granular control over user permissions and allows for role management independent of AD group structures.

## Authentication Flow

### 1. Database-First Role Lookup

The authentication process now follows this priority order:

1. **Database Lookup**: Check if user exists in `hwsap_usr_users` table by `AdObjectGuid`
2. **Use Database Role**: If user exists, use their assigned `RoleId` from the database
3. **AD Group Fallback**: If user doesn't exist, fall back to AD group mapping via `hwsap_usr_ad_group_role_mappings`
4. **Auto-Create User**: Optionally create user record in database for future database-first lookups

### 2. Implementation Details

#### AdRoleMappingService Enhancement

The `AdRoleMappingService` now provides two key methods:

```csharp
// Get role with database-first priority
public async Task<UserRole> GetUserRoleAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default)

// Get role and existence status for conditional user creation
public async Task<(UserRole role, bool userExistsInDb)> GetUserRoleWithExistenceAsync(string adObjectGuid, IEnumerable<string> adGroupMemberships, CancellationToken cancellationToken = default)
```

#### AuthController Enhancement

The login process now:

1. Authenticates user against Active Directory
2. Retrieves user information from AD
3. Checks database for existing user by `AdObjectGuid`
4. Uses database role if user exists, otherwise maps AD groups to roles
5. Optionally creates user record in database for future lookups
6. Generates JWT token with determined role

## Benefits

### 1. Granular Role Management

- **Individual Control**: Assign specific roles to users independent of AD groups
- **Override Capability**: Database roles take precedence over AD group mappings
- **Audit Trail**: Track role changes through database audit fields

### 2. Operational Flexibility

- **Role Updates**: Change user roles without modifying AD group memberships
- **Temporary Permissions**: Grant temporary elevated permissions through database
- **Multi-Factory Support**: Assign users to specific factories with appropriate roles

### 3. Backward Compatibility

- **Seamless Fallback**: Users not in database still authenticate via AD group mapping
- **Gradual Migration**: Existing AD group mappings continue to work
- **No Breaking Changes**: Existing authentication flows remain functional

## Database Schema

### User Table Structure

```sql
Table hwsap_usr_users {
  Id varchar(25) [pk]                    -- CUID primary key
  Username varchar(256) [not null]       -- AD username
  RoleId integer [not null]              -- Foreign key to roles table
  AdObjectGuid varchar(36) [not null]    -- AD Object GUID for lookup
  -- ... other fields
}
```

### Role Assignment Priority

1. **Database Role** (`hwsap_usr_users.RoleId` → `hwsap_usr_roles.RoleName`)
2. **AD Group Mapping** (`hwsap_usr_ad_group_role_mappings.AdGroupName` → `hwsap_usr_roles.RoleName`)
3. **Default Role** (`Viewer` if no mappings found)

## Configuration

### Auto-User Creation

The system automatically creates user records during authentication when:

- User authenticates successfully against AD
- User doesn't exist in database
- Role can be determined from AD group mappings

This ensures future logins use database-first role lookup.

### Error Handling

- **Database Errors**: Authentication continues with AD group mapping if database operations fail
- **Role Not Found**: Falls back to default `Viewer` role
- **Logging**: All role assignment decisions are logged for audit purposes

## Migration Strategy

### Phase 1: Parallel Operation
- Deploy database-first authentication
- Existing users continue using AD group mappings
- New users automatically get database records

### Phase 2: User Synchronization
- Run AD sync command to populate database with existing users
- Verify role assignments match expected permissions
- Update roles as needed through database

### Phase 3: Database-Primary
- Most users now have database records
- AD group mappings serve as fallback only
- Role management primarily through database

## Management

### Role Assignment

```csharp
// Create user with specific role
var command = new CreateUserCommand
{
    Username = "john.doe",
    Role = UserRole.ProcessOwner,
    AdObjectGuid = "550e8400-e29b-41d4-a716-************"
};
```

### Role Updates

```csharp
// Update user role
var command = new UpdateUserCommand
{
    Id = "c1234567890123456789012345",
    Role = UserRole.SystemManager
};
```

### Bulk Synchronization

```csharp
// Sync all users from AD
var command = new SyncUsersFromAdCommand
{
    DryRun = false
};
```

## Security Considerations

### Access Control

- **Database Roles**: Take precedence over AD groups for authorization
- **JWT Claims**: Include role from database-first lookup
- **Policy Enforcement**: Authorization policies use database-determined roles

### Audit Trail

- **Role Changes**: Tracked through `UpdatedAt` and `UpdatedByUserId` fields
- **Authentication Events**: Logged with role assignment source (database vs AD)
- **Sync Operations**: Full audit log of AD synchronization activities

## Troubleshooting

### Common Issues

1. **Role Mismatch**: User has different role in database vs AD groups
   - **Solution**: Update database role or AD group membership as appropriate

2. **User Not Found**: Authentication fails for existing AD user
   - **Solution**: Run AD sync or manually create user record

3. **Permission Denied**: User can't access expected resources
   - **Solution**: Verify database role assignment and authorization policies

### Diagnostic Commands

```bash
# Test AD connectivity
curl -X GET "https://api.hwsaudit.local/api/v1/auth/test-ad"

# Check current user info
curl -X GET "https://api.hwsaudit.local/api/v1/auth/me" -H "Authorization: Bearer {token}"

# Sync users from AD
curl -X POST "https://api.hwsaudit.local/api/v1/users/sync-from-ad"
```
