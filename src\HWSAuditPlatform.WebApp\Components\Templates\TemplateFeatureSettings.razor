@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject ITemplateApiService TemplateApiService
@inject ILogger<TemplateFeatureSettings> Logger

<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-cogs me-2"></i>
            Advanced Features
        </h6>
    </div>
    <div class="card-body">
        @if (Template != null)
        {
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-setting">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Area-Based Responsibility</h6>
                                <small class="text-muted">Automatically assign responsible users based on audit location</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       @bind="enableAreaBasedResponsibility"
                                       @bind:after="OnAreaResponsibilityChanged"
                                       id="areaResponsibilitySwitch">
                                <label class="form-check-label" for="areaResponsibilitySwitch"></label>
                            </div>
                        </div>
                        @if (enableAreaBasedResponsibility)
                        {
                            <div class="feature-info mt-2">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Enabled:</strong> Findings will automatically assign responsible users based on area configuration.
                                    <br />
                                    <small>Configure area responsibilities in Organization → Area Responsibilities</small>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="feature-setting">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Finding Categorization</h6>
                                <small class="text-muted">Enable categorization of findings for better reporting</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       @bind="enableFindingCategorization"
                                       @bind:after="OnFindingCategorizationChanged"
                                       id="categorizationSwitch">
                                <label class="form-check-label" for="categorizationSwitch"></label>
                            </div>
                        </div>
                        @if (enableFindingCategorization)
                        {
                            <div class="feature-info mt-2">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Enabled:</strong> Findings can be categorized using predefined categories.
                                    <br />
                                    <small>Configure categories in Templates → Finding Categories</small>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            @if (hasChanges)
            {
                <div class="mt-3">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        You have unsaved changes. Click "Save Changes" to apply them.
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" @onclick="SaveChanges" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Save Changes
                        </button>
                        <button class="btn btn-secondary" @onclick="ResetChanges">
                            Reset
                        </button>
                    </div>
                </div>
            }

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success mt-3">
                    <i class="fas fa-check-circle me-2"></i>
                    @successMessage
                </div>
            }
        }
        else
        {
            <div class="text-center text-muted">
                <i class="fas fa-file-alt fa-3x mb-3"></i>
                <p>Select a template to configure advanced features.</p>
            </div>
        }
    </div>
</div>

<style>
    .feature-setting {
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background: #f8f9fa;
        margin-bottom: 1rem;
    }

    .feature-info {
        margin-top: 0.75rem;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .alert {
        margin-bottom: 0;
    }

    .alert small {
        display: block;
        margin-top: 0.25rem;
    }
</style>

@code {
    [Parameter] public TemplateModel? Template { get; set; }
    [Parameter] public EventCallback OnFeaturesChanged { get; set; }

    private bool enableAreaBasedResponsibility = false;
    private bool enableFindingCategorization = false;
    private bool originalAreaBasedResponsibility = false;
    private bool originalFindingCategorization = false;
    private bool hasChanges = false;
    private bool isSaving = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    protected override void OnParametersSet()
    {
        if (Template != null)
        {
            enableAreaBasedResponsibility = Template.EnableAreaBasedResponsibility;
            enableFindingCategorization = Template.EnableFindingCategorization;
            originalAreaBasedResponsibility = Template.EnableAreaBasedResponsibility;
            originalFindingCategorization = Template.EnableFindingCategorization;
            hasChanges = false;
        }

        ClearMessages();
    }

    private void OnAreaResponsibilityChanged()
    {
        CheckForChanges();
    }

    private void OnFindingCategorizationChanged()
    {
        CheckForChanges();
    }

    private void CheckForChanges()
    {
        hasChanges = enableAreaBasedResponsibility != originalAreaBasedResponsibility ||
                    enableFindingCategorization != originalFindingCategorization;
        ClearMessages();
    }

    private async Task SaveChanges()
    {
        if (Template == null) return;

        try
        {
            isSaving = true;
            ClearMessages();

            var success = await TemplateApiService.UpdateTemplateFeatureFlagsAsync(
                Template.Id, 
                enableAreaBasedResponsibility, 
                enableFindingCategorization);

            if (success)
            {
                // Update the template object
                Template.EnableAreaBasedResponsibility = enableAreaBasedResponsibility;
                Template.EnableFindingCategorization = enableFindingCategorization;
                
                // Update original values
                originalAreaBasedResponsibility = enableAreaBasedResponsibility;
                originalFindingCategorization = enableFindingCategorization;
                
                hasChanges = false;
                successMessage = "Feature settings saved successfully.";
                
                await OnFeaturesChanged.InvokeAsync();
            }
            else
            {
                errorMessage = "Failed to save feature settings. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving template feature flags for template {TemplateId}", Template?.Id);
            errorMessage = "An error occurred while saving. Please try again.";
        }
        finally
        {
            isSaving = false;
        }
    }

    private void ResetChanges()
    {
        enableAreaBasedResponsibility = originalAreaBasedResponsibility;
        enableFindingCategorization = originalFindingCategorization;
        hasChanges = false;
        ClearMessages();
    }

    private void ClearMessages()
    {
        errorMessage = string.Empty;
        successMessage = string.Empty;
    }
}
