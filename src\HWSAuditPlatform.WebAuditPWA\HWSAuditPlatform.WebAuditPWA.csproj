<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest>
    <AssemblyTitle>HWS Audit Platform - PWA</AssemblyTitle>
    <Product>HWS Audit Platform PWA</Product>
    <Description>Progressive Web App for audit execution and management</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" PrivateAssets="all" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" />
    <PackageReference Include="Microsoft.Extensions.Http" />
    <PackageReference Include="System.Net.Http.Json" />
    <PackageReference Include="Blazored.LocalStorage" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Forms" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HWSAuditPlatform.Domain\HWSAuditPlatform.Domain.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Application\HWSAuditPlatform.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <ServiceWorker Include="wwwroot\service-worker.js" PublishedContent="wwwroot\service-worker.published.js" />
  </ItemGroup>

</Project>
