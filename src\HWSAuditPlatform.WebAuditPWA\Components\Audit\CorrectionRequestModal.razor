@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@using System.ComponentModel.DataAnnotations
@inject ILogger<CorrectionRequestModal> Logger

<!-- Modal -->
<div class="modal fade @(IsVisible ? "show" : "")" id="correctionRequestModal" tabindex="-1" 
     style="display: @(IsVisible ? "block" : "none")" aria-hidden="@(!IsVisible)">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Request Audit Correction
                </h5>
                <button type="button" class="btn-close" @onclick="CloseModal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        @errorMessage
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        @successMessage
                    </div>
                }
                
                @if (AuditSummary != null)
                {
                    <!-- Audit Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>
                                Audit Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Template:</strong> @AuditSummary.AuditTemplateName
                                </div>
                                <div class="col-md-6">
                                    <strong>Location:</strong> @AuditSummary.LocationName
                                </div>
                                <div class="col-md-6 mt-2">
                                    <strong>Status:</strong> 
                                    <span class="badge bg-@GetStatusBadgeClass()">@AuditSummary.OverallStatus</span>
                                </div>
                                <div class="col-md-6 mt-2">
                                    <strong>Completed:</strong> @AuditSummary.CompletedAt?.ToString("MMM dd, yyyy HH:mm")
                                </div>
                            </div>
                        </div>
                    </div>
                }
                
                <!-- Correction Request Form -->
                <EditForm Model="@correctionRequest" OnValidSubmit="@SubmitCorrectionRequest">
                    <DataAnnotationsValidator />
                    
                    <div class="mb-3">
                        <label for="requestReason" class="form-label">
                            <strong>Reason for Correction Request <span class="text-danger">*</span></strong>
                        </label>
                        <textarea id="requestReason" class="form-control" rows="4" 
                                  @bind="correctionRequest.RequestReason" 
                                  placeholder="Please provide a detailed explanation of why this audit needs to be corrected..."
                                  disabled="@isSubmitting"></textarea>
                        <ValidationMessage For="@(() => correctionRequest.RequestReason)" />
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Provide specific details about what needs to be corrected and why. This will help the manager review your request.
                        </div>
                    </div>
                    
                    <!-- Guidelines -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-lightbulb me-2"></i>
                            Correction Request Guidelines
                        </h6>
                        <ul class="mb-0">
                            <li>Clearly explain what needs to be corrected</li>
                            <li>Provide justification for the correction</li>
                            <li>Include any relevant details that support your request</li>
                            <li>Your manager will review and approve/deny this request</li>
                            <li>If approved, you'll be able to modify and resubmit the audit</li>
                        </ul>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModal" disabled="@isSubmitting">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-warning" disabled="@(isSubmitting || string.IsNullOrWhiteSpace(correctionRequest.RequestReason))">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>Submitting...</span>
                            }
                            else
                            {
                                <i class="fas fa-paper-plane me-2"></i>
                                <span>Submit Request</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

<!-- Modal Backdrop -->
@if (IsVisible)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public AuditSummaryDto? AuditSummary { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }
    [Parameter] public EventCallback<CorrectionRequestDto> OnCorrectionRequested { get; set; }
    
    private CorrectionRequestDto correctionRequest = new();
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    
    protected override void OnParametersSet()
    {
        if (IsVisible && AuditSummary != null)
        {
            // Reset form when modal is opened
            correctionRequest = new CorrectionRequestDto
            {
                AuditId = AuditSummary.Id
            };
            errorMessage = string.Empty;
            successMessage = string.Empty;
        }
    }
    
    private async Task CloseModal()
    {
        if (OnClose.HasDelegate)
        {
            await OnClose.InvokeAsync();
        }
    }
    
    private async Task SubmitCorrectionRequest()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            
            Logger.LogInformation("Submitting correction request for audit: {AuditId}", correctionRequest.AuditId);
            
            // Validate the request
            if (string.IsNullOrWhiteSpace(correctionRequest.RequestReason))
            {
                errorMessage = "Please provide a reason for the correction request.";
                return;
            }
            
            if (correctionRequest.RequestReason.Length < 10)
            {
                errorMessage = "Please provide a more detailed reason (at least 10 characters).";
                return;
            }
            
            // Set additional properties
            correctionRequest.RequestedAt = DateTime.UtcNow;
            correctionRequest.Status = CorrectionRequestStatus.PendingApproval;
            
            // Simulate API call delay
            await Task.Delay(1000);
            
            successMessage = "Correction request submitted successfully. Your manager will review it shortly.";
            
            Logger.LogInformation("Correction request submitted successfully for audit: {AuditId}", correctionRequest.AuditId);
            
            // Notify parent component
            if (OnCorrectionRequested.HasDelegate)
            {
                await OnCorrectionRequested.InvokeAsync(correctionRequest);
            }
            
            // Close modal after a short delay to show success message
            await Task.Delay(2000);
            await CloseModal();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error submitting correction request for audit: {AuditId}", correctionRequest.AuditId);
            errorMessage = "An error occurred while submitting the correction request. Please try again.";
        }
        finally
        {
            isSubmitting = false;
        }
    }
    
    private string GetStatusBadgeClass()
    {
        if (AuditSummary == null) return "secondary";
        
        return AuditSummary.OverallStatus switch
        {
            AuditOverallStatus.Submitted => "primary",
            AuditOverallStatus.Closed => "success",
            AuditOverallStatus.PendingManagerReview => "warning",
            _ => "secondary"
        };
    }
}

<style>
    .modal {
        z-index: 1055;
    }
    
    .modal-backdrop {
        z-index: 1050;
    }
    
    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .modal-header {
        border-radius: 12px 12px 0 0;
        border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    }
    
    .form-control:focus {
        border-color: var(--industrial-teal);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
    }
    
    .btn-warning {
        background-color: #f59e0b;
        border-color: #f59e0b;
        color: white;
    }
    
    .btn-warning:hover {
        background-color: #d97706;
        border-color: #d97706;
    }
    
    .alert {
        border-radius: 8px;
    }
    
    .card {
        border-radius: 8px;
        border: 1px solid var(--industrial-border);
    }
    
    .card-header {
        background-color: var(--industrial-light-gray);
        border-bottom: 1px solid var(--industrial-border);
    }
    
    /* Mobile optimizations */
    .mobile-modal-dialog {
        margin: 0.5rem;
    }

    .mobile-modal-lg {
        max-width: none;
    }
</style>
