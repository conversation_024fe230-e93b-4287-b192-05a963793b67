using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Scheduling.Services;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Scheduling.Commands.CreateRecurringAuditSetting;

/// <summary>
/// Handler for CreateRecurringAuditSettingCommand
/// </summary>
public class CreateRecurringAuditSettingCommandHandler : BaseCommandHandler<CreateRecurringAuditSettingCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IRecurrenceCalculationService _recurrenceCalculationService;

    public CreateRecurringAuditSettingCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IRecurrenceCalculationService recurrenceCalculationService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _recurrenceCalculationService = recurrenceCalculationService;
    }

    public override async Task<string> Handle(CreateRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        // Verify the audit template exists and is published
        var auditTemplate = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId && t.IsPublished && t.IsActive, 
                cancellationToken);

        if (auditTemplate == null)
        {
            throw new NotFoundException(nameof(AuditTemplate), request.AuditTemplateId);
        }

        // Verify factory exists
        var factoryExists = await _context.Factories
            .AnyAsync(f => f.Id == request.FactoryId && f.IsActive, cancellationToken);

        if (!factoryExists)
        {
            throw new NotFoundException("Factory", request.FactoryId);
        }

        // Verify area exists if specified
        if (request.AreaId.HasValue)
        {
            var areaExists = await _context.Areas
                .AnyAsync(a => a.Id == request.AreaId.Value && a.FactoryId == request.FactoryId && a.IsActive, 
                    cancellationToken);

            if (!areaExists)
            {
                throw new NotFoundException("Area", request.AreaId.Value);
            }
        }

        // Verify subarea exists if specified
        if (request.SubAreaId.HasValue)
        {
            var subAreaExists = await _context.SubAreas
                .AnyAsync(sa => sa.Id == request.SubAreaId.Value && 
                               (request.AreaId == null || sa.AreaId == request.AreaId.Value) && 
                               sa.IsActive, cancellationToken);

            if (!subAreaExists)
            {
                throw new NotFoundException("SubArea", request.SubAreaId.Value);
            }
        }

        // Verify assignment is valid
        await ValidateAssignmentAsync(request, cancellationToken);

        // Create the recurring audit setting
        var recurringAuditSetting = new RecurringAuditSetting
        {
            Id = CuidGenerator.Generate(),
            SettingName = request.SettingName,
            AuditTemplateId = request.AuditTemplateId,
            DeadlineDays = request.DeadlineDays,
            IsEnabled = request.IsEnabled,
            AssignmentType = request.AssignmentType,
            AssignToUserId = request.AssignToUserId,
            AssignToUserGroupId = request.AssignToUserGroupId,
            FactoryId = request.FactoryId,
            AreaId = request.AreaId,
            SubAreaId = request.SubAreaId,
            CreatedByUserId = _currentUserService.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Create the recurrence rule
        var recurrenceRule = new RecurrenceRule
        {
            Id = CuidGenerator.Generate(),
            RecurringAuditSettingId = recurringAuditSetting.Id,
            FrequencyType = request.RecurrenceRule.FrequencyType,
            Interval = request.RecurrenceRule.Interval,
            StartDate = request.RecurrenceRule.StartDate,
            EndDate = request.RecurrenceRule.EndDate,
            WeeklyDaysOfWeekMask = request.RecurrenceRule.WeeklyDaysOfWeekMask,
            MonthlyDayOfMonth = request.RecurrenceRule.MonthlyDayOfMonth,
            MonthlyNthWeek = request.RecurrenceRule.MonthlyNthWeek,
            MonthlyDayOfWeek = request.RecurrenceRule.MonthlyDayOfWeek,
            YearlyMonth = request.RecurrenceRule.YearlyMonth,
            YearlyDayOfMonth = request.RecurrenceRule.YearlyDayOfMonth,
            YearlyNthWeek = request.RecurrenceRule.YearlyNthWeek,
            YearlyDayOfWeek = request.RecurrenceRule.YearlyDayOfWeek,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Calculate the first generation date
        recurringAuditSetting.NextGenerationDate = _recurrenceCalculationService.CalculateNextGenerationDate(recurrenceRule);

        // Add to context and save
        await _context.RecurringAuditSettings.AddAsync(recurringAuditSetting, cancellationToken);
        await _context.RecurrenceRules.AddAsync(recurrenceRule, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return recurringAuditSetting.Id;
    }

    private async Task ValidateAssignmentAsync(CreateRecurringAuditSettingCommand request, CancellationToken cancellationToken)
    {
        switch (request.AssignmentType)
        {
            case Domain.Enums.AssignmentType.Individual:
                if (string.IsNullOrEmpty(request.AssignToUserId))
                {
                    throw new ValidationException("AssignToUserId", "User ID is required for Individual assignment type");
                }

                var userExists = await _context.Users
                    .AnyAsync(u => u.AdObjectGuid == request.AssignToUserId && u.IsActive, cancellationToken);

                if (!userExists)
                {
                    throw new NotFoundException("User", request.AssignToUserId);
                }
                break;

            case Domain.Enums.AssignmentType.GroupAny:
            case Domain.Enums.AssignmentType.GroupAllScheduled:
                if (string.IsNullOrEmpty(request.AssignToUserGroupId))
                {
                    throw new ValidationException("AssignToUserGroupId", "User Group ID is required for Group assignment types");
                }

                var groupExists = await _context.UserGroups
                    .AnyAsync(g => g.Id == request.AssignToUserGroupId, cancellationToken);

                if (!groupExists)
                {
                    throw new NotFoundException("UserGroup", request.AssignToUserGroupId);
                }
                break;
        }
    }



}
