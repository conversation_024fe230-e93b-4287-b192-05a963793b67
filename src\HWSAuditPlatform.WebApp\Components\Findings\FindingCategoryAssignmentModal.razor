@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject IFindingCategoryApiService FindingCategoryApiService
@inject ILogger<FindingCategoryAssignmentModal> Logger

@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Assign Finding Category</h5>
                    <button type="button" class="btn-close" @onclick="Close"></button>
                </div>
                <div class="modal-body">
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            @errorMessage
                        </div>
                    }

                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading categories...</span>
                            </div>
                            <p class="mt-2">Loading categories...</p>
                        </div>
                    }
                    else if (categories.Any())
                    {
                        <div class="mb-3">
                            <label class="form-label">Select a category for this finding:</label>
                            <div class="category-options">
                                <div class="category-option @(selectedCategoryId == null ? "selected" : "")" 
                                     @onclick="() => SelectCategory(null)">
                                    <div class="category-content">
                                        <i class="fas fa-times-circle me-2 text-muted"></i>
                                        <span class="category-name">No Category</span>
                                    </div>
                                    <small class="category-description text-muted">Remove category assignment</small>
                                </div>

                                @foreach (var category in categories.OrderBy(c => c.CategoryName))
                                {
                                    <div class="category-option @(selectedCategoryId == category.Id ? "selected" : "")" 
                                         @onclick="() => SelectCategory(category.Id)">
                                        <div class="category-content">
                                            @if (!string.IsNullOrEmpty(category.ColorCode))
                                            {
                                                <div class="category-indicator" style="background-color: @category.ColorCode;"></div>
                                            }
                                            @if (!string.IsNullOrEmpty(category.IconName))
                                            {
                                                <i class="@GetIconClass(category.IconName) me-2"></i>
                                            }
                                            <span class="category-name">@category.CategoryName</span>
                                        </div>
                                        @if (!string.IsNullOrEmpty(category.Description))
                                        {
                                            <small class="category-description">@category.Description</small>
                                        }
                                    </div>
                                }
                            </div>
                        </div>

                        @if (selectedCategoryId.HasValue)
                        {
                            var selectedCategory = categories.FirstOrDefault(c => c.Id == selectedCategoryId.Value);
                            if (selectedCategory != null)
                            {
                                <div class="selected-category-preview">
                                    <h6>Selected Category:</h6>
                                    <div class="preview-card">
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(selectedCategory.ColorCode))
                                            {
                                                <div class="category-indicator me-2" style="background-color: @selectedCategory.ColorCode;"></div>
                                            }
                                            @if (!string.IsNullOrEmpty(selectedCategory.IconName))
                                            {
                                                <i class="@GetIconClass(selectedCategory.IconName) me-2"></i>
                                            }
                                            <strong>@selectedCategory.CategoryName</strong>
                                        </div>
                                        @if (!string.IsNullOrEmpty(selectedCategory.Description))
                                        {
                                            <small class="text-muted d-block mt-1">@selectedCategory.Description</small>
                                        }
                                    </div>
                                </div>
                            }
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted">
                            <i class="fas fa-tags fa-3x mb-3"></i>
                            <p>No categories available for this audit template.</p>
                            <small>Categories must be configured for the audit template first.</small>
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="Close">Cancel</button>
                    <button type="button" class="btn btn-primary" @onclick="AssignCategory" disabled="@isAssigning">
                        @if (isAssigning)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        @(selectedCategoryId.HasValue ? "Assign Category" : "Remove Category")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .category-options {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 6px;
    }

    .category-option {
        padding: 0.75rem;
        border-bottom: 1px solid #f8f9fa;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .category-option:last-child {
        border-bottom: none;
    }

    .category-option:hover {
        background-color: #f8f9fa;
    }

    .category-option.selected {
        background-color: #e7f3ff;
        border-color: #007bff;
    }

    .category-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        flex-shrink: 0;
    }

    .category-name {
        font-weight: 500;
        color: #495057;
    }

    .category-description {
        display: block;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .selected-category-preview {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 0.75rem;
        margin-top: 1rem;
    }

    .preview-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 0.5rem;
        margin-top: 0.5rem;
    }
</style>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public string FindingId { get; set; } = string.Empty;
    [Parameter] public int? CurrentCategoryId { get; set; }
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public EventCallback OnCategoryAssigned { get; set; }

    private List<FindingCategoryModel> categories = new();
    private int? selectedCategoryId = null;
    private bool isLoading = false;
    private bool isAssigning = false;
    private string errorMessage = string.Empty;

    protected override async Task OnParametersSetAsync()
    {
        if (IsVisible && AuditTemplateId.HasValue)
        {
            selectedCategoryId = CurrentCategoryId;
            await LoadCategories();
        }
    }

    private async Task LoadCategories()
    {
        if (!AuditTemplateId.HasValue) return;

        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            categories = await FindingCategoryApiService.GetFindingCategoriesForTemplateAsync(AuditTemplateId.Value);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading categories for template {TemplateId}", AuditTemplateId);
            errorMessage = "Failed to load categories. Please try again.";
            categories = new List<FindingCategoryModel>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void SelectCategory(int? categoryId)
    {
        selectedCategoryId = categoryId;
        errorMessage = string.Empty;
    }

    private async Task AssignCategory()
    {
        try
        {
            isAssigning = true;
            errorMessage = string.Empty;

            var success = await FindingCategoryApiService.AssignFindingCategoryAsync(FindingId, selectedCategoryId);

            if (success)
            {
                await OnCategoryAssigned.InvokeAsync();
                await Close();
            }
            else
            {
                errorMessage = "Failed to assign category. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error assigning category {CategoryId} to finding {FindingId}", selectedCategoryId, FindingId);
            errorMessage = "An error occurred while assigning the category. Please try again.";
        }
        finally
        {
            isAssigning = false;
        }
    }

    private async Task Close()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        
        // Reset state
        selectedCategoryId = null;
        errorMessage = string.Empty;
        categories = new List<FindingCategoryModel>();
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }
}
