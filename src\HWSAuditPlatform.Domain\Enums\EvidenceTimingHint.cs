namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents timing hints for when evidence should be collected.
/// Maps to the evidence_timing_hint enum in the database.
/// </summary>
public enum EvidenceTimingHint
{
    /// <summary>
    /// Evidence (e.g., photos) should ideally be captured live during the audit
    /// </summary>
    OnSitePreferred,

    /// <summary>
    /// Evidence (e.g., documents) can be uploaded after the on-site portion of the audit
    /// </summary>
    LaterAllowed,

    /// <summary>
    /// No specific timing preference for evidence submission
    /// </summary>
    AnyTime
}
