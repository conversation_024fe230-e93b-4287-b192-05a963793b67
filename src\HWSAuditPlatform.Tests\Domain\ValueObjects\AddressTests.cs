using FluentAssertions;
using HWSAuditPlatform.Domain.ValueObjects;

namespace HWSAuditPlatform.Tests.Domain.ValueObjects;

public class AddressTests
{
    [Fact]
    public void Constructor_ShouldSetProperties()
    {
        // Arrange
        const string addressLine1 = "123 Main St";
        const string city = "Anytown";
        const string postalCode = "12345";

        // Act
        var address = new Address(addressLine1, city, postalCode);

        // Assert
        address.AddressLine1.Should().Be(addressLine1);
        address.City.Should().Be(city);
        address.PostalCode.Should().Be(postalCode);
    }

    [Fact]
    public void Constructor_WithNullValues_ShouldAcceptNulls()
    {
        // Act
        var address = new Address(null, null, null);

        // Assert
        address.AddressLine1.Should().BeNull();
        address.City.Should().BeNull();
        address.PostalCode.Should().BeNull();
    }

    [Fact]
    public void Equals_WithSameValues_ShouldReturnTrue()
    {
        // Arrange
        var address1 = new Address("123 Main St", "Anytown", "12345");
        var address2 = new Address("123 Main St", "Anytown", "12345");

        // Act & Assert
        address1.Should().Be(address2);
        (address1 == address2).Should().BeTrue();
        (address1 != address2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentValues_ShouldReturnFalse()
    {
        // Arrange
        var address1 = new Address("123 Main St", "Anytown", "12345");
        var address2 = new Address("456 Oak Ave", "Otherville", "67890");

        // Act & Assert
        address1.Should().NotBe(address2);
        (address1 == address2).Should().BeFalse();
        (address1 != address2).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithNullValues_ShouldHandleCorrectly()
    {
        // Arrange
        var address1 = new Address(null, null, null);
        var address2 = new Address(null, null, null);
        var address3 = new Address("123 Main St", null, null);

        // Act & Assert
        address1.Should().Be(address2);
        address1.Should().NotBe(address3);
    }

    [Fact]
    public void GetHashCode_WithSameValues_ShouldReturnSameHashCode()
    {
        // Arrange
        var address1 = new Address("123 Main St", "Anytown", "12345");
        var address2 = new Address("123 Main St", "Anytown", "12345");

        // Act & Assert
        address1.GetHashCode().Should().Be(address2.GetHashCode());
    }

    [Fact]
    public void ToString_WithAllValues_ShouldFormatCorrectly()
    {
        // Arrange
        var address = new Address("123 Main St", "Anytown", "12345");

        // Act
        var result = address.ToString();

        // Assert
        result.Should().Be("123 Main St, Anytown, 12345");
    }

    [Fact]
    public void ToString_WithSomeNullValues_ShouldSkipNulls()
    {
        // Arrange
        var address = new Address("123 Main St", null, "12345");

        // Act
        var result = address.ToString();

        // Assert
        result.Should().Be("123 Main St, 12345");
    }

    [Fact]
    public void ToString_WithAllNullValues_ShouldReturnEmpty()
    {
        // Arrange
        var address = new Address(null, null, null);

        // Act
        var result = address.ToString();

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void IsEmpty_WithAllNullValues_ShouldReturnTrue()
    {
        // Arrange
        var address = new Address(null, null, null);

        // Act & Assert
        address.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void IsEmpty_WithAllWhitespaceValues_ShouldReturnTrue()
    {
        // Arrange
        var address = new Address("   ", "\t", "\n");

        // Act & Assert
        address.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void IsEmpty_WithAnyValue_ShouldReturnFalse()
    {
        // Arrange
        var address = new Address("123 Main St", null, null);

        // Act & Assert
        address.IsEmpty.Should().BeFalse();
    }
}
