-- Migration: Add Area-Based Responsibility and Finding Categorization Support
-- Date: 2025-01-25
-- Description: Adds support for area-based responsibility assignment and finding categorization

-- Create new enum type for responsibility types
IF NOT EXISTS (SELECT 1 FROM sys.types WHERE name = 'responsibility_type' AND is_user_defined = 1)
BEGIN
    CREATE TYPE responsibility_type FROM varchar(50);
END
GO

-- Create AreaResponsibilities table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'hwsap_org_area_responsibilities')
BEGIN
    CREATE TABLE hwsap_org_area_responsibilities (
        Id varchar(25) NOT NULL PRIMARY KEY,
        AreaId int NOT NULL,
        ResponsibilityType int NOT NULL,
        ResponsibleUserId varchar(36) NOT NULL,
        AuditTemplateId int NULL,
        IsActive bit NOT NULL DEFAULT 1,
        Description varchar(500) NULL,
        Priority int NOT NULL DEFAULT 0,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        RecordVersion int NOT NULL DEFAULT 1,
        CreatedByUserId varchar(36) NULL,
        UpdatedByUserId varchar(36) NULL,
        
        CONSTRAINT FK_AreaResponsibilities_Area 
            FOREIGN KEY (AreaId) REFERENCES hwsap_org_areas(Id) ON DELETE CASCADE,
        CONSTRAINT FK_AreaResponsibilities_User 
            FOREIGN KEY (ResponsibleUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION,
        CONSTRAINT FK_AreaResponsibilities_Template 
            FOREIGN KEY (AuditTemplateId) REFERENCES hwsap_tpl_audit_templates(Id) ON DELETE CASCADE,
        CONSTRAINT FK_AreaResponsibilities_CreatedByUser 
            FOREIGN KEY (CreatedByUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION,
        CONSTRAINT FK_AreaResponsibilities_UpdatedByUser 
            FOREIGN KEY (UpdatedByUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION
    );
    
    -- Create indexes for AreaResponsibilities
    CREATE INDEX IX_AreaResponsibilities_AreaId ON hwsap_org_area_responsibilities(AreaId);
    CREATE INDEX IX_AreaResponsibilities_ResponsibleUserId ON hwsap_org_area_responsibilities(ResponsibleUserId);
    CREATE INDEX IX_AreaResponsibilities_AuditTemplateId ON hwsap_org_area_responsibilities(AuditTemplateId);
    CREATE INDEX IX_AreaResponsibilities_Area_Type_Template ON hwsap_org_area_responsibilities(AreaId, ResponsibilityType, AuditTemplateId);
    CREATE INDEX IX_AreaResponsibilities_IsActive ON hwsap_org_area_responsibilities(IsActive);
END
GO

-- Create FindingCategories table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'hwsap_fnd_finding_categories')
BEGIN
    CREATE TABLE hwsap_fnd_finding_categories (
        Id int IDENTITY(1,1) PRIMARY KEY,
        CategoryName varchar(100) NOT NULL,
        Description varchar(500) NULL,
        AuditTemplateId int NOT NULL,
        DisplayOrder int NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        ColorCode varchar(7) NULL,
        IconName varchar(50) NULL,
        RequiresDocumentation bit NOT NULL DEFAULT 0,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        RecordVersion int NOT NULL DEFAULT 1,
        CreatedByUserId varchar(36) NULL,
        UpdatedByUserId varchar(36) NULL,
        
        CONSTRAINT FK_FindingCategories_Template 
            FOREIGN KEY (AuditTemplateId) REFERENCES hwsap_tpl_audit_templates(Id) ON DELETE CASCADE,
        CONSTRAINT FK_FindingCategories_CreatedByUser 
            FOREIGN KEY (CreatedByUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION,
        CONSTRAINT FK_FindingCategories_UpdatedByUser 
            FOREIGN KEY (UpdatedByUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION
    );
    
    -- Create indexes for FindingCategories
    CREATE INDEX IX_FindingCategories_AuditTemplateId ON hwsap_fnd_finding_categories(AuditTemplateId);
    CREATE UNIQUE INDEX IX_FindingCategories_Template_Name ON hwsap_fnd_finding_categories(AuditTemplateId, CategoryName);
    CREATE INDEX IX_FindingCategories_Template_DisplayOrder ON hwsap_fnd_finding_categories(AuditTemplateId, DisplayOrder);
    CREATE INDEX IX_FindingCategories_IsActive ON hwsap_fnd_finding_categories(IsActive);
END
GO

-- Add new columns to Findings table
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('hwsap_fnd_findings') AND name = 'ResponsibleUserId')
BEGIN
    ALTER TABLE hwsap_fnd_findings ADD ResponsibleUserId varchar(36) NULL;
    ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_ResponsibleUser 
        FOREIGN KEY (ResponsibleUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION;
    CREATE INDEX IX_Findings_ResponsibleUserId ON hwsap_fnd_findings(ResponsibleUserId);
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('hwsap_fnd_findings') AND name = 'RetrospectiveAnalystUserId')
BEGIN
    ALTER TABLE hwsap_fnd_findings ADD RetrospectiveAnalystUserId varchar(36) NULL;
    ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_RetrospectiveAnalyst 
        FOREIGN KEY (RetrospectiveAnalystUserId) REFERENCES hwsap_usr_users(AdObjectGuid) ON DELETE NO ACTION;
    CREATE INDEX IX_Findings_RetrospectiveAnalystUserId ON hwsap_fnd_findings(RetrospectiveAnalystUserId);
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('hwsap_fnd_findings') AND name = 'FindingCategoryId')
BEGIN
    ALTER TABLE hwsap_fnd_findings ADD FindingCategoryId int NULL;
    ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_Category 
        FOREIGN KEY (FindingCategoryId) REFERENCES hwsap_fnd_finding_categories(Id) ON DELETE SET NULL;
    CREATE INDEX IX_Findings_FindingCategoryId ON hwsap_fnd_findings(FindingCategoryId);
END
GO

-- Add new columns to AuditTemplates table
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('hwsap_tpl_audit_templates') AND name = 'EnableAreaBasedResponsibility')
BEGIN
    ALTER TABLE hwsap_tpl_audit_templates ADD EnableAreaBasedResponsibility bit NOT NULL DEFAULT 0;
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('hwsap_tpl_audit_templates') AND name = 'EnableFindingCategorization')
BEGIN
    ALTER TABLE hwsap_tpl_audit_templates ADD EnableFindingCategorization bit NOT NULL DEFAULT 0;
END
GO

PRINT 'Migration completed: Area-Based Responsibility and Finding Categorization support added successfully.';
