using MediatR;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for command handlers that don't return a value
/// </summary>
/// <typeparam name="TCommand">The type of command being handled</typeparam>
public abstract class BaseCommandHandler<TCommand> : IRequestHandler<TCommand>
    where TCommand : BaseCommand
{
    public abstract Task Handle(TCommand request, CancellationToken cancellationToken);
}

/// <summary>
/// Base class for command handlers that return a value
/// </summary>
/// <typeparam name="TCommand">The type of command being handled</typeparam>
/// <typeparam name="TResponse">The type of response returned</typeparam>
public abstract class BaseCommandHandler<TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : BaseCommand<TResponse>
{
    public abstract Task<TResponse> Handle(TCommand request, CancellationToken cancellationToken);
}

/// <summary>
/// Base class for query handlers
/// </summary>
/// <typeparam name="TQuery">The type of query being handled</typeparam>
/// <typeparam name="TResponse">The type of response returned</typeparam>
public abstract class BaseQueryHandler<TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : BaseQuery<TResponse>
{
    public abstract Task<TResponse> Handle(TQuery request, CancellationToken cancellationToken);
}
