namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for Active Directory user information
/// </summary>
public class AdUserDto
{
    /// <summary>
    /// Active Directory ObjectGUID
    /// </summary>
    public string ObjectGuid { get; set; } = string.Empty;

    /// <summary>
    /// Username (sAMAccountName)
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// First name (givenName)
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name (sn)
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Distinguished Name
    /// </summary>
    public string? DistinguishedName { get; set; }

    /// <summary>
    /// Whether the account is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// List of AD groups the user belongs to
    /// </summary>
    public IEnumerable<string> MemberOf { get; set; } = new List<string>();

    /// <summary>
    /// Department
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// Job title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Manager's distinguished name
    /// </summary>
    public string? Manager { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// When the user was created in AD
    /// </summary>
    public DateTime? WhenCreated { get; set; }

    /// <summary>
    /// When the user was last modified in AD
    /// </summary>
    public DateTime? WhenChanged { get; set; }

    /// <summary>
    /// Last logon time
    /// </summary>
    public DateTime? LastLogon { get; set; }
}
