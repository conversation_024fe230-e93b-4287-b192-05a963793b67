{"ApiBaseUrl": "https://localhost:7490/", "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "HWSAuditPlatform.WebAuditPWA": "Debug"}}, "PWA": {"EnableOfflineMode": true, "SyncIntervalMinutes": 5, "MaxOfflineAudits": 20, "CacheExpirationHours": 12}, "Camera": {"MaxImageWidth": 1280, "MaxImageHeight": 720, "ImageQuality": 0.7, "MaxFileSizeMB": 5}, "Performance": {"EnableCompression": false, "EnableCaching": false, "CacheDurationMinutes": 5}, "Debug": {"SkipAuthentication": false, "MockUser": {"UserId": "debug-user-001", "Username": "debug.user", "Email": "<EMAIL>", "FullName": "Debug User", "Role": "Admin", "FactoryId": "1"}}}