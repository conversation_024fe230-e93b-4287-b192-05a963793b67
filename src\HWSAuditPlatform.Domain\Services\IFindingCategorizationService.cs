using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Domain.Services;

/// <summary>
/// Domain service for managing finding categorization
/// </summary>
public interface IFindingCategorizationService
{
    /// <summary>
    /// Validates that finding categories are properly configured for a template
    /// </summary>
    /// <param name="auditTemplate">The audit template to validate</param>
    /// <returns>Validation result with any issues found</returns>
    FindingCategorizationValidationResult ValidateFindingCategories(AuditTemplate auditTemplate);

    /// <summary>
    /// Gets available categories for a finding based on the audit template
    /// </summary>
    /// <param name="finding">The finding to categorize</param>
    /// <param name="auditTemplate">The audit template</param>
    /// <returns>Available categories for the finding</returns>
    IEnumerable<FindingCategory> GetAvailableCategories(Finding finding, AuditTemplate auditTemplate);

    /// <summary>
    /// Assigns a category to a finding with validation
    /// </summary>
    /// <param name="finding">The finding to categorize</param>
    /// <param name="categoryId">The category ID to assign</param>
    /// <param name="auditTemplate">The audit template</param>
    /// <returns>True if assignment was successful, false otherwise</returns>
    bool AssignCategory(Finding finding, int categoryId, AuditTemplate auditTemplate);

    /// <summary>
    /// Removes category assignment from a finding
    /// </summary>
    /// <param name="finding">The finding to uncategorize</param>
    void RemoveCategory(Finding finding);

    /// <summary>
    /// Gets category statistics for an audit template
    /// </summary>
    /// <param name="auditTemplate">The audit template</param>
    /// <param name="findings">Findings to analyze</param>
    /// <returns>Category usage statistics</returns>
    FindingCategoryStatistics GetCategoryStatistics(AuditTemplate auditTemplate, IEnumerable<Finding> findings);
}

/// <summary>
/// Result of finding categorization validation
/// </summary>
public class FindingCategorizationValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
}

/// <summary>
/// Statistics about finding category usage
/// </summary>
public class FindingCategoryStatistics
{
    public int TotalCategories { get; set; }
    public int ActiveCategories { get; set; }
    public int TotalFindings { get; set; }
    public int CategorizedFindings { get; set; }
    public int UncategorizedFindings { get; set; }
    public Dictionary<int, CategoryUsage> CategoryUsage { get; set; } = new();
}

/// <summary>
/// Usage statistics for a specific category
/// </summary>
public class CategoryUsage
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public int FindingCount { get; set; }
    public int OpenFindingCount { get; set; }
    public int ClosedFindingCount { get; set; }
    public double UsagePercentage { get; set; }
}
