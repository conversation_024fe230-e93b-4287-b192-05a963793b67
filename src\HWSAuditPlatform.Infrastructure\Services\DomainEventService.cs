using MediatR;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Infrastructure.Services;

/// <summary>
/// Service for publishing domain events using MediatR
/// </summary>
public class DomainEventService : IDomainEventService
{
    private readonly IMediator _mediator;
    private readonly ILogger<DomainEventService> _logger;

    public DomainEventService(IMediator mediator, ILogger<DomainEventService> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task PublishAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Publishing domain event: {EventType}", domainEvent.GetType().Name);
            await _mediator.Publish(domainEvent, cancellationToken);
            _logger.LogInformation("Successfully published domain event: {EventType}", domainEvent.GetType().Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing domain event: {EventType}", domainEvent.GetType().Name);
            throw;
        }
    }

    public async Task PublishAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default)
    {
        var events = domainEvents.ToList();
        
        if (!events.Any())
            return;

        _logger.LogInformation("Publishing {EventCount} domain events", events.Count);

        foreach (var domainEvent in events)
        {
            await PublishAsync(domainEvent, cancellationToken);
        }

        _logger.LogInformation("Successfully published all {EventCount} domain events", events.Count);
    }
}
