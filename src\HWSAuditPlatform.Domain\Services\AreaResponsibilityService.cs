using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Services;

/// <summary>
/// Domain service implementation for managing area-based responsibility assignments
/// </summary>
public class AreaResponsibilityService : IAreaResponsibilityService
{
    public async Task AssignResponsibilitiesAsync(
        Finding finding, 
        Audit audit, 
        IEnumerable<AreaResponsibility> areaResponsibilities,
        CancellationToken cancellationToken = default)
    {
        // Assign corrective action owner
        var correctiveActionOwner = GetResponsibleUser(
            audit.AreaId, 
            ResponsibilityType.CorrectiveActionOwner, 
            audit.AuditTemplateId,
            areaResponsibilities);

        if (!string.IsNullOrEmpty(correctiveActionOwner))
        {
            finding.ResponsibleUserId = correctiveActionOwner;
        }

        // Assign retrospective analyst
        var retrospectiveAnalyst = GetResponsibleUser(
            audit.AreaId, 
            ResponsibilityType.RetrospectiveAnalyst, 
            audit.AuditTemplateId,
            areaResponsibilities);

        if (!string.IsNullOrEmpty(retrospectiveAnalyst))
        {
            finding.RetrospectiveAnalystUserId = retrospectiveAnalyst;
        }

        await Task.CompletedTask;
    }

    public string? GetResponsibleUser(
        int areaId, 
        ResponsibilityType responsibilityType, 
        int? auditTemplateId,
        IEnumerable<AreaResponsibility> areaResponsibilities)
    {
        var activeResponsibilities = areaResponsibilities
            .Where(ar => ar.IsActive && ar.AreaId == areaId && ar.ResponsibilityType == responsibilityType)
            .ToList();

        // First, try to find template-specific responsibility
        if (auditTemplateId.HasValue)
        {
            var templateSpecific = activeResponsibilities
                .Where(ar => ar.AuditTemplateId == auditTemplateId.Value)
                .OrderBy(ar => ar.Priority)
                .FirstOrDefault();

            if (templateSpecific != null)
            {
                return templateSpecific.ResponsibleUserId;
            }
        }

        // Fall back to general (non-template-specific) responsibility
        var general = activeResponsibilities
            .Where(ar => ar.AuditTemplateId == null)
            .OrderBy(ar => ar.Priority)
            .FirstOrDefault();

        return general?.ResponsibleUserId;
    }

    public AreaResponsibilityValidationResult ValidateAreaResponsibilities(
        IEnumerable<AreaResponsibility> areaResponsibilities)
    {
        var result = new AreaResponsibilityValidationResult { IsValid = true };
        var responsibilities = areaResponsibilities.ToList();

        // Check for duplicate responsibilities (same area, type, and template)
        var duplicates = responsibilities
            .GroupBy(ar => new { ar.AreaId, ar.ResponsibilityType, ar.AuditTemplateId })
            .Where(g => g.Count() > 1)
            .ToList();

        foreach (var duplicate in duplicates)
        {
            var key = duplicate.Key;
            var templateInfo = key.AuditTemplateId.HasValue ? $" for template {key.AuditTemplateId}" : " (general)";
            result.ValidationWarnings.Add(
                $"Multiple users assigned to {key.ResponsibilityType} responsibility for area {key.AreaId}{templateInfo}. Priority will determine assignment.");
        }

        // Check for areas without corrective action owners
        var areasWithoutOwners = responsibilities
            .Where(ar => ar.IsActive)
            .GroupBy(ar => new { ar.AreaId, ar.AuditTemplateId })
            .Where(g => !g.Any(ar => ar.ResponsibilityType == ResponsibilityType.CorrectiveActionOwner))
            .ToList();

        foreach (var areaGroup in areasWithoutOwners)
        {
            var key = areaGroup.Key;
            var templateInfo = key.AuditTemplateId.HasValue ? $" for template {key.AuditTemplateId}" : " (general)";
            result.ValidationWarnings.Add(
                $"Area {key.AreaId} has no corrective action owner assigned{templateInfo}.");
        }

        return result;
    }
}
