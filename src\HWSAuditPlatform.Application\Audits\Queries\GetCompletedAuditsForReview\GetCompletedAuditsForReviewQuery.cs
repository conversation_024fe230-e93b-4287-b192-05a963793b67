using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetCompletedAuditsForReview;

/// <summary>
/// Query to get completed audits for manager review with detailed results
/// </summary>
public class GetCompletedAuditsForReviewQuery : BaseQuery<PaginatedResult<AuditReviewDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term to filter audits
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by audit status
    /// </summary>
    public AuditOverallStatus? Status { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Filter by sub-area ID
    /// </summary>
    public int? SubAreaId { get; set; }

    /// <summary>
    /// Filter by audit template ID
    /// </summary>
    public int? AuditTemplateId { get; set; }

    /// <summary>
    /// Filter by completion date from
    /// </summary>
    public DateTime? CompletedDateFrom { get; set; }

    /// <summary>
    /// Filter by completion date to
    /// </summary>
    public DateTime? CompletedDateTo { get; set; }

    /// <summary>
    /// Filter by assigned user ID
    /// </summary>
    public string? AssignedToUserId { get; set; }

    /// <summary>
    /// Show only audits with findings
    /// </summary>
    public bool? HasFindings { get; set; }

    /// <summary>
    /// Show only audits with attachments
    /// </summary>
    public bool? HasAttachments { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CompletedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    /// <summary>
    /// Include detailed answers in the response
    /// </summary>
    public bool IncludeAnswers { get; set; } = false;
}
