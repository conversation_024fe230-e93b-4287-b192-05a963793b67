using System.ComponentModel.DataAnnotations;

namespace HWSAuditPlatform.Domain.ValueObjects;

/// <summary>
/// Value object representing an address
/// </summary>
public class Address : ValueObject
{
    public string? AddressLine1 { get; private set; }
    public string? City { get; private set; }
    public string? PostalCode { get; private set; }

    private Address() { } // For EF Core

    public Address(string? addressLine1, string? city, string? postalCode)
    {
        AddressLine1 = addressLine1;
        City = city;
        PostalCode = postalCode;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AddressLine1 ?? string.Empty;
        yield return City ?? string.Empty;
        yield return PostalCode ?? string.Empty;
    }

    public override string ToString()
    {
        var parts = new[] { AddressLine1, City, PostalCode }
            .Where(part => !string.IsNullOrWhiteSpace(part));
        
        return string.Join(", ", parts);
    }

    public bool IsEmpty => string.IsNullOrWhiteSpace(AddressLine1) && 
                          string.IsNullOrWhiteSpace(City) && 
                          string.IsNullOrWhiteSpace(PostalCode);
}
