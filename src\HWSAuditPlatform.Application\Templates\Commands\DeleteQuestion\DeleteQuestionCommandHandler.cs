using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteQuestion;

/// <summary>
/// Handler for DeleteQuestionCommand
/// </summary>
public class DeleteQuestionCommandHandler : BaseCommandHandler<DeleteQuestionCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteQuestionCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(DeleteQuestionCommand request, CancellationToken cancellationToken)
    {
        // Get the question with its template and child questions
        var question = await _context.Questions
            .Include(q => q.AuditTemplate)
            .Include(q => q.ChildQuestions)
            .Include(q => q.Options)
            .FirstOrDefaultAsync(q => q.Id == request.QuestionId && q.IsActive, cancellationToken);

        if (question == null)
        {
            throw new NotFoundException(nameof(Question), request.QuestionId);
        }

        // Verify the template ID matches
        if (question.AuditTemplateId != request.TemplateId)
        {
            throw new InvalidOperationException("Question does not belong to the specified template");
        }

        // Check for optimistic concurrency
        if (question.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("The question has been modified by another user. Please refresh and try again.");
        }

        // Don't allow deletion of questions in published templates
        if (question.AuditTemplate.IsPublished)
        {
            throw new InvalidOperationException("Cannot delete questions from a published template");
        }

        // Check if this question is being used in any active audits
        var isUsedInAudits = await _context.AuditAnswers
            .AnyAsync(a => a.QuestionId == request.QuestionId, cancellationToken);

        if (isUsedInAudits)
        {
            throw new InvalidOperationException("Cannot delete a question that has been answered in audits");
        }

        // Check if this question has child questions that would become orphaned
        var hasActiveChildQuestions = question.ChildQuestions.Any(cq => cq.IsActive);
        if (hasActiveChildQuestions)
        {
            throw new InvalidOperationException("Cannot delete a question that has active child questions. Delete child questions first.");
        }

        // Soft delete the question
        question.IsActive = false;
        question.UpdatedAt = DateTime.UtcNow;
        question.UpdatedByUserId = _currentUserService.UserId;

        // Also soft delete all related question options
        foreach (var option in question.Options.Where(o => o.IsActive))
        {
            option.IsActive = false;
            option.UpdatedAt = DateTime.UtcNow;
            option.UpdatedByUserId = _currentUserService.UserId;
        }

        // Update the template's modified timestamp
        question.AuditTemplate.UpdatedAt = DateTime.UtcNow;
        question.AuditTemplate.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
