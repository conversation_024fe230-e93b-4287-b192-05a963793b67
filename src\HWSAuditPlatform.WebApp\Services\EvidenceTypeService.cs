using System.Text.Json;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Service for managing evidence types in the WebApp
/// </summary>
public interface IEvidenceTypeService
{
    /// <summary>
    /// Gets all available evidence types from the API
    /// </summary>
    Task<List<EvidenceTypeInfo>> GetEvidenceTypesAsync();
}

/// <summary>
/// Implementation of evidence type service for WebApp
/// </summary>
public class EvidenceTypeService : IEvidenceTypeService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<EvidenceTypeService> _logger;

    public EvidenceTypeService(HttpClient httpClient, ILogger<EvidenceTypeService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<List<EvidenceTypeInfo>> GetEvidenceTypesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/v1/evidencetypes");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var evidenceTypes = JsonSerializer.Deserialize<List<EvidenceTypeInfo>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });
                
                return evidenceTypes ?? new List<EvidenceTypeInfo>();
            }
            else
            {
                _logger.LogWarning("Failed to get evidence types from API. Status: {StatusCode}", response.StatusCode);
                return GetDefaultEvidenceTypes();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence types from API");
            return GetDefaultEvidenceTypes();
        }
    }

    private static List<EvidenceTypeInfo> GetDefaultEvidenceTypes()
    {
        return new List<EvidenceTypeInfo>
        {
            new() { EvidenceType = EvidenceType.Image, Name = "Image", Description = "Image files (JPEG, PNG, GIF, BMP, WebP)" },
            new() { EvidenceType = EvidenceType.Document, Name = "Document", Description = "Document files (PDF, Word, Text, RTF)" },
            new() { EvidenceType = EvidenceType.Video, Name = "Video", Description = "Video files (MP4, AVI, MOV, WMV, WebM)" },
            new() { EvidenceType = EvidenceType.Audio, Name = "Audio", Description = "Audio files (MP3, WAV, AAC, OGG, M4A)" },
            new() { EvidenceType = EvidenceType.Spreadsheet, Name = "Spreadsheet", Description = "Spreadsheet files (Excel, CSV)" },
            new() { EvidenceType = EvidenceType.Any, Name = "Any", Description = "Any file type (no restrictions)" }
        };
    }
}

/// <summary>
/// Information about an evidence type for WebApp
/// </summary>
public class EvidenceTypeInfo
{
    /// <summary>
    /// The evidence type enum value
    /// </summary>
    public EvidenceType EvidenceType { get; set; }

    /// <summary>
    /// Display name of the evidence type
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what files are allowed for this evidence type
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// List of allowed MIME types for this evidence type
    /// </summary>
    public List<string> AllowedMimeTypes { get; set; } = new();
}
