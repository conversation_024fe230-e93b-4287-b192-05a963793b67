using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.ReviewAudit;

/// <summary>
/// Handler for ReviewAuditCommand
/// </summary>
public class ReviewAuditCommandHandler : BaseCommandHandler<ReviewAuditCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public ReviewAuditCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(ReviewAuditCommand request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        // Validate status transition
        if (audit.OverallStatus != AuditOverallStatus.PendingManagerReview && 
            audit.OverallStatus != AuditOverallStatus.Submitted)
        {
            throw new InvalidOperationException($"Cannot review audit in {audit.OverallStatus} status");
        }

        // Update audit based on review decision
        if (request.Approved)
        {
            audit.OverallStatus = AuditOverallStatus.ManagerReviewed;
        }
        else
        {
            audit.OverallStatus = AuditOverallStatus.PendingCorrection;
        }

        // Set review details
        audit.ReviewedByUserId = _currentUserService.UserId;
        audit.ReviewedAt = DateTime.UtcNow;
        audit.ManagerComments = request.Comments;
        audit.UpdatedAt = DateTime.UtcNow;
        audit.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
