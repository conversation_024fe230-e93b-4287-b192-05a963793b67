using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Commands.DeleteAuditAnswer;

/// <summary>
/// Handler for DeleteAuditAnswerCommand
/// </summary>
public class DeleteAuditAnswerCommandHandler : BaseCommandHandler<DeleteAuditAnswerCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteAuditAnswerCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(DeleteAuditAnswerCommand request, CancellationToken cancellationToken)
    {
        // Get the audit answer with related data
        var auditAnswer = await _context.AuditAnswers
            .Include(a => a.Audit)
            .Include(a => a.SelectedOptions)
            .Include(a => a.FailureReasons)
            .Include(a => a.Attachments)
            .FirstOrDefaultAsync(a => a.Id == request.AnswerId, cancellationToken);

        if (auditAnswer == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.AuditAnswer), request.AnswerId);
        }

        // Verify the answer belongs to the specified audit
        if (auditAnswer.AuditId != request.AuditId)
        {
            throw new InvalidOperationException("The audit answer does not belong to the specified audit.");
        }

        // Check if the audit is in a state that allows answer deletion
        if (auditAnswer.Audit.OverallStatus == AuditOverallStatus.Closed ||
            auditAnswer.Audit.OverallStatus == AuditOverallStatus.Cancelled)
        {
            throw new InvalidOperationException("Cannot delete answers from a closed or cancelled audit.");
        }

        // Check if the current user has permission to delete this answer
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated.");
        }

        // Only allow deletion by the user who created the answer or users with appropriate roles
        if (auditAnswer.CreatedByUserId != currentUserId &&
            !_currentUserService.HasAnyRole(UserRole.ProcessOwner, UserRole.SystemManager, UserRole.DevAdmin))
        {
            throw new UnauthorizedAccessException("You can only delete your own audit answers unless you are a Process Owner, System Manager, or Dev Admin.");
        }

        // Remove related entities first (due to foreign key constraints)
        if (auditAnswer.SelectedOptions.Any())
        {
            _context.AuditAnswerSelectedOptions.RemoveRange(auditAnswer.SelectedOptions);
        }

        if (auditAnswer.FailureReasons.Any())
        {
            _context.AuditAnswerFailureReasons.RemoveRange(auditAnswer.FailureReasons);
        }

        // Note: Attachments might need special handling for file cleanup
        // For now, we'll just remove the database records
        if (auditAnswer.Attachments.Any())
        {
            _context.AuditAttachments.RemoveRange(auditAnswer.Attachments);
        }

        // Remove the audit answer itself
        _context.AuditAnswers.Remove(auditAnswer);

        await _context.SaveChangesAsync(cancellationToken);
    }
}
