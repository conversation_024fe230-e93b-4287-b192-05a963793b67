﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Components.WebAssembly.Http
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using Microsoft.JSInterop
@using Blazored.LocalStorage
@using HWSAuditPlatform.WebAuditPWA
@using HWSAuditPlatform.WebAuditPWA.Layout
@using HWSAuditPlatform.WebAuditPWA.Services
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components
@using HWSAuditPlatform.WebAuditPWA.Components.Navigation
@using HWSAuditPlatform.WebAuditPWA.Components.Audit
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Entities.Users
@using HWSAuditPlatform.Domain.Entities.Organization
@using HWSAuditPlatform.Domain.Enums
