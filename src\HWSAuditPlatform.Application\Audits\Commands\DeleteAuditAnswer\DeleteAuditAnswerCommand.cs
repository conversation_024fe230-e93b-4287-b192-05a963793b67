using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Audits.Commands.DeleteAuditAnswer;

/// <summary>
/// Command to delete an audit answer
/// </summary>
public class DeleteAuditAnswerCommand : BaseCommand
{
    /// <summary>
    /// The ID of the audit answer to delete
    /// </summary>
    public string AnswerId { get; set; } = string.Empty;

    /// <summary>
    /// The ID of the audit (for validation)
    /// </summary>
    public string AuditId { get; set; } = string.Empty;

    public DeleteAuditAnswerCommand(string answerId, string auditId)
    {
        AnswerId = answerId;
        AuditId = auditId;
    }

    public DeleteAuditAnswerCommand()
    {
    }
}
