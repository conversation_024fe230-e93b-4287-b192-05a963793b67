using HWSAuditPlatform.Domain.Exceptions;

namespace HWSAuditPlatform.Domain.ValueObjects;

/// <summary>
/// Value object representing an audit score
/// </summary>
public class AuditScore : ValueObject
{
    public decimal Value { get; private set; }
    public decimal MaxValue { get; private set; }

    private AuditScore() { } // For EF Core

    public AuditScore(decimal value, decimal maxValue)
    {
        if (maxValue <= 0)
            throw new ValidationException("Maximum value must be greater than zero");
        
        if (value < 0)
            throw new ValidationException("Score value cannot be negative");
        
        if (value > maxValue)
            throw new ValidationException("Score value cannot exceed maximum value");

        Value = value;
        MaxValue = maxValue;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
        yield return MaxValue;
    }

    /// <summary>
    /// Gets the score as a percentage
    /// </summary>
    public decimal Percentage => MaxValue > 0 ? (Value / MaxValue) * 100 : 0;

    /// <summary>
    /// Gets the score as a percentage rounded to specified decimal places
    /// </summary>
    public decimal GetPercentage(int decimalPlaces = 2)
    {
        return Math.Round(Percentage, decimalPlaces);
    }

    /// <summary>
    /// Indicates if this is a perfect score
    /// </summary>
    public bool IsPerfect => Value == MaxValue;

    /// <summary>
    /// Indicates if this is a failing score (less than 60%)
    /// </summary>
    public bool IsFailing => Percentage < 60;

    public override string ToString()
    {
        return $"{Value}/{MaxValue} ({GetPercentage()}%)";
    }

    public static AuditScore Zero(decimal maxValue) => new(0, maxValue);
    public static AuditScore Perfect(decimal maxValue) => new(maxValue, maxValue);
}
