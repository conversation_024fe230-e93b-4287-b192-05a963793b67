@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@inject ILogger<ResponsibilityAssignmentModal> Logger

@if (IsVisible)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.8);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-users me-2"></i>
                        Assign Responsibilities
                    </h5>
                    <button type="button" class="btn-close" @onclick="Close"></button>
                </div>
                <div class="modal-body">
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @errorMessage
                        </div>
                    }

                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading available users...</p>
                        </div>
                    }
                    else
                    {
                        <div class="responsibility-assignments">
                            <div class="assignment-section">
                                <h6 class="assignment-title">
                                    <i class="fas fa-user-check me-2"></i>
                                    Corrective Action Owner
                                </h6>
                                <p class="assignment-description">
                                    Responsible for implementing corrective actions for findings.
                                </p>
                                <select class="form-select" @bind="selectedCorrectiveActionOwner">
                                    <option value="">Select user...</option>
                                    @foreach (var user in availableUsers)
                                    {
                                        <option value="@user.Id">@user.FullName (@user.Username)</option>
                                    }
                                </select>
                            </div>

                            <div class="assignment-section">
                                <h6 class="assignment-title">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Retrospective Analyst
                                </h6>
                                <p class="assignment-description">
                                    Responsible for analyzing trends and root causes.
                                </p>
                                <select class="form-select" @bind="selectedRetrospectiveAnalyst">
                                    <option value="">Select user...</option>
                                    @foreach (var user in availableUsers)
                                    {
                                        <option value="@user.Id">@user.FullName (@user.Username)</option>
                                    }
                                </select>
                            </div>

                            <div class="assignment-section">
                                <h6 class="assignment-title">
                                    <i class="fas fa-user-edit me-2"></i>
                                    Finding Reviewer
                                </h6>
                                <p class="assignment-description">
                                    Responsible for reviewing and validating findings.
                                </p>
                                <select class="form-select" @bind="selectedFindingReviewer">
                                    <option value="">Select user...</option>
                                    @foreach (var user in availableUsers)
                                    {
                                        <option value="@user.Id">@user.FullName (@user.Username)</option>
                                    }
                                </select>
                            </div>

                            <div class="assignment-section">
                                <h6 class="assignment-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Escalation Contact
                                </h6>
                                <p class="assignment-description">
                                    Contact for escalating critical or overdue findings.
                                </p>
                                <select class="form-select" @bind="selectedEscalationContact">
                                    <option value="">Select user...</option>
                                    @foreach (var user in availableUsers)
                                    {
                                        <option value="@user.Id">@user.FullName (@user.Username)</option>
                                    }
                                </select>
                            </div>

                            @if (hasChanges)
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> These assignments will apply to all findings in this audit area.
                                    Existing assignments may be updated.
                                </div>
                            }
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="Close">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SaveAssignments" disabled="@(isAssigning || !hasChanges)">
                        @if (isAssigning)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        <i class="fas fa-save me-2"></i>
                        Save Assignments
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .modal-content {
        background: var(--industrial-dark);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
    }

    .modal-header {
        border-bottom: 1px solid var(--industrial-border);
    }

    .modal-title {
        color: var(--industrial-teal);
        font-weight: 700;
    }

    .btn-close {
        filter: invert(1);
    }

    .responsibility-assignments {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .assignment-section {
        background: var(--industrial-light-gray);
        border: 1px solid var(--industrial-border);
        border-radius: 8px;
        padding: 1.25rem;
    }

    .assignment-title {
        color: var(--industrial-teal);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .assignment-description {
        color: var(--industrial-text-muted);
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .form-select {
        background-color: var(--industrial-dark);
        border-color: var(--industrial-border);
        color: var(--industrial-text);
    }

    .form-select:focus {
        border-color: var(--industrial-teal);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
        background-color: var(--industrial-dark);
        color: var(--industrial-text);
    }

    .form-select option {
        background-color: var(--industrial-dark);
        color: var(--industrial-text);
    }

    .alert {
        border-radius: 8px;
    }

    .btn-primary {
        background-color: var(--industrial-teal);
        border-color: var(--industrial-teal);
    }

    .btn-primary:hover {
        background-color: var(--industrial-teal-dark);
        border-color: var(--industrial-teal-dark);
    }

    .btn-primary:disabled {
        background-color: var(--industrial-border);
        border-color: var(--industrial-border);
        opacity: 0.6;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .modal-dialog {
            margin: 0.5rem;
        }

        .assignment-section {
            padding: 1rem;
        }

        .assignment-title {
            font-size: 0.95rem;
        }

        .assignment-description {
            font-size: 0.85rem;
        }
    }
</style>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public string? AuditId { get; set; }
    [Parameter] public int? AreaId { get; set; }
    [Parameter] public EventCallback OnAssignmentsChanged { get; set; }

    private List<UserSummaryModel> availableUsers = new();
    private string selectedCorrectiveActionOwner = string.Empty;
    private string selectedRetrospectiveAnalyst = string.Empty;
    private string selectedFindingReviewer = string.Empty;
    private string selectedEscalationContact = string.Empty;
    
    private bool isLoading = false;
    private bool isAssigning = false;
    private string errorMessage = string.Empty;
    private bool hasChanges = false;

    protected override async Task OnParametersSetAsync()
    {
        if (IsVisible && !availableUsers.Any())
        {
            await LoadAvailableUsers();
        }
    }

    private async Task LoadAvailableUsers()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            // TODO: Load available users from API
            // This would typically call a user service to get users
            // who can be assigned responsibilities
            availableUsers = new List<UserSummaryModel>
            {
                new() { Id = "user1", Username = "john.doe", FullName = "John Doe" },
                new() { Id = "user2", Username = "jane.smith", FullName = "Jane Smith" },
                new() { Id = "user3", Username = "mike.johnson", FullName = "Mike Johnson" }
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading available users");
            errorMessage = "Failed to load available users. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void OnSelectionChanged()
    {
        hasChanges = !string.IsNullOrEmpty(selectedCorrectiveActionOwner) ||
                    !string.IsNullOrEmpty(selectedRetrospectiveAnalyst) ||
                    !string.IsNullOrEmpty(selectedFindingReviewer) ||
                    !string.IsNullOrEmpty(selectedEscalationContact);
    }

    private async Task SaveAssignments()
    {
        try
        {
            isAssigning = true;
            errorMessage = string.Empty;

            // TODO: Save assignments via API
            // This would typically call an area responsibility service
            // to create or update responsibility assignments

            await Task.Delay(1000); // Simulate API call

            await OnAssignmentsChanged.InvokeAsync();
            await Close();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving responsibility assignments");
            errorMessage = "Failed to save assignments. Please try again.";
        }
        finally
        {
            isAssigning = false;
        }
    }

    private async Task Close()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        
        // Reset state
        selectedCorrectiveActionOwner = string.Empty;
        selectedRetrospectiveAnalyst = string.Empty;
        selectedFindingReviewer = string.Empty;
        selectedEscalationContact = string.Empty;
        hasChanges = false;
        errorMessage = string.Empty;
    }

    // Simple user model for the dropdown
    private class UserSummaryModel
    {
        public string Id { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
    }
}
