using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;

/// <summary>
/// Validator for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandValidator : AbstractValidator<PublishTemplateCommand>
{
    private readonly IApplicationDbContext _context;

    public PublishTemplateCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0).WithMessage("Audit template ID must be greater than 0")
            .MustAsync(BeValidAuditTemplate).WithMessage("Audit template does not exist")
            .MustAsync(NotBeAlreadyPublished).WithMessage("Audit template is already published")
            .MustAsync(HaveAtLeastOneQuestion).WithMessage("Audit template must have at least one question to be published")
            .MustAsync(HaveValidQuestions).WithMessage("All questions must have valid configuration")
            .MustAsync(HaveValidSelectQuestionOptions).WithMessage("Single/Multi-select questions must have at least 2 options");
    }

    private async Task<bool> BeValidAuditTemplate(int auditTemplateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates.AnyAsync(t => t.Id == auditTemplateId && t.IsActive, cancellationToken);
    }

    private async Task<bool> NotBeAlreadyPublished(int auditTemplateId, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == auditTemplateId, cancellationToken);
        return template != null && !template.IsPublished;
    }

    private async Task<bool> HaveAtLeastOneQuestion(int auditTemplateId, CancellationToken cancellationToken)
    {
        return await _context.Questions.AnyAsync(q => q.AuditTemplateId == auditTemplateId && q.IsActive, cancellationToken);
    }

    private async Task<bool> HaveValidQuestions(int auditTemplateId, CancellationToken cancellationToken)
    {
        var questions = await _context.Questions
            .Where(q => q.AuditTemplateId == auditTemplateId && q.IsActive)
            .ToListAsync(cancellationToken);

        // Check that all questions have valid text
        if (questions.Any(q => string.IsNullOrWhiteSpace(q.QuestionText)))
        {
            return false;
        }

        // Check that all questions have valid question types
        if (questions.Any(q => !Enum.IsDefined(typeof(Domain.Enums.QuestionType), q.QuestionType)))
        {
            return false;
        }

        return true;
    }

    private async Task<bool> HaveValidSelectQuestionOptions(int auditTemplateId, CancellationToken cancellationToken)
    {
        var selectQuestions = await _context.Questions
            .Include(q => q.Options)
            .Where(q => q.AuditTemplateId == auditTemplateId && q.IsActive &&
                       (q.QuestionType == Domain.Enums.QuestionType.SingleSelect ||
                        q.QuestionType == Domain.Enums.QuestionType.MultiSelect))
            .ToListAsync(cancellationToken);

        // Check that all single/multi-select questions have at least 2 active options
        foreach (var question in selectQuestions)
        {
            var activeOptions = question.Options.Count(o => o.IsActive);
            if (activeOptions < 2)
            {
                return false;
            }

            // Check that all options have valid text
            if (question.Options.Any(o => o.IsActive && string.IsNullOrWhiteSpace(o.OptionText)))
            {
                return false;
            }
        }

        return true;
    }
}
