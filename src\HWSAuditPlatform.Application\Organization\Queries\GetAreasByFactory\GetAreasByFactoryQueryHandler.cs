using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetAreasByFactory;

/// <summary>
/// Handler for GetAreasByFactoryQuery
/// </summary>
public class GetAreasByFactoryQueryHandler : BaseQueryHandler<GetAreasByFactoryQuery, List<AreaSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAreasByFactoryQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<AreaSummaryDto>> Handle(GetAreasByFactoryQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Areas
            .Include(a => a.Factory)
            .Where(a => a.FactoryId == request.FactoryId)
            .AsQueryable();

        // Apply filters
        if (request.IsActive.HasValue)
        {
            query = query.Where(a => a.IsActive == request.IsActive.Value);
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(a =>
                EF.Functions.Like(a.AreaName, searchPattern) ||
                (a.Description != null && EF.Functions.Like(a.Description, searchPattern)));
        }

        // Get areas with sub-area counts
        var areas = await query
            .Select(a => new AreaSummaryDto
            {
                Id = a.Id,
                AreaName = a.AreaName,
                FactoryId = a.FactoryId,
                FactoryName = a.Factory != null ? a.Factory.FactoryName : null,
                Description = a.Description,
                IsActive = a.IsActive,
                SubAreaCount = a.SubAreas.Count(sa => sa.IsActive)
            })
            .OrderBy(a => a.AreaName)
            .ToListAsync(cancellationToken);

        return areas;
    }
}
