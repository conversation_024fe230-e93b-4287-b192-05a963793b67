using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Organization.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Organization.Queries.GetLocations;

/// <summary>
/// Handler for GetLocationsQuery
/// </summary>
public class GetLocationsQueryHandler : BaseQueryHandler<GetLocationsQuery, List<LocationSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetLocationsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<List<LocationSummaryDto>> Handle(GetLocationsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Locations.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.OwnerGroupId))
        {
            query = query.Where(l => l.OwnerGroupId == request.OwnerGroupId);
        }

        // Get locations with factory counts
        var locations = await query
            .Select(l => new LocationSummaryDto
            {
                Id = l.Id,
                LocationName = l.LocationName,
                LocationCountry = l.LocationCountry,
                LocationCountryCode = l.LocationCountryCode,
                FactoryCount = l.Factories.Count(f => request.IncludeInactive == true || f.IsActive)
            })
            .OrderBy(l => l.LocationCountry)
            .ThenBy(l => l.LocationName)
            .ToListAsync(cancellationToken);

        return locations;
    }
}
