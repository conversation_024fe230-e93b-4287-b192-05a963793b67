# Domain Events Documentation

This document describes the domain events system in the HWS Audit Platform and provides detailed information about each event type.

## Overview

The HWS Audit Platform implements an event-driven architecture using domain events to enable loose coupling between aggregates and support reactive behaviors. Domain events represent significant business occurrences that other parts of the system may need to react to.

## Event Architecture

### Event Interface
All domain events implement the `IDomainEvent` interface:

```csharp
public interface IDomainEvent
{
    DateTime OccurredOn { get; }
}
```

### Event Collection and Publishing
- Events are collected in aggregate root entities via the `BaseEntity` class
- Events are published by application services after successful persistence
- Events enable side effects without tight coupling between aggregates

### Event Handling Patterns
- **Immediate Handlers**: Process events synchronously within the same transaction
- **Asynchronous Handlers**: Process events asynchronously for non-critical operations
- **Integration Events**: Convert domain events to integration events for external systems

## Audit Events

### AuditCreatedEvent
**Purpose**: Raised when a new audit instance is created

**Properties**:
- `AuditId`: Unique identifier of the created audit
- `AuditTemplateId`: Template used for the audit
- `AssignedToUserId`: User assigned (if individual assignment)
- `AssignedToUserGroupId`: Group assigned (if group assignment)
- `ScheduledDate`: When the audit is scheduled to be performed

**Typical Handlers**:
- Send notification to assigned user/group
- Update audit scheduling dashboards
- Log audit creation in system logs
- Initialize audit tracking metrics

**Business Scenarios**:
- Manual audit creation by managers
- Automatic audit generation from recurring settings
- Audit assignment changes

### AuditStatusChangedEvent
**Purpose**: Raised when an audit's overall status changes

**Properties**:
- `AuditId`: Audit identifier
- `PreviousStatus`: Status before the change
- `NewStatus`: Status after the change
- `ChangedByUserId`: User who initiated the status change

**Typical Handlers**:
- Send status change notifications
- Update audit progress dashboards
- Trigger workflow actions based on new status
- Log status transitions for audit trail

**Business Scenarios**:
- Auditor starts working on audit (Scheduled → InProgress)
- Auditor submits audit (InProgress → Submitted)
- Manager reviews audit (Submitted → PendingManagerReview)
- Audit requires corrections (PendingManagerReview → PendingCorrection)
- Audit is completed (ManagerReviewed → Closed)

### AuditSubmittedEvent
**Purpose**: Raised when an auditor submits a completed audit

**Properties**:
- `AuditId`: Audit identifier
- `SubmittedByUserId`: User who submitted the audit
- `SubmittedAt`: Timestamp of submission

**Typical Handlers**:
- Notify managers of pending review
- Calculate audit scores and metrics
- Generate findings from failed answers
- Update completion statistics

**Business Scenarios**:
- Auditor completes all required questions
- Auditor provides all required evidence
- Audit moves to review queue

### AuditAssignedEvent
**Purpose**: Raised when an audit is assigned to a specific user

**Properties**:
- `AuditId`: Audit identifier
- `AssignedToUserId`: User receiving the assignment
- `AssignedByUserId`: User who made the assignment (optional)

**Typical Handlers**:
- Send assignment notification to user
- Update user's audit workload
- Add audit to user's task list
- Log assignment for tracking

**Business Scenarios**:
- Manager assigns audit to specific auditor
- Group member claims a group-assigned audit
- Audit reassignment due to availability changes

### AuditStartedEvent
**Purpose**: Raised when an auditor begins working on an audit

**Properties**:
- `AuditId`: Audit identifier
- `StartedByUserId`: User who started the audit
- `StartedAt`: Timestamp when audit was started

**Typical Handlers**:
- Update audit status tracking
- Start time-based metrics collection
- Notify stakeholders of audit commencement
- Initialize audit session logging

**Business Scenarios**:
- Auditor opens audit in PWA for first time
- Audit status changes from Scheduled to InProgress
- Audit timing and duration tracking begins

## Finding Events

### FindingCreatedEvent
**Purpose**: Raised when a new finding (non-conformity) is identified

**Properties**:
- `FindingId`: Unique identifier of the finding
- `AuditAnswerId`: Audit answer that generated the finding
- `SeverityLevel`: Severity level of the finding
- `ReportedByUserId`: User who identified the finding

**Typical Handlers**:
- Send finding notifications based on severity
- Update finding statistics and dashboards
- Trigger escalation workflows for critical findings
- Log finding for compliance tracking

**Business Scenarios**:
- Auditor answers question negatively
- System automatically generates finding from failed answer
- Manual finding creation by auditor or manager

### FindingStatusChangedEvent
**Purpose**: Raised when a finding's status changes throughout its lifecycle

**Properties**:
- `FindingId`: Finding identifier
- `PreviousStatus`: Status before the change
- `NewStatus`: Status after the change
- `ChangedByUserId`: User who changed the status

**Typical Handlers**:
- Update finding tracking dashboards
- Send status change notifications
- Trigger workflow actions based on new status
- Update compliance metrics

**Business Scenarios**:
- Finding moves to investigation (Open → UnderInvestigation)
- Corrective actions planned (UnderInvestigation → PendingCorrectiveAction)
- Actions completed (PendingCorrectiveAction → PendingVerification)
- Finding resolved (PendingVerification → Closed)

### CorrectiveActionCreatedEvent
**Purpose**: Raised when a corrective action is created to address a finding

**Properties**:
- `CorrectiveActionId`: Unique identifier of the corrective action
- `FindingId`: Finding being addressed
- `AssignedToUserId`: User responsible for the action
- `DueDate`: Target completion date
- `CreatedByUserId`: User who created the action

**Typical Handlers**:
- Send assignment notification to responsible user
- Add action to user's task list
- Update finding resolution tracking
- Schedule follow-up reminders

**Business Scenarios**:
- Manager creates corrective action for finding
- System suggests actions based on finding type
- Action assignment during finding review process

### CorrectiveActionStatusChangedEvent
**Purpose**: Raised when a corrective action's status changes

**Properties**:
- `CorrectiveActionId`: Action identifier
- `PreviousStatus`: Status before the change
- `NewStatus`: Status after the change
- `ChangedByUserId`: User who changed the status

**Typical Handlers**:
- Update action tracking dashboards
- Send progress notifications
- Trigger verification workflows
- Update finding status if all actions complete

**Business Scenarios**:
- User starts working on action (Assigned → InProgress)
- User completes action (InProgress → CompletedPendingVerification)
- Manager verifies completion (CompletedPendingVerification → VerifiedClosed)
- Action proves ineffective (VerifiedClosed → Ineffective)

### CorrectiveActionCompletedEvent
**Purpose**: Raised when a corrective action is marked as completed

**Properties**:
- `CorrectiveActionId`: Action identifier
- `FindingId`: Associated finding
- `CompletedByUserId`: User who completed the action
- `CompletionDate`: Date of completion

**Typical Handlers**:
- Send completion notifications
- Schedule verification activities
- Update completion metrics
- Check if all finding actions are complete

**Business Scenarios**:
- User marks action as complete with evidence
- System validates completion criteria
- Action moves to verification queue

## Event Handling Patterns

### Synchronous Event Handling
Used for critical business logic that must complete within the same transaction:

```csharp
// Example: Generate findings when audit is submitted
public class AuditSubmittedHandler : IDomainEventHandler<AuditSubmittedEvent>
{
    public async Task Handle(AuditSubmittedEvent domainEvent)
    {
        // Generate findings from failed answers
        // Must complete successfully for audit submission to succeed
    }
}
```

### Asynchronous Event Handling
Used for non-critical operations that can be processed later:

```csharp
// Example: Send notifications when audit is assigned
public class AuditAssignedNotificationHandler : IDomainEventHandler<AuditAssignedEvent>
{
    public async Task Handle(AuditAssignedEvent domainEvent)
    {
        // Send email/SMS notifications
        // Update dashboard displays
        // Can be retried if it fails
    }
}
```

### Event Aggregation
Multiple events can be combined for complex business scenarios:

```csharp
// Example: Audit completion workflow
public class AuditCompletionOrchestrator : 
    IDomainEventHandler<AuditSubmittedEvent>,
    IDomainEventHandler<FindingCreatedEvent>
{
    // Coordinates multiple events for audit completion
}
```

## Event Ordering and Dependencies

### Event Sequence Guarantees
- Events within the same aggregate are processed in order
- Cross-aggregate events may be processed out of order
- Use event correlation IDs for related event sequences

### Event Dependencies
Some events have implicit dependencies:
- `FindingCreatedEvent` typically follows `AuditSubmittedEvent`
- `CorrectiveActionCreatedEvent` follows `FindingCreatedEvent`
- `AuditStatusChangedEvent` may trigger multiple downstream events

### Idempotency
All event handlers should be idempotent to handle:
- Duplicate event delivery
- Event replay scenarios
- System recovery situations

## Integration Events

Domain events can be converted to integration events for external systems:

### Audit Integration Events
- **AuditCompletedIntegrationEvent**: Notify external systems of audit completion
- **FindingCreatedIntegrationEvent**: Send findings to compliance systems
- **CorrectiveActionDueIntegrationEvent**: Integrate with task management systems

### Event Transformation
```csharp
public class DomainEventToIntegrationEventMapper
{
    public IntegrationEvent Map(IDomainEvent domainEvent)
    {
        return domainEvent switch
        {
            AuditSubmittedEvent e => new AuditCompletedIntegrationEvent(e.AuditId),
            FindingCreatedEvent e => new FindingCreatedIntegrationEvent(e.FindingId),
            _ => null
        };
    }
}
```

## Event Storage and Replay

### Event Store
Consider implementing an event store for:
- Event sourcing capabilities
- Audit trail requirements
- System recovery and replay
- Analytics and reporting

### Event Versioning
Plan for event schema evolution:
- Version event contracts
- Support backward compatibility
- Handle event migration scenarios

## Monitoring and Observability

### Event Metrics
Track important event metrics:
- Event processing times
- Event failure rates
- Event volume by type
- Handler performance

### Event Logging
Log significant events for:
- Debugging and troubleshooting
- Business intelligence
- Compliance auditing
- Performance analysis

### Event Tracing
Implement distributed tracing for:
- Cross-service event flows
- Performance bottleneck identification
- Error correlation and debugging
