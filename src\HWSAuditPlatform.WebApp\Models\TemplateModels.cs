namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Model for template summary information
/// </summary>
public class TemplateModel
{
    public int Id { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Version { get; set; }
    public bool IsPublished { get; set; }
    public bool IsActive { get; set; }
    public bool EnableAreaBasedResponsibility { get; set; }
    public bool EnableFindingCategorization { get; set; }
    public int QuestionCount { get; set; }
    public int AuditCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedByUserName { get; set; }
    public string? UpdatedByUserName { get; set; }
}

/// <summary>
/// Model for template feature flags update
/// </summary>
public class UpdateTemplateFeatureFlagsModel
{
    public bool EnableAreaBasedResponsibility { get; set; }
    public bool EnableFindingCategorization { get; set; }
}

/// <summary>
/// Model for template statistics
/// </summary>
public class TemplateStatisticsModel
{
    public int TotalTemplates { get; set; }
    public int PublishedTemplates { get; set; }
    public int DraftTemplates { get; set; }
    public int TemplatesWithAreaResponsibility { get; set; }
    public int TemplatesWithCategorization { get; set; }
    public int TotalQuestions { get; set; }
    public int TotalAudits { get; set; }
}

/// <summary>
/// Model for template usage analytics
/// </summary>
public class TemplateUsageModel
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public int AuditCount { get; set; }
    public int CompletedAuditCount { get; set; }
    public int FindingCount { get; set; }
    public int CategorizedFindingCount { get; set; }
    public double AverageCompletionTime { get; set; }
    public DateTime LastUsed { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Model for template configuration summary
/// </summary>
public class TemplateConfigurationModel
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public bool EnableAreaBasedResponsibility { get; set; }
    public bool EnableFindingCategorization { get; set; }
    public int AreaResponsibilityCount { get; set; }
    public int FindingCategoryCount { get; set; }
    public bool IsFullyConfigured { get; set; }
    public List<string> MissingConfigurations { get; set; } = new();
}
