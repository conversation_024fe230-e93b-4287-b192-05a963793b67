using System.Text.Json;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of recurring audit API service
/// </summary>
public class RecurringAuditApiService : IRecurringAuditApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<RecurringAuditApiService> _logger;

    public RecurringAuditApiService(AuthenticatedHttpClientService httpClient, ILogger<RecurringAuditApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<PaginatedResult<RecurringAuditSettingSummaryDto>> GetRecurringAuditSettingsAsync(
        int pageNumber = 1,
        int pageSize = 20,
        string? searchTerm = null,
        bool? isEnabled = null,
        int? auditTemplateId = null,
        int? factoryId = null,
        int? areaId = null,
        int? subAreaId = null)
    {
        try
        {
            _logger.LogInformation("Getting recurring audit settings - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

            var queryParams = new List<string>
            {
                $"pageNumber={pageNumber}",
                $"pageSize={pageSize}"
            };

            if (!string.IsNullOrEmpty(searchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");
            if (isEnabled.HasValue)
                queryParams.Add($"isEnabled={isEnabled.Value}");
            if (auditTemplateId.HasValue)
                queryParams.Add($"auditTemplateId={auditTemplateId.Value}");
            if (factoryId.HasValue)
                queryParams.Add($"factoryId={factoryId.Value}");
            if (areaId.HasValue)
                queryParams.Add($"areaId={areaId.Value}");
            if (subAreaId.HasValue)
                queryParams.Add($"subAreaId={subAreaId.Value}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/v1/recurring-audits?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PaginatedResult<RecurringAuditSettingSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return apiResponse?.Data ?? new PaginatedResult<RecurringAuditSettingSummaryDto>(
                    new List<RecurringAuditSettingSummaryDto>(), 0, 1, pageSize);
            }

            _logger.LogWarning("Failed to get recurring audit settings. Status: {StatusCode}", response.StatusCode);
            return new PaginatedResult<RecurringAuditSettingSummaryDto>(
                new List<RecurringAuditSettingSummaryDto>(), 0, 1, pageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recurring audit settings");
            return new PaginatedResult<RecurringAuditSettingSummaryDto>(
                new List<RecurringAuditSettingSummaryDto>(), 0, 1, pageSize);
        }
    }

    public async Task<RecurringAuditSettingDto?> GetRecurringAuditSettingByIdAsync(string id)
    {
        try
        {
            _logger.LogInformation("Getting recurring audit setting {Id}", id);

            var response = await _httpClient.GetAsync($"api/v1/recurring-audits/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<RecurringAuditSettingDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return apiResponse?.Data;
            }

            _logger.LogWarning("Failed to get recurring audit setting {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recurring audit setting {Id}", id);
            return null;
        }
    }

    public async Task<string> CreateRecurringAuditSettingAsync(CreateRecurringAuditSettingRequest request)
    {
        try
        {
            _logger.LogInformation("Creating recurring audit setting: {SettingName}", request.SettingName);

            var response = await _httpClient.PostAsJsonAsync("api/v1/recurring-audits", request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<string>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Successfully created recurring audit setting: {SettingName}", request.SettingName);
                return apiResponse?.Data ?? string.Empty;
            }

            _logger.LogWarning("Failed to create recurring audit setting. Status: {StatusCode}", response.StatusCode);
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating recurring audit setting: {SettingName}", request.SettingName);
            return string.Empty;
        }
    }

    public async Task<bool> UpdateRecurringAuditSettingAsync(string id, UpdateRecurringAuditSettingRequest request)
    {
        try
        {
            _logger.LogInformation("Updating recurring audit setting {Id}", id);

            var response = await _httpClient.PutAsJsonAsync($"api/v1/recurring-audits/{id}", request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully updated recurring audit setting {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to update recurring audit setting {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating recurring audit setting {Id}", id);
            return false;
        }
    }

    public async Task<bool> DeleteRecurringAuditSettingAsync(string id)
    {
        try
        {
            _logger.LogInformation("Deleting recurring audit setting {Id}", id);

            var response = await _httpClient.DeleteAsync($"api/v1/recurring-audits/{id}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted recurring audit setting {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to delete recurring audit setting {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting recurring audit setting {Id}", id);
            return false;
        }
    }

    public async Task<bool> ToggleRecurringAuditSettingAsync(string id)
    {
        try
        {
            _logger.LogInformation("Toggling recurring audit setting {Id}", id);

            var response = await _httpClient.PostAsync($"api/v1/recurring-audits/{id}/toggle", null);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully toggled recurring audit setting {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to toggle recurring audit setting {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling recurring audit setting {Id}", id);
            return false;
        }
    }

    public async Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> GenerateRecurringAuditsAsync(
        string? settingId = null,
        bool dryRun = false,
        int maxAuditsToGenerate = 100)
    {
        try
        {
            _logger.LogInformation("Generating recurring audits - SettingId: {SettingId}, DryRun: {DryRun}", settingId, dryRun);

            var queryParams = new List<string>
            {
                $"dryRun={dryRun}",
                $"maxAuditsToGenerate={maxAuditsToGenerate}"
            };

            if (!string.IsNullOrEmpty(settingId))
                queryParams.Add($"settingId={settingId}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.PostAsync($"api/v1/recurring-audits/generate?{queryString}", null);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return apiResponse?.Data ?? new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
            }

            _logger.LogWarning("Failed to generate recurring audits. Status: {StatusCode}", response.StatusCode);
            return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating recurring audits");
            return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
        }
    }

    public async Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> PreviewRecurringAuditGenerationAsync(string settingId)
    {
        try
        {
            _logger.LogInformation("Previewing recurring audit generation for setting {SettingId}", settingId);

            var response = await _httpClient.PostAsync($"api/v1/recurring-audits/{settingId}/preview", null);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return apiResponse?.Data ?? new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
            }

            _logger.LogWarning("Failed to preview recurring audit generation for setting {SettingId}. Status: {StatusCode}", settingId, response.StatusCode);
            return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing recurring audit generation for setting {SettingId}", settingId);
            return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult();
        }
    }
}
