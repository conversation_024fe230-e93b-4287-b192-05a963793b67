using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Queries.GetUserGroup;
using HWSAuditPlatform.Application.Users.Queries.GetUserGroups;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for user group management operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class UserGroupsController : BaseController
{
    public UserGroupsController(IMediator mediator, ILogger<UserGroupsController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get a paginated list of user groups
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of user groups</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<UserGroupSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<UserGroupSummaryDto>>> GetUserGroups(
        [FromQuery] GetUserGroupsQuery query,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting user groups with query: {@Query}", query);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific user group by ID
    /// </summary>
    /// <param name="id">User group ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User group details with members</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(UserGroupDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserGroupDto>> GetUserGroup(
        string id,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting user group with ID: {Id}", id);
        var query = new GetUserGroupQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Search user groups by term
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated search results</returns>
    [HttpGet("search")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<UserGroupSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<UserGroupSummaryDto>>> SearchUserGroups(
        [FromQuery] string searchTerm,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Searching user groups with term: {SearchTerm}", searchTerm);
        
        var query = new GetUserGroupsQuery
        {
            SearchTerm = searchTerm,
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50) // Limit page size
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }
}
