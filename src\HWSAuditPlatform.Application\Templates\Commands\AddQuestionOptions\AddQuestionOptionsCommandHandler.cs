using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestionOptions;

/// <summary>
/// Handler for AddQuestionOptionsCommand
/// </summary>
public class AddQuestionOptionsCommandHandler : BaseCommandHandler<AddQuestionOptionsCommand, List<int>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public AddQuestionOptionsCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<List<int>> Handle(AddQuestionOptionsCommand request, CancellationToken cancellationToken)
    {
        // Get the question with its template
        var question = await _context.Questions
            .Include(q => q.AuditTemplate)
            .Include(q => q.Options)
            .FirstOrDefaultAsync(q => q.Id == request.QuestionId && q.IsActive, cancellationToken);

        if (question == null)
        {
            throw new NotFoundException(nameof(Question), request.QuestionId);
        }

        // Verify the template ID matches
        if (question.AuditTemplateId != request.TemplateId)
        {
            throw new InvalidOperationException("Question does not belong to the specified template");
        }

        // Don't allow adding options to questions in published templates
        if (question.AuditTemplate.IsPublished)
        {
            throw new InvalidOperationException("Cannot add options to questions in a published template");
        }

        // Verify the question type supports options
        if (question.QuestionType != QuestionType.SingleSelect && question.QuestionType != QuestionType.MultiSelect)
        {
            throw new InvalidOperationException($"Cannot add options to {question.QuestionType} question type. Only SingleSelect and MultiSelect questions support options.");
        }

        var createdOptionIds = new List<int>();

        // Add each option
        var newOptions = new List<QuestionOption>();
        foreach (var optionDto in request.Options)
        {
            var option = new QuestionOption
            {
                QuestionId = question.Id,
                OptionText = optionDto.OptionText,
                OptionValue = optionDto.OptionValue,
                DisplayOrder = optionDto.DisplayOrder,
                IsCorrectOption = optionDto.IsCorrectOption,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedByUserId = _currentUserService.UserId
            };

            newOptions.Add(option);
        }

        await _context.QuestionOptions.AddRangeAsync(newOptions, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        createdOptionIds.AddRange(newOptions.Select(o => o.Id));

        // Update the question's modified timestamp
        question.UpdatedAt = DateTime.UtcNow;
        question.UpdatedByUserId = _currentUserService.UserId;

        // Update the template's modified timestamp
        question.AuditTemplate.UpdatedAt = DateTime.UtcNow;
        question.AuditTemplate.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);

        return createdOptionIds;
    }
}
