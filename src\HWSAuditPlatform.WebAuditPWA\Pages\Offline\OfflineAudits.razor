@page "/offline"
@attribute [Authorize]
@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Components.Audit
@using HWSAuditPlatform.WebAuditPWA.Components.Common
@inject IOfflineStorageService OfflineStorage
@inject ISyncService SyncService
@inject IPwaService PwaService
@inject NavigationManager Navigation
@inject ILogger<OfflineAudits> Logger

<PageTitle>Offline Audits - HWS Audit Platform PWA</PageTitle>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-wifi-slash me-2 text-warning"></i>
                        Offline Audits
                    </h1>
                    <p class="text-muted mb-0">Audits available for offline completion</p>
                </div>
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary me-2" @onclick="RefreshOfflineData" disabled="@isLoading">
                        <i class="fas fa-sync-alt me-1 @(isLoading ? "fa-spin" : "")"></i>
                        Refresh
                    </button>
                    <button class="btn btn-primary" @onclick="SyncData" disabled="@isSyncing || !isOnline">
                        <i class="fas fa-cloud-upload-alt me-1 @(isSyncing ? "fa-spin" : "")"></i>
                        @(isSyncing ? "Syncing..." : "Sync Data")
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Network Status Alert -->
    @if (!isOnline)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-wifi-slash me-2"></i>
                    <strong>You are currently offline.</strong> 
                    You can continue working with downloaded audits. Your changes will be synced when connection is restored.
                </div>
            </div>
        </div>
    }

    <!-- Sync Status -->
    @if (syncStatus != null)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sync-alt me-2"></i>
                            Sync Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 col-md-3 text-center">
                                <div class="h4 mb-1 text-@(syncStatus.PendingAnswers > 0 ? "warning" : "success")">
                                    @syncStatus.PendingAnswers
                                </div>
                                <small class="text-muted">Pending Answers</small>
                            </div>
                            <div class="col-6 col-md-3 text-center">
                                <div class="h4 mb-1 text-@(syncStatus.PendingAttachments > 0 ? "warning" : "success")">
                                    @syncStatus.PendingAttachments
                                </div>
                                <small class="text-muted">Pending Attachments</small>
                            </div>
                            <div class="col-6 col-md-3 text-center">
                                <div class="h4 mb-1 text-@(syncStatus.PendingAudits > 0 ? "warning" : "success")">
                                    @syncStatus.PendingAudits
                                </div>
                                <small class="text-muted">Pending Audits</small>
                            </div>
                            <div class="col-6 col-md-3 text-center">
                                <div class="h4 mb-1 text-@(isOnline ? "success" : "danger")">
                                    <i class="fas fa-@(isOnline ? "wifi" : "wifi-slash")"></i>
                                </div>
                                <small class="text-muted">@(isOnline ? "Online" : "Offline")</small>
                            </div>
                        </div>
                        
                        @if (syncStatus.LastSyncTime.HasValue)
                        {
                            <div class="row mt-3">
                                <div class="col-12 text-center">
                                    <small class="text-muted">
                                        Last sync: @syncStatus.LastSyncTime.Value.ToString("MMM dd, yyyy HH:mm")
                                    </small>
                                </div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(syncStatus.LastSyncError))
                        {
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="alert alert-danger mb-0">
                                        <small>
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Last sync error: @syncStatus.LastSyncError
                                        </small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Offline Audits List -->
    <div class="row">
        <div class="col-12">
            @if (isLoading)
            {
                <LoadingSpinner Message="Loading offline audits..." />
            }
            else if (offlineAudits.Any())
            {
                <div class="row">
                    @foreach (var audit in offlineAudits)
                    {
                        <div class="col-12 col-lg-6 col-xl-4 mb-3">
                            <AuditCard AuditSummary="@ConvertToAuditSummary(audit)" 
                                      ShowActions="true"
                                      OnCardClicked="@HandleAuditCardClick"
                                      OnStartClicked="@HandleStartAudit"
                                      OnContinueClicked="@HandleContinueAudit"
                                      OnViewClicked="@HandleViewAudit" />
                        </div>
                    }
                </div>
                
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle me-2 text-info"></i>
                                    Offline Mode Tips
                                </h6>
                                <ul class="list-unstyled mb-0 text-start">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        You can complete audits and take photos while offline
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        All your work is automatically saved locally
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Data will sync automatically when you're back online
                                    </li>
                                    <li>
                                        <i class="fas fa-check text-success me-2"></i>
                                        Use the sync button to manually sync when connected
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <EmptyState Title="No Offline Audits"
                           Description="You don't have any audits available for offline use. Connect to the internet and download audits from the main audit list."
                           IconClass="fas fa-wifi-slash fa-3x text-muted"
                           ActionText="Go to Audits"
                           ActionIcon="fas fa-clipboard-list"
                           OnActionClick="@(() => Navigation.NavigateTo("/audits"))" />
            }
        </div>
    </div>
</div>

@code {
    private List<Audit> offlineAudits = new();
    private SyncStatus? syncStatus;
    private bool isLoading = true;
    private bool isSyncing = false;
    private bool isOnline = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadOfflineData();
        await CheckNetworkStatus();
        
        // Register for network status changes
        await PwaService.RegisterNetworkStatusChangedAsync(OnNetworkStatusChanged);
    }

    private async Task LoadOfflineData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            offlineAudits = await OfflineStorage.GetOfflineAuditsAsync();
            syncStatus = await SyncService.GetSyncStatusAsync();
            
            Logger.LogInformation("Loaded {Count} offline audits", offlineAudits.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading offline data");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshOfflineData()
    {
        await LoadOfflineData();
    }

    private async Task CheckNetworkStatus()
    {
        try
        {
            isOnline = await PwaService.IsOnlineAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking network status");
            isOnline = false;
        }
    }

    private async Task OnNetworkStatusChanged(bool online)
    {
        isOnline = online;
        await InvokeAsync(StateHasChanged);
        
        if (online)
        {
            // Refresh sync status when coming back online
            await LoadOfflineData();
        }
    }

    private async Task SyncData()
    {
        try
        {
            isSyncing = true;
            StateHasChanged();

            var result = await SyncService.SyncAsync();
            if (result.IsSuccess)
            {
                Logger.LogInformation("Sync completed successfully");
                await LoadOfflineData(); // Refresh data after sync
            }
            else
            {
                Logger.LogWarning("Sync failed: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during sync");
        }
        finally
        {
            isSyncing = false;
            StateHasChanged();
        }
    }

    private AuditSummaryDto ConvertToAuditSummary(Audit audit)
    {
        return new AuditSummaryDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignedToUserName = audit.AssignedToUser?.FullName,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory?.FactoryName,
            AreaName = audit.Area?.AreaName,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = (double?)audit.OverallScore,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.Now && 
                       audit.OverallStatus != AuditOverallStatus.Closed,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            TotalQuestions = audit.Answers?.Count ?? 0,
            AnsweredQuestions = audit.Answers?.Count(a => !string.IsNullOrEmpty(a.AnswerText) ||
                                                              a.AnswerBoolean.HasValue || 
                                                              a.AnswerNumeric.HasValue || 
                                                              a.AnswerDate.HasValue) ?? 0
        };
    }

    private async Task HandleAuditCardClick(AuditSummaryDto audit)
    {
        if (audit.OverallStatus == AuditOverallStatus.Scheduled)
        {
            await HandleStartAudit(audit);
        }
        else if (audit.OverallStatus == AuditOverallStatus.InProgress)
        {
            await HandleContinueAudit(audit);
        }
        else
        {
            await HandleViewAudit(audit);
        }
    }

    private async Task HandleStartAudit(AuditSummaryDto audit)
    {
        Navigation.NavigateTo($"/audits/{audit.Id}/execute");
        await Task.CompletedTask;
    }

    private async Task HandleContinueAudit(AuditSummaryDto audit)
    {
        Navigation.NavigateTo($"/audits/{audit.Id}/execute");
        await Task.CompletedTask;
    }

    private async Task HandleViewAudit(AuditSummaryDto audit)
    {
        Navigation.NavigateTo($"/audits/{audit.Id}/view");
        await Task.CompletedTask;
    }
}
