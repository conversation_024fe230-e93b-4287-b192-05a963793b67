# HWS Audit Platform - Quick Start Guide

Get up and running with the HWS Audit Platform in minutes! This guide provides the fastest path to a working development environment.

## 🚀 Choose Your Setup Method

### Option 1: Docker Environment (Recommended)
**Best for**: First-time setup, complete isolated environment with test data

```powershell
# 1. Start containerized environment (includes SQL Server + Active Directory)
./docker-start.ps1

# 2. Run the application
$env:ASPNETCORE_ENVIRONMENT="Docker"
dotnet run --project src/HWSAuditPlatform.ApiService

# 3. Access the application
# API Documentation: https://localhost:7490/scalar/v1
# Health Check: https://localhost:7490/health
```

**What you get**:
- SQL Server 2022 container with pre-configured databases
- Active Directory container with test users
- No local SQL Server installation required
- Test users: admin.user, manager.user, auditor.user (password: HWSAudit123!)

### Option 2: Local Development
**Best for**: Ongoing development, using existing SQL Server installation

```bash
# 1. Clone and setup
git clone [repository-url]
cd hws-audit-platform
dotnet restore

# 2. Setup database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# 3. Run application
dotnet run --project src/HWSAuditPlatform.ApiService

# 4. Access the application
# API Documentation: https://localhost:7490/scalar/v1
# Health Check: https://localhost:7490/health
```

**What you need**:
- .NET 9 SDK
- SQL Server (LocalDB, Express, or full version)
- Visual Studio 2024 or VS Code

## ✅ Verify Your Setup

### 1. Check Health Status
Visit: https://localhost:7490/health

You should see:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.123",
  "entries": {
    "database": { "status": "Healthy" },
    "file_storage": { "status": "Healthy" }
  }
}
```

### 2. Test API Documentation
Visit: https://localhost:7490/scalar/v1

You should see the interactive API documentation interface.

### 3. Test Authentication (Docker only)
```bash
curl -X POST "https://localhost:7490/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin.user",
    "password": "HWSAudit123!"
  }'
```

You should receive a JWT token in the response.

## 🔧 Configuration

### Local Development Configuration
Create `src/HWSAuditPlatform.ApiService/appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  }
}
```

### Docker Configuration
The Docker environment uses `appsettings.Docker.json` automatically when `ASPNETCORE_ENVIRONMENT=Docker`.

## 🧪 Run Tests

```bash
# Run all tests
dotnet test

# Run with coverage report
.\run-tests.ps1 -Coverage

# Run specific test category
.\run-tests.ps1 -Filter "Domain"
```

## 📚 Next Steps

### For Developers
1. **Read the Architecture**: [Architecture Overview](architecture/README.md)
2. **Learn the Patterns**: [Getting Started Guide](development/getting-started.md)
3. **Understand Testing**: [Testing Guide](development/testing-guide.md)
4. **Review Code Standards**: [Coding Standards](development/coding-standards.md)

### For API Integration
1. **API Overview**: [API Documentation](api/README.md)
2. **Authentication**: [Authentication Guide](api/authentication.md)
3. **Integration Examples**: [Integration Guide](api/integration-guide.md)

### For Deployment
1. **Docker Details**: [Docker Setup Guide](deployment/docker-setup.md)
2. **External Storage**: [External Storage Setup](deployment/external-storage.md)
3. **Production Deployment**: [Production Guide](deployment/production-deployment.md)

## 🆘 Troubleshooting

### Common Issues

**Port Conflicts (Docker)**
```powershell
# Check if ports are in use
netstat -an | findstr ":1433"
netstat -an | findstr ":389"

# Stop conflicting services
net stop MSSQLSERVER
```

**Database Connection Issues**
```bash
# Verify database exists
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Check connection string in appsettings
```

**Docker Container Issues**
```powershell
# Check container status
docker-compose ps

# View container logs
docker-compose logs -f sqlserver
docker-compose logs -f samba-ad

# Restart containers
./docker-stop.ps1
./docker-start.ps1
```

### Getting Help

- **Documentation**: Browse the comprehensive docs in `/docs`
- **Health Checks**: Monitor system status at `/health`
- **API Testing**: Use Scalar UI at `/scalar/v1`
- **Logs**: Check application logs for detailed error information

## 🎯 Key Endpoints

Once running, these endpoints are available:

- **API Documentation**: https://localhost:7490/scalar/v1
- **Health Check**: https://localhost:7490/health
- **API Info**: https://localhost:7490/health/info
- **Swagger UI**: https://localhost:7490/swagger (fallback)

## 🔐 Test Users (Docker Environment)

| Username | Password | Role | Purpose |
|----------|----------|------|---------|
| admin.user | HWSAudit123! | Administrator | Full system access |
| manager.user | HWSAudit123! | Manager | Factory-level management |
| auditor.user | HWSAudit123! | Auditor | Audit execution |
| viewer.user | HWSAudit123! | Viewer | Read-only access |

---

**Ready to dive deeper?** Check out the [Complete Developer Guide](development/getting-started.md) for detailed information about the system architecture and development workflows.
