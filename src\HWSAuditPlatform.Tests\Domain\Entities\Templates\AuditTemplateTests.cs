using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Templates;

/// <summary>
/// Unit tests for AuditTemplate entity
/// </summary>
public class AuditTemplateTests
{
    [Fact]
    public void CanBePublished_WithActiveTemplateAndQuestions_ShouldReturnTrue()
    {
        // Arrange
        var template = CreateValidTemplate();
        AddActiveQuestionToTemplate(template);

        // Act
        var result = template.CanBePublished();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void CanBePublished_WithInactiveTemplate_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsActive = false;
        AddActiveQuestionToTemplate(template);

        // Act
        var result = template.CanBePublished();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void CanBePublished_WithAlreadyPublishedTemplate_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsPublished = true;
        AddActiveQuestionToTemplate(template);

        // Act
        var result = template.CanBePublished();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void CanBePublished_WithNoQuestions_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateValidTemplate();
        // No questions added

        // Act
        var result = template.CanBePublished();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void CanBePublished_WithOnlyInactiveQuestions_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateValidTemplate();
        AddInactiveQuestionToTemplate(template);

        // Act
        var result = template.CanBePublished();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetPublishValidationErrors_WithInactiveTemplate_ShouldReturnInactiveError()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsActive = false;
        AddActiveQuestionToTemplate(template);

        // Act
        var errors = template.GetPublishValidationErrors();

        // Assert
        Assert.Contains("Template is not active", errors);
    }

    [Fact]
    public void GetPublishValidationErrors_WithAlreadyPublishedTemplate_ShouldReturnAlreadyPublishedError()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsPublished = true;
        AddActiveQuestionToTemplate(template);

        // Act
        var errors = template.GetPublishValidationErrors();

        // Assert
        Assert.Contains("Template is already published", errors);
    }

    [Fact]
    public void GetPublishValidationErrors_WithNoQuestions_ShouldReturnNoQuestionsError()
    {
        // Arrange
        var template = CreateValidTemplate();
        // No questions added

        // Act
        var errors = template.GetPublishValidationErrors();

        // Assert
        Assert.Contains("Template must have at least one question to be published", errors);
    }

    [Fact]
    public void GetPublishValidationErrors_WithMultipleIssues_ShouldReturnAllErrors()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsActive = false;
        template.IsPublished = true;
        // No questions added

        // Act
        var errors = template.GetPublishValidationErrors();

        // Assert
        Assert.Equal(3, errors.Count);
        Assert.Contains("Template is not active", errors);
        Assert.Contains("Template is already published", errors);
        Assert.Contains("Template must have at least one question to be published", errors);
    }

    [Fact]
    public void GetPublishValidationErrors_WithValidTemplate_ShouldReturnEmptyList()
    {
        // Arrange
        var template = CreateValidTemplate();
        AddActiveQuestionToTemplate(template);

        // Act
        var errors = template.GetPublishValidationErrors();

        // Assert
        Assert.Empty(errors);
    }

    [Fact]
    public void TryPublish_WithValidTemplate_ShouldPublishSuccessfully()
    {
        // Arrange
        var template = CreateValidTemplate();
        AddActiveQuestionToTemplate(template);
        var userId = "test-user-id";
        var originalUpdatedAt = template.UpdatedAt;

        // Act
        var result = template.TryPublish(userId);

        // Assert
        Assert.True(result);
        Assert.True(template.IsPublished);
        Assert.Equal(userId, template.UpdatedByUserId);
        Assert.True(template.UpdatedAt > originalUpdatedAt);
    }

    [Fact]
    public void TryPublish_WithInvalidTemplate_ShouldNotPublish()
    {
        // Arrange
        var template = CreateValidTemplate();
        // No questions added - makes it invalid
        var userId = "test-user-id";
        var originalUpdatedAt = template.UpdatedAt;

        // Act
        var result = template.TryPublish(userId);

        // Assert
        Assert.False(result);
        Assert.False(template.IsPublished);
        Assert.NotEqual(userId, template.UpdatedByUserId);
        Assert.Equal(originalUpdatedAt, template.UpdatedAt);
    }

    [Fact]
    public void TryPublish_WithAlreadyPublishedTemplate_ShouldNotPublish()
    {
        // Arrange
        var template = CreateValidTemplate();
        template.IsPublished = true;
        AddActiveQuestionToTemplate(template);
        var userId = "test-user-id";
        var originalUpdatedAt = template.UpdatedAt;

        // Act
        var result = template.TryPublish(userId);

        // Assert
        Assert.False(result);
        Assert.True(template.IsPublished); // Still published, but not by this call
        Assert.NotEqual(userId, template.UpdatedByUserId);
        Assert.Equal(originalUpdatedAt, template.UpdatedAt);
    }

    private static AuditTemplate CreateValidTemplate()
    {
        return new AuditTemplate
        {
            Id = 1,
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = false,
            IsActive = true,
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            UpdatedAt = DateTime.UtcNow.AddDays(-1),
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "creator-id"
        };
    }

    private static void AddActiveQuestionToTemplate(AuditTemplate template)
    {
        var question = new Question
        {
            Id = 1,
            AuditTemplateId = template.Id,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "creator-id"
        };

        template.Questions.Add(question);
    }

    private static void AddInactiveQuestionToTemplate(AuditTemplate template)
    {
        var question = new Question
        {
            Id = 1,
            AuditTemplateId = template.Id,
            QuestionText = "Inactive Test Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsActive = false, // Inactive question
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "creator-id"
        };

        template.Questions.Add(question);
    }
}
