@using HWSAuditPlatform.Domain.Entities.Audits

<div class="findings-container">
    <div class="findings-header">
        <h4 class="findings-title">
            <i class="fas fa-exclamation-triangle"></i>
            Findings Required
        </h4>
        <p class="findings-subtitle">Please provide at least one finding to explain why this answer is "No"</p>
    </div>

    <div class="findings-list">
        @if (Findings?.Any() == true)
        {
            @for (int i = 0; i < Findings.Count; i++)
            {
                var index = i; // Capture for closure
                <div class="finding-item">
                    <div class="finding-input-group">
                        <div class="finding-number">@(index + 1)</div>
                        <textarea class="finding-input @(HasValidationError(index) ? "error" : "")"
                                  rows="3"
                                  placeholder="Describe the finding..."
                                  value="@Findings[index]"
                                  @onchange="@((e) => UpdateFinding(index, e.Value?.ToString() ?? ""))"
                                  @oninput="@((e) => UpdateFinding(index, e.Value?.ToString() ?? ""))">
                        </textarea>
                        <button type="button" 
                                class="remove-finding-btn" 
                                @onclick="@(() => RemoveFinding(index))"
                                title="Remove finding">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    @if (HasValidationError(index))
                    {
                        <div class="finding-error">Finding cannot be empty</div>
                    }
                </div>
            }
        }
        else
        {
            <div class="no-findings-message">
                <i class="fas fa-info-circle"></i>
                No findings added yet. Click "Add Finding" to get started.
            </div>
        }
    </div>

    <div class="findings-actions">
        <button type="button" 
                class="add-finding-btn" 
                @onclick="AddFinding">
            <i class="fas fa-plus"></i>
            Add Finding
        </button>
        
        @if (ShowValidationSummary && !IsValid)
        {
            <div class="validation-summary">
                <i class="fas fa-exclamation-triangle"></i>
                At least one finding is required for "No" answers
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<string> Findings { get; set; } = new();
    [Parameter] public EventCallback<List<string>> FindingsChanged { get; set; }
    [Parameter] public bool ShowValidationSummary { get; set; } = false;

    private HashSet<int> validationErrors = new();

    public bool IsValid => Findings?.Any(f => !string.IsNullOrWhiteSpace(f)) == true;

    protected override void OnParametersSet()
    {
        Console.WriteLine($"OnParametersSet called. Findings count: {Findings?.Count ?? 0}");
        // Ensure we have at least one empty finding to start with
        if (Findings?.Any() != true)
        {
            Console.WriteLine("Creating initial empty finding");
            Findings = new List<string> { "" };
        }
        else
        {
            Console.WriteLine($"Findings already exist: {string.Join(", ", Findings.Select((f, i) => $"[{i}]: '{f}'"))}");
        }
    }

    private async Task AddFinding()
    {
        Console.WriteLine($"AddFinding called. Current count: {Findings?.Count ?? 0}");
        // Create a new list to ensure proper change detection
        var newFindings = new List<string>(Findings ?? new List<string>());
        newFindings.Add("");
        Console.WriteLine($"New list created with count: {newFindings.Count}");

        // Don't modify the parameter directly, let the parent handle it
        await NotifyFindingsChanged(newFindings);
        Console.WriteLine("AddFinding completed");
    }

    private async Task RemoveFinding(int index)
    {
        if (index >= 0 && index < Findings.Count)
        {
            // Create a new list to ensure proper change detection
            var newFindings = new List<string>(Findings);
            newFindings.RemoveAt(index);
            validationErrors.Remove(index);

            // Ensure we always have at least one finding input
            if (!newFindings.Any())
            {
                newFindings.Add("");
            }

            // Don't modify the parameter directly, let the parent handle it
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateFinding(int index, string value)
    {
        if (index >= 0 && index < Findings.Count)
        {
            // Create a new list to ensure proper change detection
            var newFindings = new List<string>(Findings);
            newFindings[index] = value;

            // Clear validation error if finding is no longer empty
            if (!string.IsNullOrWhiteSpace(value))
            {
                validationErrors.Remove(index);
            }
            else
            {
                validationErrors.Add(index);
            }

            // Don't modify the parameter directly, let the parent handle it
            await NotifyFindingsChanged(newFindings);
        }
    }

    private bool HasValidationError(int index)
    {
        return ShowValidationSummary && validationErrors.Contains(index);
    }

    private async Task NotifyFindingsChanged(List<string>? newFindings = null)
    {
        var findingsToNotify = newFindings ?? Findings;
        if (FindingsChanged.HasDelegate)
        {
            await FindingsChanged.InvokeAsync(findingsToNotify);
        }
    }

    public void ValidateFindings()
    {
        validationErrors.Clear();
        
        for (int i = 0; i < Findings.Count; i++)
        {
            if (string.IsNullOrWhiteSpace(Findings[i]))
            {
                validationErrors.Add(i);
            }
        }
    }
}

<style>
    .findings-container {
        margin: 1.5rem 0;
        padding: 1.5rem;
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        border: 2px solid var(--industrial-border);
        border-radius: 12px;
        border-left: 4px solid var(--industrial-red);
    }

    .findings-header {
        margin-bottom: 1.5rem;
    }

    .findings-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--industrial-red);
        font-size: 1.1rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .findings-title i {
        font-size: 1.2rem;
    }

    .findings-subtitle {
        color: var(--industrial-text-muted);
        font-size: 0.9rem;
        margin: 0;
        line-height: 1.4;
    }

    .findings-list {
        margin-bottom: 1.5rem;
    }

    .finding-item {
        margin-bottom: 1rem;
    }

    .finding-input-group {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .finding-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: var(--industrial-teal);
        color: white;
        border-radius: 50%;
        font-weight: 700;
        font-size: 0.9rem;
        flex-shrink: 0;
        margin-top: 0.5rem;
    }

    .finding-input {
        flex: 1;
        background-color: var(--industrial-light-gray);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        padding: 0.875rem;
        font-size: 0.95rem;
        font-family: inherit;
        transition: all 0.3s ease;
        resize: vertical;
        min-height: 80px;
    }

    .finding-input:focus {
        background-color: white;
        border-color: var(--industrial-teal);
        color: var(--industrial-dark);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
        outline: none;
    }

    .finding-input.error {
        border-color: var(--industrial-red);
        background-color: rgba(239, 68, 68, 0.1);
    }

    .finding-input::placeholder {
        color: var(--industrial-text-muted);
    }

    .remove-finding-btn {
        background: var(--industrial-red);
        color: white;
        border: none;
        border-radius: 6px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;
        margin-top: 0.5rem;
    }

    .remove-finding-btn:hover {
        background: #dc2626;
        transform: scale(1.05);
    }

    .finding-error {
        color: var(--industrial-red);
        font-size: 0.85rem;
        margin-top: 0.5rem;
        margin-left: 3rem;
        font-weight: 500;
    }

    .no-findings-message {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--industrial-text-muted);
        font-style: italic;
        padding: 1.5rem;
        text-align: center;
        justify-content: center;
        background: rgba(20, 184, 166, 0.05);
        border: 1px dashed var(--industrial-border-light);
        border-radius: 8px;
    }

    .findings-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .add-finding-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        background: var(--industrial-teal);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .add-finding-btn:hover {
        background: var(--industrial-teal-dark);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
    }

    .validation-summary {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid var(--industrial-red);
        border-radius: 8px;
        padding: 1rem;
        color: var(--industrial-red);
        font-weight: 500;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .findings-container {
            padding: 1.25rem;
        }

        .finding-input-group {
            gap: 0.5rem;
        }

        .finding-number {
            width: 28px;
            height: 28px;
            font-size: 0.8rem;
        }

        .remove-finding-btn {
            width: 28px;
            height: 28px;
        }

        .finding-error {
            margin-left: 2.25rem;
        }

        .findings-title {
            font-size: 1rem;
        }

        .add-finding-btn {
            padding: 1rem;
            font-size: 0.9rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .finding-input {
            padding: 1rem;
            font-size: 16px; /* Prevents zoom on iOS */
            min-height: 88px;
        }

        .add-finding-btn {
            padding: 1.25rem;
            min-height: 56px;
        }

        .remove-finding-btn {
            width: 40px;
            height: 40px;
        }

        .finding-number {
            width: 36px;
            height: 36px;
        }
    }
</style>
