using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;
using HWSAuditPlatform.Application.Workflow.Commands.CreateCorrectionRequest;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Workflow.Commands;

public class CreateCorrectionRequestCommandHandlerTests : BaseDbTestClass
{
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly CreateCorrectionRequestCommandHandler _handler;

    public CreateCorrectionRequestCommandHandlerTests()
    {
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _handler = new CreateCorrectionRequestCommandHandler(Context, _mockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_CreatesCorrectionRequest()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "audit1",
            RequestReason = "Need to update some findings based on new information"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);

        var correctionRequest = await Context.AuditCorrectionRequests
            .FirstOrDefaultAsync(cr => cr.Id == result);

        Assert.NotNull(correctionRequest);
        Assert.Equal("audit1", correctionRequest.AuditId);
        Assert.Equal("user1", correctionRequest.RequestedByUserId);
        Assert.Equal("Need to update some findings based on new information", correctionRequest.RequestReason);
        Assert.Equal(CorrectionRequestStatus.PendingApproval, correctionRequest.Status);
        Assert.True(correctionRequest.RequestedAt <= DateTime.UtcNow);
    }

    [Fact]
    public async Task Handle_WithNonExistentAudit_ThrowsNotFoundException()
    {
        // Arrange
        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "nonexistent",
            RequestReason = "Test reason"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithUnauthorizedUser_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user1 = new User { Id = "user1", FirstName = "John", LastName = "Doe" };
        var user2 = new User { Id = "user2", FirstName = "Jane", LastName = "Smith" };

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1", // Assigned to user1
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddRangeAsync(user1, user2);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user2"); // Different user trying to request correction

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "audit1",
            RequestReason = "Test reason"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => 
            _handler.Handle(command, CancellationToken.None));
        
        Assert.Equal("You can only request corrections for your own audits", exception.Message);
    }

    [Fact]
    public async Task Handle_WithInvalidAuditStatus_ThrowsInvalidOperationException()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Closed, // Invalid status for correction request
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "audit1",
            RequestReason = "Test reason"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
        
        Assert.Contains("Cannot request correction for audit in Closed status", exception.Message);
    }

    [Fact]
    public async Task Handle_WithExistingPendingRequest_ThrowsInvalidOperationException()
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit = new Audit
        {
            Id = "audit1",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = AuditOverallStatus.Submitted,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var existingRequest = new AuditCorrectionRequest
        {
            Id = "existing1",
            AuditId = "audit1",
            RequestedByUserId = "user1",
            RequestReason = "Existing request",
            RequestedAt = DateTime.UtcNow.AddHours(-1),
            Status = CorrectionRequestStatus.PendingApproval,
            CreatedAt = DateTime.UtcNow.AddHours(-1),
            UpdatedAt = DateTime.UtcNow.AddHours(-1),
            CreatedByUserId = "user1",
            UpdatedByUserId = "user1"
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.AuditCorrectionRequests.AddAsync(existingRequest);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "audit1",
            RequestReason = "Another request"
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
        
        Assert.Equal("There is already a pending correction request for this audit", exception.Message);
    }

    [Theory]
    [InlineData(AuditOverallStatus.Submitted)]
    [InlineData(AuditOverallStatus.ManagerReviewed)]
    [InlineData(AuditOverallStatus.PendingManagerReview)]
    public async Task Handle_WithValidAuditStatuses_CreatesCorrectionRequest(AuditOverallStatus status)
    {
        // Arrange
        var factory = new Factory { Id = 1, FactoryName = "Test Factory" };
        var area = new Area { Id = 1, AreaName = "Test Area", FactoryId = 1 };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };
        var user = new User { Id = "user1", FirstName = "John", LastName = "Doe" };

        var audit = new Audit
        {
            Id = $"audit_{status}",
            AuditTemplateId = 1,
            FactoryId = 1,
            AreaId = 1,
            AssignedToUserId = "user1",
            OverallStatus = status,
            CompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        await Context.Factories.AddAsync(factory);
        await Context.Areas.AddAsync(area);
        await Context.AuditTemplates.AddAsync(template);
        await Context.Users.AddAsync(user);
        await Context.Audits.AddAsync(audit);
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns("user1");

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = $"audit_{status}",
            RequestReason = $"Test reason for {status}"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);

        var correctionRequest = await Context.AuditCorrectionRequests
            .FirstOrDefaultAsync(cr => cr.Id == result);

        Assert.NotNull(correctionRequest);
        Assert.Equal($"audit_{status}", correctionRequest.AuditId);
        Assert.Equal(CorrectionRequestStatus.PendingApproval, correctionRequest.Status);
    }

    [Fact]
    public async Task Handle_WithUnauthenticatedUser_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        _mockCurrentUserService.Setup(x => x.UserId).Returns((string?)null);

        var command = new CreateCorrectionRequestCommand
        {
            AuditId = "audit1",
            RequestReason = "Test reason"
        };

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }
}
