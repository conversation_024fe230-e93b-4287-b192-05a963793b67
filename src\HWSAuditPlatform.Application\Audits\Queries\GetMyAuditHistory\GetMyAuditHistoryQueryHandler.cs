using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Queries.GetMyAuditHistory;

/// <summary>
/// Handler for GetMyAuditHistoryQuery
/// </summary>
public class GetMyAuditHistoryQueryHandler : BaseQueryHandler<GetMyAuditHistoryQuery, PaginatedResult<AuditSummaryDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetMyAuditHistoryQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<PaginatedResult<AuditSummaryDto>> Handle(GetMyAuditHistoryQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.AssignedToUserId == currentUserId && 
                       (a.OverallStatus == AuditOverallStatus.Submitted ||
                        a.OverallStatus == AuditOverallStatus.ManagerReviewed ||
                        a.OverallStatus == AuditOverallStatus.PendingManagerReview ||
                        a.OverallStatus == AuditOverallStatus.Closed ||
                        a.OverallStatus == AuditOverallStatus.PendingCorrection))
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchPattern = $"%{request.SearchTerm}%";
            query = query.Where(a =>
                EF.Functions.Like(a.AuditTemplate.TemplateName, searchPattern) ||
                EF.Functions.Like(a.Factory.FactoryName, searchPattern) ||
                EF.Functions.Like(a.Area.AreaName, searchPattern));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == request.Status.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(a => a.AreaId == request.AreaId.Value);
        }

        if (request.CompletedDateFrom.HasValue)
        {
            query = query.Where(a => a.CompletedAt >= request.CompletedDateFrom.Value);
        }

        if (request.CompletedDateTo.HasValue)
        {
            query = query.Where(a => a.CompletedAt <= request.CompletedDateTo.Value);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "templatename" => request.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.AuditTemplate.TemplateName)
                : query.OrderBy(a => a.AuditTemplate.TemplateName),
            "factory" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.Factory.FactoryName)
                : query.OrderBy(a => a.Factory.FactoryName),
            "area" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.Area.AreaName)
                : query.OrderBy(a => a.Area.AreaName),
            "status" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.OverallStatus)
                : query.OrderBy(a => a.OverallStatus),
            "score" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.OverallScore)
                : query.OrderBy(a => a.OverallScore),
            "scheduleddate" => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.ScheduledDate)
                : query.OrderBy(a => a.ScheduledDate),
            _ => request.SortDirection.ToLower() == "desc"
                ? query.OrderByDescending(a => a.CompletedAt)
                : query.OrderBy(a => a.CompletedAt)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and select data
        var audits = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(a => new AuditSummaryDto
            {
                Id = a.Id,
                AuditTemplateName = a.AuditTemplate.TemplateName,
                AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                ScheduledDate = a.ScheduledDate,
                DueDate = a.DueDate,
                OverallStatus = a.OverallStatus,
                FactoryName = a.Factory.FactoryName,
                AreaName = a.Area.AreaName,
                OverallScore = a.OverallScore,
                IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow && 
                    a.OverallStatus != AuditOverallStatus.Closed && 
                    a.OverallStatus != AuditOverallStatus.Cancelled
            })
            .ToListAsync(cancellationToken);

        return new PaginatedResult<AuditSummaryDto>(audits, totalCount, request.PageNumber, request.PageSize);
    }
}
