using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.RegularExpressions;

namespace HWSAuditPlatform.Infrastructure.Services.FileStorage;

/// <summary>
/// Local file system implementation of file storage service
/// </summary>
public class LocalFileStorageService : IFileStorageService
{
    private readonly ILogger<LocalFileStorageService> _logger;
    private readonly LocalFileStorageOptions _options;

    public LocalFileStorageService(
        IOptions<LocalFileStorageOptions> options,
        ILogger<LocalFileStorageService> logger)
    {
        _options = options.Value;
        _logger = logger;

        // Validate and ensure the storage directory exists
        EnsureStorageDirectoryExists();
    }

    private void EnsureStorageDirectoryExists()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(_options.StoragePath))
            {
                throw new InvalidOperationException("Storage path is not configured");
            }

            // Check if the drive exists (for external drives like D:\)
            var rootPath = Path.GetPathRoot(_options.StoragePath);
            if (!string.IsNullOrEmpty(rootPath) && !Directory.Exists(rootPath))
            {
                throw new DirectoryNotFoundException($"Drive '{rootPath}' is not available. Please ensure the external drive is connected and accessible.");
            }

            // Create the directory if it doesn't exist
            if (!Directory.Exists(_options.StoragePath))
            {
                _logger.LogInformation("Creating storage directory: {StoragePath}", _options.StoragePath);
                Directory.CreateDirectory(_options.StoragePath);
                _logger.LogInformation("Storage directory created successfully: {StoragePath}", _options.StoragePath);
            }
            else
            {
                _logger.LogInformation("Using existing storage directory: {StoragePath}", _options.StoragePath);
            }

            // Test write permissions
            var testFile = Path.Combine(_options.StoragePath, ".write_test");
            try
            {
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                _logger.LogInformation("Storage directory write permissions verified: {StoragePath}", _options.StoragePath);
            }
            catch (UnauthorizedAccessException)
            {
                throw new UnauthorizedAccessException($"No write permissions for storage directory: {_options.StoragePath}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize storage directory: {StoragePath}", _options.StoragePath);
            throw;
        }
    }

    public async Task<UploadedFile> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify storage directory is still accessible
            if (!Directory.Exists(_options.StoragePath))
            {
                _logger.LogWarning("Storage directory no longer exists, attempting to recreate: {StoragePath}", _options.StoragePath);
                EnsureStorageDirectoryExists();
            }

            var storagePath = GenerateFilePath(fileName);
            var fullPath = Path.Combine(_options.StoragePath, storagePath);
            var directory = Path.GetDirectoryName(fullPath);

            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                _logger.LogDebug("Creating subdirectory: {Directory}", directory);
                Directory.CreateDirectory(directory);
            }

            using var fileStreamOutput = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
            await fileStream.CopyToAsync(fileStreamOutput, cancellationToken);

            // Get file size from the written file
            var fileInfo = new FileInfo(fullPath);
            var fileSize = fileInfo.Length;
            var url = await GenerateDownloadUrlAsync(storagePath, TimeSpan.FromDays(365), cancellationToken);

            _logger.LogInformation("Successfully uploaded file {FileName} to {StoragePath} (Size: {FileSize} bytes)",
                fileName, storagePath, fileSize);

            return new UploadedFile
            {
                FileName = fileName,
                ContentType = contentType,
                Size = fileSize,
                StoragePath = storagePath,
                Url = url
            };
        }
        catch (DirectoryNotFoundException ex)
        {
            _logger.LogError(ex, "Storage drive not accessible while uploading file {FileName}", fileName);
            throw new InvalidOperationException("File storage drive is not accessible. Please check if the external drive is connected.", ex);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogError(ex, "Access denied while uploading file {FileName} to {StoragePath}", fileName, _options.StoragePath);
            throw new InvalidOperationException("Access denied to file storage location. Please check permissions.", ex);
        }
        catch (IOException ex)
        {
            _logger.LogError(ex, "I/O error while uploading file {FileName}", fileName);
            throw new InvalidOperationException("File system error occurred during upload. Please try again.", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error uploading file {FileName}", fileName);
            throw;
        }
    }

    public async Task<UploadedFile> UploadFileAsync(string fileName, Stream fileStream, string contentType, CancellationToken cancellationToken = default)
    {
        return await UploadFileAsync(fileName, contentType, fileStream, cancellationToken);
    }

    public async Task<Stream> GetFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);

            // Check if storage directory is accessible
            if (!Directory.Exists(_options.StoragePath))
            {
                _logger.LogError("Storage directory not accessible: {StoragePath}", _options.StoragePath);
                throw new DirectoryNotFoundException($"Storage drive is not accessible: {_options.StoragePath}");
            }

            if (!System.IO.File.Exists(fullPath))
            {
                _logger.LogWarning("File not found: {FilePath}", filePath);
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(fullPath, cancellationToken);
            _logger.LogDebug("Successfully retrieved file: {FilePath} (Size: {FileSize} bytes)", filePath, fileBytes.Length);
            return new MemoryStream(fileBytes);
        }
        catch (DirectoryNotFoundException ex)
        {
            _logger.LogError(ex, "Storage drive not accessible while getting file {FilePath}", filePath);
            throw new InvalidOperationException("File storage drive is not accessible. Please check if the external drive is connected.", ex);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogError(ex, "Access denied while getting file {FilePath}", filePath);
            throw new InvalidOperationException("Access denied to file storage location. Please check permissions.", ex);
        }
        catch (FileNotFoundException)
        {
            // Re-throw FileNotFoundException as-is
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting file {FilePath}", filePath);
            throw;
        }
    }

    public Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
                _logger.LogInformation("Successfully deleted file {FilePath}", filePath);
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
            throw;
        }
    }

    public Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            return Task.FromResult(System.IO.File.Exists(fullPath));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if file exists {FilePath}", filePath);
            throw;
        }
    }

    public Task<long> GetFileSizeAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (!System.IO.File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileInfo = new FileInfo(fullPath);
            return Task.FromResult(fileInfo.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file size {FilePath}", filePath);
            throw;
        }
    }

    public Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime, CancellationToken cancellationToken = default)
    {
        // For local storage, we return the file path as the URL
        // In a real implementation, you might generate a signed URL or token
        var url = $"{_options.BaseUrl?.TrimEnd('/')}/files/{filePath}";
        return Task.FromResult(url);
    }

    private static string GenerateFilePath(string fileName)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy/MM/dd");
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var extension = Path.GetExtension(fileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

        // Sanitize the filename for storage path - remove special characters and spaces
        var sanitizedName = SanitizeFileName(nameWithoutExtension);

        return Path.Combine(timestamp, $"{sanitizedName}_{uniqueId}{extension}");
    }

    private static string SanitizeFileName(string fileName)
    {
        // Remove or replace characters that are not suitable for file paths
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = fileName;

        // Replace spaces and common special characters
        sanitized = Regex.Replace(sanitized, @"[^\w\.-]", "_");

        // Remove multiple underscores
        sanitized = Regex.Replace(sanitized, @"_{2,}", "_");

        // Remove leading/trailing underscores
        sanitized = sanitized.Trim('_');

        return string.IsNullOrEmpty(sanitized) ? "file" : sanitized;
    }
}

/// <summary>
/// Configuration options for Local File Storage
/// </summary>
public class LocalFileStorageOptions
{
    public const string SectionName = "LocalFileStorage";

    public string StoragePath { get; set; } = "wwwroot/uploads";
    public string? BaseUrl { get; set; }
}

/// <summary>
/// Represents an uploaded file
/// </summary>
public class UploadedFile
{
    /// <summary>
    /// Original filename
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Content type of the file
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Storage path of the file
    /// </summary>
    public string StoragePath { get; set; } = string.Empty;

    /// <summary>
    /// URL to access the file
    /// </summary>
    public string Url { get; set; } = string.Empty;
}
