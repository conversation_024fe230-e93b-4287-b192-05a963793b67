# HWS Audit Platform - Templates & Scheduling API

## Overview

This document provides comprehensive documentation for the Audit Templates and Recurring Audit Scheduling endpoints of the HWS Audit Platform API. These endpoints handle audit template management and automated audit scheduling.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **Authentication**: J<PERSON><PERSON> required
- **Content-Type**: `application/json`
- **API Version**: `1.0`

## Template Management Endpoints

### GET /templates

Get a paginated list of audit templates with filtering options.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `searchTerm` (string, optional): Search in template name or description
- `category` (string, optional): Filter by template category
- `isPublished` (bool, optional): Filter by published status
- `factoryId` (int, optional): Filter by factory applicability

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 10,
        "name": "Safety Audit Template v2.1",
        "description": "Comprehensive safety audit for manufacturing floors",
        "category": "Safety",
        "version": "2.1",
        "isPublished": true,
        "questionCount": 25,
        "estimatedDurationMinutes": 120,
        "createdBy": "admin.user",
        "createdAt": "2023-12-01T00:00:00Z",
        "publishedAt": "2023-12-15T00:00:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 15,
    "totalPages": 2,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/templates?category=Safety&isPublished=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /templates/{id}

Get detailed information about a specific audit template including all questions.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (int): Template ID

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": 10,
    "name": "Safety Audit Template v2.1",
    "description": "Comprehensive safety audit for manufacturing floors",
    "category": "Safety",
    "version": "2.1",
    "isPublished": true,
    "estimatedDurationMinutes": 120,
    "instructions": "Complete all questions and provide evidence where required",
    "questions": [
      {
        "id": 101,
        "questionText": "Are all safety signs clearly visible and in good condition?",
        "questionType": "YesNo",
        "isRequired": true,
        "evidenceRequired": true,
        "orderIndex": 1,
        "helpText": "Check all safety signage in the area",
        "options": null
      },
      {
        "id": 102,
        "questionText": "Rate the overall cleanliness of the work area",
        "questionType": "SingleSelect",
        "isRequired": true,
        "evidenceRequired": false,
        "orderIndex": 2,
        "helpText": "Consider floors, equipment, and workstations",
        "options": [
          { "id": 1, "text": "Excellent", "value": "5" },
          { "id": 2, "text": "Good", "value": "4" },
          { "id": 3, "text": "Fair", "value": "3" },
          { "id": 4, "text": "Poor", "value": "2" },
          { "id": 5, "text": "Unacceptable", "value": "1" }
        ]
      }
    ],
    "createdBy": "admin.user",
    "createdAt": "2023-12-01T00:00:00Z",
    "publishedAt": "2023-12-15T00:00:00Z",
    "recordVersion": 5
  }
}
```

**Error Responses**:
- `404 Not Found`: Template not found

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/templates/10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /templates

Create a new audit template.

**Authorization**: Manager role or above required

**Request Body**:
```json
{
  "name": "Quality Control Template v1.0",
  "description": "Standard quality control audit for production lines",
  "category": "Quality",
  "version": "1.0",
  "estimatedDurationMinutes": 90,
  "instructions": "Follow the checklist systematically and document all findings"
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": 25,
  "message": "Template created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `409 Conflict`: Template name already exists

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/templates" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Quality Control Template v1.0",
    "description": "Standard quality control audit for production lines",
    "category": "Quality",
    "version": "1.0",
    "estimatedDurationMinutes": 90,
    "instructions": "Follow the checklist systematically and document all findings"
  }'
```

### POST /templates/{id}/questions

Add a new question to an audit template.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (int): Template ID

**Request Body**:
```json
{
  "questionText": "Are all emergency exits clearly marked and unobstructed?",
  "questionType": "YesNo",
  "isRequired": true,
  "evidenceRequired": true,
  "orderIndex": 10,
  "helpText": "Check all emergency exit routes in the area",
  "options": null
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": 150,
  "message": "Question added successfully"
}
```

**Question Types**:
- `YesNo`: Simple yes/no question
- `SingleSelect`: Single choice from predefined options
- `MultiSelect`: Multiple choices from predefined options
- `Numeric`: Numeric input with optional min/max validation
- `Text`: Free text input
- `LongText`: Multi-line text input

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/templates/10/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "questionText": "Are all emergency exits clearly marked and unobstructed?",
    "questionType": "YesNo",
    "isRequired": true,
    "evidenceRequired": true,
    "orderIndex": 10,
    "helpText": "Check all emergency exit routes in the area"
  }'
```

### POST /templates/{id}/publish

Publish a template to make it available for creating audits.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (int): Template ID

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Template cannot be published (missing questions or validation errors)
- `404 Not Found`: Template not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/templates/10/publish" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Recurring Audit Scheduling Endpoints

### GET /recurring-audits

Get a paginated list of recurring audit settings.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `factoryId` (int, optional): Filter by factory ID
- `isEnabled` (bool, optional): Filter by enabled status

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "cr1234567890abcdef",
        "settingName": "Monthly Safety Audit - Production Floor A",
        "auditTemplateId": 10,
        "auditTemplateName": "Safety Audit Template v2.1",
        "factoryId": 1,
        "factoryName": "Main Factory",
        "areaId": 5,
        "areaName": "Production Floor A",
        "assignedToUserId": "c9876543210fedcba",
        "assignedToUserName": "John Doe",
        "recurrencePattern": "Monthly",
        "dayOfMonth": 15,
        "timeOfDay": "09:00:00",
        "isEnabled": true,
        "nextGenerationDate": "2024-02-15T09:00:00Z",
        "lastGeneratedAt": "2024-01-15T09:00:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 12,
    "totalPages": 2,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/recurring-audits?factoryId=1&isEnabled=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /recurring-audits

Create a new recurring audit setting.

**Authorization**: Manager role or above required

**Request Body**:
```json
{
  "settingName": "Weekly Quality Check - Assembly Line 1",
  "auditTemplateId": 15,
  "factoryId": 1,
  "areaId": 8,
  "assignedToUserId": "c9876543210fedcba",
  "recurrencePattern": "Weekly",
  "dayOfWeek": 1,
  "timeOfDay": "14:00:00",
  "isEnabled": true
}
```

**Recurrence Patterns**:
- `Daily`: Every day at specified time
- `Weekly`: Every week on specified day
- `Monthly`: Every month on specified day
- `Quarterly`: Every 3 months on specified day

**Response** (201 Created):
```json
{
  "success": true,
  "data": "cr2468135790abcdef",
  "message": "Recurring audit setting created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `404 Not Found`: Template, factory, area, or user not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/recurring-audits" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "settingName": "Weekly Quality Check - Assembly Line 1",
    "auditTemplateId": 15,
    "factoryId": 1,
    "areaId": 8,
    "assignedToUserId": "c9876543210fedcba",
    "recurrencePattern": "Weekly",
    "dayOfWeek": 1,
    "timeOfDay": "14:00:00",
    "isEnabled": true
  }'
```

### PATCH /recurring-audits/{id}/toggle

Enable or disable a recurring audit setting.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (string): Recurring audit setting ID

**Query Parameters**:
- `isEnabled` (bool): Whether to enable or disable the setting
- `recordVersion` (int): Record version for concurrency control

**Response** (204 No Content)

**Error Responses**:
- `404 Not Found`: Recurring audit setting not found
- `409 Conflict`: Concurrency conflict (record version mismatch)

**Example**:
```bash
curl -X PATCH "https://localhost:7490/api/v1/recurring-audits/cr1234567890abcdef/toggle?isEnabled=false&recordVersion=3" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Error Handling

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "auditTemplateId",
      "message": "Audit template is required"
    }
  ]
}
```

## Business Rules

- Only published templates can be used to create audits
- Templates cannot be deleted if they have associated audits
- Recurring audit settings automatically generate audits based on schedule
- Questions in published templates cannot be modified (create new version instead)
- Template versioning follows semantic versioning (major.minor format)
- Recurring audits respect factory working hours and holidays
