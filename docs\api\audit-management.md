# HWS Audit Platform - Audit Management API

## Overview

This document provides comprehensive documentation for the Audit Management endpoints of the HWS Audit Platform API. These endpoints handle the complete audit lifecycle including creation, execution, submission, and review.

## Base Information

- **Base URL**: `https://localhost:7490/api/v1`
- **Authentication**: JWT Bearer <PERSON>ken required
- **Content-Type**: `application/json`
- **API Version**: `1.0`

## Audit Endpoints

### GET /audits

Get a paginated list of audits with comprehensive filtering options.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `searchTerm` (string, optional): Search in audit title or description
- `status` (string, optional): Filter by audit status (Draft, InProgress, Submitted, Approved, Rejected)
- `factoryId` (int, optional): Filter by factory ID
- `assignedToUserId` (string, optional): Filter by assigned user ID
- `areaId` (int, optional): Filter by area ID
- `templateId` (int, optional): Filter by audit template ID
- `scheduledDateFrom` (datetime, optional): Filter by scheduled date range start
- `scheduledDateTo` (datetime, optional): Filter by scheduled date range end
- `dueDateFrom` (datetime, optional): Filter by due date range start
- `dueDateTo` (datetime, optional): Filter by due date range end
- `isOverdue` (bool, optional): Filter overdue audits
- `sortBy` (string, optional): Sort field
- `sortDirection` (string, optional): "asc" or "desc"

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "c1234567890abcdef",
        "title": "Monthly Safety Audit",
        "description": "Comprehensive safety audit for production floor",
        "status": "InProgress",
        "factoryId": 1,
        "factoryName": "Main Factory",
        "areaId": 5,
        "areaName": "Production Floor A",
        "auditTemplateId": 10,
        "auditTemplateName": "Safety Audit Template v2.1",
        "assignedToUserId": "c9876543210fedcba",
        "assignedToUserName": "John Doe",
        "scheduledDate": "2024-01-15T09:00:00Z",
        "dueDate": "2024-01-17T17:00:00Z",
        "isOverdue": false,
        "createdAt": "2024-01-10T08:00:00Z",
        "startedAt": "2024-01-15T09:15:00Z",
        "completionPercentage": 65
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 45,
    "totalPages": 5,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/audits?pageNumber=1&pageSize=20&status=InProgress&factoryId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /audits/{id}

Get detailed information about a specific audit including all answers and attachments.

**Authorization**: Auditor role or above required

**Path Parameters**:
- `id` (string): Audit ID

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "id": "c1234567890abcdef",
    "title": "Monthly Safety Audit",
    "description": "Comprehensive safety audit for production floor",
    "status": "InProgress",
    "factory": {
      "id": 1,
      "name": "Main Factory",
      "location": "New York"
    },
    "area": {
      "id": 5,
      "name": "Production Floor A",
      "code": "PFA"
    },
    "auditTemplate": {
      "id": 10,
      "name": "Safety Audit Template v2.1",
      "version": "2.1"
    },
    "assignedToUser": {
      "id": "c9876543210fedcba",
      "username": "john.doe",
      "fullName": "John Doe"
    },
    "scheduledDate": "2024-01-15T09:00:00Z",
    "dueDate": "2024-01-17T17:00:00Z",
    "startedAt": "2024-01-15T09:15:00Z",
    "submittedAt": null,
    "reviewedAt": null,
    "auditorComments": "Initial inspection completed, proceeding with detailed checks",
    "reviewerComments": null,
    "answers": [
      {
        "id": "ca1234567890abcdef",
        "questionId": 101,
        "questionText": "Are all safety signs clearly visible?",
        "questionType": "YesNo",
        "answer": "Yes",
        "comments": "All signs are in good condition",
        "evidenceRequired": true,
        "attachments": [
          {
            "id": "cf1234567890abcdef",
            "fileName": "safety_signs_photo.jpg",
            "filePath": "audits/c1234567890abcdef/evidence/safety_signs_photo.jpg",
            "fileSize": 2048576,
            "contentType": "image/jpeg",
            "uploadedAt": "2024-01-15T10:30:00Z"
          }
        ]
      }
    ],
    "completionPercentage": 65,
    "createdAt": "2024-01-10T08:00:00Z",
    "updatedAt": "2024-01-15T11:00:00Z",
    "recordVersion": 8
  }
}
```

**Error Responses**:
- `404 Not Found`: Audit not found

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/audits/c1234567890abcdef" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### GET /audits/my-audits

Get audits assigned to the current user.

**Authorization**: Auditor role or above required

**Query Parameters**:
- `pageNumber` (int, optional): Page number (default: 1)
- `pageSize` (int, optional): Items per page (default: 10, max: 50)
- `status` (string, optional): Filter by audit status
- `isOverdue` (bool, optional): Filter overdue audits

**Response** (200 OK):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "c1234567890abcdef",
        "title": "Monthly Safety Audit",
        "status": "InProgress",
        "factoryName": "Main Factory",
        "areaName": "Production Floor A",
        "scheduledDate": "2024-01-15T09:00:00Z",
        "dueDate": "2024-01-17T17:00:00Z",
        "isOverdue": false,
        "completionPercentage": 65
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 8,
    "totalPages": 1,
    "hasPreviousPage": false,
    "hasNextPage": false
  }
}
```

**Example**:
```bash
curl -X GET "https://localhost:7490/api/v1/audits/my-audits?status=InProgress" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /audits

Create a new audit.

**Authorization**: Manager role or above required

**Request Body**:
```json
{
  "title": "Weekly Quality Audit",
  "description": "Quality control audit for production line B",
  "auditTemplateId": 15,
  "factoryId": 1,
  "areaId": 8,
  "assignedToUserId": "c9876543210fedcba",
  "scheduledDate": "2024-01-20T08:00:00Z",
  "dueDate": "2024-01-22T17:00:00Z"
}
```

**Response** (201 Created):
```json
{
  "success": true,
  "data": "c2468135790abcdef",
  "message": "Audit created successfully"
}
```

**Error Responses**:
- `400 Bad Request`: Validation errors
- `404 Not Found`: Template, factory, area, or user not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/audits" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Weekly Quality Audit",
    "description": "Quality control audit for production line B",
    "auditTemplateId": 15,
    "factoryId": 1,
    "areaId": 8,
    "assignedToUserId": "c9876543210fedcba",
    "scheduledDate": "2024-01-20T08:00:00Z",
    "dueDate": "2024-01-22T17:00:00Z"
  }'
```

### POST /audits/{id}/start

Start an audit execution (changes status from Draft to InProgress).

**Authorization**: Assigned auditor or Manager/Admin

**Path Parameters**:
- `id` (string): Audit ID

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Audit cannot be started (wrong status)
- `403 Forbidden`: Not authorized to start this audit
- `404 Not Found`: Audit not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/audits/c1234567890abcdef/start" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### POST /audits/{id}/submit

Submit an audit for review (changes status from InProgress to Submitted).

**Authorization**: Assigned auditor or Manager/Admin

**Path Parameters**:
- `id` (string): Audit ID

**Request Body**:
```json
{
  "auditorComments": "Audit completed successfully. All safety protocols are being followed."
}
```

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Audit cannot be submitted (wrong status or incomplete)
- `403 Forbidden`: Not authorized to submit this audit
- `404 Not Found`: Audit not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/audits/c1234567890abcdef/submit" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "auditorComments": "Audit completed successfully. All safety protocols are being followed."
  }'
```

### POST /audits/{id}/review

Review and approve/reject a submitted audit.

**Authorization**: Manager role or above required

**Path Parameters**:
- `id` (string): Audit ID

**Request Body**:
```json
{
  "isApproved": true,
  "reviewerComments": "Audit is thorough and well-documented. Approved."
}
```

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Audit cannot be reviewed (wrong status)
- `404 Not Found`: Audit not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/audits/c1234567890abcdef/review" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "isApproved": true,
    "reviewerComments": "Audit is thorough and well-documented. Approved."
  }'
```

## Audit Answer Management

### POST /audits/{auditId}/answers

Submit or update an answer for a specific question in an audit.

**Authorization**: Assigned auditor or Manager/Admin

**Path Parameters**:
- `auditId` (string): Audit ID

**Request Body**:
```json
{
  "questionId": 101,
  "answer": "Yes",
  "comments": "All equipment is properly maintained and documented",
  "attachmentIds": ["cf1234567890abcdef", "cf2468135790abcdef"]
}
```

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Invalid answer format or audit not in progress
- `403 Forbidden`: Not authorized to answer this audit
- `404 Not Found`: Audit or question not found

**Example**:
```bash
curl -X POST "https://localhost:7490/api/v1/audits/c1234567890abcdef/answers" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "questionId": 101,
    "answer": "Yes",
    "comments": "All equipment is properly maintained and documented",
    "attachmentIds": ["cf1234567890abcdef"]
  }'
```

### DELETE /audits/{auditId}/answers/{questionId}

Delete an answer for a specific question in an audit.

**Authorization**: Assigned auditor or Manager/Admin

**Path Parameters**:
- `auditId` (string): Audit ID
- `questionId` (int): Question ID

**Response** (204 No Content)

**Error Responses**:
- `400 Bad Request`: Audit not in progress
- `403 Forbidden`: Not authorized to modify this audit
- `404 Not Found`: Audit, question, or answer not found

**Example**:
```bash
curl -X DELETE "https://localhost:7490/api/v1/audits/c1234567890abcdef/answers/101" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Audit Status Flow

1. **Draft** → **InProgress** (via `/start`)
2. **InProgress** → **Submitted** (via `/submit`)
3. **Submitted** → **Approved** or **Rejected** (via `/review`)

## Error Handling

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "auditTemplateId",
      "message": "Audit template is required"
    }
  ]
}
```

## Business Rules

- Only assigned auditors can start and submit their audits
- Managers and Admins can perform any audit operation
- Audits must have all required questions answered before submission
- Evidence attachments are required for questions marked as `evidenceRequired`
- Overdue audits are automatically flagged in the system
