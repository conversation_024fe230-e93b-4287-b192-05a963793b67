namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the different user roles in the audit platform.
/// Maps to the user_role enum in the database.
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Developer administrator with full system access
    /// </summary>
    DevAdmin,

    /// <summary>
    /// System manager with template creation and user management capabilities
    /// </summary>
    SystemManager,

    /// <summary>
    /// Process owner with audit assignment and results management capabilities (replaces Manager)
    /// </summary>
    ProcessOwner,

    /// <summary>
    /// Auditor who performs audits in the field
    /// </summary>
    Auditor,

    /// <summary>
    /// Viewer with read-only access
    /// </summary>
    Viewer
}
