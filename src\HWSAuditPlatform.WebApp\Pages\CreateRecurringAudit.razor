@page "/recurring-audits/create"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "ProcessOwner,SystemManager,DevAdmin")]
@inject IRecurringAuditApiService RecurringAuditService
@inject ITemplateApiService TemplateService
@inject IOrganizationApiService OrganizationService
@inject IUserApiService UserService
@inject NavigationManager Navigation
@inject ILogger<CreateRecurringAudit> Logger

<PageTitle>Create Recurring Audit - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-plus-lg me-2"></i>
                        Create Recurring Audit Schedule
                    </h2>
                    <p class="text-muted mb-0">Set up automated audit scheduling</p>
                </div>
                <button class="btn btn-outline-secondary" @onclick="GoBack">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to List
                </button>
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    @errorMessage
                    <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    @successMessage
                    <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
                </div>
            }

            @if (isLoadingData)
            {
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="text-muted">Loading form data...</div>
                    </div>
                </div>
            }
            else
            {
                <EditForm Model="@model" OnValidSubmit="@HandleValidSubmit">
                    <DataAnnotationsValidator />

                    <div class="row">
                        <div class="col-lg-8">
                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Basic Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Setting Name <span class="text-danger">*</span></label>
                                        <InputText @bind-Value="model.SettingName" class="form-control" placeholder="e.g., Weekly Safety Audit" />
                                        <ValidationMessage For="@(() => model.SettingName)" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Deadline Days <span class="text-danger">*</span></label>
                                        <InputNumber @bind-Value="model.DeadlineDays" class="form-control" min="1" max="365" />
                                        <ValidationMessage For="@(() => model.DeadlineDays)" />
                                        <div class="form-text">Days from scheduled date to complete the audit</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Template Selection -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-file-earmark-ruled me-2"></i>
                                    Audit Template
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label class="form-label">Select Template <span class="text-danger">*</span></label>
                                        <InputSelect @bind-Value="model.AuditTemplateId" class="form-select" disabled="@isLoadingData">
                                            <option value="0">@(isLoadingData ? "Loading templates..." : "-- Select Template --")</option>
                                            @foreach (var template in templates)
                                            {
                                                <option value="@template.Id">@template.TemplateName</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="@(() => model.AuditTemplateId)" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Location Selection -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    Location
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Factory <span class="text-danger">*</span></label>
                                        <InputSelect @bind-Value="model.FactoryId" class="form-select" disabled="@isLoadingData" @onchange="OnFactoryChangedHandler">
                                            <option value="0">@(isLoadingData ? "Loading factories..." : "-- Select Factory --")</option>
                                            @foreach (var factory in factories.Where(f => f.IsActive))
                                            {
                                                <option value="@factory.Id">@factory.FactoryName (@factory.LocationName)</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="@(() => model.FactoryId)" />
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Area</label>
                                        <InputSelect @bind-Value="model.AreaId" class="form-select" disabled="@(model.FactoryId == 0)" @onchange="OnAreaChangedHandler">
                                            <option value="">@(model.FactoryId > 0 ? "-- Select Area --" : "Select Factory First")</option>
                                            @foreach (var area in areas)
                                            {
                                                <option value="@area.Id">@area.AreaName</option>
                                            }
                                        </InputSelect>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Sub Area</label>
                                        <InputSelect @bind-Value="model.SubAreaId" class="form-select" disabled="@(!model.AreaId.HasValue || model.AreaId == 0)">
                                            <option value="">@(model.AreaId.HasValue && model.AreaId > 0 ? "-- Select Sub Area --" : "Select Area First")</option>
                                            @foreach (var subArea in subAreas)
                                            {
                                                <option value="@subArea.Id">@subArea.SubAreaName</option>
                                            }
                                        </InputSelect>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Assignment -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-check me-2"></i>
                                    Assignment
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Assignment Type <span class="text-danger">*</span></label>
                                        <InputSelect @bind-Value="model.AssignmentType" class="form-select" @onchange="OnAssignmentTypeChangedHandler">
                                            <option value="@AssignmentType.Individual">Individual User</option>
                                            <option value="@AssignmentType.GroupAny">Group (Any Member)</option>
                                            <option value="@AssignmentType.GroupAllScheduled">Group (All Members)</option>
                                        </InputSelect>
                                    </div>
                                    <div class="col-md-6">
                                        @if (model.AssignmentType == AssignmentType.Individual)
                                        {
                                            <label class="form-label">Assign to User <span class="text-danger">*</span></label>
                                            <InputSelect @bind-Value="model.AssignToUserId" class="form-select" disabled="@isLoadingData">
                                                <option value="">@(isLoadingData ? "Loading users..." : "-- Select User --")</option>
                                                @foreach (var user in users)
                                                {
                                                    <option value="@user.AdObjectGuid">@user.FullName (@user.Username)</option>
                                                }
                                            </InputSelect>
                                            <ValidationMessage For="@(() => model.AssignToUserId)" />
                                        }
                                        else
                                        {
                                            <label class="form-label">Assign to Group <span class="text-danger">*</span></label>
                                            <InputSelect @bind-Value="model.AssignToUserGroupId" class="form-select" disabled="@isLoadingData">
                                                <option value="">@(isLoadingData ? "Loading groups..." : "-- Select Group --")</option>
                                                @foreach (var group in userGroups)
                                                {
                                                    <option value="@group.Id">@group.GroupName</option>
                                                }
                                            </InputSelect>
                                            <ValidationMessage For="@(() => model.AssignToUserGroupId)" />
                                            @if (model.AssignmentType == AssignmentType.GroupAllScheduled)
                                            {
                                                <div class="form-text">Individual audits will be created for each group member</div>
                                            }
                                            else
                                            {
                                                <div class="form-text">Any group member can claim and complete the audit</div>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recurrence Pattern -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-arrow-repeat me-2"></i>
                                    Recurrence Pattern
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Frequency <span class="text-danger">*</span></label>
                                        <InputSelect @bind-Value="model.RecurrenceRule.FrequencyType" class="form-select" @onchange="OnFrequencyChangedHandler">
                                            <option value="@FrequencyType.DAILY">Daily</option>
                                            <option value="@FrequencyType.WEEKLY">Weekly</option>
                                            <option value="@FrequencyType.MONTHLY">Monthly</option>
                                            <option value="@FrequencyType.YEARLY">Yearly</option>
                                        </InputSelect>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Interval <span class="text-danger">*</span></label>
                                        <InputNumber @bind-Value="model.RecurrenceRule.Interval" class="form-control" min="1" max="365" />
                                        <ValidationMessage For="@(() => model.RecurrenceRule.Interval)" />
                                        <div class="form-text">Every @model.RecurrenceRule.Interval @GetFrequencyText()</div>
                                    </div>
                                    <div class="col-md-4">
                                        @if (model.RecurrenceRule.FrequencyType == FrequencyType.WEEKLY)
                                        {
                                            <label class="form-label">Day of Week</label>
                                            <InputSelect @bind-Value="model.RecurrenceRule.DayOfWeek" class="form-select">
                                                <option value="@DayOfWeek.Monday">Monday</option>
                                                <option value="@DayOfWeek.Tuesday">Tuesday</option>
                                                <option value="@DayOfWeek.Wednesday">Wednesday</option>
                                                <option value="@DayOfWeek.Thursday">Thursday</option>
                                                <option value="@DayOfWeek.Friday">Friday</option>
                                                <option value="@DayOfWeek.Saturday">Saturday</option>
                                                <option value="@DayOfWeek.Sunday">Sunday</option>
                                            </InputSelect>
                                        }
                                        else if (model.RecurrenceRule.FrequencyType == FrequencyType.MONTHLY)
                                        {
                                            <label class="form-label">Day of Month</label>
                                            <InputNumber @bind-Value="model.RecurrenceRule.DayOfMonth" class="form-control" min="1" max="31" />
                                            <div class="form-text">Day of the month (1-31)</div>
                                        }
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                        <InputDate @bind-Value="startDate" class="form-control" />
                                        <ValidationMessage For="@(() => model.RecurrenceRule.StartDate)" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">End Date</label>
                                        <InputDate @bind-Value="endDate" class="form-control" />
                                        <div class="form-text">Leave empty for no end date</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Preview -->
                        <div class="card sticky-top">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-eye me-2"></i>
                                    Preview
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Schedule:</strong>
                                    <div class="text-muted">@GetScheduleDescription()</div>
                                </div>
                                <div class="mb-3">
                                    <strong>Next 3 Generations:</strong>
                                    <ul class="list-unstyled mt-2">
                                        @foreach (var date in GetNextGenerationDates().Take(3))
                                        {
                                            <li class="text-muted">• @date.ToString("MMM dd, yyyy")</li>
                                        }
                                    </ul>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success" disabled="@isSubmitting">
                                        @if (isSubmitting)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        }
                                        else
                                        {
                                            <i class="bi bi-check-lg me-2"></i>
                                        }
                                        Create Schedule
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" @onclick="GoBack">
                                        <i class="bi bi-x-lg me-2"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </EditForm>
            }
        </div>
    </div>
</div>

@code {
    private CreateRecurringAuditSettingRequest model = new()
    {
        SettingName = string.Empty,
        DeadlineDays = 7,
        AssignmentType = AssignmentType.Individual,
        RecurrenceRule = new CreateRecurrenceRuleRequest
        {
            FrequencyType = FrequencyType.WEEKLY,
            Interval = 1,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            DayOfWeek = DayOfWeek.Monday
        }
    };

    private DateTime startDate = DateTime.Today;
    private DateTime? endDate;
    private bool isSubmitting = false;
    private bool isLoadingData = true;
    private string? errorMessage;
    private string? successMessage;

    // Data collections
    private IEnumerable<AuditTemplate> templates = new List<AuditTemplate>();
    private IEnumerable<FactorySummary> factories = new List<FactorySummary>();
    private IEnumerable<AreaSummary> areas = new List<AreaSummary>();
    private IEnumerable<SubAreaSummary> subAreas = new List<SubAreaSummary>();
    private IEnumerable<UserSummary> users = new List<UserSummary>();
    private IEnumerable<UserGroupSummary> userGroups = new List<UserGroupSummary>();

    protected override async Task OnInitializedAsync()
    {
        // Initialize dates
        model.RecurrenceRule.StartDate = DateOnly.FromDateTime(startDate);

        // Load initial data
        await LoadInitialData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            isLoadingData = true;

            // Load all initial data in parallel
            var templateTask = TemplateService.GetTemplatesAsync(isActive: true);
            var factoryTask = OrganizationService.GetFactoriesAsync();
            var userTask = OrganizationService.GetUsersAsync();
            var userGroupTask = OrganizationService.GetUserGroupsAsync();

            await Task.WhenAll(templateTask, factoryTask, userTask, userGroupTask);

            templates = await templateTask;
            factories = await factoryTask;
            users = await userTask;
            userGroups = await userGroupTask;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading initial data for recurring audit creation");
            errorMessage = "Failed to load required data. Please refresh the page and try again.";
        }
        finally
        {
            isLoadingData = false;
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            isSubmitting = true;
            errorMessage = null;

            // Update model with date values
            model.RecurrenceRule.StartDate = DateOnly.FromDateTime(startDate);
            model.RecurrenceRule.EndDate = endDate.HasValue ? DateOnly.FromDateTime(endDate.Value) : null;

            var settingId = await RecurringAuditService.CreateRecurringAuditSettingAsync(model);

            if (!string.IsNullOrEmpty(settingId))
            {
                successMessage = "Recurring audit schedule created successfully!";
                await Task.Delay(1500); // Show success message briefly
                Navigation.NavigateTo("/recurring-audits");
            }
            else
            {
                errorMessage = "Failed to create recurring audit schedule. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating recurring audit setting");
            errorMessage = "An error occurred while creating the schedule. Please try again.";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/recurring-audits");
    }

    private async Task OnFactoryChanged()
    {
        // Reset dependent selections
        model.AreaId = null;
        model.SubAreaId = null;
        areas = new List<AreaSummary>();
        subAreas = new List<SubAreaSummary>();

        // Load areas for selected factory
        if (model.FactoryId > 0)
        {
            try
            {
                areas = await OrganizationService.GetAreasByFactoryAsync(model.FactoryId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading areas for factory {FactoryId}", model.FactoryId);
            }
        }
    }

    private async Task OnAreaChanged()
    {
        // Reset dependent selections
        model.SubAreaId = null;
        subAreas = new List<SubAreaSummary>();

        // Load sub areas for selected area
        if (model.AreaId.HasValue && model.AreaId > 0)
        {
            try
            {
                subAreas = await OrganizationService.GetSubAreasByAreaAsync(model.AreaId.Value);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading sub-areas for area {AreaId}", model.AreaId);
            }
        }
    }

    private void OnAssignmentTypeChanged()
    {
        // Clear assignment selections when type changes
        model.AssignToUserId = null;
        model.AssignToUserGroupId = null;
    }

    private void OnFrequencyChanged()
    {
        // Reset frequency-specific fields
        model.RecurrenceRule.DayOfWeek = model.RecurrenceRule.FrequencyType == FrequencyType.WEEKLY ? DayOfWeek.Monday : null;
        model.RecurrenceRule.DayOfMonth = model.RecurrenceRule.FrequencyType == FrequencyType.MONTHLY ? 1 : null;
    }

    private string GetFrequencyText()
    {
        return model.RecurrenceRule.FrequencyType switch
        {
            FrequencyType.DAILY => model.RecurrenceRule.Interval == 1 ? "day" : "days",
            FrequencyType.WEEKLY => model.RecurrenceRule.Interval == 1 ? "week" : "weeks",
            FrequencyType.MONTHLY => model.RecurrenceRule.Interval == 1 ? "month" : "months",
            FrequencyType.YEARLY => model.RecurrenceRule.Interval == 1 ? "year" : "years",
            _ => "period"
        };
    }

    private string GetScheduleDescription()
    {
        var frequency = model.RecurrenceRule.FrequencyType.ToString().ToLower();
        var interval = model.RecurrenceRule.Interval;

        var description = interval == 1 ? $"Every {frequency.TrimEnd('s')}" : $"Every {interval} {frequency}";

        if (model.RecurrenceRule.FrequencyType == FrequencyType.WEEKLY && model.RecurrenceRule.DayOfWeek.HasValue)
        {
            description += $" on {model.RecurrenceRule.DayOfWeek}";
        }
        else if (model.RecurrenceRule.FrequencyType == FrequencyType.MONTHLY && model.RecurrenceRule.DayOfMonth.HasValue)
        {
            description += $" on day {model.RecurrenceRule.DayOfMonth}";
        }

        return description;
    }

    private IEnumerable<DateTime> GetNextGenerationDates()
    {
        var dates = new List<DateTime>();
        var current = startDate;

        for (int i = 0; i < 5; i++)
        {
            dates.Add(current);

            current = model.RecurrenceRule.FrequencyType switch
            {
                FrequencyType.DAILY => current.AddDays(model.RecurrenceRule.Interval),
                FrequencyType.WEEKLY => current.AddDays(7 * model.RecurrenceRule.Interval),
                FrequencyType.MONTHLY => current.AddMonths(model.RecurrenceRule.Interval),
                FrequencyType.YEARLY => current.AddYears(model.RecurrenceRule.Interval),
                _ => current.AddDays(1)
            };
        }

        return dates;
    }

    // Event handlers for manual binding
    private async Task OnFactoryChangedHandler(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var factoryId))
        {
            model.FactoryId = factoryId;
            await OnFactoryChanged();
        }
    }

    private async Task OnAreaChangedHandler(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var areaId))
        {
            model.AreaId = areaId;
            await OnAreaChanged();
        }
        else if (string.IsNullOrEmpty(e.Value?.ToString()))
        {
            model.AreaId = null;
            await OnAreaChanged();
        }
    }

    private void OnAssignmentTypeChangedHandler(ChangeEventArgs e)
    {
        if (Enum.TryParse<AssignmentType>(e.Value?.ToString(), out var assignmentType))
        {
            model.AssignmentType = assignmentType;
            OnAssignmentTypeChanged();
        }
    }

    private void OnFrequencyChangedHandler(ChangeEventArgs e)
    {
        if (Enum.TryParse<FrequencyType>(e.Value?.ToString(), out var frequencyType))
        {
            model.RecurrenceRule.FrequencyType = frequencyType;
            OnFrequencyChanged();
        }
    }
}
