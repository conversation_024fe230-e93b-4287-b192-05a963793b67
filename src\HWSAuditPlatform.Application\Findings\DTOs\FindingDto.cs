using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.DTOs;

/// <summary>
/// Data Transfer Object for Finding entity
/// </summary>
public class FindingDto : AuditableDto<string>
{
    public string AuditAnswerId { get; set; } = string.Empty;
    public string? AuditId { get; set; }
    public string? FindingCode { get; set; }
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    public string? RootCauseAnalysis { get; set; }
    public string? ImmediateActionTaken { get; set; }
    public FindingStatus Status { get; set; }
    public string ReportedByUserId { get; set; } = string.Empty;
    public string? ReportedByUserName { get; set; }
    public string? ReportedByUserFullName { get; set; }
    public DateOnly? DueDate { get; set; }
    
    // Area-based responsibility properties
    public string? ResponsibleUserId { get; set; }
    public string? ResponsibleUserName { get; set; }
    public string? ResponsibleUserFullName { get; set; }
    public string? RetrospectiveAnalystUserId { get; set; }
    public string? RetrospectiveAnalystUserName { get; set; }
    public string? RetrospectiveAnalystUserFullName { get; set; }
    
    // Finding categorization properties
    public int? FindingCategoryId { get; set; }
    public string? FindingCategoryName { get; set; }
    public string? FindingCategoryColorCode { get; set; }
    public string? FindingCategoryIconName { get; set; }
    
    // Audit context
    public string? AuditTemplateName { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public string? SubAreaName { get; set; }
    
    // Corrective actions summary
    public List<CorrectiveActionSummaryDto> CorrectiveActions { get; set; } = new();
    
    // Computed properties
    public bool IsOverdue { get; set; }
    public bool IsOpen { get; set; }
    public bool IsClosed { get; set; }
    public int CorrectiveActionCount { get; set; }
    public int OpenCorrectiveActionCount { get; set; }
    public bool HasResponsibleUser => !string.IsNullOrEmpty(ResponsibleUserId);
    public bool HasRetrospectiveAnalyst => !string.IsNullOrEmpty(RetrospectiveAnalystUserId);
    public bool IsCategorized => FindingCategoryId.HasValue;
}

/// <summary>
/// Summary DTO for Finding entity used in lists
/// </summary>
public class FindingSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string? FindingCode { get; set; }
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    public FindingStatus Status { get; set; }
    public DateOnly? DueDate { get; set; }
    public string? ResponsibleUserName { get; set; }
    public string? FindingCategoryName { get; set; }
    public string? FindingCategoryColorCode { get; set; }
    public string? AreaName { get; set; }
    public string? FactoryName { get; set; }
    public int CorrectiveActionCount { get; set; }
    public int OpenCorrectiveActionCount { get; set; }
    public bool IsOverdue { get; set; }
    public bool IsOpen { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Summary DTO for CorrectiveAction entity
/// </summary>
public class CorrectiveActionSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string ActionDescription { get; set; } = string.Empty;
    public string AssignedToUserId { get; set; } = string.Empty;
    public string? AssignedToUserName { get; set; }
    public DateOnly DueDate { get; set; }
    public DateOnly? CompletionDate { get; set; }
    public CorrectiveActionStatus Status { get; set; }
    public bool IsOverdue { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsInProgress { get; set; }
}
