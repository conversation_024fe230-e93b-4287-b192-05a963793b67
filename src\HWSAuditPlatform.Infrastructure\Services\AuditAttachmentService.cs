using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Infrastructure.Services.FileStorage;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Infrastructure.Services;

/// <summary>
/// Service for managing audit attachments
/// </summary>
public class AuditAttachmentService : IAuditAttachmentService
{
    private readonly IFileStorageService _fileStorageService;
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<AuditAttachmentService> _logger;

    // Allowed file types for audit evidence
    private static readonly HashSet<string> AllowedContentTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        "image/jpeg",
        "image/jpg", 
        "image/png",
        "image/gif",
        "image/bmp",
        "image/webp",
        "application/pdf",
        "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "video/mp4",
        "video/avi",
        "video/mov",
        "video/wmv"
    };

    private const long MaxFileSizeBytes = 50 * 1024 * 1024; // 50 MB

    public AuditAttachmentService(
        IFileStorageService fileStorageService,
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<AuditAttachmentService> logger)
    {
        _fileStorageService = fileStorageService;
        _context = context;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<AuditAttachment> UploadAttachmentAsync(
        string auditAnswerId,
        string fileName,
        string contentType,
        Stream fileStream,
        string? description = null,
        CancellationToken cancellationToken = default)
    {
        // Validate file type
        if (!IsAllowedContentType(contentType))
        {
            throw new InvalidOperationException($"File type '{contentType}' is not allowed for audit attachments");
        }

        // Validate file size
        if (fileStream.Length > MaxFileSizeBytes)
        {
            throw new InvalidOperationException($"File size exceeds maximum allowed size of {MaxFileSizeBytes / (1024 * 1024)} MB");
        }

        // Verify audit answer exists and user has permission
        var auditAnswer = await _context.AuditAnswers
            .Include(aa => aa.Audit)
            .FirstOrDefaultAsync(aa => aa.Id == auditAnswerId, cancellationToken);

        if (auditAnswer == null)
        {
            throw new InvalidOperationException($"Audit answer with ID {auditAnswerId} not found");
        }

        // Check if user has permission to add attachments to this audit
        var currentUserId = _currentUserService.UserId;
        if (auditAnswer.Audit.AssignedToUserId != currentUserId)
        {
            throw new UnauthorizedAccessException("User does not have permission to add attachments to this audit");
        }

        try
        {
            // Upload file to storage
            var uploadedFile = await _fileStorageService.UploadFileAsync(fileName, contentType, fileStream, cancellationToken);

            // Create attachment entity
            var attachment = new AuditAttachment
            {
                Id = CuidGenerator.Generate(),
                AuditAnswerId = auditAnswerId,
                FileName = GenerateUniqueFileName(fileName),
                OriginalFileName = fileName,
                ContentType = contentType,
                FileSize = uploadedFile.Size,
                FilePath = uploadedFile.StoragePath,
                Description = description,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedByUserId = currentUserId
            };

            // Save to database
            await _context.AuditAttachments.AddAsync(attachment, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully uploaded attachment {FileName} for audit answer {AuditAnswerId}", 
                fileName, auditAnswerId);

            return attachment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading attachment {FileName} for audit answer {AuditAnswerId}", 
                fileName, auditAnswerId);
            throw;
        }
    }

    public async Task<(Stream FileStream, string ContentType, string FileName)> DownloadAttachmentAsync(
        string attachmentId,
        CancellationToken cancellationToken = default)
    {
        var attachment = await _context.AuditAttachments
            .Include(a => a.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
            .FirstOrDefaultAsync(a => a.Id == attachmentId, cancellationToken);

        if (attachment == null)
        {
            throw new InvalidOperationException($"Attachment with ID {attachmentId} not found");
        }

        // Check if user has permission to download this attachment
        var currentUserId = _currentUserService.UserId;
        if (attachment.AuditAnswer.Audit.AssignedToUserId != currentUserId)
        {
            // Additional check: allow managers and admins to download any attachment
            // This would require role checking which we'll implement later
            throw new UnauthorizedAccessException("User does not have permission to download this attachment");
        }

        try
        {
            var fileStream = await _fileStorageService.GetFileAsync(attachment.FilePath!, cancellationToken);
            return (fileStream, attachment.ContentType!, attachment.OriginalFileName!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading attachment {AttachmentId}", attachmentId);
            throw;
        }
    }

    public async Task DeleteAttachmentAsync(string attachmentId, CancellationToken cancellationToken = default)
    {
        var attachment = await _context.AuditAttachments
            .Include(a => a.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
            .FirstOrDefaultAsync(a => a.Id == attachmentId, cancellationToken);

        if (attachment == null)
        {
            throw new InvalidOperationException($"Attachment with ID {attachmentId} not found");
        }

        // Check if user has permission to delete this attachment
        var currentUserId = _currentUserService.UserId;
        if (attachment.AuditAnswer.Audit.AssignedToUserId != currentUserId)
        {
            throw new UnauthorizedAccessException("User does not have permission to delete this attachment");
        }

        try
        {
            // Delete from file storage
            if (!string.IsNullOrEmpty(attachment.FilePath))
            {
                await _fileStorageService.DeleteFileAsync(attachment.FilePath, cancellationToken);
            }

            // Delete from database
            _context.AuditAttachments.Remove(attachment);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully deleted attachment {AttachmentId}", attachmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting attachment {AttachmentId}", attachmentId);
            throw;
        }
    }

    public async Task<IReadOnlyList<AuditAttachment>> GetAttachmentsByAuditAnswerAsync(
        string auditAnswerId,
        CancellationToken cancellationToken = default)
    {
        return await _context.AuditAttachments
            .Where(a => a.AuditAnswerId == auditAnswerId)
            .OrderBy(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ValidateAttachmentPermissionAsync(
        string attachmentId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        var attachment = await _context.AuditAttachments
            .Include(a => a.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
            .FirstOrDefaultAsync(a => a.Id == attachmentId, cancellationToken);

        if (attachment == null)
        {
            return false;
        }

        return attachment.AuditAnswer.Audit.AssignedToUserId == userId;
    }

    public async Task<long> GetTotalAttachmentSizeAsync(string auditId, CancellationToken cancellationToken = default)
    {
        return await _context.AuditAttachments
            .Where(a => a.AuditAnswer.AuditId == auditId)
            .SumAsync(a => a.FileSize ?? 0, cancellationToken);
    }

    private static bool IsAllowedContentType(string contentType)
    {
        return AllowedContentTypes.Contains(contentType);
    }

    private static string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        
        return $"{nameWithoutExtension}_{timestamp}_{uniqueId}{extension}";
    }


}
