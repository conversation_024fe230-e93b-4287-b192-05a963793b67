using FluentAssertions;
using HWSAuditPlatform.Domain.Exceptions;
using HWSAuditPlatform.Domain.ValueObjects;

namespace HWSAuditPlatform.Tests.Domain.ValueObjects;

public class AuditScoreTests
{
    [Fact]
    public void Constructor_WithValidValues_ShouldSetProperties()
    {
        // Arrange & Act
        var score = new AuditScore(85, 100);

        // Assert
        score.Value.Should().Be(85);
        score.MaxValue.Should().Be(100);
    }

    [Fact]
    public void Constructor_WithZeroMaxValue_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new AuditScore(50, 0);
        act.Should().Throw<ValidationException>()
           .WithMessage("Maximum value must be greater than zero");
    }

    [Fact]
    public void Constructor_WithNegativeMaxValue_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new AuditScore(50, -10);
        act.Should().Throw<ValidationException>()
           .WithMessage("Maximum value must be greater than zero");
    }

    [Fact]
    public void Constructor_WithNegativeValue_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new AuditScore(-10, 100);
        act.Should().Throw<ValidationException>()
           .WithMessage("Score value cannot be negative");
    }

    [Fact]
    public void Constructor_WithValueExceedingMax_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new AuditScore(150, 100);
        act.Should().Throw<ValidationException>()
           .WithMessage("Score value cannot exceed maximum value");
    }

    [Fact]
    public void Percentage_ShouldCalculateCorrectly()
    {
        // Arrange
        var score = new AuditScore(75, 100);

        // Act & Assert
        score.Percentage.Should().Be(75);
    }

    [Fact]
    public void Percentage_WithZeroValue_ShouldReturnZero()
    {
        // Arrange
        var score = new AuditScore(0, 100);

        // Act & Assert
        score.Percentage.Should().Be(0);
    }

    [Fact]
    public void Percentage_WithDecimalResult_ShouldReturnExactValue()
    {
        // Arrange
        var score = new AuditScore(33, 100);

        // Act & Assert
        score.Percentage.Should().Be(33);
    }

    [Fact]
    public void GetPercentage_WithDefaultDecimals_ShouldRoundToTwoPlaces()
    {
        // Arrange
        var score = new AuditScore(33.333m, 100);

        // Act & Assert
        score.GetPercentage().Should().Be(33.33m);
    }

    [Fact]
    public void GetPercentage_WithCustomDecimals_ShouldRoundCorrectly()
    {
        // Arrange
        var score = new AuditScore(33.333m, 100);

        // Act & Assert
        score.GetPercentage(1).Should().Be(33.3m);
        score.GetPercentage(0).Should().Be(33m);
    }

    [Fact]
    public void IsPerfect_WithMaxScore_ShouldReturnTrue()
    {
        // Arrange
        var score = new AuditScore(100, 100);

        // Act & Assert
        score.IsPerfect.Should().BeTrue();
    }

    [Fact]
    public void IsPerfect_WithLessThanMaxScore_ShouldReturnFalse()
    {
        // Arrange
        var score = new AuditScore(99, 100);

        // Act & Assert
        score.IsPerfect.Should().BeFalse();
    }

    [Fact]
    public void IsFailing_WithLessThan60Percent_ShouldReturnTrue()
    {
        // Arrange
        var score = new AuditScore(59, 100);

        // Act & Assert
        score.IsFailing.Should().BeTrue();
    }

    [Fact]
    public void IsFailing_With60PercentOrMore_ShouldReturnFalse()
    {
        // Arrange
        var score1 = new AuditScore(60, 100);
        var score2 = new AuditScore(85, 100);

        // Act & Assert
        score1.IsFailing.Should().BeFalse();
        score2.IsFailing.Should().BeFalse();
    }

    [Fact]
    public void ToString_ShouldFormatCorrectly()
    {
        // Arrange
        var score = new AuditScore(85, 100);

        // Act
        var result = score.ToString();

        // Assert
        result.Should().Be("85/100 (85.00%)");
    }

    [Fact]
    public void Equals_WithSameValues_ShouldReturnTrue()
    {
        // Arrange
        var score1 = new AuditScore(85, 100);
        var score2 = new AuditScore(85, 100);

        // Act & Assert
        score1.Should().Be(score2);
        (score1 == score2).Should().BeTrue();
        (score1 != score2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentValues_ShouldReturnFalse()
    {
        // Arrange
        var score1 = new AuditScore(85, 100);
        var score2 = new AuditScore(90, 100);

        // Act & Assert
        score1.Should().NotBe(score2);
        (score1 == score2).Should().BeFalse();
        (score1 != score2).Should().BeTrue();
    }

    [Fact]
    public void Zero_ShouldCreateZeroScore()
    {
        // Act
        var score = AuditScore.Zero(100);

        // Assert
        score.Value.Should().Be(0);
        score.MaxValue.Should().Be(100);
        score.Percentage.Should().Be(0);
    }

    [Fact]
    public void Perfect_ShouldCreatePerfectScore()
    {
        // Act
        var score = AuditScore.Perfect(100);

        // Assert
        score.Value.Should().Be(100);
        score.MaxValue.Should().Be(100);
        score.IsPerfect.Should().BeTrue();
    }
}
