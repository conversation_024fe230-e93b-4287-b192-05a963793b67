using System.Net.Http.Json;
using System.Text.Json;
using Blazored.LocalStorage;
using HWSAuditPlatform.WebApp.Models;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of authentication service for WebApp
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<AuthenticationService> _logger;
    private const string TokenKey = "auth_token";
    private const string UserKey = "user_info";

    public AuthenticationService(
        HttpClient httpClient,
        ILocalStorageService localStorage,
        ILogger<AuthenticationService> logger)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _logger = logger;
    }

    public async Task<AuthenticationResult> LoginAsync(string username, string password)
    {
        try
        {
            _logger.LogInformation("Attempting login for user: {Username}", username);

            var loginRequest = new LoginRequest
            {
                Username = username,
                Password = password
            };

            // Use the correct API endpoint format
            var response = await _httpClient.PostAsJsonAsync("api/v1/auth/login", loginRequest);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("Login response: {Response}", responseContent);

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuthenticationResult>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var authResult = apiResponse.Data;
                    authResult.IsSuccess = true;

                    // Extract user ID from JWT token claims or use username as fallback
                    var userId = ExtractUserIdFromToken(authResult.Token) ?? username;

                    // Store token and user info
                    await _localStorage.SetItemAsync(TokenKey, authResult.Token);
                    await _localStorage.SetItemAsync(UserKey, new UserInfo
                    {
                        UserId = userId,
                        Username = authResult.Username,
                        Email = authResult.Email,
                        FullName = authResult.FullName,
                        Role = authResult.Role,
                        FactoryId = authResult.FactoryId,
                        IsActive = true
                    });

                    // Set default authorization header for future requests
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authResult.Token);

                    _logger.LogInformation("Login successful for user: {Username}", username);
                    return authResult;
                }
                else
                {
                    _logger.LogWarning("API response indicates failure: {Message}", apiResponse?.Message);
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = apiResponse?.Message ?? "Login failed"
                    };
                }
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Login failed for user: {Username}. Status: {StatusCode}, Content: {Content}",
                username, response.StatusCode, errorContent);

            // Try to parse error response
            try
            {
                var errorResponse = JsonSerializer.Deserialize<ApiErrorResponse>(errorContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = errorResponse?.Detail ?? "Invalid username or password"
                };
            }
            catch
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = response.StatusCode == System.Net.HttpStatusCode.Unauthorized
                        ? "Invalid username or password"
                        : "Login failed. Please try again."
                };
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error during login for user: {Username}", username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Unable to connect to the server. Please check your internet connection."
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user: {Username}", username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "An unexpected error occurred. Please try again."
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            _logger.LogInformation("Logging out user");

            // Clear HTTP client authorization header
            _httpClient.DefaultRequestHeaders.Authorization = null;

            // Clear stored authentication data
            await _localStorage.RemoveItemAsync(TokenKey);
            await _localStorage.RemoveItemAsync(UserKey);

            _logger.LogInformation("User logged out successfully - all authentication data cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");

            // Even if there's an error, try to clear what we can
            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = null;
                await _localStorage.RemoveItemAsync(TokenKey);
                await _localStorage.RemoveItemAsync(UserKey);
            }
            catch (Exception clearEx)
            {
                _logger.LogError(clearEx, "Error during fallback logout cleanup");
            }
        }
    }

    public async Task<AuthenticationResult?> RefreshTokenAsync()
    {
        try
        {
            var token = await GetTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                return null;
            }

            // Check if token is close to expiry (within 5 minutes)
            if (!IsTokenNearExpiry(token))
            {
                // Token is still valid, return current auth result
                var currentUser = await GetCurrentUserAsync();
                if (currentUser != null)
                {
                    return new AuthenticationResult
                    {
                        Token = token,
                        Username = currentUser.Username,
                        Email = currentUser.Email,
                        FullName = currentUser.FullName,
                        Role = currentUser.Role,
                        FactoryId = currentUser.FactoryId,
                        ExpiresAt = GetTokenExpiry(token) ?? DateTime.UtcNow.AddHours(8),
                        IsSuccess = true
                    };
                }
            }

            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsync("api/v1/auth/refresh", null);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuthenticationResult>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var authResult = apiResponse.Data;
                    authResult.IsSuccess = true;

                    // Update stored token
                    await _localStorage.SetItemAsync(TokenKey, authResult.Token);

                    // Update authorization header
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authResult.Token);

                    _logger.LogInformation("Token refreshed successfully");
                    return authResult;
                }
            }

            _logger.LogWarning("Token refresh failed, logging out user");
            // If refresh fails, clear stored data
            await LogoutAsync();
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            await LogoutAsync();
            return null;
        }
    }

    public async Task<string?> GetTokenAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>(TokenKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving token from storage");
            return null;
        }
    }

    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<UserInfo>(UserKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user info from storage");
            return null;
        }
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            var token = await GetTokenAsync();
            var user = await GetCurrentUserAsync();

            if (string.IsNullOrEmpty(token) || user == null)
            {
                return false;
            }

            // Check if token is expired
            if (IsTokenExpired(token))
            {
                _logger.LogInformation("Token is expired, attempting refresh");
                var refreshResult = await RefreshTokenAsync();
                return refreshResult?.IsSuccess == true;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    /// <summary>
    /// Extracts user ID from JWT token
    /// </summary>
    private string? ExtractUserIdFromToken(string token)
    {
        try
        {
            var payload = GetTokenPayload(token);
            if (payload != null)
            {
                // Try different claim names for user ID
                if (payload.TryGetValue("nameid", out var nameId))
                    return nameId.ToString();
                if (payload.TryGetValue("sub", out var sub))
                    return sub.ToString();
                if (payload.TryGetValue("unique_name", out var uniqueName))
                    return uniqueName.ToString();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting user ID from token");
        }
        return null;
    }

    /// <summary>
    /// Checks if token is expired
    /// </summary>
    private bool IsTokenExpired(string token)
    {
        var expiry = GetTokenExpiry(token);
        return expiry.HasValue && expiry.Value <= DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if token is near expiry (within 5 minutes)
    /// </summary>
    private bool IsTokenNearExpiry(string token)
    {
        var expiry = GetTokenExpiry(token);
        return expiry.HasValue && expiry.Value <= DateTime.UtcNow.AddMinutes(5);
    }

    /// <summary>
    /// Gets token expiry date
    /// </summary>
    private DateTime? GetTokenExpiry(string token)
    {
        try
        {
            var payload = GetTokenPayload(token);
            if (payload?.TryGetValue("exp", out var exp) == true)
            {
                long expUnix;

                // Handle different types that exp might be
                if (exp is JsonElement jsonElement)
                {
                    if (jsonElement.ValueKind == JsonValueKind.Number)
                    {
                        expUnix = jsonElement.GetInt64();
                    }
                    else if (jsonElement.ValueKind == JsonValueKind.String &&
                             long.TryParse(jsonElement.GetString(), out var parsedValue))
                    {
                        expUnix = parsedValue;
                    }
                    else
                    {
                        _logger.LogWarning("Unable to parse exp claim from JWT token - unexpected JsonElement type: {ValueKind}", jsonElement.ValueKind);
                        return null;
                    }
                }
                else if (exp is long longValue)
                {
                    expUnix = longValue;
                }
                else if (exp is int intValue)
                {
                    expUnix = intValue;
                }
                else if (long.TryParse(exp.ToString(), out var stringParsedValue))
                {
                    expUnix = stringParsedValue;
                }
                else
                {
                    _logger.LogWarning("Unable to parse exp claim from JWT token - unexpected type: {Type}", exp.GetType());
                    return null;
                }

                return DateTimeOffset.FromUnixTimeSeconds(expUnix).DateTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting token expiry");
        }
        return null;
    }

    /// <summary>
    /// Parses JWT token payload
    /// </summary>
    private Dictionary<string, object>? GetTokenPayload(string token)
    {
        try
        {
            var parts = token.Split('.');
            if (parts.Length != 3)
                return null;

            var payload = parts[1];

            // Add padding if needed
            switch (payload.Length % 4)
            {
                case 2: payload += "=="; break;
                case 3: payload += "="; break;
            }

            var payloadBytes = Convert.FromBase64String(payload);
            var payloadJson = System.Text.Encoding.UTF8.GetString(payloadBytes);

            return JsonSerializer.Deserialize<Dictionary<string, object>>(payloadJson);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error parsing JWT token payload");
            return null;
        }
    }
}
