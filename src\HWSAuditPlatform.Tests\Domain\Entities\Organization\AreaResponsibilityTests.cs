using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Entities.Organization;

public class AreaResponsibilityTests
{
    [Fact]
    public void AreaResponsibility_ShouldInitializeWithDefaultValues()
    {
        // Act
        var areaResponsibility = new AreaResponsibility();

        // Assert
        Assert.True(areaResponsibility.IsActive);
        Assert.Equal(0, areaResponsibility.Priority);
        Assert.False(areaResponsibility.IsTemplateSpecific);
        Assert.True(areaResponsibility.IsEffective);
    }

    [Fact]
    public void AreaResponsibility_WithAuditTemplateId_ShouldBeTemplateSpecific()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility
        {
            AuditTemplateId = 1
        };

        // Act & Assert
        Assert.True(areaResponsibility.IsTemplateSpecific);
    }

    [Fact]
    public void AreaResponsibility_WithoutAuditTemplateId_ShouldNotBeTemplateSpecific()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility
        {
            AuditTemplateId = null
        };

        // Act & Assert
        Assert.False(areaResponsibility.IsTemplateSpecific);
    }

    [Fact]
    public void AreaResponsibility_WhenActive_ShouldBeEffective()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility
        {
            IsActive = true
        };

        // Act & Assert
        Assert.True(areaResponsibility.IsEffective);
    }

    [Fact]
    public void AreaResponsibility_WhenInactive_ShouldNotBeEffective()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility
        {
            IsActive = false
        };

        // Act & Assert
        Assert.False(areaResponsibility.IsEffective);
    }

    [Theory]
    [InlineData(ResponsibilityType.CorrectiveActionOwner)]
    [InlineData(ResponsibilityType.RetrospectiveAnalyst)]
    [InlineData(ResponsibilityType.FindingReviewer)]
    [InlineData(ResponsibilityType.EscalationContact)]
    public void AreaResponsibility_ShouldAcceptAllResponsibilityTypes(ResponsibilityType responsibilityType)
    {
        // Arrange & Act
        var areaResponsibility = new AreaResponsibility
        {
            ResponsibilityType = responsibilityType
        };

        // Assert
        Assert.Equal(responsibilityType, areaResponsibility.ResponsibilityType);
    }

    [Fact]
    public void AreaResponsibility_ShouldSetNavigationProperties()
    {
        // Arrange
        var area = new Area { Id = 1, AreaName = "Test Area" };
        var user = new User { AdObjectGuid = "12345678-1234-1234-1234-123456789012" };
        var template = new AuditTemplate { Id = 1, TemplateName = "Test Template" };

        var areaResponsibility = new AreaResponsibility
        {
            AreaId = area.Id,
            Area = area,
            ResponsibleUserId = user.AdObjectGuid,
            ResponsibleUser = user,
            AuditTemplateId = template.Id,
            AuditTemplate = template
        };

        // Act & Assert
        Assert.Equal(area, areaResponsibility.Area);
        Assert.Equal(user, areaResponsibility.ResponsibleUser);
        Assert.Equal(template, areaResponsibility.AuditTemplate);
    }

    [Fact]
    public void AreaResponsibility_ShouldValidateRequiredProperties()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility
        {
            Id = "c1234567890123456789012345",
            AreaId = 1,
            ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
            ResponsibleUserId = "12345678-1234-1234-1234-123456789012"
        };

        // Act & Assert
        Assert.NotEmpty(areaResponsibility.Id);
        Assert.True(areaResponsibility.AreaId > 0);
        Assert.NotEmpty(areaResponsibility.ResponsibleUserId);
        Assert.Equal(36, areaResponsibility.ResponsibleUserId.Length);
    }

    [Fact]
    public void AreaResponsibility_Description_ShouldAcceptNullAndValidStrings()
    {
        // Arrange
        var areaResponsibility = new AreaResponsibility();

        // Act & Assert - Null description
        areaResponsibility.Description = null;
        Assert.Null(areaResponsibility.Description);

        // Act & Assert - Valid description
        areaResponsibility.Description = "Test description";
        Assert.Equal("Test description", areaResponsibility.Description);
    }

    [Fact]
    public void AreaResponsibility_Priority_ShouldDefaultToZero()
    {
        // Arrange & Act
        var areaResponsibility = new AreaResponsibility();

        // Assert
        Assert.Equal(0, areaResponsibility.Priority);
    }
}
