using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using Xunit;

namespace HWSAuditPlatform.Tests.Domain.Services;

public class AreaResponsibilityServiceTests
{
    private readonly AreaResponsibilityService _service;

    public AreaResponsibilityServiceTests()
    {
        _service = new AreaResponsibilityService();
    }

    [Fact]
    public async Task AssignResponsibilitiesAsync_ShouldAssignCorrectiveActionOwner()
    {
        // Arrange
        var finding = new Finding { Id = "f1" };
        var audit = new Audit { AreaId = 1, AuditTemplateId = 1 };
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "user1",
                AuditTemplateId = 1,
                IsActive = true,
                Priority = 0
            }
        };

        // Act
        await _service.AssignResponsibilitiesAsync(finding, audit, responsibilities);

        // Assert
        Assert.Equal("user1", finding.ResponsibleUserId);
    }

    [Fact]
    public async Task AssignResponsibilitiesAsync_ShouldAssignRetrospectiveAnalyst()
    {
        // Arrange
        var finding = new Finding { Id = "f1" };
        var audit = new Audit { AreaId = 1, AuditTemplateId = 1 };
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.RetrospectiveAnalyst,
                ResponsibleUserId = "analyst1",
                AuditTemplateId = 1,
                IsActive = true,
                Priority = 0
            }
        };

        // Act
        await _service.AssignResponsibilitiesAsync(finding, audit, responsibilities);

        // Assert
        Assert.Equal("analyst1", finding.RetrospectiveAnalystUserId);
    }

    [Fact]
    public void GetResponsibleUser_ShouldReturnTemplateSpecificUser_WhenAvailable()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "general-user",
                AuditTemplateId = null,
                IsActive = true,
                Priority = 0
            },
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "template-user",
                AuditTemplateId = 1,
                IsActive = true,
                Priority = 0
            }
        };

        // Act
        var result = _service.GetResponsibleUser(1, ResponsibilityType.CorrectiveActionOwner, 1, responsibilities);

        // Assert
        Assert.Equal("template-user", result);
    }

    [Fact]
    public void GetResponsibleUser_ShouldReturnGeneralUser_WhenNoTemplateSpecific()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "general-user",
                AuditTemplateId = null,
                IsActive = true,
                Priority = 0
            }
        };

        // Act
        var result = _service.GetResponsibleUser(1, ResponsibilityType.CorrectiveActionOwner, 1, responsibilities);

        // Assert
        Assert.Equal("general-user", result);
    }

    [Fact]
    public void GetResponsibleUser_ShouldReturnHighestPriorityUser_WhenMultipleUsers()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "low-priority-user",
                AuditTemplateId = null,
                IsActive = true,
                Priority = 10
            },
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "high-priority-user",
                AuditTemplateId = null,
                IsActive = true,
                Priority = 1
            }
        };

        // Act
        var result = _service.GetResponsibleUser(1, ResponsibilityType.CorrectiveActionOwner, null, responsibilities);

        // Assert
        Assert.Equal("high-priority-user", result);
    }

    [Fact]
    public void GetResponsibleUser_ShouldReturnNull_WhenNoMatchingResponsibility()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 2, // Different area
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "user1",
                IsActive = true
            }
        };

        // Act
        var result = _service.GetResponsibleUser(1, ResponsibilityType.CorrectiveActionOwner, null, responsibilities);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetResponsibleUser_ShouldIgnoreInactiveResponsibilities()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "inactive-user",
                IsActive = false,
                Priority = 0
            },
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "active-user",
                IsActive = true,
                Priority = 1
            }
        };

        // Act
        var result = _service.GetResponsibleUser(1, ResponsibilityType.CorrectiveActionOwner, null, responsibilities);

        // Assert
        Assert.Equal("active-user", result);
    }

    [Fact]
    public void ValidateAreaResponsibilities_ShouldReturnValid_WhenNoIssues()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "user1",
                IsActive = true
            }
        };

        // Act
        var result = _service.ValidateAreaResponsibilities(responsibilities);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.ValidationErrors);
    }

    [Fact]
    public void ValidateAreaResponsibilities_ShouldWarnAboutDuplicates()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "user1",
                AuditTemplateId = null,
                IsActive = true
            },
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.CorrectiveActionOwner,
                ResponsibleUserId = "user2",
                AuditTemplateId = null,
                IsActive = true
            }
        };

        // Act
        var result = _service.ValidateAreaResponsibilities(responsibilities);

        // Assert
        Assert.True(result.IsValid);
        Assert.Single(result.ValidationWarnings);
        Assert.Contains("Multiple users assigned", result.ValidationWarnings[0]);
    }

    [Fact]
    public void ValidateAreaResponsibilities_ShouldWarnAboutMissingOwners()
    {
        // Arrange
        var responsibilities = new List<AreaResponsibility>
        {
            new()
            {
                AreaId = 1,
                ResponsibilityType = ResponsibilityType.RetrospectiveAnalyst, // Not CorrectiveActionOwner
                ResponsibleUserId = "user1",
                IsActive = true
            }
        };

        // Act
        var result = _service.ValidateAreaResponsibilities(responsibilities);

        // Assert
        Assert.True(result.IsValid);
        Assert.Single(result.ValidationWarnings);
        Assert.Contains("no corrective action owner assigned", result.ValidationWarnings[0]);
    }
}
