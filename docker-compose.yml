# docker-compose.yml

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: hwsaudit-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=HWSAudit123!
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./docker/sql-init:/docker-entrypoint-initdb.d
    networks:
      - hwsaudit-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "/opt/mssql-tools18/bin/sqlcmd",
          "-S",
          "localhost",
          "-U",
          "sa",
          "-P",
          "HWSAudit123!",
          "-Q",
          "SELECT 1",
          "-C",
        ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    depends_on: # <-- ADDED: Ensures AD is healthy before this starts
      samba-ad:
        condition: service_healthy

  # Active Directory using Samba AD with working domain configuration
  samba-ad:
    image: nowsci/samba-domain:latest
    container_name: hwsaudit-ad
    environment:
      - DOMAIN=hwsaudit.local
      - DOMAINPASS=HWSAudit123!
      - DNSFORWARDER=*******
      - NOCOMPLEXITY=true
      - INSECURELDAP=true
    ports:
      - "389:389"
      - "636:636"
      - "88:88"
      - "88:88/udp"
      - "464:464"
      - "464:464/udp"
      - "53:53"
      - "53:53/udp"
    volumes:
      - samba_data:/var/lib/samba
      - samba_config:/etc/samba/external
      # The path below is for LDIF files used during initial provisioning
      - ./docker/samba-init:/container/service/slapd/assets/config/bootstrap/ldif/custom
      # <-- ADDED: Mount the same directory to /scripts to run shell scripts from
      - ./docker/samba-init:/scripts
    networks:
      hwsaudit-network:
        ipv4_address: ***********
    # --- ADD THIS BLOCK ---
    # This is the crucial fix. It tells the container to use its own
    # IP address for DNS lookups first.
    dns:
      - *********** # Use itself for DNS
      - ******* # Use the forwarder as a fallback
    # ----------------------
    restart: unless-stopped
    hostname: dc1
    domainname: hwsaudit.local
    privileged: true
    healthcheck:
      test: [
          "CMD-SHELL",
          "smbclient -L localhost -U Administrator%HWSAudit123! || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

volumes:
  # <-- SIMPLIFIED: 'driver: local' is the default
  sqlserver_data:
  samba_data:
  samba_config:

networks:
  hwsaudit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16