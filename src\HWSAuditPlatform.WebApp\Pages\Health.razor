@page "/health"
@using HWSAuditPlatform.WebApp.Services
@inject IAuditApiService AuditApiService
@inject ITemplateApiService TemplateApiService
@inject IUserApiService UserApiService
@inject IAuthenticationService AuthService
@inject ILogger<Health> Logger

<PageTitle>Health Check - HWS Audit Platform</PageTitle>

<div class="health-container">
    <div class="page-header mb-4">
        <h1>
            <i class="bi bi-heart-pulse me-2"></i>System Health Check
        </h1>
        <p class="text-muted">Monitor system status and service availability</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Checking system health..." />

    @if (!isLoading)
    {
        <!-- Overall Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="health-status @(overallHealthy ? "healthy" : "unhealthy")">
                            <i class="bi @(overallHealthy ? "bi-check-circle-fill" : "bi-x-circle-fill") health-icon"></i>
                            <h3 class="mt-2">System @(overallHealthy ? "Healthy" : "Unhealthy")</h3>
                            <p class="text-muted">Last checked: @lastChecked.ToString("yyyy-MM-dd HH:mm:ss")</p>
                        </div>
                        <button class="btn btn-primary" @onclick="RunHealthCheck" disabled="@isLoading">
                            <i class="bi bi-arrow-clockwise me-2"></i>Refresh Health Check
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Status -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear me-2"></i>Core Services
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="service-list">
                            <div class="service-item">
                                <div class="service-info">
                                    <h6>Authentication Service</h6>
                                    <small class="text-muted">User authentication and authorization</small>
                                </div>
                                <span class="badge @(authServiceHealthy ? "bg-success" : "bg-danger")">
                                    @(authServiceHealthy ? "Healthy" : "Unhealthy")
                                </span>
                            </div>

                            <div class="service-item">
                                <div class="service-info">
                                    <h6>Audit API Service</h6>
                                    <small class="text-muted">Audit management operations</small>
                                </div>
                                <span class="badge @(auditServiceHealthy ? "bg-success" : "bg-danger")">
                                    @(auditServiceHealthy ? "Healthy" : "Unhealthy")
                                </span>
                            </div>

                            <div class="service-item">
                                <div class="service-info">
                                    <h6>Template API Service</h6>
                                    <small class="text-muted">Template management operations</small>
                                </div>
                                <span class="badge @(templateServiceHealthy ? "bg-success" : "bg-danger")">
                                    @(templateServiceHealthy ? "Healthy" : "Unhealthy")
                                </span>
                            </div>

                            <div class="service-item">
                                <div class="service-info">
                                    <h6>User API Service</h6>
                                    <small class="text-muted">User management operations</small>
                                </div>
                                <span class="badge @(userServiceHealthy ? "bg-success" : "bg-danger")">
                                    @(userServiceHealthy ? "Healthy" : "Unhealthy")
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>System Metrics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value">@totalAudits</div>
                                <div class="metric-label">Total Audits</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">@totalTemplates</div>
                                <div class="metric-label">Total Templates</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">@totalUsers</div>
                                <div class="metric-label">Total Users</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">@(DateTime.UtcNow - startTime).TotalMinutes.ToString("F0")m</div>
                                <div class="metric-label">Uptime</div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <h6>Response Times</h6>
                            <div class="response-times">
                                <div class="response-item">
                                    <span>Auth Service:</span>
                                    <span class="@(authResponseTime < 1000 ? "text-success" : "text-warning")">
                                        @authResponseTime ms
                                    </span>
                                </div>
                                <div class="response-item">
                                    <span>Audit Service:</span>
                                    <span class="@(auditResponseTime < 1000 ? "text-success" : "text-warning")">
                                        @auditResponseTime ms
                                    </span>
                                </div>
                                <div class="response-item">
                                    <span>Template Service:</span>
                                    <span class="@(templateResponseTime < 1000 ? "text-success" : "text-warning")">
                                        @templateResponseTime ms
                                    </span>
                                </div>
                                <div class="response-item">
                                    <span>User Service:</span>
                                    <span class="@(userResponseTime < 1000 ? "text-success" : "text-warning")">
                                        @userResponseTime ms
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (healthCheckErrors.Any())
        {
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0 text-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>Health Check Errors
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach (var error in healthCheckErrors)
                            {
                                <div class="alert alert-danger">
                                    <strong>@error.Service:</strong> @error.Message
                                    <br><small class="text-muted">@error.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

<style>
    .health-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #007bff;
        border-radius: 12px 12px 0 0 !important;
    }

    .health-status {
        padding: 2rem;
    }

    .health-status.healthy {
        color: #28a745;
    }

    .health-status.unhealthy {
        color: #dc3545;
    }

    .health-icon {
        font-size: 4rem;
    }

    .service-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .service-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.375rem;
        border-left: 4px solid #007bff;
    }

    .service-info h6 {
        margin: 0;
        font-weight: 600;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .metric-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.375rem;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: #007bff;
    }

    .metric-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .response-times {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
    }

    .response-item {
        display: flex;
        justify-content: space-between;
        padding: 0.25rem 0;
    }
</style>

@code {
    private bool isLoading = true;
    private bool overallHealthy = true;
    private DateTime lastChecked = DateTime.UtcNow;
    private DateTime startTime = DateTime.UtcNow;

    // Service health status
    private bool authServiceHealthy = true;
    private bool auditServiceHealthy = true;
    private bool templateServiceHealthy = true;
    private bool userServiceHealthy = true;

    // Metrics
    private int totalAudits = 0;
    private int totalTemplates = 0;
    private int totalUsers = 0;

    // Response times
    private long authResponseTime = 0;
    private long auditResponseTime = 0;
    private long templateResponseTime = 0;
    private long userResponseTime = 0;

    private List<HealthCheckError> healthCheckErrors = new();

    protected override async Task OnInitializedAsync()
    {
        await RunHealthCheck();
    }

    private async Task RunHealthCheck()
    {
        isLoading = true;
        healthCheckErrors.Clear();
        StateHasChanged();

        try
        {
            // Check Authentication Service
            var authStart = DateTime.UtcNow;
            try
            {
                await AuthService.IsAuthenticatedAsync();
                authServiceHealthy = true;
                authResponseTime = (long)(DateTime.UtcNow - authStart).TotalMilliseconds;
            }
            catch (Exception ex)
            {
                authServiceHealthy = false;
                healthCheckErrors.Add(new HealthCheckError("Authentication Service", ex.Message, DateTime.UtcNow));
                Logger.LogError(ex, "Auth service health check failed");
            }

            // Check Audit Service
            var auditStart = DateTime.UtcNow;
            try
            {
                var audits = await AuditApiService.GetAuditsAsync();
                totalAudits = audits?.Count() ?? 0;
                auditServiceHealthy = true;
                auditResponseTime = (long)(DateTime.UtcNow - auditStart).TotalMilliseconds;
            }
            catch (Exception ex)
            {
                auditServiceHealthy = false;
                healthCheckErrors.Add(new HealthCheckError("Audit Service", ex.Message, DateTime.UtcNow));
                Logger.LogError(ex, "Audit service health check failed");
            }

            // Check Template Service
            var templateStart = DateTime.UtcNow;
            try
            {
                var templates = await TemplateApiService.GetTemplatesAsync();
                totalTemplates = templates?.Count() ?? 0;
                templateServiceHealthy = true;
                templateResponseTime = (long)(DateTime.UtcNow - templateStart).TotalMilliseconds;
            }
            catch (Exception ex)
            {
                templateServiceHealthy = false;
                healthCheckErrors.Add(new HealthCheckError("Template Service", ex.Message, DateTime.UtcNow));
                Logger.LogError(ex, "Template service health check failed");
            }

            // Check User Service
            var userStart = DateTime.UtcNow;
            try
            {
                var users = await UserApiService.GetUsersAsync();
                totalUsers = users?.Count() ?? 0;
                userServiceHealthy = true;
                userResponseTime = (long)(DateTime.UtcNow - userStart).TotalMilliseconds;
            }
            catch (Exception ex)
            {
                userServiceHealthy = false;
                healthCheckErrors.Add(new HealthCheckError("User Service", ex.Message, DateTime.UtcNow));
                Logger.LogError(ex, "User service health check failed");
            }

            // Calculate overall health
            overallHealthy = authServiceHealthy && auditServiceHealthy && templateServiceHealthy && userServiceHealthy;
            lastChecked = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Health check failed");
            overallHealthy = false;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private record HealthCheckError(string Service, string Message, DateTime Timestamp);
}
