using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Organization.DTOs;

/// <summary>
/// Data Transfer Object for SubArea entity
/// </summary>
public class SubAreaDto : AuditableDto<int>
{
    public string SubAreaName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Simplified SubArea DTO for lists and lookups
/// </summary>
public class SubAreaSummaryDto
{
    public int Id { get; set; }
    public string SubAreaName { get; set; } = string.Empty;
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}
