using System.Net.Http.Json;
using System.Text.Json;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Implementation of template API service
/// </summary>
public class TemplateApiService : ITemplateApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<TemplateApiService> _logger;

    public TemplateApiService(
        AuthenticatedHttpClientService httpClient,
        ILogger<TemplateApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesAsync(string? searchTerm = null, bool? isActive = null, int pageSize = 100)
    {
        try
        {
            _logger.LogInformation("Fetching templates from API with search term: {SearchTerm}, isActive: {IsActive}", searchTerm, isActive);

            // Build query parameters
            var queryParams = new List<string> { $"pageSize={pageSize}" };

            if (!string.IsNullOrWhiteSpace(searchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");

            if (isActive.HasValue)
                queryParams.Add($"isActive={isActive.Value.ToString().ToLower()}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/v1/templates?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PaginatedResult<AuditTemplateSummaryDto>>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data?.Items != null)
                {
                    // Convert AuditTemplateSummaryDto to AuditTemplate entities
                    var templates = apiResponse.Data.Items.Select(dto => new AuditTemplate
                    {
                        Id = dto.Id,
                        TemplateName = dto.TemplateName,
                        Description = dto.Description,
                        Version = dto.Version,
                        IsPublished = dto.IsPublished,
                        IsActive = dto.IsActive,
                        CreatedAt = dto.CreatedAt,
                        UpdatedAt = dto.UpdatedAt,
                        // Note: Summary DTO doesn't include feature flags, so we default to false
                        // TODO: Update API to include EnableAreaBasedResponsibility in summary DTO
                        EnableAreaBasedResponsibility = false,
                        EnableFindingCategorization = false
                    }).ToList();

                    _logger.LogInformation("Successfully fetched {Count} templates", templates.Count);
                    return templates;
                }
            }

            _logger.LogWarning("Failed to fetch templates. Status: {StatusCode}", response.StatusCode);
            return Enumerable.Empty<AuditTemplate>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching templates");
            return Enumerable.Empty<AuditTemplate>();
        }
    }

    public async Task<AuditTemplate?> GetTemplateByIdAsync(int id)
    {
        try
        {
            _logger.LogInformation("Fetching template with ID {Id}", id);
            var response = await _httpClient.GetAsync($"api/v1/templates/{id}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditTemplateDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    // Convert AuditTemplateDto to AuditTemplate entity
                    var dto = apiResponse.Data;
                    var template = new AuditTemplate
                    {
                        Id = dto.Id,
                        TemplateName = dto.TemplateName,
                        Description = dto.Description,
                        Version = dto.Version,
                        IsPublished = dto.IsPublished,
                        IsActive = dto.IsActive,
                        CreatedAt = dto.CreatedAt,
                        UpdatedAt = dto.UpdatedAt,
                        CreatedByUserId = dto.CreatedByUserId,
                        UpdatedByUserId = dto.UpdatedByUserId,
                        RecordVersion = dto.RecordVersion
                    };

                    // Convert question groups
                    foreach (var groupDto in dto.QuestionGroups)
                    {
                        var questionGroup = new QuestionGroup
                        {
                            Id = groupDto.Id,
                            AuditTemplateId = groupDto.AuditTemplateId,
                            GroupName = groupDto.GroupName,
                            Description = groupDto.Description,
                            DisplayOrder = groupDto.DisplayOrder,
                            IsActive = groupDto.IsActive
                        };
                        template.QuestionGroups.Add(questionGroup);
                    }

                    // Convert questions
                    foreach (var questionDto in dto.Questions)
                    {
                        var question = new Question
                        {
                            Id = questionDto.Id,
                            AuditTemplateId = questionDto.AuditTemplateId,
                            QuestionGroupId = questionDto.QuestionGroupId,
                            QuestionText = questionDto.QuestionText,
                            QuestionType = questionDto.QuestionType,
                            DisplayOrder = questionDto.DisplayOrder,
                            IsRequired = questionDto.IsRequired,
                            Weight = questionDto.Weight,
                            HelpText = questionDto.HelpText,
                            ParentQuestionId = questionDto.ParentQuestionId,
                            TriggerAnswerValue = questionDto.TriggerAnswerValue,
                            SeverityLevel = questionDto.SeverityLevel,
                            EvidenceRequired = questionDto.EvidenceRequired,
                            AllowedEvidenceTypes = questionDto.AllowedEvidenceTypes?.Select((et, index) => new QuestionAllowedEvidenceType
                            {
                                EvidenceType = et.EvidenceType,
                                DisplayOrder = et.DisplayOrder,
                                IsActive = et.IsActive
                            }).ToList() ?? new List<QuestionAllowedEvidenceType>(),
                            IsActive = questionDto.IsActive
                        };

                        // Convert question options
                        foreach (var optionDto in questionDto.Options)
                        {
                            var option = new QuestionOption
                            {
                                Id = optionDto.Id,
                                QuestionId = optionDto.QuestionId,
                                OptionText = optionDto.OptionText,
                                OptionValue = optionDto.OptionValue,
                                DisplayOrder = optionDto.DisplayOrder,
                                IsActive = optionDto.IsActive
                            };
                            question.Options.Add(option);
                        }

                        template.Questions.Add(question);
                    }

                    _logger.LogInformation("Successfully fetched template with ID {Id}", id);
                    return template;
                }
            }

            _logger.LogWarning("Failed to fetch template with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching template with ID {Id}", id);
            return null;
        }
    }

    public async Task<AuditTemplate> CreateTemplateAsync(AuditTemplate template)
    {
        try
        {
            _logger.LogInformation("Creating new template: {TemplateName}", template.TemplateName);
            var response = await _httpClient.PostAsJsonAsync("api/v1/templates", template);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditTemplate>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully created template with ID {Id}", apiResponse.Data.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to create template. Status: {StatusCode}", response.StatusCode);
            throw new InvalidOperationException("Failed to create template");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating template");
            throw;
        }
    }

    public async Task<AuditTemplate> UpdateTemplateAsync(AuditTemplate template)
    {
        try
        {
            _logger.LogInformation("Updating template with ID {Id}", template.Id);
            var response = await _httpClient.PutAsJsonAsync($"api/v1/templates/{template.Id}", template);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditTemplate>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully updated template with ID {Id}", template.Id);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to update template with ID {Id}. Status: {StatusCode}", template.Id, response.StatusCode);
            throw new InvalidOperationException("Failed to update template");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating template with ID {Id}", template.Id);
            throw;
        }
    }

    public async Task<bool> DeleteTemplateAsync(int id)
    {
        try
        {
            _logger.LogInformation("Deleting template with ID {Id}", id);
            var response = await _httpClient.DeleteAsync($"api/v1/templates/{id}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted template with ID {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to delete template with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting template with ID {Id}", id);
            return false;
        }
    }

    public async Task<int?> CreateTemplateAsync(CreateAuditTemplateRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new template: {TemplateName}", request.TemplateName);
            var response = await _httpClient.PostAsJsonAsync("api/v1/templates", request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<int>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true)
                {
                    _logger.LogInformation("Successfully created template with ID {Id}", apiResponse.Data);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to create template. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating template");
            return null;
        }
    }

    public async Task<bool> UpdateTemplateAsync(int id, UpdateAuditTemplateRequest request)
    {
        try
        {
            _logger.LogInformation("Updating template with ID {Id}", id);
            var response = await _httpClient.PutAsJsonAsync($"api/v1/templates/{id}", request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully updated template with ID {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to update template with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating template with ID {Id}", id);
            return false;
        }
    }

    public async Task<int?> CreateTemplateVersionAsync(int originalTemplateId, string? newTemplateName = null, string? newDescription = null)
    {
        try
        {
            _logger.LogInformation("Creating new version of template with ID {OriginalTemplateId}", originalTemplateId);

            var request = new
            {
                NewTemplateName = newTemplateName,
                NewDescription = newDescription
            };

            var response = await _httpClient.PostAsJsonAsync($"api/v1/templates/{originalTemplateId}/create-version", request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<int>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true)
                {
                    _logger.LogInformation("Successfully created template version with ID {Id}", apiResponse.Data);
                    return apiResponse.Data;
                }
            }

            _logger.LogWarning("Failed to create template version. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating template version for template {OriginalTemplateId}", originalTemplateId);
            return null;
        }
    }

    public async Task<bool> PublishTemplateAsync(int id)
    {
        try
        {
            _logger.LogInformation("Publishing template with ID {Id}", id);
            var response = await _httpClient.PostAsync($"api/v1/templates/{id}/publish", null);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully published template with ID {Id}", id);
                return true;
            }

            _logger.LogWarning("Failed to publish template with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing template with ID {Id}", id);
            return false;
        }
    }

    public async Task<PublishTemplateResult> PublishTemplateWithDetailsAsync(int id)
    {
        try
        {
            _logger.LogInformation("Publishing template with ID {Id}", id);
            var response = await _httpClient.PostAsync($"api/v1/templates/{id}/publish", null);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully published template with ID {Id}", id);
                return PublishTemplateResult.Success();
            }

            // Handle error responses with detailed messages
            var content = await response.Content.ReadAsStringAsync();

            if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
                try
                {
                    var errorResponse = JsonSerializer.Deserialize<ApiErrorResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (errorResponse?.Errors != null && errorResponse.Errors.Any())
                    {
                        var errorMessages = errorResponse.Errors
                            .SelectMany(kvp => kvp.Value)
                            .ToList();

                        _logger.LogWarning("Template {Id} validation failed: {Errors}", id, string.Join("; ", errorMessages));
                        return PublishTemplateResult.ValidationFailed(errorMessages);
                    }
                    else if (!string.IsNullOrEmpty(errorResponse?.Detail))
                    {
                        _logger.LogWarning("Template {Id} publish failed: {Detail}", id, errorResponse.Detail);
                        return PublishTemplateResult.Failed(errorResponse.Detail);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse error response for template {Id}", id);
                }
            }

            _logger.LogWarning("Failed to publish template with ID {Id}. Status: {StatusCode}", id, response.StatusCode);
            return PublishTemplateResult.Failed($"Failed to publish template. Status: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing template with ID {Id}", id);
            return PublishTemplateResult.Failed("An error occurred while publishing the template.");
        }
    }

    public async Task<int?> AddQuestionToTemplateAsync(int templateId, CreateQuestionRequest request)
    {
        try
        {
            _logger.LogInformation("Adding question to template {TemplateId}", templateId);

            // Map the WebApp request to the API command structure
            var apiCommand = MapToAddQuestionCommand(templateId, request);

            // Log the command being sent for debugging
            var commandJson = JsonSerializer.Serialize(apiCommand, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("Sending AddQuestionCommand: {Command}", commandJson);

            var response = await _httpClient.PostAsJsonAsync($"api/v1/templates/{templateId}/questions", apiCommand);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<int>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true)
                {
                    _logger.LogInformation("Successfully added question with ID {Id} to template {TemplateId}", apiResponse.Data, templateId);
                    return apiResponse.Data;
                }
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to add question to template {TemplateId}. Status: {StatusCode}. Response: {Response}", templateId, response.StatusCode, errorContent);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding question to template {TemplateId}", templateId);
            return null;
        }
    }

    public async Task<bool> UpdateQuestionAsync(int questionId, UpdateQuestionRequest request)
    {
        try
        {
            _logger.LogInformation("Updating question with ID {QuestionId}", questionId);
            var response = await _httpClient.PutAsJsonAsync($"api/v1/questions/{questionId}", request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully updated question with ID {QuestionId}", questionId);
                return true;
            }

            _logger.LogWarning("Failed to update question with ID {QuestionId}. Status: {StatusCode}", questionId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating question with ID {QuestionId}", questionId);
            return false;
        }
    }

    public async Task<bool> DeleteQuestionAsync(int questionId)
    {
        try
        {
            _logger.LogInformation("Deleting question with ID {QuestionId}", questionId);
            var response = await _httpClient.DeleteAsync($"api/v1/questions/{questionId}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully deleted question with ID {QuestionId}", questionId);
                return true;
            }

            _logger.LogWarning("Failed to delete question with ID {QuestionId}. Status: {StatusCode}", questionId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting question with ID {QuestionId}", questionId);
            return false;
        }
    }

    public async Task<bool> UpdateTemplateFeatureFlagsAsync(int templateId, bool enableAreaBasedResponsibility, bool enableFindingCategorization)
    {
        try
        {
            _logger.LogInformation("Updating feature flags for template {TemplateId}", templateId);

            var request = new
            {
                EnableAreaBasedResponsibility = enableAreaBasedResponsibility,
                EnableFindingCategorization = enableFindingCategorization
            };

            var response = await _httpClient.PutAsJsonAsync($"api/v1/templates/{templateId}/features", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flags for template {TemplateId}", templateId);
            return false;
        }
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesWithAreaResponsibilityAsync()
    {
        try
        {
            _logger.LogInformation("Fetching templates with area-based responsibility enabled");
            var allTemplates = await GetTemplatesAsync();

            // Filter templates that have area-based responsibility enabled
            // Note: This is a simplified implementation. In a real scenario,
            // you'd want to add a specific API endpoint for this
            return allTemplates.Where(t => t.EnableAreaBasedResponsibility);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching templates with area responsibility");
            return Enumerable.Empty<AuditTemplate>();
        }
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesWithCategorizationAsync()
    {
        try
        {
            _logger.LogInformation("Fetching templates with finding categorization enabled");
            var allTemplates = await GetTemplatesAsync();

            // Filter templates that have finding categorization enabled
            // Note: This is a simplified implementation. In a real scenario,
            // you'd want to add a specific API endpoint for this
            return allTemplates.Where(t => t.EnableFindingCategorization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching templates with categorization");
            return Enumerable.Empty<AuditTemplate>();
        }
    }

    private object MapToAddQuestionCommand(int templateId, CreateQuestionRequest request)
    {
        // Parse QuestionType enum
        if (!Enum.TryParse<QuestionType>(request.QuestionType, true, out var questionType))
        {
            questionType = QuestionType.ShortText; // Default fallback
        }

        // Parse SeverityLevel enum if provided
        SeverityLevel? severityLevel = null;
        if (!string.IsNullOrEmpty(request.SeverityLevel) &&
            Enum.TryParse<SeverityLevel>(request.SeverityLevel, true, out var parsedSeverity))
        {
            severityLevel = parsedSeverity;
        }

        // Map evidence types from strings to AddEvidenceTypeRequest objects
        var allowedEvidenceTypes = request.AllowedEvidenceTypes?.Select((evidenceTypeString, index) =>
        {
            if (Enum.TryParse<EvidenceType>(evidenceTypeString, true, out var evidenceType))
            {
                return new
                {
                    EvidenceType = evidenceType,
                    DisplayOrder = index
                };
            }
            return null;
        }).Where(et => et != null).Cast<object>().ToArray() ?? Array.Empty<object>();

        // Map options to the correct structure
        var options = request.Options?.Select(o => new
        {
            OptionText = o.OptionText,
            OptionValue = o.OptionValue,
            DisplayOrder = o.DisplayOrder
        }).ToArray() ?? Array.Empty<object>();

        return new
        {
            AuditTemplateId = templateId, // Include the template ID
            QuestionText = request.QuestionText,
            QuestionType = questionType,
            Weight = request.Weight > 0 ? request.Weight : 1, // Ensure weight is > 0
            SeverityLevel = severityLevel,
            DisplayOrder = request.DisplayOrder,
            HelpText = request.HelpText,
            IsRequired = request.IsRequired,
            EvidenceRequired = request.EvidenceRequired,
            AllowedEvidenceTypes = allowedEvidenceTypes,
            Options = options
        };
    }
}
