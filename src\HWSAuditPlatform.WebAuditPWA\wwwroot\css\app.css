/* Industrial Theme - HWS Audit Platform PWA */
@import url('fonts.css');

html, body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #e0e0e0;
    margin: 0;
    padding: 0;
    height: 100%;
    overflow-x: hidden;
    /* Hide scrollbars while maintaining functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbars for WebKit browsers */
html::-webkit-scrollbar,
body::-webkit-scrollbar,
*::-webkit-scrollbar {
    display: none;
}

/* Ensure smooth scrolling on all elements */
* {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Enhanced Industrial Color Palette - PWA with Teal Accent */
:root {
    --industrial-dark: #1a1a1a;
    --industrial-darker: #0f0f0f;
    --industrial-gray: #2d2d2d;
    --industrial-light-gray: #404040;
    --industrial-steel: #4a5568;
    --industrial-blue: #2b6cb0;
    --industrial-teal: #14b8a6;
    --industrial-teal-light: #5eead4;
    --industrial-teal-dark: #0f766e;
    --industrial-red: #e53e3e;
    --industrial-green: #38a169;
    --industrial-orange: #f59e0b;
    --industrial-text: #e0e0e0;
    --industrial-text-muted: #a0a0a0;
    --industrial-text-light: #f5f5f5;
    --industrial-border: #404040;
    --industrial-border-light: #525252;
    --industrial-shadow: rgba(0, 0, 0, 0.3);
    --industrial-shadow-heavy: rgba(0, 0, 0, 0.6);
    --industrial-shadow-light: rgba(0, 0, 0, 0.15);
    --industrial-glow: rgba(20, 184, 166, 0.3);
    --industrial-glow-heavy: rgba(20, 184, 166, 0.5);
}

/* PWA App Container */
#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--industrial-dark);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto Condensed', sans-serif;
    color: var(--industrial-text-light);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 4px var(--industrial-shadow);
}

h1 {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--industrial-text-light) 0%, var(--industrial-teal-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
}

h2 {
    font-size: 2rem;
    color: var(--industrial-text-light);
    border-bottom: 2px solid var(--industrial-teal);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

h3 {
    font-size: 1.5rem;
    color: var(--industrial-teal-light);
}

h4 {
    font-size: 1.25rem;
    color: var(--industrial-text);
}

h5, h6 {
    font-size: 1rem;
    color: var(--industrial-text-muted);
}

h1:focus, h2:focus, h3:focus, h4:focus, h5:focus, h6:focus {
    outline: none;
}

p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--industrial-text);
}

.lead {
    font-size: 1.1rem;
    font-weight: 400;
    color: var(--industrial-text-muted);
    line-height: 1.7;
}

/* Links and Navigation */
a, .btn-link {
    color: var(--industrial-teal);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover, .btn-link:hover {
    color: var(--industrial-teal-light);
    text-decoration: none;
}

/* Enhanced Primary Button - Teal Theme */
.btn-primary {
    color: #fff;
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
    border: 2px solid var(--industrial-teal);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px var(--industrial-glow);
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    touch-action: manipulation;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--industrial-teal-light) 0%, var(--industrial-teal) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--industrial-glow-heavy);
    border-color: var(--industrial-teal-light);
    color: #fff;
}

.btn-primary:focus {
    box-shadow: 0 0 0 0.2rem var(--industrial-dark), 0 0 0 0.4rem var(--industrial-teal);
    outline: none;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px var(--industrial-glow);
}

/* Enhanced Secondary Button */
.btn-secondary {
    color: var(--industrial-text-light);
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    border: 2px solid var(--industrial-border-light);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px var(--industrial-shadow);
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    touch-action: manipulation;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-secondary:hover::before {
    left: 100%;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-steel) 100%);
    border-color: var(--industrial-teal);
    color: var(--industrial-teal-light);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--industrial-shadow-heavy);
}

.btn-secondary:focus {
    box-shadow: 0 0 0 0.2rem var(--industrial-dark), 0 0 0 0.4rem var(--industrial-steel);
    outline: none;
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px var(--industrial-shadow);
}

/* Enhanced Outline Buttons */
.btn-outline-primary {
    color: var(--industrial-teal);
    background: transparent;
    border: 2px solid var(--industrial-teal);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    min-height: 48px;
    touch-action: manipulation;
}

.btn-outline-primary:hover {
    background: var(--industrial-teal);
    border-color: var(--industrial-teal);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--industrial-glow-heavy);
}

.btn-outline-secondary {
    color: var(--industrial-text-muted);
    background: transparent;
    border: 2px solid var(--industrial-border-light);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    min-height: 48px;
    touch-action: manipulation;
}

.btn-outline-secondary:hover {
    background: var(--industrial-steel);
    border-color: var(--industrial-teal);
    color: var(--industrial-teal-light);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--industrial-shadow-heavy);
}

/* Base Button Improvements */
.btn {
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 0.9rem;
    min-height: 48px;
    touch-action: manipulation;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem var(--industrial-dark), 0 0 0 0.4rem var(--industrial-teal);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn i {
    margin-right: 0.5rem;
}

.btn.w-100 {
    width: 100% !important;
}

/* Small Button Variant */
.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.8rem;
    min-height: 40px;
}

/* Large Button Variant */
.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1rem;
    min-height: 56px;
}

/* Form Controls */
.form-control {
    background-color: var(--industrial-gray);
    border: 1px solid var(--industrial-border);
    color: var(--industrial-text);
    border-radius: 4px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    background-color: var(--industrial-light-gray);
    border-color: var(--industrial-teal);
    color: var(--industrial-text);
    box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
}

.form-control::placeholder {
    color: var(--industrial-text-muted);
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid var(--industrial-green);
    border-color: var(--industrial-green);
}

.invalid {
    outline: 1px solid var(--industrial-red);
    border-color: var(--industrial-red);
}

.validation-message {
    color: var(--industrial-red);
    font-weight: 500;
}

/* Form Select */
.form-select {
    background-color: var(--industrial-gray);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2314b8a6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    border: 1px solid var(--industrial-border);
    color: var(--industrial-text);
    border-radius: 4px;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.form-select:focus {
    background-color: var(--industrial-light-gray);
    border-color: var(--industrial-teal);
    color: var(--industrial-text);
    box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
}

/* Enhanced Cards */
.card {
    background: linear-gradient(145deg, var(--industrial-gray) 0%, #252525 100%);
    border: 1px solid var(--industrial-border-light);
    border-radius: 12px;
    box-shadow: 0 8px 24px var(--industrial-shadow), 0 2px 8px var(--industrial-shadow-light);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px var(--industrial-shadow-heavy), 0 4px 12px var(--industrial-shadow);
    border-color: var(--industrial-teal);
}

.card-header {
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    border-bottom: 2px solid var(--industrial-teal);
    color: var(--industrial-text-light);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 1.25rem 1.75rem;
    border-radius: 12px 12px 0 0;
    position: relative;
    overflow: hidden;
}

.card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(20, 184, 166, 0.1));
}

.card-body {
    color: var(--industrial-text);
    padding: 2rem;
    background: rgba(45, 45, 45, 0.3);
}

.card-footer {
    background: var(--industrial-darker);
    border-top: 1px solid var(--industrial-border);
    color: var(--industrial-text-muted);
    padding: 1rem 1.75rem;
}

/* Tables */
.table {
    color: var(--industrial-text) !important;
    background-color: var(--industrial-gray) !important;
    border-color: var(--industrial-border) !important;
}

.table th {
    background-color: var(--industrial-steel) !important;
    color: var(--industrial-text) !important;
    border-color: var(--industrial-border) !important;
    font-weight: 700 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-top: none !important;
}

.table td {
    border-color: var(--industrial-border) !important;
    color: var(--industrial-text) !important;
    background-color: transparent !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(64, 64, 64, 0.3) !important;
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(20, 184, 166, 0.1) !important;
    color: var(--industrial-text) !important;
}

/* Badges */
.badge {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
}

.bg-primary {
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--industrial-green) 0%, #2f855a 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--industrial-red) 0%, #c53030 100%) !important;
}

/* Alerts */
.alert {
    background: linear-gradient(135deg, var(--industrial-gray) 0%, var(--industrial-steel) 100%);
    border: 1px solid var(--industrial-border);
    color: var(--industrial-text);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--industrial-shadow);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-info {
    background: linear-gradient(135deg, rgba(20, 184, 166, 0.2) 0%, rgba(15, 118, 110, 0.1) 100%);
    border-color: var(--industrial-teal);
    color: var(--industrial-text);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.1) 100%);
    border-color: #f59e0b;
    color: var(--industrial-text);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.2) 0%, rgba(197, 48, 48, 0.1) 100%);
    border-color: var(--industrial-red);
    color: var(--industrial-text);
}

.alert-success {
    background: linear-gradient(135deg, rgba(56, 161, 105, 0.2) 0%, rgba(47, 133, 90, 0.1) 100%);
    border-color: var(--industrial-green);
    color: var(--industrial-text);
}

/* Utility Classes */
.text-muted {
    color: var(--industrial-text-muted) !important;
}

.text-primary {
    color: var(--industrial-teal) !important;
}

.text-success {
    color: var(--industrial-green) !important;
}

.text-danger {
    color: var(--industrial-red) !important;
}

.border {
    border-color: var(--industrial-border) !important;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 3px solid var(--industrial-border);
    border-radius: 50%;
    border-top-color: var(--industrial-teal);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* PWA Loading Progress */
.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

.loading-progress circle {
    fill: none;
    stroke: var(--industrial-text-muted);
    stroke-width: 0.6rem;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-progress circle:last-child {
    stroke: var(--industrial-teal);
    stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
    transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    color: var(--industrial-text);
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

.loading-progress-text:after {
    content: var(--blazor-load-percentage-text, "Loading");
}

/* Enhanced PWA Specific Styles - Tablet Optimized */
.pwa-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: linear-gradient(135deg, var(--industrial-dark) 0%, var(--industrial-darker) 100%);
    /* Enable hardware acceleration for smooth scrolling */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.pwa-header {
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    border-bottom: 3px solid var(--industrial-teal);
    padding: 1rem;
    box-shadow: 0 4px 16px var(--industrial-shadow);
    z-index: 100;
}

.pwa-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 2rem;
    background: linear-gradient(135deg, var(--industrial-dark) 0%, var(--industrial-darker) 100%);
    position: relative;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    /* Hide scrollbar */
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.pwa-content::-webkit-scrollbar {
    display: none;
}

.pwa-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(20, 184, 166, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.pwa-footer {
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--industrial-gray) 0%, var(--industrial-steel) 100%);
    border-top: 2px solid var(--industrial-teal);
    padding: 1.25rem;
    box-shadow: 0 -4px 16px var(--industrial-shadow);
}

/* Enhanced Responsive Design for Tablets - Comprehensive Optimization */
@media (min-width: 768px) and (max-width: 1024px) {
    /* Root optimizations for tablet */
    html, body {
        /* Enhanced momentum scrolling */
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* Prevent bounce scrolling on iOS */
        overscroll-behavior: none;
        /* Optimize touch response */
        touch-action: manipulation;
    }

    /* Typography adjustments for tablets */
    h1 {
        font-size: 2.75rem;
        line-height: 1.2;
    }

    h2 {
        font-size: 2.25rem;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.75rem;
        line-height: 1.4;
    }

    /* Button optimizations for touch */
    .btn {
        padding: 1.25rem 2.5rem;
        font-size: 1.1rem;
        min-height: 56px; /* Increased for better touch targets */
        touch-action: manipulation;
        border-radius: 12px;
        /* Enhanced touch feedback */
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateZ(0); /* Hardware acceleration */
    }

    .btn:active {
        transform: scale(0.98) translateZ(0);
    }

    .btn-primary {
        padding: 1.25rem 2.5rem;
        font-size: 1rem;
        letter-spacing: 1px;
        min-height: 56px;
    }

    /* Form controls for tablet touch */
    .form-control, .form-select {
        padding: 1.25rem;
        font-size: 1.1rem;
        min-height: 56px;
        border-radius: 12px;
        /* Prevent zoom on focus for iOS */
        font-size: 16px;
    }

    /* Card enhancements for tablets */
    .card {
        margin-bottom: 2rem;
        border-radius: 16px;
        /* Enhanced shadow for depth */
        box-shadow: 0 8px 32px var(--industrial-shadow);
        /* Smooth transitions */
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px var(--industrial-shadow-heavy);
    }

    .card-body {
        padding: 2.5rem;
    }

    .card-header {
        padding: 1.5rem 2rem;
        font-size: 1.1rem;
    }

    /* PWA layout optimizations for tablets */
    .pwa-content {
        padding: 2.5rem;
        /* Optimize scroll performance */
        will-change: scroll-position;
        contain: layout style paint;
    }

    .pwa-footer {
        padding: 1.75rem;
        /* Ensure footer stays accessible */
        z-index: 50;
    }

    /* Navigation adjustments */
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
        max-width: none; /* Use full tablet width */
    }

    /* Grid improvements for tablets */
    .row {
        margin-left: -1rem;
        margin-right: -1rem;
    }

    .row > * {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Table improvements for tablets */
    .table {
        font-size: 1rem;
    }

    .table th,
    .table td {
        padding: 1rem;
        /* Better touch targets for table interactions */
        min-height: 48px;
    }

    /* Alert improvements */
    .alert {
        padding: 1.5rem 2rem;
        font-size: 1rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
    }

    /* Badge improvements */
    .badge {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border-radius: 8px;
    }

    /* List group optimizations */
    .list-group-item {
        padding: 1.5rem;
        min-height: 64px;
        border-radius: 12px;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }

    .list-group-item:hover {
        transform: translateX(4px);
    }

    /* Modal optimizations for tablets */
    .modal-dialog {
        max-width: 80vw;
        margin: 2rem auto;
    }

    .modal-content {
        border-radius: 16px;
        border: none;
        box-shadow: 0 16px 48px var(--industrial-shadow-heavy);
    }

    .modal-header,
    .modal-footer {
        padding: 1.5rem 2rem;
    }

    .modal-body {
        padding: 2rem;
    }
}

/* Mobile Responsive */
@media (max-width: 767px) {
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Ensure button text and icons are always visible on mobile */
    .btn .btn-text {
        display: inline !important;
        white-space: nowrap;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    .btn i {
        display: inline !important;
    }

    /* Prevent buttons from being too small to show content */
    .btn:not(.btn-sm):not(.btn-lg) {
        min-height: 48px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 1rem;
    }

    .pwa-header {
        padding: 1rem;
    }

    .pwa-content {
        padding: 1rem;
    }
}

/* Error UI */
#blazor-error-ui {
    background: linear-gradient(135deg, var(--industrial-red) 0%, #c53030 100%);
    color: white;
    bottom: 0;
    box-shadow: 0 -4px 16px var(--industrial-shadow-heavy);
    display: none;
    left: 0;
    padding: 1rem 1.5rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    border-top: 3px solid var(--industrial-teal);
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 1rem;
    top: 1rem;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

#blazor-error-ui .dismiss:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA9Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, var(--industrial-red);
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
    border-radius: 8px;
    margin: 1rem;
    box-shadow: 0 4px 16px var(--industrial-shadow-heavy);
}

.blazor-error-boundary::after {
    content: "An error has occurred.";
}

/* Code styling */
code {
    color: var(--industrial-teal-light);
    background-color: var(--industrial-darker);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

/* Form floating labels */
.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
    color: var(--industrial-text-muted);
    text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
    text-align: start;
}

/* Enhanced Utility Classes */
.glass-effect {
    background: rgba(45, 45, 45, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.industrial-gradient {
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
}

.industrial-shadow {
    box-shadow: 0 8px 24px var(--industrial-shadow);
}

.industrial-shadow-heavy {
    box-shadow: 0 12px 32px var(--industrial-shadow-heavy);
}

.industrial-glow {
    box-shadow: 0 0 20px var(--industrial-glow);
}

.text-gradient {
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-light) 100%) 1;
}

.status-online {
    color: var(--industrial-green);
}

.status-offline {
    color: var(--industrial-red);
}

.status-pending {
    color: var(--industrial-orange);
}

.status-synced {
    color: var(--industrial-teal);
}

/* Enhanced Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 48px;
        padding: 1.25rem 2rem;
        font-size: 1rem;
    }

    .form-control, .form-select {
        min-height: 48px;
        padding: 1.25rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .card {
        margin-bottom: 1.5rem;
    }

    .card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* Tablet Gesture UI Components */
.tablet-toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background: var(--industrial-gray);
    color: var(--industrial-text);
    padding: 1rem 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px var(--industrial-shadow-heavy);
    z-index: 3000;
    font-weight: 600;
    border: 2px solid var(--industrial-border);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 90vw;
    text-align: center;
}

.tablet-toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.tablet-toast-success {
    border-color: var(--industrial-green);
    background: linear-gradient(135deg, var(--industrial-green) 0%, #059669 100%);
    color: white;
}

.tablet-toast-error {
    border-color: var(--industrial-red);
    background: linear-gradient(135deg, var(--industrial-red) 0%, #dc2626 100%);
    color: white;
}

.tablet-toast-warning {
    border-color: var(--industrial-orange);
    background: linear-gradient(135deg, var(--industrial-orange) 0%, #d97706 100%);
    color: white;
}

.tablet-toast-info {
    border-color: var(--industrial-teal);
    background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
    color: white;
}

/* Swipe Animation Effects */
.swipe-transition {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

.swipe-left {
    transform: translateX(-100%);
    opacity: 0;
}

.swipe-right {
    transform: translateX(100%);
    opacity: 0;
}

/* Pull-to-refresh loading animation */
@keyframes pullToRefreshSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pull-to-refresh-spinner {
    animation: pullToRefreshSpin 1s linear infinite;
}

/* Enhanced scroll indicators for tablet */
.scroll-indicator {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100px;
    background: rgba(20, 184, 166, 0.2);
    border-radius: 2px;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.scroll-indicator.visible {
    opacity: 1;
}

.scroll-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20%;
    background: var(--industrial-teal);
    border-radius: 2px;
    transition: top 0.1s ease;
}

/* Tablet-specific focus styles */
@media (min-width: 768px) and (max-width: 1024px) {
    *:focus {
        outline: 3px solid var(--industrial-teal);
        outline-offset: 2px;
        border-radius: 4px;
    }

    .btn:focus,
    .form-control:focus,
    .form-select:focus {
        outline: 3px solid var(--industrial-teal);
        outline-offset: 3px;
        box-shadow: 0 0 0 6px rgba(20, 184, 166, 0.2);
    }

    /* Enhanced visual feedback for active states */
    .btn:active {
        transform: scale(0.98);
        box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .enhanced-yesno-label:active,
    .enhanced-option-label:active {
        transform: scale(0.99);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

/* Gesture hint animations */
@keyframes swipeHint {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(10px); }
}

.swipe-hint {
    animation: swipeHint 2s ease-in-out infinite;
}

.swipe-hint-left {
    animation: swipeHint 2s ease-in-out infinite reverse;
}

/* Long press visual feedback */
@keyframes longPressRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.long-press-ripple {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(20, 184, 166, 0.5);
    animation: longPressRipple 0.5s ease-out;
    pointer-events: none;
}

/* Tablet landscape optimizations */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    .audit-execution-container {
        flex-direction: row;
    }

    .question-navigation {
        order: -1;
        width: 200px;
        flex-shrink: 0;
        border-right: 2px solid var(--industrial-teal);
        border-bottom: none;
    }

    .question-content {
        flex: 1;
        padding: 1.5rem;
    }

    .audit-footer {
        position: fixed;
        bottom: 0;
        right: 0;
        width: calc(100% - 200px);
        margin-left: 200px;
    }

    .question-indicators {
        flex-direction: column;
        height: 100%;
        overflow-y: auto;
        padding: 1rem;
    }

    .question-indicators .btn {
        margin-bottom: 0.5rem;
        width: 100%;
        height: 40px;
    }
}