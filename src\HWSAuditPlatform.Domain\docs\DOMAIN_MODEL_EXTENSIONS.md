# Domain Model Extensions for Post-Audit Findings and Corrective Actions Management

## Overview

This document outlines the proposed extensions to the HWS Audit Platform domain model to support:

1. **Area-Based Responsibility Assignment**: Defining responsible parties based on audit area/location
2. **Finding Categorization System**: Flexible categorization of findings for reporting purposes

## Current State Analysis

### Existing Domain Structure
- **Organizational Hierarchy**: Location → Factory → Area → SubArea
- **Audit Execution**: Audit → AuditAnswer → Finding → CorrectiveAction
- **User Assignment**: Individual users or groups assigned to audits
- **Finding Management**: Basic finding tracking with severity levels and corrective actions

### Limitations Identified
1. **No Area-Based Responsibility**: No mechanism to define who is responsible for corrective actions based on audit location
2. **No Retrospective Analysis Assignment**: No tracking of who should perform efficiency analysis
3. **Limited Finding Categorization**: Only severity levels, no flexible categorization system
4. **Template-Level Configuration Gap**: No way to configure responsibility or categorization at template level

## Proposed Domain Model Extensions

### 1. Area-Based Responsibility System

#### New Entity: AreaResponsibility
```csharp
public class AreaResponsibility : AuditableEntity<string>
{
    // Links to organizational area
    public int AreaId { get; set; }
    public virtual Area Area { get; set; } = null!;
    
    // Responsibility type (enum)
    public ResponsibilityType ResponsibilityType { get; set; }
    
    // Responsible user
    public string ResponsibleUserId { get; set; } = string.Empty;
    public virtual User ResponsibleUser { get; set; } = null!;
    
    // Optional: Specific to audit template
    public int? AuditTemplateId { get; set; }
    public virtual AuditTemplate? AuditTemplate { get; set; }
    
    // Active status
    public bool IsActive { get; set; } = true;
}
```

#### New Enum: ResponsibilityType
```csharp
public enum ResponsibilityType
{
    CorrectiveActionOwner,      // Responsible for corrective actions
    RetrospectiveAnalyst,       // Responsible for efficiency analysis
    FindingReviewer,            // Reviews and validates findings
    EscalationContact           // Contact for escalations
}
```

#### Extended Finding Entity
```csharp
// Add to existing Finding entity
public string? ResponsibleUserId { get; set; }
public virtual User? ResponsibleUser { get; set; }

public string? RetrospectiveAnalystUserId { get; set; }
public virtual User? RetrospectiveAnalystUser { get; set; }
```

### 2. Finding Categorization System

#### New Entity: FindingCategory
```csharp
public class FindingCategory : AuditableEntity<int>
{
    // Category name
    public string CategoryName { get; set; } = string.Empty;
    
    // Optional description
    public string? Description { get; set; }
    
    // Links to audit template (template-specific categories)
    public int AuditTemplateId { get; set; }
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;
    
    // Display order
    public int DisplayOrder { get; set; }
    
    // Active status
    public bool IsActive { get; set; } = true;
    
    // Color coding for UI (optional)
    public string? ColorCode { get; set; }
}
```

#### Extended Finding Entity
```csharp
// Add to existing Finding entity
public int? FindingCategoryId { get; set; }
public virtual FindingCategory? FindingCategory { get; set; }
```

### 3. Template Configuration Extensions

#### Extended AuditTemplate Entity
```csharp
// Add to existing AuditTemplate entity
public bool EnableFindingCategorization { get; set; } = false;
public bool EnableAreaBasedResponsibility { get; set; } = false;

// Navigation properties
public virtual ICollection<FindingCategory> FindingCategories { get; set; } = new List<FindingCategory>();
public virtual ICollection<AreaResponsibility> AreaResponsibilities { get; set; } = new List<AreaResponsibility>();
```

## Database Schema Changes

### New Tables

#### hwsap_org_area_responsibilities
```sql
CREATE TABLE hwsap_org_area_responsibilities (
    Id varchar(25) PRIMARY KEY,
    AreaId int NOT NULL,
    ResponsibilityType int NOT NULL,
    ResponsibleUserId varchar(36) NOT NULL,
    AuditTemplateId int NULL,
    IsActive bit NOT NULL DEFAULT 1,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    RecordVersion int NOT NULL DEFAULT 1,
    CreatedByUserId varchar(36) NULL,
    UpdatedByUserId varchar(36) NULL,
    
    CONSTRAINT FK_AreaResponsibilities_Area FOREIGN KEY (AreaId) REFERENCES hwsap_org_areas(Id),
    CONSTRAINT FK_AreaResponsibilities_User FOREIGN KEY (ResponsibleUserId) REFERENCES hwsap_usr_users(AdObjectGuid),
    CONSTRAINT FK_AreaResponsibilities_Template FOREIGN KEY (AuditTemplateId) REFERENCES hwsap_tpl_audit_templates(Id)
);
```

#### hwsap_fnd_finding_categories
```sql
CREATE TABLE hwsap_fnd_finding_categories (
    Id int IDENTITY(1,1) PRIMARY KEY,
    CategoryName varchar(100) NOT NULL,
    Description text NULL,
    AuditTemplateId int NOT NULL,
    DisplayOrder int NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1,
    ColorCode varchar(7) NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    RecordVersion int NOT NULL DEFAULT 1,
    CreatedByUserId varchar(36) NULL,
    UpdatedByUserId varchar(36) NULL,
    
    CONSTRAINT FK_FindingCategories_Template FOREIGN KEY (AuditTemplateId) REFERENCES hwsap_tpl_audit_templates(Id)
);
```

### Modified Tables

#### hwsap_fnd_findings (additions)
```sql
ALTER TABLE hwsap_fnd_findings ADD ResponsibleUserId varchar(36) NULL;
ALTER TABLE hwsap_fnd_findings ADD RetrospectiveAnalystUserId varchar(36) NULL;
ALTER TABLE hwsap_fnd_findings ADD FindingCategoryId int NULL;

ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_ResponsibleUser 
    FOREIGN KEY (ResponsibleUserId) REFERENCES hwsap_usr_users(AdObjectGuid);
ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_RetrospectiveAnalyst 
    FOREIGN KEY (RetrospectiveAnalystUserId) REFERENCES hwsap_usr_users(AdObjectGuid);
ALTER TABLE hwsap_fnd_findings ADD CONSTRAINT FK_Findings_Category 
    FOREIGN KEY (FindingCategoryId) REFERENCES hwsap_fnd_finding_categories(Id);
```

#### hwsap_tpl_audit_templates (additions)
```sql
ALTER TABLE hwsap_tpl_audit_templates ADD EnableFindingCategorization bit NOT NULL DEFAULT 0;
ALTER TABLE hwsap_tpl_audit_templates ADD EnableAreaBasedResponsibility bit NOT NULL DEFAULT 0;
```

### New Enum Types

#### responsibility_type
```sql
CREATE TYPE responsibility_type AS ENUM (
    'CorrectiveActionOwner',
    'RetrospectiveAnalyst', 
    'FindingReviewer',
    'EscalationContact'
);
```

## Business Logic and Rules

### Area-Based Responsibility Assignment
1. **Automatic Assignment**: When a finding is created, automatically assign responsible users based on audit area
2. **Template-Specific Rules**: Area responsibilities can be global or template-specific
3. **Fallback Logic**: If no area-specific responsibility exists, fall back to audit assignee
4. **Multiple Responsibilities**: An area can have multiple responsible users for different responsibility types

### Finding Categorization
1. **Template Configuration**: Categories are defined per audit template
2. **Optional Feature**: Templates can enable/disable categorization
3. **Flexible Categories**: No predefined categories - fully configurable per template
4. **Reporting Integration**: Categories enable better reporting and analytics

### Backward Compatibility
1. **Optional Features**: All new features are optional and disabled by default
2. **Existing Data**: Existing audits and findings continue to work without modification
3. **Migration Path**: Gradual adoption possible - templates can be updated individually

## Integration Points

### Application Layer Impact
- New commands for managing area responsibilities and finding categories
- Extended queries to include responsibility and categorization data
- Updated DTOs to support new properties

### API Changes
- New endpoints for area responsibility management
- Extended finding endpoints to include categorization
- Template configuration endpoints for new features

### UI Considerations
- Area responsibility management interface
- Finding categorization during audit review
- Template configuration for new features
- Enhanced reporting with categorization filters

## Next Steps

1. Implement domain entities and enums
2. Create Entity Framework configurations
3. Generate database migrations
4. Update application layer services
5. Extend API endpoints
6. Create unit tests
7. Update documentation
