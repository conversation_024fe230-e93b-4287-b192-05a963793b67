using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.WebApp.Models;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for audit API operations
/// </summary>
public interface IAuditApiService
{
    /// <summary>
    /// Gets all audits
    /// </summary>
    Task<IEnumerable<Audit>> GetAuditsAsync();

    /// <summary>
    /// Gets an audit by ID
    /// </summary>
    Task<Audit?> GetAuditByIdAsync(string id);

    /// <summary>
    /// Gets an audit with detailed information for review
    /// </summary>
    Task<AuditReviewDto?> GetAuditForReviewAsync(string id);

    /// <summary>
    /// Creates a new audit
    /// </summary>
    Task<Audit> CreateAuditAsync(Audit audit);

    /// <summary>
    /// Creates a new audit from request model
    /// </summary>
    Task<string> CreateAuditAsync(CreateAuditRequest request);

    /// <summary>
    /// Updates an existing audit
    /// </summary>
    Task<Audit> UpdateAuditAsync(Audit audit);

    /// <summary>
    /// Deletes an audit
    /// </summary>
    Task<bool> DeleteAuditAsync(string id);

    /// <summary>
    /// Reviews an audit (approve or reject)
    /// </summary>
    Task<bool> ReviewAuditAsync(string id, ReviewAuditRequest request);

    /// <summary>
    /// Gets findings for an audit
    /// </summary>
    Task<IEnumerable<FindingDto>> GetAuditFindingsAsync(string auditId);

    /// <summary>
    /// Gets findings with filtering and pagination
    /// </summary>
    Task<PagedResult<FindingDto>> GetFindingsAsync(FindingFilterModel filter);

    /// <summary>
    /// Gets finding statistics
    /// </summary>
    Task<FindingStatisticsModel> GetFindingStatisticsAsync();

    /// <summary>
    /// Assigns a category to a finding
    /// </summary>
    Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId);

    /// <summary>
    /// Assigns a responsible user to a finding
    /// </summary>
    Task<bool> AssignFindingResponsibleUserAsync(string findingId, string? userId);

    /// <summary>
    /// Performs bulk operations on findings
    /// </summary>
    Task<bool> BulkUpdateFindingsAsync(BulkFindingOperationModel operation);
}

/// <summary>
/// Request model for reviewing audits
/// </summary>
public class ReviewAuditRequest
{
    /// <summary>
    /// Manager's comments on the audit
    /// </summary>
    public string? ManagerComments { get; set; }

    /// <summary>
    /// Whether the audit is approved or rejected
    /// </summary>
    public bool Approved { get; set; }
}
