# Simple test for authentication fix
Write-Host "Testing API login..." -ForegroundColor Yellow

$response = Invoke-RestMethod -Uri "https://localhost:7490/api/v1/auth/login" -Method POST -ContentType "application/json" -Body '{"username": "admin.user", "password": "HWSAudit123!"}'

if ($response.success) {
    Write-Host "✓ API login successful" -ForegroundColor Green
    Write-Host "  ExpiresAt: $($response.data.expiresAt)" -ForegroundColor Cyan
    Write-Host "✓ Authentication fix is working!" -ForegroundColor Green
}
else {
    Write-Host "✗ API login failed" -ForegroundColor Red
}
