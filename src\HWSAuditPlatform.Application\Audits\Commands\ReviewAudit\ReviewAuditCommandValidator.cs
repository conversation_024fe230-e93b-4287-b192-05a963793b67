using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.ReviewAudit;

/// <summary>
/// Validator for ReviewAuditCommand
/// </summary>
public class ReviewAuditCommandValidator : AbstractValidator<ReviewAuditCommand>
{
    private readonly IApplicationDbContext _context;

    public ReviewAuditCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AuditId)
            .NotEmpty().WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit).WithMessage("Audit does not exist")
            .MustAsync(BeInReviewableStatus).WithMessage("Audit is not in a reviewable status");

        RuleFor(x => x.Comments)
            .MaximumLength(2000).WithMessage("Comments cannot exceed 2000 characters");
    }

    private async Task<bool> BeValidAudit(string auditId, CancellationToken cancellationToken)
    {
        return await _context.Audits
            .AnyAsync(a => a.Id == auditId, cancellationToken);
    }

    private async Task<bool> BeInReviewableStatus(string auditId, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);

        if (audit == null) return false;

        return audit.OverallStatus == AuditOverallStatus.PendingManagerReview ||
               audit.OverallStatus == AuditOverallStatus.Submitted;
    }
}
