using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;

/// <summary>
/// Command to generate audits from recurring audit settings that are due
/// </summary>
public class GenerateRecurringAuditsCommand : BaseCommand<GenerateRecurringAuditsResult>
{
    /// <summary>
    /// Optional specific recurring audit setting ID to process (if null, processes all due settings)
    /// </summary>
    public string? RecurringAuditSettingId { get; set; }

    /// <summary>
    /// Whether this is a dry run (don't actually create audits, just report what would be created)
    /// </summary>
    public bool DryRun { get; set; } = false;

    /// <summary>
    /// Maximum number of audits to generate in this batch
    /// </summary>
    public int MaxAuditsToGenerate { get; set; } = 100;
}

/// <summary>
/// Result of the audit generation process
/// </summary>
public class GenerateRecurringAuditsResult
{
    /// <summary>
    /// Number of recurring audit settings processed
    /// </summary>
    public int SettingsProcessed { get; set; }

    /// <summary>
    /// Number of audits successfully generated
    /// </summary>
    public int AuditsGenerated { get; set; }

    /// <summary>
    /// Number of errors encountered
    /// </summary>
    public int ErrorsEncountered { get; set; }

    /// <summary>
    /// List of generated audit IDs
    /// </summary>
    public List<string> GeneratedAuditIds { get; set; } = new();

    /// <summary>
    /// List of errors that occurred during generation
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Whether this was a dry run
    /// </summary>
    public bool WasDryRun { get; set; }

    /// <summary>
    /// Processing duration
    /// </summary>
    public TimeSpan ProcessingDuration { get; set; }

    public override string ToString()
    {
        var action = WasDryRun ? "would generate" : "generated";
        return $"Processed {SettingsProcessed} settings, {action} {AuditsGenerated} audits, {ErrorsEncountered} errors in {ProcessingDuration.TotalSeconds:F2}s";
    }
}
