using FluentValidation;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.Commands.AddQuestionOptions;

/// <summary>
/// Validator for AddQuestionOptionsCommand
/// </summary>
public class AddQuestionOptionsCommandValidator : AbstractValidator<AddQuestionOptionsCommand>
{
    private readonly IApplicationDbContext _context;

    public AddQuestionOptionsCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.QuestionId)
            .GreaterThan(0).WithMessage("Question ID must be greater than 0")
            .MustAsync(BeValidQuestion).WithMessage("Question does not exist")
            .MustAsync(SupportOptions).WithMessage("Question type does not support options");

        RuleFor(x => x.TemplateId)
            .GreaterThan(0).WithMessage("Template ID must be greater than 0")
            .MustAsync(BeValidTemplate).WithMessage("Template does not exist or is published");

        RuleFor(x => x.Options)
            .NotEmpty().WithMessage("At least one option must be provided")
            .Must(HaveUniqueDisplayOrders).WithMessage("Option display orders must be unique")
            .Must(HaveUniqueOptionTexts).WithMessage("Option texts must be unique");

        RuleForEach(x => x.Options).SetValidator(new AddQuestionOptionDtoValidator());

        RuleFor(x => x)
            .MustAsync(QuestionBelongsToTemplate).WithMessage("Question does not belong to the specified template");
    }

    private async Task<bool> BeValidQuestion(int questionId, CancellationToken cancellationToken)
    {
        return await _context.Questions
            .AnyAsync(q => q.Id == questionId && q.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidTemplate(int templateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates
            .AnyAsync(t => t.Id == templateId && t.IsActive && !t.IsPublished, cancellationToken);
    }

    private async Task<bool> SupportOptions(int questionId, CancellationToken cancellationToken)
    {
        var question = await _context.Questions
            .FirstOrDefaultAsync(q => q.Id == questionId, cancellationToken);

        if (question == null)
            return false;

        return question.QuestionType == QuestionType.SingleSelect || 
               question.QuestionType == QuestionType.MultiSelect;
    }

    private async Task<bool> QuestionBelongsToTemplate(AddQuestionOptionsCommand command, CancellationToken cancellationToken)
    {
        return await _context.Questions
            .AnyAsync(q => q.Id == command.QuestionId && 
                          q.AuditTemplateId == command.TemplateId && 
                          q.IsActive, cancellationToken);
    }

    private static bool HaveUniqueDisplayOrders(List<AddQuestionOptionDto> options)
    {
        var displayOrders = options.Select(o => o.DisplayOrder).ToList();
        return displayOrders.Count == displayOrders.Distinct().Count();
    }

    private static bool HaveUniqueOptionTexts(List<AddQuestionOptionDto> options)
    {
        var optionTexts = options.Select(o => o.OptionText.Trim().ToLowerInvariant()).ToList();
        return optionTexts.Count == optionTexts.Distinct().Count();
    }
}

/// <summary>
/// Validator for individual AddQuestionOptionDto
/// </summary>
public class AddQuestionOptionDtoValidator : AbstractValidator<AddQuestionOptionDto>
{
    public AddQuestionOptionDtoValidator()
    {
        RuleFor(x => x.OptionText)
            .NotEmpty().WithMessage("Option text is required")
            .MaximumLength(500).WithMessage("Option text cannot exceed 500 characters");

        RuleFor(x => x.OptionValue)
            .MaximumLength(100).WithMessage("Option value cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.OptionValue));

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Display order must be greater than or equal to 0");
    }
}
