@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Templates
@inject ITemplateApiService TemplateApiService
@inject ILogger<TemplateListComponent> Logger
@inject NavigationManager Navigation

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">
                        <i class="bi bi-file-earmark-ruled me-2 text-primary"></i>
                        Template Management
                    </h1>
                    <p class="text-muted mb-0">Create and manage audit templates and their questions</p>
                </div>
                <div>
                    <button class="btn btn-primary" @onclick="CreateNewTemplate">
                        <i class="bi bi-plus-circle me-2"></i>Create Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <HWSAuditPlatform.WebApp.Components.Shared.ErrorComponent 
        HasError="@(!string.IsNullOrEmpty(errorMessage))"
        Title="Error"
        Message="@errorMessage"
        AlertType="danger"
        OnDismiss="DismissMessages" />

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@successMessage
            <button type="button" class="btn-close" @onclick="DismissMessages"></button>
        </div>
    }

    <!-- Templates Grid -->
    <div class="row">
        <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
            IsLoading="@isLoading" 
            Message="Loading templates..." />

        @if (!isLoading && templates?.Any() == true)
        {
            @foreach (var template in templates)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <TemplatePreviewComponent Template="template" 
                                            OnView="ViewTemplate" 
                                            OnEdit="EditTemplate" 
                                            OnDelete="DeleteTemplate" />
                </div>
            }
        }
        else if (!isLoading)
        {
            <div class="col-12">
                <div class="card text-center py-5">
                    <div class="card-body">
                        <i class="bi bi-file-earmark-ruled display-1 text-muted mb-3"></i>
                        <h4>No Templates Found</h4>
                        <p class="text-muted mb-4">Get started by creating your first audit template.</p>
                        <button class="btn btn-primary" @onclick="CreateNewTemplate">
                            <i class="bi bi-plus-circle me-2"></i>Create First Template
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    private IEnumerable<AuditTemplate>? templates;
    private bool isLoading = true;
    private string? successMessage = null;
    private string? errorMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplates();
    }

    private async Task LoadTemplates()
    {
        try
        {
            isLoading = true;
            templates = await TemplateApiService.GetTemplatesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading templates");
            ShowErrorMessage("Failed to load templates. Please try again.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void CreateNewTemplate()
    {
        Navigation.NavigateTo("/templates/create");
    }

    private void ViewTemplate(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}");
    }

    private void EditTemplate(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}/edit");
    }

    private async Task DeleteTemplate(int templateId)
    {
        try
        {
            var success = await TemplateApiService.DeleteTemplateAsync(templateId);
            if (success)
            {
                ShowSuccessMessage("Template deleted successfully!");
                await LoadTemplates();
            }
            else
            {
                ShowErrorMessage("Failed to delete template. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting template {TemplateId}", templateId);
            ShowErrorMessage("An error occurred while deleting the template.");
        }
    }

    private void ShowSuccessMessage(string message)
    {
        successMessage = message;
        errorMessage = null;
        StateHasChanged();

        _ = Task.Delay(5000).ContinueWith(_ =>
        {
            successMessage = null;
            InvokeAsync(StateHasChanged);
        });
    }

    private void ShowErrorMessage(string message)
    {
        errorMessage = message;
        successMessage = null;
        StateHasChanged();
    }

    private void DismissMessages()
    {
        successMessage = null;
        errorMessage = null;
        StateHasChanged();
    }
}
