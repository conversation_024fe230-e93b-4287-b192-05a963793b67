using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Audits.Queries.GetAudits;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for dashboard operations and statistics
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class DashboardController : BaseController
{
    public DashboardController(IMediator mediator, ILogger<DashboardController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get dashboard statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dashboard statistics</returns>
    [HttpGet("stats")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(DashboardStatsDto), 200)]
    public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats(CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Getting dashboard statistics for user: {UserId}", GetCurrentUserId());

            // Get all audits for statistics calculation
            var auditsQuery = new GetAuditsQuery
            {
                PageNumber = 1,
                PageSize = 1000, // Get a large number for statistics
                SortBy = "ScheduledDate",
                SortDirection = "desc"
            };

            var auditsResult = await Mediator.Send(auditsQuery, cancellationToken);
            var allAudits = auditsResult?.Items ?? new List<AuditSummaryDto>();

            // Calculate statistics
            var totalAudits = allAudits.Count;
            var pendingAudits = allAudits.Count(a =>
                a.OverallStatus == AuditOverallStatus.Scheduled ||
                a.OverallStatus == AuditOverallStatus.InProgress);

            var overdueAudits = allAudits.Where(a =>
                a.DueDate.HasValue &&
                a.DueDate.Value < DateTime.UtcNow &&
                a.OverallStatus != AuditOverallStatus.Closed).ToList();

            var completedThisMonth = allAudits.Count(a =>
                a.OverallStatus == AuditOverallStatus.Closed &&
                a.ScheduledDate.Month == DateTime.UtcNow.Month &&
                a.ScheduledDate.Year == DateTime.UtcNow.Year);

            // Calculate average score (mock calculation for now)
            var averageScore = allAudits.Any() ? 85.5m : 0m;

            // Get recent audits (last 10)
            var recentAudits = allAudits
                .OrderByDescending(a => a.ScheduledDate)
                .Take(10)
                .ToList();

            var dashboardStats = new DashboardStatsDto
            {
                TotalAudits = totalAudits,
                PendingAudits = pendingAudits,
                OverdueAuditsCount = overdueAudits.Count,
                CompletedAuditsThisMonth = completedThisMonth,
                AverageScore = averageScore,
                RecentAudits = recentAudits,
                OverdueAudits = overdueAudits.Take(10).ToList()
            };

            Logger.LogInformation("Dashboard statistics calculated successfully. Total: {Total}, Pending: {Pending}, Overdue: {Overdue}", 
                totalAudits, pendingAudits, overdueAudits.Count);

            return Success(dashboardStats);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting dashboard statistics");
            return Problem("An error occurred while retrieving dashboard statistics");
        }
    }
}

/// <summary>
/// Dashboard statistics DTO
/// </summary>
public class DashboardStatsDto
{
    public int TotalAudits { get; set; }
    public int PendingAudits { get; set; }
    public int OverdueAuditsCount { get; set; }
    public int CompletedAuditsThisMonth { get; set; }
    public decimal AverageScore { get; set; }
    public List<AuditSummaryDto> RecentAudits { get; set; } = new();
    public List<AuditSummaryDto> OverdueAudits { get; set; } = new();
}
