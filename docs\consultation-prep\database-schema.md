# Database Schema - Key Relationships & Concerns

## 🗄️ **Schema Overview**

### **Database Statistics**
- **Total Tables**: 25+ tables
- **Primary Key Strategy**: Mixed (CUIDs + Integers)
- **Relationships**: 50+ foreign key relationships
- **Indexes**: 30+ indexes (basic optimization)
- **Business Rules**: 40+ constraints

## 🔑 **Primary Key Strategy Analysis**

### **CUID vs Integer Strategy**
```mermaid
graph TB
    subgraph "CUID Primary Keys (Client-Generated)"
        CUIDTables[Offline-Created Entities<br/>25-character CUIDs]
        Audits[Audits<br/>varchar(25)]
        AuditAnswers[AuditAnswers<br/>varchar(25)]
        AuditAttachments[AuditAttachments<br/>varchar(25)]
        Findings[Findings<br/>varchar(25)]
        Users[Users<br/>varchar(36) - AdGuid]
    end
    
    subgraph "Integer Primary Keys (Server-Generated)"
        IntTables[Server-Managed Entities<br/>Auto-increment integers]
        AuditTemplates[AuditTemplates<br/>int]
        Questions[Questions<br/>int]
        Factories[Factories<br/>int]
        Roles[Roles<br/>int]
    end
    
    subgraph "Rationale"
        OfflineSync[Offline Sync<br/>No server roundtrip]
        Performance[Query Performance<br/>Integer joins faster]
        Consistency[Data Consistency<br/>Server-controlled]
    end
    
    CUIDTables --> OfflineSync
    IntTables --> Performance
    IntTables --> Consistency
    
    style Audits fill:#e1f5fe
    style AuditAnswers fill:#e1f5fe
    style Users fill:#fff3e0
    style AuditTemplates fill:#e8f5e8
    style Questions fill:#e8f5e8
```

## 📊 **Core Entity Relationships**

### **User Management Domain**
```mermaid
erDiagram
    Users ||--o{ UserGroupMembers : "belongs to"
    UserGroups ||--o{ UserGroupMembers : "contains"
    UserGroups ||--o{ AdGroupRoleMapping : "maps to"
    Roles ||--o{ AdGroupRoleMapping : "assigned to"
    Roles ||--o{ Users : "has role"
    
    Users {
        varchar(36) AdGuid PK "AD GUID"
        varchar(100) Username
        varchar(200) FullName
        varchar(100) Email
        int RoleId FK
        bit IsActive
        datetime CreatedAt
        datetime UpdatedAt
        varchar(36) CreatedByUserId FK
        varchar(36) UpdatedByUserId FK
        int RecordVersion
    }
    
    Roles {
        int RoleId PK
        varchar(50) RoleName
        varchar(200) Description
        bit IsActive
    }
    
    UserGroups {
        int UserGroupId PK
        varchar(100) GroupName
        varchar(200) Description
        varchar(255) AdGroupName
        bit IsActive
    }
```

### **Organizational Structure**
```mermaid
erDiagram
    Factories ||--o{ Areas : "contains"
    Areas ||--o{ SubAreas : "subdivided into"
    Factories ||--o{ Users : "employs"
    Areas ||--o{ Users : "assigned to"
    SubAreas ||--o{ Users : "works in"
    
    Factories {
        int FactoryId PK
        varchar(100) FactoryName
        varchar(200) Description
        varchar(200) Address
        bit IsActive
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    Areas {
        int AreaId PK
        int FactoryId FK
        varchar(100) AreaName
        varchar(200) Description
        bit IsActive
    }
    
    SubAreas {
        int SubAreaId PK
        int AreaId FK
        varchar(100) SubAreaName
        varchar(200) Description
        bit IsActive
    }
```

### **Audit Template Structure**
```mermaid
erDiagram
    AuditTemplates ||--o{ Questions : "contains"
    Questions ||--o{ QuestionOptions : "has options"
    Questions ||--o{ QuestionGroups : "grouped by"
    AuditTemplates ||--o{ Audits : "instantiated as"
    
    AuditTemplates {
        int AuditTemplateId PK
        varchar(200) TemplateName
        varchar(500) Description
        varchar(50) Status
        varchar(50) Version
        bit IsActive
        datetime CreatedAt
        varchar(36) CreatedByUserId FK
    }
    
    Questions {
        int QuestionId PK
        int AuditTemplateId FK
        varchar(500) QuestionText
        varchar(50) QuestionType
        text AllowedEvidenceTypes
        bit IsRequired
        int SortOrder
        bit IsActive
    }
    
    QuestionOptions {
        int QuestionOptionId PK
        int QuestionId FK
        varchar(200) OptionText
        varchar(100) OptionValue
        int SortOrder
        bit IsActive
    }
```

### **Audit Execution & Results**
```mermaid
erDiagram
    Audits ||--o{ AuditAnswers : "contains"
    AuditAnswers ||--o{ AuditAnswerSelectedOptions : "selected"
    AuditAnswers ||--o{ AuditAnswerFailureReasons : "failure reasons"
    Audits ||--o{ AuditAttachments : "has evidence"
    Audits ||--o{ Findings : "generates"
    Findings ||--o{ CorrectiveActions : "requires"
    
    Audits {
        varchar(25) AuditId PK "CUID"
        int AuditTemplateId FK
        varchar(36) AssignedToUserId FK
        varchar(100) Status
        datetime ScheduledDate
        datetime StartedDate
        datetime CompletedDate
        varchar(36) CreatedByUserId FK
        int RecordVersion
    }
    
    AuditAnswers {
        varchar(25) AuditAnswerId PK "CUID"
        varchar(25) AuditId FK
        int QuestionId FK
        varchar(50) AnswerType
        text AnswerValue
        datetime AnsweredAt
        varchar(36) AnsweredByUserId FK
    }
    
    Findings {
        varchar(25) FindingId PK "CUID"
        varchar(25) AuditId FK
        varchar(500) FindingDescription
        varchar(100) Severity
        varchar(100) Status
        varchar(36) AssignedToUserId FK
        datetime CreatedAt
    }
```

## ⚠️ **Schema Concerns & Questions**

### **Performance Concerns**

1. **Mixed Primary Key Types**
   ```sql
   -- CUID joins (string comparison)
   SELECT * FROM Audits a 
   JOIN AuditAnswers aa ON a.AuditId = aa.AuditId  -- varchar(25) join
   
   -- Integer joins (faster)
   SELECT * FROM AuditTemplates at 
   JOIN Questions q ON at.AuditTemplateId = q.AuditTemplateId  -- int join
   ```

2. **Complex Query Patterns**
   ```sql
   -- Typical audit retrieval query
   SELECT a.*, at.TemplateName, u.FullName,
          COUNT(aa.AuditAnswerId) as AnswerCount,
          COUNT(f.FindingId) as FindingCount
   FROM Audits a
   JOIN AuditTemplates at ON a.AuditTemplateId = at.AuditTemplateId
   JOIN Users u ON a.AssignedToUserId = u.AdGuid
   LEFT JOIN AuditAnswers aa ON a.AuditId = aa.AuditId
   LEFT JOIN Findings f ON a.AuditId = f.AuditId
   GROUP BY a.AuditId, at.TemplateName, u.FullName
   ```

### **Indexing Strategy**
```mermaid
graph TB
    subgraph "Current Indexes"
        PK[Primary Key Indexes<br/>Automatic]
        FK[Foreign Key Indexes<br/>Basic]
        Status[Status Indexes<br/>Query optimization]
    end
    
    subgraph "Missing Indexes?"
        Composite[Composite Indexes<br/>Multi-column queries]
        Covering[Covering Indexes<br/>Include columns]
        Filtered[Filtered Indexes<br/>Active records only]
    end
    
    subgraph "Query Patterns"
        UserAudits[User's Audits<br/>AssignedToUserId + Status]
        DateRange[Date Range Queries<br/>ScheduledDate + Status]
        TemplateUsage[Template Usage<br/>AuditTemplateId + CreatedAt]
    end
    
    PK --> Composite
    FK --> Covering
    Status --> Filtered
    
    UserAudits --> Composite
    DateRange --> Composite
    TemplateUsage --> Covering
    
    style PK fill:#e8f5e8
    style FK fill:#e8f5e8
    style Composite fill:#fff3e0
    style Covering fill:#fff3e0
```

### **Data Integrity Concerns**

1. **Cascade Delete Behavior**
   ```sql
   -- Potential cascade cycles
   Users -> CreatedByUserId (multiple tables)
   Users -> UpdatedByUserId (multiple tables)
   Users -> AssignedToUserId (Audits, Findings)
   
   -- May require ON DELETE NO ACTION
   ```

2. **Optimistic Concurrency**
   ```sql
   -- RecordVersion strategy
   UPDATE Audits 
   SET Status = 'Completed', RecordVersion = RecordVersion + 1
   WHERE AuditId = @AuditId AND RecordVersion = @ExpectedVersion
   
   -- What happens on version mismatch?
   ```

## 🔄 **Offline Sync Challenges**

### **CUID Collision Risk**
```mermaid
sequenceDiagram
    participant PWA1 as PWA Client 1
    participant PWA2 as PWA Client 2
    participant API as API Service
    participant DB as Database
    
    Note over PWA1,DB: Potential CUID Collision Scenario
    PWA1->>PWA1: Generate CUID: c123abc...
    PWA2->>PWA2: Generate CUID: c123abc... (collision!)
    
    PWA1->>API: POST audit with CUID
    API->>DB: INSERT audit (success)
    
    PWA2->>API: POST audit with same CUID
    API->>DB: INSERT audit (PRIMARY KEY violation)
    API->>PWA2: Conflict error
    
    Note over PWA1,DB: Resolution Strategy Needed
    PWA2->>PWA2: Generate new CUID
    PWA2->>API: POST audit with new CUID
    API->>DB: INSERT audit (success)
```

### **Sync Conflict Resolution**
```mermaid
graph TB
    subgraph "Conflict Scenarios"
        SameCUID[Same CUID<br/>Different Data]
        VersionMismatch[RecordVersion<br/>Mismatch]
        DeletedRecord[Record Deleted<br/>on Server]
        UserConflict[User Assignment<br/>Changed]
    end
    
    subgraph "Resolution Strategies"
        LastWins[Last Writer Wins<br/>Simple but data loss]
        UserChoice[User Chooses<br/>Complex UI]
        ServerWins[Server Always Wins<br/>Client data lost]
        Merge[Intelligent Merge<br/>Complex logic]
    end
    
    SameCUID --> UserChoice
    VersionMismatch --> LastWins
    DeletedRecord --> ServerWins
    UserConflict --> Merge
    
    style SameCUID fill:#ffcdd2
    style VersionMismatch fill:#fff3e0
    style UserChoice fill:#e1f5fe
    style LastWins fill:#e8f5e8
```

## 📋 **Questions for Senior Developer**

### **Critical Schema Decisions**

1. **Is our mixed PK strategy (CUIDs + Integers) optimal?**
   - Performance implications of varchar(25) joins
   - Alternative approaches for offline sync
   - When to use CUIDs vs integers

2. **How should we optimize for our query patterns?**
   - Most common queries involve user assignments and date ranges
   - Should we denormalize for performance?
   - Indexing strategy for mixed PK types

3. **What's the best conflict resolution strategy?**
   - CUID collision handling (rare but possible)
   - RecordVersion conflicts during sync
   - User experience during conflicts

4. **Are we missing critical indexes?**
   - Query performance analysis needed
   - Composite indexes for common patterns
   - Covering indexes for read-heavy operations

5. **How do we handle cascade deletes safely?**
   - Multiple foreign key paths to Users table
   - Preventing cascade cycles
   - Soft delete vs hard delete strategy
