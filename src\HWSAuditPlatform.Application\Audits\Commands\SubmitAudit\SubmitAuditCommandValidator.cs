using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAudit;

/// <summary>
/// Validator for SubmitAuditCommand
/// </summary>
public class SubmitAuditCommandValidator : AbstractValidator<SubmitAuditCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public SubmitAuditCommandValidator(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;

        RuleFor(x => x.AuditId)
            .NotEmpty().WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit).WithMessage("Audit does not exist")
            .MustAsync(BeInProgressStatus).WithMessage("Audit must be in progress to submit")
            //.MustAsync(BeAssignedToCurrentUser).WithMessage("Audit is not assigned to current user")
            .MustAsync(HaveAllRequiredAnswers).WithMessage("All required questions must be answered");

        RuleFor(x => x.AuditorComments)
            .MaximumLength(2000).WithMessage("Auditor comments cannot exceed 2000 characters");
    }

    private async Task<bool> BeValidAudit(string auditId, CancellationToken cancellationToken)
    {
        return await _context.Audits.AnyAsync(a => a.Id == auditId, cancellationToken);
    }

    private async Task<bool> BeInProgressStatus(string auditId, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);
        return audit?.OverallStatus == AuditOverallStatus.InProgress;
    }

    private async Task<bool> BeAssignedToCurrentUser(string auditId, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId)) return false;

        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);
        return audit?.AssignedToUserId == currentUserId;
    }

    private async Task<bool> HaveAllRequiredAnswers(string auditId, CancellationToken cancellationToken)
    {
        // Get the audit and its template
        var audit = await _context.Audits
            .Include(a => a.AuditTemplate)
            .FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);

        if (audit == null) return false;

        // Get all required questions for this template
        var requiredQuestions = await _context.Questions
            .Where(q => q.AuditTemplateId == audit.AuditTemplateId && q.IsRequired)
            .Select(q => q.Id)
            .ToListAsync(cancellationToken);

        // Get all answered questions for this audit
        var answeredQuestions = await _context.AuditAnswers
            .Where(a => a.AuditId == auditId)
            .Select(a => a.QuestionId)
            .ToListAsync(cancellationToken);

        // Check if all required questions have been answered
        return requiredQuestions.All(rq => answeredQuestions.Contains(rq));
    }
}
