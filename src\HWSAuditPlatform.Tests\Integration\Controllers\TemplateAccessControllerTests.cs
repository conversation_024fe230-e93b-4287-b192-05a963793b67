using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.Commands.CreateTemplateAccessAssignment;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using Microsoft.VisualStudio.TestPlatform.TestHost;

namespace HWSAuditPlatform.Tests.Integration.Controllers;

/// <summary>
/// Integration tests for TemplateAccessController
/// </summary>
public class TemplateAccessControllerTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ApplicationDbContext _context;

    public TemplateAccessControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing ApplicationDbContext registration
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(ApplicationDbContext));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // Add in-memory database for testing
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString());
                });
            });
        });

        _client = _factory.CreateClient();

        // Get the database context
        using var scope = _factory.Services.CreateScope();
        _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }

    [Fact]
    public async Task GetTemplateUserAccess_WithValidTemplateId_ShouldReturnUsersWithAccess()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var processOwnerUser = await CreateTestUserAsync("processowner", UserRole.ProcessOwner);
        
        // Grant template access to the process owner
        var assignment = TemplateAccessAssignment.Create(
            processOwnerUser.AdObjectGuid, 
            template.Id, 
            "admin-user-guid", 
            "Test assignment");
        
        await _context.TemplateAccessAssignments.AddAsync(assignment);
        await _context.SaveChangesAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/template-access/template/{template.Id}/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ApiResponse<List<object>>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data.Should().NotBeEmpty();
        
        // Should include the process owner user plus any admin users
        result.Data.Should().HaveCountGreaterThanOrEqualTo(1);
    }

    [Fact]
    public async Task GetTemplateUserAccess_WithInvalidTemplateId_ShouldReturnEmptyList()
    {
        // Arrange
        await SeedTestDataAsync();
        var nonExistentTemplateId = 99999;

        // Act
        var response = await _client.GetAsync($"/api/v1/template-access/template/{nonExistentTemplateId}/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ApiResponse<List<object>>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        
        // Should still include admin users who have access to all templates
        // but no users with explicit access to this non-existent template
    }

    [Fact]
    public async Task GetTemplateUserAccess_ShouldIncludeAdminUsers()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/template-access/template/{template.Id}/users");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ApiResponse<List<object>>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        result.Should().NotBeNull();
        result!.Success.Should().BeTrue();
        result.Data.Should().NotBeNull();
        
        // Should include admin users (DevAdmin and SystemManager) who have access to all templates
        result.Data.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GrantTemplateAccess_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var processOwnerUser = await CreateTestUserAsync("processowner2", UserRole.ProcessOwner);
        
        var command = new CreateTemplateAccessAssignmentCommand
        {
            UserId = processOwnerUser.AdObjectGuid,
            AuditTemplateId = template.Id,
            Description = "Test access assignment"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/template-access/grant", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        // Verify the assignment was created
        var assignment = await _context.TemplateAccessAssignments
            .FirstOrDefaultAsync(taa => taa.UserId == processOwnerUser.AdObjectGuid && taa.AuditTemplateId == template.Id);
        
        assignment.Should().NotBeNull();
        assignment!.IsActive.Should().BeTrue();
        assignment.Description.Should().Be("Test access assignment");
    }

    [Fact]
    public async Task RevokeTemplateAccess_WithValidData_ShouldReturnOk()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestTemplateAsync();
        var processOwnerUser = await CreateTestUserAsync("processowner3", UserRole.ProcessOwner);
        
        // Create an active assignment
        var assignment = TemplateAccessAssignment.Create(
            processOwnerUser.AdObjectGuid, 
            template.Id, 
            "admin-user-guid", 
            "Test assignment");
        
        await _context.TemplateAccessAssignments.AddAsync(assignment);
        await _context.SaveChangesAsync();

        // Act
        var response = await _client.DeleteAsync($"/api/v1/template-access/revoke/{processOwnerUser.AdObjectGuid}/{template.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // Verify the assignment was revoked
        var revokedAssignment = await _context.TemplateAccessAssignments
            .FirstOrDefaultAsync(taa => taa.UserId == processOwnerUser.AdObjectGuid && taa.AuditTemplateId == template.Id);
        
        revokedAssignment.Should().NotBeNull();
        revokedAssignment!.IsActive.Should().BeFalse();
    }

    private async Task<User> CreateTestUserAsync(string username, UserRole role)
    {
        var roleEntity = await _context.Roles.FirstAsync(r => r.RoleName == role);
        var factory = await _context.Factories.FirstAsync();
        
        var user = User.Create(
            username,
            "Test",
            "User",
            $"{username}@test.com",
            roleEntity.Id,
            factory.Id,
            true,
            Guid.NewGuid().ToString(),
            $"CN={username},OU=Users,DC=test,DC=com",
            "admin-user-guid"
        );

        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();
        
        return user;
    }

    private async Task<AuditTemplate> CreateTestTemplateAsync()
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test template description",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedByUserId = "admin-user-guid",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.AuditTemplates.AddAsync(template);
        await _context.SaveChangesAsync();

        return template;
    }

    private async Task SeedTestDataAsync()
    {
        await TestDbContextFactory.SeedTestDataAsync(_context);
    }

    public void Dispose()
    {
        _client?.Dispose();
        _factory?.Dispose();
    }

    // Helper class to match the API response structure
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
