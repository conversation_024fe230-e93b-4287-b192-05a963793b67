using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for User entity
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("hwsap_usr_users");

        // Primary Key
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(u => u.Username)
            .HasMaxLength(256)
            .IsRequired();

        builder.Property(u => u.FirstName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.LastName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.Email)
            .HasMaxLength(256)
            .IsRequired(false);

        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(u => u.LastLoginDate)
            .IsRequired(false);

        builder.Property(u => u.AdObjectGuid)
            .HasMaxLength(36)
            .IsRequired(true);

        builder.Property(u => u.AdDistinguishedName)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(u => u.AdSyncLastDate)
            .IsRequired(false);

        // Auditable properties
        builder.Property(u => u.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(u => u.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(u => u.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(u => u.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        builder.Property(u => u.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        builder.HasIndex(u => u.AdObjectGuid)
            .IsUnique()
            .HasDatabaseName("IX_Users_AdObjectGuid")
            .HasFilter("[AdObjectGuid] IS NOT NULL");

        builder.HasIndex(u => u.RoleId)
            .HasDatabaseName("IX_Users_RoleId");

        builder.HasIndex(u => u.FactoryId)
            .HasDatabaseName("IX_Users_FactoryId");

        // Relationships
        builder.HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId)
            .OnDelete(DeleteBehavior.Restrict);

        // Factory relationship is configured from Factory side

        // Ignore domain events and duplicate navigation properties
        builder.Ignore(u => u.DomainEvents);
        builder.Ignore(u => u.UserGroupMembers); // Use UserGroupMemberships instead
    }
}

/// <summary>
/// Entity Framework configuration for ProcessOwnerAssignment entity
/// </summary>
public class ProcessOwnerAssignmentConfiguration : IEntityTypeConfiguration<ProcessOwnerAssignment>
{
    public void Configure(EntityTypeBuilder<ProcessOwnerAssignment> builder)
    {
        builder.ToTable("hwsap_usr_process_owner_assignments");

        // Primary Key
        builder.HasKey(poa => poa.Id);
        builder.Property(poa => poa.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(poa => poa.ProcessOwnerUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(poa => poa.AssignedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(poa => poa.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(poa => poa.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        // Auditable properties
        builder.Property(poa => poa.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(poa => poa.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(poa => poa.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(poa => poa.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(poa => poa.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Relationships
        builder.HasOne(poa => poa.ProcessOwnerUser)
            .WithMany()
            .HasForeignKey(poa => poa.ProcessOwnerUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(poa => poa.AssignedByUser)
            .WithMany()
            .HasForeignKey(poa => poa.AssignedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder.HasOne(poa => poa.Factory)
            .WithMany()
            .HasForeignKey(poa => poa.FactoryId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder.HasOne(poa => poa.Area)
            .WithMany()
            .HasForeignKey(poa => poa.AreaId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder.HasOne(poa => poa.SubArea)
            .WithMany()
            .HasForeignKey(poa => poa.SubAreaId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(poa => poa.ProcessOwnerUserId)
            .HasDatabaseName("IX_ProcessOwnerAssignments_ProcessOwnerUserId");

        builder.HasIndex(poa => new { poa.ProcessOwnerUserId, poa.FactoryId, poa.AreaId, poa.SubAreaId })
            .HasDatabaseName("IX_ProcessOwnerAssignments_Scope")
            .IsUnique();

        builder.HasIndex(poa => poa.IsActive)
            .HasDatabaseName("IX_ProcessOwnerAssignments_IsActive");

        // Ignore domain events
        builder.Ignore(poa => poa.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for TemplateAccessAssignment entity
/// </summary>
public class TemplateAccessAssignmentConfiguration : IEntityTypeConfiguration<TemplateAccessAssignment>
{
    public void Configure(EntityTypeBuilder<TemplateAccessAssignment> builder)
    {
        builder.ToTable("hwsap_usr_template_access_assignments");

        // Primary Key
        builder.HasKey(taa => taa.Id);
        builder.Property(taa => taa.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(taa => taa.UserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(taa => taa.AuditTemplateId)
            .IsRequired();

        builder.Property(taa => taa.AssignedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(taa => taa.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(taa => taa.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(taa => taa.ExpiresAt)
            .IsRequired(false);

        // Auditable properties
        builder.Property(taa => taa.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(taa => taa.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(taa => taa.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(taa => taa.CreatedByUserId)
            .HasMaxLength(36)
            .IsRequired();

        builder.Property(taa => taa.UpdatedByUserId)
            .HasMaxLength(36)
            .IsRequired(false);

        // Relationships
        builder.HasOne(taa => taa.User)
            .WithMany()
            .HasForeignKey(taa => taa.UserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(taa => taa.AssignedByUser)
            .WithMany()
            .HasForeignKey(taa => taa.AssignedByUserId)
            .HasPrincipalKey(u => u.AdObjectGuid)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder.HasOne(taa => taa.AuditTemplate)
            .WithMany()
            .HasForeignKey(taa => taa.AuditTemplateId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        // Indexes
        builder.HasIndex(taa => taa.UserId)
            .HasDatabaseName("IX_TemplateAccessAssignments_UserId");

        builder.HasIndex(taa => taa.AuditTemplateId)
            .HasDatabaseName("IX_TemplateAccessAssignments_AuditTemplateId");

        builder.HasIndex(taa => new { taa.UserId, taa.AuditTemplateId })
            .HasDatabaseName("IX_TemplateAccessAssignments_UserTemplate")
            .IsUnique();

        builder.HasIndex(taa => taa.IsActive)
            .HasDatabaseName("IX_TemplateAccessAssignments_IsActive");

        builder.HasIndex(taa => taa.ExpiresAt)
            .HasDatabaseName("IX_TemplateAccessAssignments_ExpiresAt");

        // Ignore domain events
        builder.Ignore(taa => taa.DomainEvents);
    }
}
