using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Audits.Commands.SubmitAudit;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Commands;

public class SubmitAuditCommandHandlerTests : BaseDbTestClass
{
    private readonly SubmitAuditCommandHandler _handler;

    public SubmitAuditCommandHandlerTests()
    {
        _handler = new SubmitAuditCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldSubmitAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithAnswersAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id,
            AuditorComments = "Audit completed successfully"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.OverallStatus.Should().Be(AuditOverallStatus.PendingManagerReview);
        updatedAudit.CompletedAt.Should().NotBeNull();
        updatedAudit.CompletedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithNonExistentAudit_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = "non-existent-id"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithScheduledAudit_ShouldThrowInvalidOperationException()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(AuditOverallStatus.Scheduled);
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithWeightedQuestions_ShouldCalculateScore()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithWeightedAnswersAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.OverallScore.Should().NotBeNull();
        updatedAudit.OverallScore.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_WithoutWeightedQuestions_ShouldNotCalculateScore()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithAnswersAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.OverallScore.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldSetUpdatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-current-user";
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var audit = await CreateTestAuditWithAnswersAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.UpdatedByUserId.Should().Be(currentUserId);
        updatedAudit.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task Handle_WithAuditorComments_ShouldStoreComments()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditWithAnswersAsync();
        
        var command = new SubmitAuditCommand
        {
            AuditId = audit.Id,
            AuditorComments = "All checks completed successfully"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedAudit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == audit.Id);
        updatedAudit.Should().NotBeNull();
        updatedAudit!.ManagerComments.Should().Be("All checks completed successfully");
    }

    private async Task<Audit> CreateTestAuditAsync(AuditOverallStatus status = AuditOverallStatus.InProgress)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = status,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }

    private async Task<Audit> CreateTestAuditWithAnswersAsync()
    {
        var audit = await CreateTestAuditAsync();

        var question = new Question
        {
            Id = 1,
            AuditTemplateId = audit.AuditTemplateId,
            QuestionText = "Test Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);

        var answer = new AuditAnswer
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerBoolean = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditAnswers.Add(answer);
        await Context.SaveChangesAsync();

        return audit;
    }

    private async Task<Audit> CreateTestAuditWithWeightedAnswersAsync()
    {
        var audit = await CreateTestAuditAsync();

        var question = new Question
        {
            Id = 1,
            AuditTemplateId = audit.AuditTemplateId,
            QuestionText = "Weighted Test Question",
            QuestionType = QuestionType.YesNo,
            DisplayOrder = 1,
            IsRequired = true,
            Weight = 10.0m,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);

        var answer = new AuditAnswer
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerBoolean = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditAnswers.Add(answer);
        await Context.SaveChangesAsync();

        return audit;
    }
}
