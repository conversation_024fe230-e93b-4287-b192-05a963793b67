using FluentAssertions;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Events;

namespace HWSAuditPlatform.Tests.Domain.Common;

public class BaseEntityTests
{
    private class TestEntity : BaseEntity<int>
    {
        public TestEntity(int id)
        {
            Id = id;
        }

        public void RaiseTestEvent()
        {
            AddDomainEvent(new TestDomainEvent());
        }
    }

    private class TestDomainEvent : IDomainEvent
    {
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    [Fact]
    public void Constructor_ShouldSetTimestamps()
    {
        // Arrange & Act
        var entity = new TestEntity(1);

        // Assert
        entity.Id.Should().Be(1);
        entity.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        entity.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        entity.DomainEvents.Should().BeEmpty();
    }

    [Fact]
    public void AddDomainEvent_ShouldAddEventToCollection()
    {
        // Arrange
        var entity = new TestEntity(1);

        // Act
        entity.RaiseTestEvent();

        // Assert
        entity.DomainEvents.Should().HaveCount(1);
        entity.DomainEvents.First().Should().BeOfType<TestDomainEvent>();
    }

    [Fact]
    public void ClearDomainEvents_ShouldRemoveAllEvents()
    {
        // Arrange
        var entity = new TestEntity(1);
        entity.RaiseTestEvent();
        entity.RaiseTestEvent();

        // Act
        entity.ClearDomainEvents();

        // Assert
        entity.DomainEvents.Should().BeEmpty();
    }

    [Fact]
    public void Equals_WithSameId_ShouldReturnTrue()
    {
        // Arrange
        var entity1 = new TestEntity(1);
        var entity2 = new TestEntity(1);

        // Act & Assert
        entity1.Should().Be(entity2);
        (entity1 == entity2).Should().BeTrue();
        (entity1 != entity2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentId_ShouldReturnFalse()
    {
        // Arrange
        var entity1 = new TestEntity(1);
        var entity2 = new TestEntity(2);

        // Act & Assert
        entity1.Should().NotBe(entity2);
        (entity1 == entity2).Should().BeFalse();
        (entity1 != entity2).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDefaultId_ShouldReturnFalse()
    {
        // Arrange
        var entity1 = new TestEntity(0);
        var entity2 = new TestEntity(0);

        // Act & Assert
        entity1.Should().NotBe(entity2);
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var entity = new TestEntity(1);

        // Act & Assert
        entity.Should().NotBe(null);
        (entity == null).Should().BeFalse();
        (entity != null).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_WithSameId_ShouldReturnSameHashCode()
    {
        // Arrange
        var entity1 = new TestEntity(1);
        var entity2 = new TestEntity(1);

        // Act & Assert
        entity1.GetHashCode().Should().Be(entity2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_WithDifferentId_ShouldReturnDifferentHashCode()
    {
        // Arrange
        var entity1 = new TestEntity(1);
        var entity2 = new TestEntity(2);

        // Act & Assert
        entity1.GetHashCode().Should().NotBe(entity2.GetHashCode());
    }
}
