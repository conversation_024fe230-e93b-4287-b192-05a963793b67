/* Local Font Definitions - HWS Audit Platform PWA */

/* Roboto Font Family */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('../lib/fonts/roboto/roboto-light.woff2') format('woff2'),
         url('../lib/fonts/roboto/roboto-light.ttf') format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../lib/fonts/roboto/roboto-regular.woff2') format('woff2'),
         url('../lib/fonts/roboto/roboto-regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../lib/fonts/roboto/roboto-medium.woff2') format('woff2'),
         url('../lib/fonts/roboto/roboto-medium.ttf') format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('../lib/fonts/roboto/roboto-bold.woff2') format('woff2'),
         url('../lib/fonts/roboto/roboto-bold.ttf') format('truetype');
}

/* Roboto Condensed Font Family */
@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../lib/fonts/roboto-condensed/roboto-condensed-regular.woff2') format('woff2'),
         url('../lib/fonts/roboto-condensed/roboto-condensed-regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('../lib/fonts/roboto-condensed/roboto-condensed-bold.ttf') format('truetype');
}
