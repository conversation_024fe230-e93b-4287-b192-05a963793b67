using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Models;

/// <summary>
/// Request model for creating audit templates
/// </summary>
public class CreateAuditTemplateRequest
{
    [Required(ErrorMessage = "Template name is required")]
    [StringLength(255, ErrorMessage = "Template name cannot exceed 255 characters")]
    public string TemplateName { get; set; } = string.Empty;

    [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request model for creating audits
/// </summary>
public class CreateAuditRequest
{
    [Required(ErrorMessage = "Audit template is required")]
    public int AuditTemplateId { get; set; }

    [Required(ErrorMessage = "Assignment type is required")]
    public AssignmentType AssignmentType { get; set; } = AssignmentType.Individual;

    public string? AssignedToUserGroupId { get; set; }

    public string? AssignedToUserId { get; set; }

    [Required(ErrorMessage = "Scheduled date is required")]
    public DateTime ScheduledDate { get; set; } = DateTime.Today.AddDays(1);

    public DateTime? DueDate { get; set; }

    [Required(ErrorMessage = "Factory is required")]
    public int FactoryId { get; set; }

    [Required(ErrorMessage = "Area is required")]
    public int AreaId { get; set; }

    public int? SubAreaId { get; set; }
}

/// <summary>
/// Request model for updating audit templates
/// </summary>
public class UpdateAuditTemplateRequest
{
    [Required(ErrorMessage = "Template name is required")]
    [StringLength(255, ErrorMessage = "Template name cannot exceed 255 characters")]
    public string TemplateName { get; set; } = string.Empty;

    [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request model for creating questions
/// </summary>
public class CreateQuestionRequest
{
    public int? QuestionGroupId { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public string QuestionType { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public bool IsRequired { get; set; }
    public decimal? Weight { get; set; }
    public string? HelpText { get; set; }
    public int? ParentQuestionId { get; set; }
    public string? TriggerAnswerValue { get; set; }
    public string? SeverityLevel { get; set; }
    public bool EvidenceRequired { get; set; }
    public List<string> AllowedEvidenceTypes { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public List<CreateQuestionOptionRequest> Options { get; set; } = new();
}

/// <summary>
/// Request model for updating questions
/// </summary>
public class UpdateQuestionRequest
{
    public int? QuestionGroupId { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public string QuestionType { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public bool IsRequired { get; set; }
    public decimal? Weight { get; set; }
    public string? HelpText { get; set; }
    public int? ParentQuestionId { get; set; }
    public string? TriggerAnswerValue { get; set; }
    public string? SeverityLevel { get; set; }
    public bool EvidenceRequired { get; set; }
    public List<string> AllowedEvidenceTypes { get; set; } = new();
    public bool IsActive { get; set; }
    public List<CreateQuestionOptionRequest> Options { get; set; } = new();
}

/// <summary>
/// Request model for creating question options
/// </summary>
public class CreateQuestionOptionRequest
{
    public string OptionText { get; set; } = string.Empty;
    public string? OptionValue { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; } = true;
}
