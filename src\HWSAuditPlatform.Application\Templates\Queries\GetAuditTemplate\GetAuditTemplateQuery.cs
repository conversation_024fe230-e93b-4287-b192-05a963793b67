using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;

/// <summary>
/// Query to get a single audit template with full details
/// </summary>
public class GetAuditTemplateQuery : BaseQuery<AuditTemplateDto>
{
    /// <summary>
    /// The ID of the audit template to retrieve
    /// </summary>
    public int Id { get; set; }

    public GetAuditTemplateQuery(int id)
    {
        Id = id;
    }    
}

/// <summary>
/// Query to retrieve a specific audit template, ensuring it is active.
/// The corresponding handler for this query must filter to return the template 
/// only if its <see cref="AuditTemplateDto.IsActive"/> property is true.
/// </summary>
public class GetAuditActiveTemplateQuery : BaseQuery<AuditTemplateDto>
{
    /// <summary>
    /// The ID of the audit template to retrieve.
    /// </summary>
    public int Id { get; set; }

    public GetAuditActiveTemplateQuery(int id)
    {
        Id = id;
    }
}