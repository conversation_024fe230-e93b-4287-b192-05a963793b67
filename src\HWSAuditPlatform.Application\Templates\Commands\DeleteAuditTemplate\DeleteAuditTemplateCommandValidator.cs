using FluentValidation;

namespace HWSAuditPlatform.Application.Templates.Commands.DeleteAuditTemplate;

/// <summary>
/// Validator for DeleteAuditTemplateCommand
/// </summary>
public class DeleteAuditTemplateCommandValidator : AbstractValidator<DeleteAuditTemplateCommand>
{
    public DeleteAuditTemplateCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Template ID must be greater than 0");

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0)
            .WithMessage("Record version must be greater than 0");
    }
}
