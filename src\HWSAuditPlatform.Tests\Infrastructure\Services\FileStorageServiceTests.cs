using Castle.Core.Logging;
using FluentAssertions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Infrastructure.Services;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;
using HWSAuditPlatform.Infrastructure.Services.FileStorage;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
namespace HWSAuditPlatform.Tests.Infrastructure.Services;

public class FileStorageServiceTests : IDisposable
{
    private readonly string _testDirectory;
    private readonly Mock<IOptions<LocalFileStorageOptions>> _mockOptions;
    private readonly Mock<ILogger<LocalFileStorageService>> _mockLogger;

    private readonly LocalFileStorageService _service;

    public FileStorageServiceTests()
    {
        _testDirectory = Path.Combine(Path.GetTempPath(), "HWSAuditPlatformTests", Guid.NewGuid().ToString());

        Directory.CreateDirectory(_testDirectory);

        var options = new LocalFileStorageOptions
        {
            StoragePath = _testDirectory,
            BaseUrl = "https://localhost:5001"
        };

        _mockOptions = new();
        _mockOptions.Setup(x => x.Value).Returns(options);
        _mockLogger = new();
        _mockLogger.Setup(x => x.IsEnabled(It.IsAny<LogLevel>())).Returns(true);

        _service = new LocalFileStorageService(_mockOptions.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task UploadFileAsync_WithValidFile_ShouldSaveFile()
    {
        // Arrange
        var fileName = "test.txt";
        var content = "Test file content"u8.ToArray();
        var stream = new MemoryStream(content);

        // Act
        var result = await _service.UploadFileAsync(fileName,  "text/plain", stream);

        // Assert
        result.Should().NotBeNull();
        result.FileName.Should().Be(fileName);
        result.ContentType.Should().Be("text/plain");
        result.Size.Should().Be(content.Length);
        result.StoragePath.Should().NotBeNullOrEmpty();
        result.Url.Should().StartWith("https://localhost:5001");

        // Verify file exists
        var filePath = Path.Combine(_testDirectory, result.StoragePath);
        File.Exists(filePath).Should().BeTrue();
        
        var savedContent = await File.ReadAllBytesAsync(filePath);
        savedContent.Should().BeEquivalentTo(content);
    }

    [Fact]
    public async Task UploadFileAsync_WithLargeFile_ShouldSaveFile()
    {
        // Arrange
        var fileName = "large.bin";
        var content = new byte[1024 * 1024]; // 1MB
        new Random().NextBytes(content);
        var stream = new MemoryStream(content);

        // Act
        var result = await _service.UploadFileAsync(fileName, stream, "application/octet-stream");

        // Assert
        result.Should().NotBeNull();
        result.Size.Should().Be(content.Length);
        
        var filePath = Path.Combine(_testDirectory, result.StoragePath);
        File.Exists(filePath).Should().BeTrue();
        
        var fileInfo = new FileInfo(filePath);
        fileInfo.Length.Should().Be(content.Length);
    }

    [Fact]
    public async Task UploadFileAsync_WithSpecialCharactersInFileName_ShouldSanitizeFileName()
    {
        // Arrange
        var fileName = "test file with spaces & special chars!.txt";
        var content = "Test content"u8.ToArray();
        var stream = new MemoryStream(content);

        // Act
        var result = await _service.UploadFileAsync(fileName, "text/plain", stream);

        // Assert
        result.Should().NotBeNull();
        result.FileName.Should().Be(fileName); // Original name preserved
        result.StoragePath.Should().NotContain(" "); // Storage path sanitized
        result.StoragePath.Should().NotContain("&");
        result.StoragePath.Should().NotContain("!");
    }

    [Fact]
    public async Task UploadFileAsync_WithDuplicateFileName_ShouldCreateUniqueStoragePath()
    {
        // Arrange
        var fileName = "duplicate.txt";
        var content1 = "Content 1"u8.ToArray();
        var content2 = "Content 2"u8.ToArray();
        var stream1 = new MemoryStream(content1);
        var stream2 = new MemoryStream(content2);

        // Act
        var result1 = await _service.UploadFileAsync(fileName, stream1, "text/plain");
        var result2 = await _service.UploadFileAsync(fileName, stream2, "text/plain");

        // Assert
        result1.StoragePath.Should().NotBe(result2.StoragePath);
        
        var filePath1 = Path.Combine(_testDirectory, result1.StoragePath);
        var filePath2 = Path.Combine(_testDirectory, result2.StoragePath);
        
        File.Exists(filePath1).Should().BeTrue();
        File.Exists(filePath2).Should().BeTrue();
        
        var savedContent1 = await File.ReadAllBytesAsync(filePath1);
        var savedContent2 = await File.ReadAllBytesAsync(filePath2);
        
        savedContent1.Should().BeEquivalentTo(content1);
        savedContent2.Should().BeEquivalentTo(content2);
    }

    [Fact]
    public async Task GetFileAsync_WithValidPath_ShouldReturnFileStream()
    {
        // Arrange
        var fileName = "test.txt";
        var content = "Test file content"u8.ToArray();
        var stream = new MemoryStream(content);
        var uploadResult = await _service.UploadFileAsync(fileName, stream, "text/plain");

        // Act
        var fileStream = await _service.GetFileAsync(uploadResult.StoragePath);

        // Assert
        fileStream.Should().NotBeNull();
        
        using var memoryStream = new MemoryStream();
        await fileStream.CopyToAsync(memoryStream);
        var retrievedContent = memoryStream.ToArray();
        
        retrievedContent.Should().BeEquivalentTo(content);
    }

    [Fact]
    public async Task GetFileAsync_WithNonExistentPath_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentPath = "non-existent/file.txt";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(() => 
            _service.GetFileAsync(nonExistentPath));
    }

    [Fact]
    public async Task DeleteFileAsync_WithValidPath_ShouldDeleteFile()
    {
        // Arrange
        var fileName = "test.txt";
        var content = "Test file content"u8.ToArray();
        var stream = new MemoryStream(content);
        var uploadResult = await _service.UploadFileAsync(fileName, stream, "text/plain");
        
        var filePath = Path.Combine(_testDirectory, uploadResult.StoragePath);
        File.Exists(filePath).Should().BeTrue(); // Verify file exists

        // Act
        await _service.DeleteFileAsync(uploadResult.StoragePath);

        // Assert
        File.Exists(filePath).Should().BeFalse();
    }

    [Fact]
    public async Task DeleteFileAsync_WithNonExistentPath_ShouldNotThrow()
    {
        // Arrange
        var nonExistentPath = "non-existent/file.txt";

        // Act & Assert
        var act = async () => await _service.DeleteFileAsync(nonExistentPath);
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task FileExistsAsync_WithExistingFile_ShouldReturnTrue()
    {
        // Arrange
        var fileName = "test.txt";
        var content = "Test file content"u8.ToArray();
        var stream = new MemoryStream(content);
        var uploadResult = await _service.UploadFileAsync(fileName, stream, "text/plain");

        // Act
        var exists = await _service.FileExistsAsync(uploadResult.StoragePath);

        // Assert
        exists.Should().BeTrue();
    }

    [Fact]
    public async Task FileExistsAsync_WithNonExistentFile_ShouldReturnFalse()
    {
        // Arrange
        var nonExistentPath = "non-existent/file.txt";

        // Act
        var exists = await _service.FileExistsAsync(nonExistentPath);

        // Assert
        exists.Should().BeFalse();
    }

    [Fact]
    public async Task UploadFileAsync_WithEmptyStream_ShouldCreateEmptyFile()
    {
        // Arrange
        var fileName = "empty.txt";
        var stream = new MemoryStream();

        // Act
        var result = await _service.UploadFileAsync(fileName, stream, "text/plain");

        // Assert
        result.Should().NotBeNull();
        result.Size.Should().Be(0);
        
        var filePath = Path.Combine(_testDirectory, result.StoragePath);
        File.Exists(filePath).Should().BeTrue();
        
        var fileInfo = new FileInfo(filePath);
        fileInfo.Length.Should().Be(0);
    }

    [Fact]
    public async Task UploadFileAsync_ShouldCreateDirectoryStructure()
    {
        // Arrange
        var fileName = "test.txt";
        var content = "Test content"u8.ToArray();
        var stream = new MemoryStream(content);

        // Act
        var result = await _service.UploadFileAsync(fileName, stream, "text/plain");

        // Assert
        var filePath = Path.Combine(_testDirectory, result.StoragePath);
        var directory = Path.GetDirectoryName(filePath);
        Directory.Exists(directory).Should().BeTrue();
    }

    [Theory]
    [InlineData("test.txt", "text/plain")]
    [InlineData("image.jpg", "image/jpeg")]
    [InlineData("document.pdf", "application/pdf")]
    [InlineData("data.json", "application/json")]
    public async Task UploadFileAsync_WithDifferentContentTypes_ShouldPreserveContentType(
        string fileName, string contentType)
    {
        // Arrange
        var content = "Test content"u8.ToArray();
        var stream = new MemoryStream(content);

        // Act
        var result = await _service.UploadFileAsync(fileName, stream, contentType);

        // Assert
        result.ContentType.Should().Be(contentType);
    }

    public void Dispose()
    {
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }
}
