@page "/templates/finding-categories"
@using HWSAuditPlatform.WebApp.Components.Templates
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject IFindingCategoryApiService FindingCategoryApiService
@inject ITemplateApiService TemplateApiService
@inject ILogger<FindingCategories> Logger

<PageTitle>Finding Categories - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Finding Categories</h2>
                    <p class="text-muted">Manage finding categorization for better reporting and analysis.</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary me-2" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Template Selection</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Audit Template</label>
                        <select class="form-select" @bind="selectedTemplateId" @bind:after="OnTemplateChangedWrapper">
                            <option value="">Select a template...</option>
                            @foreach (var template in templates)
                            {
                                <option value="@template.Id">@template.TemplateName <EMAIL></option>
                            }
                        </select>
                    </div>

                    @if (selectedTemplate != null)
                    {
                        <div class="template-info">
                            <h6>Template Information</h6>
                            <div class="info-item">
                                <strong>Name:</strong> @selectedTemplate.TemplateName
                            </div>
                            <div class="info-item">
                                <strong>Version:</strong> @selectedTemplate.Version
                            </div>
                            <div class="info-item">
                                <strong>Status:</strong> 
                                @if (selectedTemplate.IsPublished)
                                {
                                    <span class="badge bg-success">Published</span>
                                }
                                else
                                {
                                    <span class="badge bg-warning">Draft</span>
                                }
                            </div>
                            <div class="info-item">
                                <strong>Categorization:</strong>
                                @if (selectedTemplate.EnableFindingCategorization)
                                {
                                    <span class="badge bg-success">Enabled</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Disabled</span>
                                }
                            </div>
                        </div>

                        @if (!selectedTemplate.EnableFindingCategorization)
                        {
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-2"></i>
                                Finding categorization is not enabled for this template. 
                                Enable it in template settings to add categories.
                            </div>
                        }
                    }

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" @bind="showUsageStatistics" @bind:after="OnShowStatisticsChanged" id="showStats">
                        <label class="form-check-label" for="showStats">
                            Show usage statistics
                        </label>
                    </div>
                </div>
            </div>

            @if (selectedTemplateId.HasValue && statistics != null)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value">@statistics.TotalCategories</div>
                                    <div class="stat-label">Categories</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value">@statistics.ActiveCategories</div>
                                    <div class="stat-label">Active</div>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center mt-2">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value">@statistics.TotalFindings</div>
                                    <div class="stat-label">Findings</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-value">@statistics.CategorizedFindings</div>
                                    <div class="stat-label">Categorized</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="col-md-9">
            <FindingCategoryList 
                AuditTemplateId="selectedTemplateId"
                ShowUsageStatistics="showUsageStatistics"
                OnCategoryChanged="OnCategoryChanged" />
        </div>
    </div>
</div>

<style>
    .template-info {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 0.75rem;
        margin-top: 1rem;
    }

    .info-item {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .stat-item {
        padding: 0.5rem 0;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
    }
</style>

@code {
    private List<AuditTemplate> templates = new();
    private int? selectedTemplateId = null;
    private AuditTemplate? selectedTemplate = null;
    private bool showUsageStatistics = false;
    private CategoryStatisticsModel? statistics = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplates();
    }

    private async Task LoadTemplates()
    {
        try
        {
            templates = (await TemplateApiService.GetTemplatesAsync()).ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading templates");
        }
    }

    private void OnTemplateChangedWrapper()
    {
        selectedTemplate = templates.FirstOrDefault(t => t.Id == selectedTemplateId);
        _ = LoadStatistics(); // Fire and forget
    }

    private async Task OnTemplateChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var templateId))
        {
            selectedTemplateId = templateId;
            selectedTemplate = templates.FirstOrDefault(t => t.Id == templateId);
        }
        else
        {
            selectedTemplateId = null;
            selectedTemplate = null;
        }

        statistics = null;
        await LoadStatistics();
        StateHasChanged();
    }

    private async Task OnShowStatisticsChanged()
    {
        await LoadStatistics();
        StateHasChanged();
    }

    private async Task LoadStatistics()
    {
        if (!selectedTemplateId.HasValue || !showUsageStatistics)
        {
            statistics = null;
            return;
        }

        try
        {
            statistics = await FindingCategoryApiService.GetCategoryStatisticsAsync(selectedTemplateId.Value);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading category statistics for template {TemplateId}", selectedTemplateId);
            statistics = null;
        }
    }

    private async Task RefreshData()
    {
        await LoadTemplates();
        await LoadStatistics();
        StateHasChanged();
    }

    private async Task OnCategoryChanged()
    {
        await LoadStatistics();
        StateHasChanged();
    }
}
